{"name": "ng", "version": "1.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "~18.2.13", "@angular/cdk": "~18.2.14", "@angular/common": "~18.2.13", "@angular/compiler": "~18.2.13", "@angular/core": "~18.2.13", "@angular/forms": "~18.2.13", "@angular/platform-browser": "~18.2.13", "@angular/platform-browser-dynamic": "~18.2.13", "@angular/router": "~18.2.13", "@date-fns/utc": "^1.2.0", "@lsw/error-lib": "^18.1.7", "@lsw/building-blocks": "18.7.1", "@lsw/notification-hub": "^18.3.0", "@lsw/pipes": "^18.1.0", "chart.js": "^3.9.1", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "filesize": "^8.0.7", "primeicons": "^5.0.0", "primeng": "^17.18.15", "rxjs": "~7.8.2", "tslib": "^2.8.1", "zone.js": "^0.14.4"}, "devDependencies": {"@angular-devkit/build-angular": "~18.2.19", "@angular-eslint/builder": "~18.4.3", "@angular-eslint/eslint-plugin": "~18.4.3", "@angular-eslint/eslint-plugin-template": "~18.4.3", "@angular-eslint/schematics": "~18.4.3", "@angular-eslint/template-parser": "~18.4.3", "@angular/cli": "^18.2.19", "@angular/compiler-cli": "~18.2.13", "@angular/localize": "^18.2.13", "@types/node": "^20.17.57", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.1", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsdoc": "^48.11.0", "eslint-plugin-prefer-arrow": "1.2.3", "typescript": "~5.5.4"}}