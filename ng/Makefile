all: dependencies lint locale build

.PHONY: dependencies
dependencies:
	npm install

.PHONY: lint
lint:
	prettier --check 'src/app/**/*.ts' 'src/app/**/*.html' 'src/app/**/*.css'
	node_modules/.bin/ng lint

.PHONY: watch
watch:
	node_modules/.bin/ng build --watch --configuration development

.PHONY: build
build:
	node_modules/.bin/ng build --configuration production

.PHONY: locale
locale:
	node_modules/.bin/ng extract-i18n --output-path src/locale
