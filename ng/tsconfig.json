{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "forceConsistentCasingInFileNames": true, "strict": false, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "sourceMap": true, "declaration": false, "downlevelIteration": true, "experimentalDecorators": true, "moduleResolution": "node", "importHelpers": true, "target": "ES2022", "module": "es2020", "resolveJsonModule": true, "lib": ["es2020", "dom"], "useDefineForClassFields": false, "skipLibCheck": true, "typeRoots": ["./src/types"]}, "angularCompilerOptions": {"allowSyntheticDefaultImports": true, "enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": false, "strictInputAccessModifiers": false, "strictTemplates": false, "enableIvy": false}}