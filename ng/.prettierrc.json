{"arrowParens": "always", "bracketSpacing": true, "embeddedLanguageFormatting": "off", "endOfLine": "lf", "htmlWhitespaceSensitivity": "css", "bracketSameLine": true, "jsxSingleQuote": false, "printWidth": 120, "quoteProps": "as-needed", "semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false, "overrides": [{"files": "*.html", "options": {"printWidth": 999, "singleQuote": false, "tabWidth": 4}}]}