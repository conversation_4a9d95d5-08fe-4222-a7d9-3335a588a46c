/* Make <button pButton class="p-button-link"> and <a pButton class="p-button-link"> look similar */

.p-button-link:not(:disabled):hover .p-button-label {
  text-decoration: underline !important;
}

.p-button-link,
.p-button-link:hover,
.p-button:not(:disabled):active {
  background-color: transparent !important;
  color: #5685c4 !important;
}

/* Remove white background on job-wizard tab view panels */

app-job-wizard .p-tabview .p-tabview-panels {
  background: none;
}

app-private-network-add-server-modal-v3 p-table div.p-datatable {
  padding-left: 0;
  padding-right: 0;
}

app-private-network-add-rack-modal-v3 p-table div.p-datatable {
  padding-left: 0;
  padding-right: 0;
}

app-private-network-add-dedicated-storage-modal-v3 p-table div.p-datatable {
  padding-left: 0;
  padding-right: 0;
}

p-dialog button.p-dialog-header-icon.p-dialog-header-close {
  padding: 1rem !important;
}

p-dialog p-radiobutton[ng-reflect-disabled="true"] {
  pointer-events: none;
}

app-private-network-add-server-modal-v3 p-dialog p-table tr.disabled p-radiobutton + label {
  pointer-events: none;
}