import { Rack } from '../v3/model/racks.model';
import { FeatureAvailability } from '../v3/model/shared.model';

export interface Server {
  id: string;
  assetId: string;
  location: Location;
  rack: Rack;
  specs: Specs;
  serialNumber: string;
  networkInterfaces: NetworkInterfaces;
  powerPorts: PowerPort[];
  featureAvailability: FeatureAvailability;
  contract: Contract;
  privateNetworks: any[];
  isPrivateNetworkEnabled: boolean;
  isPrivateNetworkCapable: boolean;
}

interface Location {
  site: string;
  suite: string;
  rack: string;
  unit: string;
}

interface Specs {
  brand: string;
  chassis: string;
  hardwareRaidCapable: boolean;
  cpu: Cpu;
  ram: Ram;
  hdd: Hdd[];
  pciCards: any[];
}

interface Cpu {
  type: string;
  quantity: number;
}

interface Ram {
  size: number;
  unit: string;
}

interface Hdd {
  id: string;
  size: number;
  unit: string;
  amount: number;
  type: string;
  performanceType: any;
}

interface NetworkInterfaces {
  public: NetworkInterface;
  internal: NetworkInterface;
  remoteManagement: NetworkInterface;
}

interface NetworkInterface {
  mac: string;
  ip?: string;
  nullRouted?: boolean;
  gateway?: string;
  ports: Port[];
  locationId?: any;
}

interface Port {
  name: string;
  port: string;
}

type PowerPort = Port;

interface Contract {
  id: string;
  customerId: string;
  salesOrgId: string;
  deliveryStatus: string;
  reference?: any;
  subnets?: any[];
  status: string;
  startsAt: string;
  endsAt?: string;
  sla: string;
  contractTerm: number;
  contractType: string;
  billingCycle: number;
  billingFrequency: string;
  pricePerFrequency: number;
  currency: string;
  networkTraffic: NetworkTraffic;
  softwareLicenses: SoftwareLicense[];
}

interface NetworkTraffic {
  type: string;
  connectivityType: string;
  trafficType: string;
  datatrafficLimit?: any;
  datatrafficUnit?: any;
}

interface SoftwareLicense {
  name: string;
  price?: number;
  currency: string;
  type: string;
}
