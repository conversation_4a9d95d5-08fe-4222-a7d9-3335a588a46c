import { Component, OnInit, OnChanges, Input } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { CurrentUserService } from 'src/app/services/current-user.service';
import filesize from 'filesize';
import { DateFormatterService } from 'src/app/services/date-formatter.service';

@Component({
  selector: 'app-graph-datatraffic',
  templateUrl: './graph-datatraffic.component.html',
  styleUrls: ['./graph-datatraffic.component.css'],
})
export class GraphDatatrafficComponent implements OnInit, OnChanges {
  @Input() customerId: any;
  @Input() salesOrgId: any;
  @Input() equipment: any;
  @Input() equipmentType: any;
  @Input() networkType: any;
  @Input() from: any;
  @Input() to: any;

  // chart data and options
  data: any;
  options: any;

  // legend information
  totalIn: number;
  totalOut: number;
  averageIn: number;
  averageOut: number;
  peakTraffic: number;
  peakDate: string;
  expectedIn: number;
  expectedOut: number;

  constructor(
    private currentUserService: CurrentUserService,
    private httpClient: HttpClient,
    private dateService: DateFormatterService
  ) {}

  ngOnInit(): void {
    this.options = {
      scales: {
        x: {
          offset: true,
          type: 'time',
          time: {
            unit: 'day',
            stepSize: 1,
            tooltipFormat: 'MMM d',
          },
          stacked: true,
          gridLines: {
            drawOnChartArea: false,
            color: '#ebedef',
          },
          grid: {
            color: '#ebedef',
          },
        },
        y: {
          stacked: true,
          scaleLabel: {
            display: true,
            labelString: $localize`:@@bma_graphdatatraffic_trafficperday:Traffic per day`,
          },
          ticks: {
            callback: (value, values) => {
              if (!isNaN(value)) {
                const itemObj: any = filesize(value, { base: 10, output: 'object' });
                return itemObj.value + ' ' + itemObj.unit;
              }
              return '';
            },
          },
          grid: {
            color: '#ebedef',
          },
          gridLines: {
            color: '#ebedef',
          },
          afterDataLimits: (scale) => {
            // add 1% to top
            const range = scale.max - scale.min;
            const grace = range * 0.01;
            scale.max += grace;
          },
        },
      },
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            usePointStyle: true,
            boxWidth: 20,
            padding: 10,
            fontSize: 17,
          },
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          callbacks: {
            label: (context) => {
              const itemObj: any = filesize(context.raw, { base: 10, output: 'object' });
              return itemObj.value.toFixed(2) + ' ' + itemObj.unit;
            },
          },
        },
      },
    };
  }

  ngOnChanges() {
    const id = this.equipmentType === 'networkDevices' ? this.equipment.name : this.equipment.id;
    const url =
      this.equipmentType === 'metrics'
        ? '/_/internal/bmusageapi/metrics/datatraffic'
        : `/_/internal/bmusageapi/v2/${this.equipmentType}/${id}/metrics/datatraffic`;
    const params: any = {
      aggregation: 'SUM',
      granularity: 'DAY',
      from: this.from,
      to: this.to,
    };

    if (this.networkType) {
      params.networkType = this.networkType;
    }

    if (this.equipmentType === 'metrics' && this.currentUserService.isEmployee()) {
      params.customerId = this.customerId;
      params.salesOrgId = this.salesOrgId;
    }

    this.httpClient.get<any>(url, { params }).subscribe(
      (data: any) => {
        const up = data.metrics.UP_PUBLIC?.values || data.metrics.UP_INTERNAL?.values || data.metrics.UP?.values || [];
        const down =
          data.metrics.DOWN_PUBLIC?.values || data.metrics.DOWN_INTERNAL?.values || data.metrics.DOWN?.values || [];
        this.convertToChartData(up, down);
        this.convertToLegendData(up, down);
      },
      (error: any) => {
        console.log(error);
      }
    );
  }

  convertToLegendData(up: Array<any>, down: Array<any>): void {
    const averageFactor = this.dateService.averageFactor(this.from, this.to, down.length);

    this.totalIn = down.reduce((total, metric) => total + metric.value, 0);
    this.totalOut = up.reduce((total, metric) => total + metric.value, 0);
    this.averageIn = this.totalIn / averageFactor;
    this.averageOut = this.totalOut / averageFactor;
    this.expectedIn = this.averageIn * down.length;
    this.expectedOut = this.averageOut * up.length;
    this.peakTraffic = 0;
    this.peakDate = '';

    for (let i = 0; i < up.length; i++) {
      if (up[i].value + down[i].value > this.peakTraffic) {
        this.peakTraffic = up[i].value + down[i].value;
        this.peakDate = this.dateService.init(up[i].timestamp).format('d MMM yyyy'); // TODO do this in the template with a pipe?
      }
    }
  }

  convertToChartData(up: Array<any>, down: Array<any>): void {
    const chartData = {
      labels: [],
      datasets: [
        {
          label: $localize`:@@bma_graphdatatraffic_incoming:Incoming`,
          backgroundColor: this.currentUserService.isFiberring() ? '#663398' : '#2d2e86',
          data: [],
        },
        {
          label: $localize`:@@bma_graphbandwidth_outgoing:Outgoing`,
          backgroundColor: this.currentUserService.isFiberring() ? '#b299cb' : '#5685c4',
          data: [],
        },
      ],
    };

    down.forEach((item) => {
      chartData.labels.push(item.timestamp);
      chartData.datasets[0].data.push(item.value);
    });
    up.forEach((item) => {
      chartData.datasets[1].data.push(item.value);
    });

    this.data = chartData;
  }
}
