import { Component, Input, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { DateFormatterService } from 'src/app/services/date-formatter.service';
import CountriesJson from './../../../data/countries.json';

@Component({
  selector: 'app-ip-performance-measurement-graphs',
  templateUrl: './ip-performance-measurement-graphs.component.html',
  styleUrls: ['./ip-performance-measurement-graphs.component.css'],
})
export class IpPerformanceMeasurementGraphsComponent implements OnInit {
  @Input() measurement: any;
  isGraphAvailable = true;
  probesPerCountry: any;
  probesNr = 0;
  from = '';
  to = '';
  country = '';
  probes = '';
  isEmployee = false;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    private dateService: DateFormatterService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
    this.getProbesPerCountry();
  }

  onDateRangeChange(range: any): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { from: range.from, to: range.to },
      queryParamsHandling: 'merge',
    });
  }

  onCountryChange(country: any): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { country },
      queryParamsHandling: 'merge',
    });
  }

  onSelectAllProbesChange(event: any): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { probes: event.target.checked },
      queryParamsHandling: 'merge',
    });
  }

  onQueryParamsChanged(queryParams): void {
    this.from = queryParams.from || this.dateService.today().startOfMonth().format();
    this.to = queryParams.to || this.dateService.today().addMonths(1).startOfMonth().format();
    this.country = queryParams.country || 'all';
    this.probes = queryParams.probes;
  }

  getProbesPerCountry() {
    const url = '/_/internal/networkperformanceapi/measurements/' + this.measurement.measurementId + '/metrics/latency';
    const params: any = {
      from: this.from,
      to: this.to,
      aggregation: 'AVG',
    };

    this.httpClient.get<any>(url, { params }).subscribe((data: any) => {
      this.probesNr = Object.keys(data._metadata.probes).length;
      const probesPerCountry = [];
      for (const [key, probe] of Object.entries(data._metadata.probes)) {
        const countryCode = 'country_code';
        const countryFromCode = CountriesJson.find((country) => country.Code === probe[countryCode]);
        if (!(countryFromCode.Name in probesPerCountry)) {
          probesPerCountry[countryFromCode.Name] = [];
        }
        probesPerCountry[countryFromCode.Name].push(key);
      }
      this.probesPerCountry = probesPerCountry;
    });
  }
}
