<div *ngIf="isGraphAvailable; else graphNotAvailable">
    <div class="row">
        <div class="col-12 graph-filter-block">
            <app-range-calendar [from]="from" [to]="to" (dateRangeChange)="onDateRangeChange($event)"></app-range-calendar>
        </div>
    </div>
    <div class="row margin-bottom-10 margin-left-5">
        <div i18n="@@bma_graph_numberofprobes" class="margin-top-8">Number of Probes: {{ probesNr }}</div>
        <div *ngIf="probesPerCountry" class="col-3 margin-bottom-10">
            <select label="country" class="form-control" (change)="onCountryChange($event.target.value)">
                <option value="all">Country</option>
                <option *ngFor="let probes of probesPerCountry | keyvalue" [value]="probes.value">{{probes.key}}</option>
            </select>
        </div>
    </div>
    <div class="row margin-left-5">
        <div i18n="@@bma_graph_selectallprobes">Select all probes</div>
        <div class="checkbox margin-left-5">
            <input type="checkbox" class="form-check-input" checked="true" (change)="onSelectAllProbesChange($event)" />
        </div>
    </div>
    <div class="row mt-3">
        <div class="col-12 mt-3">
            <app-graph-latency [measurement]="measurement" [from]="from" [to]="to" [country]="country" [probes]="probes"></app-graph-latency>
        </div>
    </div>
    <div class="row mt-3">
        <div class="col-12 mt-3">
            <app-graph-packet-loss [measurement]="measurement" [from]="from" [to]="to" [country]="country" [probes]="probes"></app-graph-packet-loss>
        </div>
    </div>
    <div *ngIf="isEmployee" class="row mt-3">
        <div class="col-12 mt-3">
            <app-graph-hops [measurement]="measurement" [from]="from" [to]="to" [country]="country" [probes]="probes"></app-graph-hops>
        </div>
    </div>
    <div *ngIf="isEmployee" class="row mt-3">
        <div class="col-12 mt-3">
            <app-graph-reachability [measurement]="measurement" [from]="from" [to]="to" [country]="country" [probes]="probes"></app-graph-reachability>
        </div>
    </div>
</div>

<ng-template #graphNotAvailable>
    <table class="table table-striped mt-2">
        <tbody>
            <tr>
                <td class="p-2 text-center">
                    Graphs are not available due to the unavailability of
                    {{ equipmentType == 'networkDevices' ? 'uplinks' : (networkType==='INTERNAL' ? 'Internal Network Interfaces' : 'Public Network Interfaces') }}
                </td>
            </tr>
        </tbody>
    </table>
</ng-template>
