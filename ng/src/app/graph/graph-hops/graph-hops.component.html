<div>
    <h4>
        <span i18n="@@bma_graphhops">Traceroute Hops:</span> {{ from|lswDate:"" }} <span i18n="@@bma_to">to</span> {{ to|lswDate:"" }}
        <small class="pull-right">
            <div class="d-flex align-items-center">
                <p-inputSwitch [(ngModel)]="isUtcMode" i18n-pTooltip="@@bma_tooltip_utc_localtime" pTooltip="Choose whether to display the graph in your local time or in UTC." tooltipPosition="top" (onChange)="buildChartData()"></p-inputSwitch>
                <span class="label">
                    <span *ngIf="isUtcMode">UTC</span>
                    <span *ngIf="!isUtcMode" i18n="@@bma_localtime">Local time</span>
                </span>
            </div>
        </small>
    </h4>

    <p-chart type="line" [data]="chart" [options]="options"></p-chart>

    <table class="table ipperformance-table" *ngIf="avgHops">
        <thead>
            <tr>
                <th class="text-center" scope="col">Probe ID</th>
                <th class="text-center" scope="col">Name</th>
                <th class="text-center" scope="col">ASN</th>
                <th class="text-center" scope="col">Average Hops</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let probe of avgHops._metadata.probes | keyvalue">
                <td *ngIf="!hiddenProbe[probe.key]" class="text-center" scope="col">
                    <small>{{ probe.key }}</small>
                </td>
                <td *ngIf="!hiddenProbe[probe.key]" class="text-center" scope="col">
                    <app-country-flag countryCode="{{ probe.value.country_code }}"></app-country-flag><br />
                    <small>{{ probe.value.description }}</small>
                </td>
                <td *ngIf="!hiddenProbe[probe.key]" class="text-center" scope="col">
                    <small *ngIf="probe.value.asn_v4"
                        ><a href="https://stat.ripe.net/{{probe.value.asn_v4}}" target="_blank">{{ probe.value.asn_v4 }}</a></small
                    >
                </td>
                <td *ngIf="!hiddenProbe[probe.key]" class="text-center" scope="col">
                    <small>{{ avgHops.hops.value[probe.key][0].value }}</small>
                </td>
            </tr>
        </tbody>
    </table>
</div>
