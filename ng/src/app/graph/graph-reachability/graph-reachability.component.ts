import { Component, OnInit, OnChanges, Input } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { DateFormatterService } from 'src/app/services/date-formatter.service';

@Component({
  selector: 'app-graph-reachability',
  templateUrl: './graph-reachability.component.html',
  styleUrls: ['./graph-reachability.component.css'],
})
export class GraphReachabilityComponent implements OnInit, OnChanges {
  @Input() from: any;
  @Input() to: any;
  @Input() measurement: any;
  @Input() country: any;
  @Input() probes: any;

  chart: any;
  options: any;
  avgReachability: any;
  hiddenProbe: any;
  isEmployee = false;
  isUtcMode = false;
  graphData: any;

  constructor(
    private currentUserService: CurrentUserService,
    private httpClient: HttpClient,
    private dateService: DateFormatterService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.options = {
      stacked: true,
      hover: {
        mode: 'index',
        intersect: false,
      },
      interaction: {
        mode: 'index',
        intersect: false,
      },
      elements: {
        point: {
          radius: 0,
        },
      },
      scales: {
        x: {
          type: 'time',
          time: {
            unit: 'hour',
            stepSize: 1,
            tooltipFormat: 'MMM d ha',
          },
          ticks: {
            major: {
              enabled: true,
              // TODO figure out how we can get daily ticks, without inceasing font size and using bold text
            },
          },
          grid: {
            color: '#ebedef',
          },
          gridLines: {
            drawOnChartArea: false,
          },
        },
        y: {
          scaleLabel: {
            display: true,
            labelString: 'No. of Traceroutes',
          },
          grid: {
            color: '#ebedef',
          },
          gridLines: {
            color: '#ebedef',
          },
          afterDataLimits: (scale) => {
            // add 1% to top
            const range = scale.max - scale.min;
            const grace = range * 0.01;
            scale.max += grace;
          },
        },
      },
      plugins: {
        legend: {
          position: 'bottom',
          onClick: (e, legendItem, legend) => {
            const index = legendItem.datasetIndex;
            const ci = legend.chart;
            if (ci.isDatasetVisible(index)) {
              ci.hide(index);
              this.hiddenProbe[parseInt(legendItem.text, 10)] = true;
            } else {
              ci.show(index);
              this.hiddenProbe[parseInt(legendItem.text, 10)] = false;
            }
          },
          labels: {
            usePointStyle: true,
            boxWidth: 20,
            padding: 10,
            fontSize: 17,
          },
        },
        tooltips: {
          mode: 'index',
          intersect: false,
          callbacks: {
            label: (context) => context.raw,
          },
        },
      },
    };
  }

  ngOnChanges() {
    this.fetchData();
  }

  getReachability(url: string, params: any) {
    params.aggregation = 'AVG';
    params.granularity = 'HOUR';

    this.httpClient.get<any>(url, { params }).subscribe((data: any) => {
      const reachability = data.reachability;
      this.graphData = reachability;
      this.buildChartData();
    });
  }

  buildChartData() {
    const colors = [
      '#3f0089',
      '#5d78c7',
      '#039be5',
      '#29b6f6',
      '#81d4fa',
      '#01579b',
      '#b3e5fc',
      '#3bffdc',
      '#b5e6eb',
      '#f9ffb9',
      '#f6d052',
      '#83e95d',
      '#ff9f4a',
      '#79e4d0',
      '#d42f23',
      '#773d7d',
      '#0082a7',
    ];

    const chartData = {
      labels: [],
      datasets: [],
    };

    const probes = [];
    const countryProbes = this.country.split(',');
    Object.entries(this.graphData[0]).forEach(([key, probe]) => {
      if (countryProbes.includes('all')) {
        probes.push({ id: key, measurements: probe });
      }
      if (countryProbes.includes(key)) {
        probes.push({ id: key, measurements: probe });
      }
    });

    let labels = false;
    probes.forEach((probe, index) => {
      const measurementData = [];
      probe.measurements.forEach((measurement) => {
        measurementData.push(measurement.value);
        if (labels === false) {
          chartData.labels.push(
            this.isUtcMode
              ? this.dateService.localTimestampToUtc(measurement.timestamp)
              : this.dateService.utcToLocalTimestamp(measurement.timestamp)
          );
        }
      });
      labels = true;
      const dataset = {
        showLine: true,
        label: probe.id,
        backgroundColor: colors[index],
        data: measurementData,
        fill: true,
      };
      chartData.datasets.push(dataset);
    });

    if (this.probes === 'false') {
      chartData.datasets.forEach((ds) => {
        ds.hidden = !ds.hidden;
      });
    }

    this.chart = chartData;
  }

  getAvgReachability(url: string, params: any): any {
    params.aggregation = 'AVG';
    this.httpClient.get<any>(url, { params }).subscribe((data: any) => {
      if (typeof data.reachability !== 'undefined') {
        this.avgReachability = data;

        const countryProbes = this.country.split(',');
        if (!countryProbes.includes('all')) {
          Object.keys(this.avgReachability._metadata.probes).forEach((key) => {
            if (!countryProbes.includes(key)) {
              delete this.avgReachability._metadata.probes[key];
            }
          });

          Object.keys(this.avgReachability.reachability.value).forEach((key) => {
            if (!countryProbes.includes(key)) {
              delete this.avgReachability.reachability.value[key];
            }
          });
        }
        // set for each probe a default visibility value for the table
        this.hiddenProbe = [];
        Object.keys(this.avgReachability._metadata.probes).forEach((key) => {
          this.hiddenProbe[key] = this.probes === 'false' ? true : false;
        });
      }
    });
  }

  fetchData() {
    const url =
      '/_/internal/networkperformanceapi/measurements/' + this.measurement.measurementId + '/metrics/reachability';

    const params: any = {
      from: this.from,
      to: this.to,
    };

    this.getReachability(url, { ...params });
    this.getAvgReachability(url, { ...params });
  }
}
