import { Component, OnInit, OnChanges, Input } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { DateFormatterService } from 'src/app/services/date-formatter.service';
import { BandWidthGraphData } from 'src/app/models/graphs.model';

@Component({
  selector: 'app-graph-bandwidth',
  templateUrl: './graph-bandwidth.component.html',
  styleUrls: ['./graph-bandwidth.component.css'],
})
export class GraphBandwidthComponent implements OnInit, OnChanges {
  @Input() customerId: any;
  @Input() salesOrgId: any;
  @Input() equipment: any;
  @Input() equipmentType: any;
  @Input() networkType: any;
  @Input() from: any;
  @Input() to: any;

  // chart data and options
  data: any;
  options: any;
  isUtcMode = false;

  // legend information
  averageBytesIn: number;
  averageBytesOut: number;
  percentile: any;

  private graphData: BandWidthGraphData = { up: [], down: [] };

  constructor(
    private currentUserService: CurrentUserService,
    private httpClient: HttpClient,
    private dateService: DateFormatterService
  ) {}

  ngOnInit(): void {
    this.options = {
      stacked: false,
      hover: {
        mode: 'index',
        intersect: false,
      },
      interaction: {
        mode: 'index',
        intersect: false,
      },
      elements: {
        point: {
          radius: 0,
        },
      },
      scales: {
        x: {
          type: 'time',
          time: {
            displayFormats: {
              quarter: 'MMM d ha',
            },
            unit: 'hour',
            stepSize: 1,
            tooltipFormat: 'MMM d ha',
          },
          ticks: {
            major: {
              enabled: true,
              // TODO figure out how we can get daily ticks, without inceasing font size and using bold text
            },
          },
          grid: {
            color: '#ebedef',
          },
          gridLines: {
            drawOnChartArea: false,
          },
        },
        y: {
          scaleLabel: {
            display: true,
            labelString: $localize`:@@bma_graphbandwidth_average:Average`,
          },
          ticks: {
            callback: (value) => {
              let t = -1;
              const a = ['kbps', 'Mbps', 'Gbps', 'Tbps', 'Pbps', 'Ebps', 'Zbps', 'Ybps'];
              do {
                value /= 1000;
                ++t;
              } while (Math.abs(value) >= 1000 && t < a.length - 1);
              return value + ' ' + a[t];
            },
          },
          grid: {
            color: '#ebedef',
          },
          gridLines: {
            color: '#ebedef',
          },
          afterDataLimits: (scale) => {
            // add 1% to top
            const range = scale.max - scale.min;
            const grace = range * 0.01;
            scale.max += grace;
          },
        },
      },
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            usePointStyle: true,
            boxWidth: 20,
            padding: 10,
            fontSize: 17,
          },
          reverse: true,
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          callbacks: {
            label: (context) => {
              let t = -1;
              const a = ['kbps', 'Mbps', 'Gbps', 'Tbps', 'Pbps', 'Ebps', 'Zbps', 'Ybps'];
              do {
                context.raw /= 1000;
                ++t;
              } while (Math.abs(context.raw) >= 1000 && t < a.length - 1);
              return context.raw.toFixed(2) + ' ' + a[t];
            },
          },
        },
      },
    };
  }

  ngOnChanges() {
    this.fetchData();
  }

  detectGranularity(): string {
    const numberOfDays = this.dateService.durationDiffAsDays(this.to, this.from);

    if (numberOfDays < 8) {
      return '5MIN';
    }
    if (numberOfDays > 90) {
      return 'DAY';
    }
    return 'HOUR';
  }

  async getBandwidth(url: string, params: any) {
    await this.getNintyFifthBandwidth(url, { ...params });

    params.aggregation = 'AVG';
    params.granularity = this.detectGranularity();

    this.httpClient.get<any>(url, { params }).subscribe((data: any) => {
      const up = data.metrics.UP_PUBLIC?.values || data.metrics.UP_INTERNAL?.values || data.metrics.UP?.values || [];
      const down =
        data.metrics.DOWN_PUBLIC?.values || data.metrics.DOWN_INTERNAL?.values || data.metrics.DOWN?.values || [];

      this.graphData = { up, down };

      this.buildChartData();
    });
  }

  buildChartData() {
    const { up, down } = this.graphData;

    const chartData = {
      labels: [],
      datasets: [
        {
          type: 'line',
          label: $localize`:@@bma_graphdatatraffic_incoming:Incoming`,
          borderColor: this.currentUserService.isFiberring() ? '#663398' : '#2d2e86',
          borderWidth: 0,
          backgroundColor: this.currentUserService.isFiberring() ? '#663398' : '#2d2e86',
          data: [],
          fill: true,
          order: 3,
        },
        {
          type: 'line',
          label: $localize`:@@bma_graphbandwidth_outgoing:Outgoing`,
          borderColor: this.currentUserService.isFiberring() ? '#b299cb' : '#5685c4',
          borderWidth: 1,
          data: [],
          fill: false,
          order: 2,
        },
      ],
    };

    down.forEach((item) => {
      chartData.labels.push(
        this.isUtcMode
          ? this.dateService.localTimestampToUtc(item.timestamp)
          : this.dateService.utcToLocalTimestamp(item.timestamp)
      );
      chartData.datasets[0].data.push(item.value);
    });

    up.forEach((item) => {
      chartData.datasets[1].data.push(item.value);
    });

    if (this.percentile) {
      const percentileDataset = {
        type: 'line',
        label: $localize`:@@bma_graphbandwidth_95thpercentile:95th percentile`,
        borderColor: 'red',
        borderWidth: 1,
        data: new Array(Math.max(up.length, down.length)).fill(this.percentile),
        fill: false,
        order: 1,
        hidden: true,
      };
      chartData.datasets.push(percentileDataset);
    }

    this.data = chartData;
  }

  async getNintyFifthBandwidth(url: string, params: any) {
    params.aggregation = '95TH';

    await this.httpClient
      .get<any>(url, { params })
      .toPromise()
      .then(
        (data: any) => {
          this.percentile = data.metrics.PUBLIC?.values[0].value || data.metrics.INTERNAL?.values[0].value || 0;
        },
        (error: any) => {
          this.percentile = false;
        }
      );
  }

  getAvgBandwidth(url: string, params: any) {
    params.aggregation = 'AVG';

    // Make sure that we never calculate AVG for future dates.
    if (this.dateService.utcNowIsBeforeTo(params.to)) {
      params.to = this.dateService.utc().addDays(1).format();
    }

    this.httpClient.get<any>(url, { params }).subscribe((data: any) => {
      this.averageBytesIn =
        data.metrics.DOWN_PUBLIC?.values[0].value ||
        data.metrics.DOWN_INTERNAL?.values[0].value ||
        data.metrics.DOWN?.values[0].value ||
        0;
      this.averageBytesOut =
        data.metrics.UP_PUBLIC?.values[0].value ||
        data.metrics.UP_INTERNAL?.values[0].value ||
        data.metrics.UP?.values[0].value ||
        0;
    });
  }

  fetchData() {
    const id = this.equipmentType === 'networkDevices' ? this.equipment.name : this.equipment.id;
    const url =
      this.equipmentType === 'metrics'
        ? '/_/internal/bmusageapi/metrics/bandwidth'
        : `/_/internal/bmusageapi/v2/${this.equipmentType}/${id}/metrics/bandwidth`;

    const params: any = {
      from: this.from,
      to: this.to,
    };

    if (this.networkType) {
      params.networkType = this.networkType;
    }

    if (this.equipmentType === 'metrics' && this.currentUserService.isEmployee()) {
      params.customerId = this.customerId;
      params.salesOrgId = this.salesOrgId;
    }

    this.getBandwidth(url, { ...params });
    this.getAvgBandwidth(url, { ...params });
  }
}
