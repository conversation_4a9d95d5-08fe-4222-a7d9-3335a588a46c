<div>
    <h4>
        <span i18n="@@bma_graphbandwidth">Bandwidth:</span> {{ from|lswDate:"" }} <span i18n="@@bma_to">to</span> {{ to|lswDate:"" }}
        <small class="pull-right">
            <div class="d-flex align-items-center">
                <p-inputSwitch [(ngModel)]="isUtcMode" i18n-pTooltip="@@bma_tooltip_utc_localtime" pTooltip="Choose whether to display the graph in your local time or in UTC." tooltipPosition="top" (onChange)="buildChartData()"></p-inputSwitch>
                <span class="label">
                    <span *ngIf="isUtcMode">UTC</span>
                    <span *ngIf="!isUtcMode" i18n="@@bma_localtime">Local time</span>
                </span>
            </div>
        </small>
    </h4>

    <p-chart type="line" [data]="data" [options]="options"></p-chart>

    <dl class="row mt-3">
        <dt i18n="@@bma_graphbandwidth_averageincoming" class="col-6 text-right">Average incoming</dt>
        <dd class="col-6">{{ averageBytesIn|formatSizeUnit:false:'bytes/s' }}</dd>

        <dt i18n="@@bma_graphbandwidth_averageoutgoing" class="col-6 text-right">Average outgoing</dt>
        <dd class="col-6">{{ averageBytesOut|formatSizeUnit:false:'bytes/s' }}</dd>

        <dt i18n="@@bma_graphbandwidth_percentile" class="col-6 text-right">95<sup>th</sup> percentile</dt>
        <dd class="col-6" *ngIf="percentile|default; else noPercentile">{{ percentile|formatSizeUnit:false:'bytes/s' }}</dd>
        <ng-template #noPercentile>
            <dd class="col-6">N/A</dd>
        </ng-template>
    </dl>
</div>
