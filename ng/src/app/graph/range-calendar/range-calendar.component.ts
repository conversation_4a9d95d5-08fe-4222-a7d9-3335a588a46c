import { Component, OnInit, OnChanges, SimpleChanges, EventEmitter, Input, Output } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { DateFormatterService } from 'src/app/services/date-formatter.service';

@Component({
  selector: 'app-range-calendar',
  templateUrl: './range-calendar.component.html',
  styleUrls: ['./range-calendar.component.css'],
})
export class RangeCalendarComponent implements OnInit, OnChanges {
  @Input() from: string;
  @Input() to: string;

  @Output() dateRangeChange: EventEmitter<any> = new EventEmitter();

  range: Date[];
  items: MenuItem[];

  constructor(private dateService: DateFormatterService) {}

  ngOnChanges(changes: SimpleChanges): void {
    this.range = [this.dateService.init(this.from).toDate(), this.dateService.init(this.to).subtractDays(1).toDate()];
  }

  ngOnInit(): void {
    this.items = [
      {
        label: 'Ranges',
        items: [
          {
            label: 'Current Month',
            command: () =>
              this.dispatchDateRangeChange(
                this.dateService.today().startOfMonth().format(),
                this.dateService.today().addMonths(1).startOfMonth().format()
              ),
          },
          {
            label: 'Last Month',
            command: () =>
              this.dispatchDateRangeChange(
                this.dateService.today().subtractMonths(1).startOfMonth().format(),
                this.dateService.today().startOfMonth().format()
              ),
          },
          {
            label: 'Current Week',
            command: () =>
              this.dispatchDateRangeChange(
                this.dateService.today().startOfWeek().format(),
                this.dateService.today().addWeeks(1).startOfWeek().format()
              ),
          },
          {
            label: 'Last Week',
            command: () =>
              this.dispatchDateRangeChange(
                this.dateService.today().subtractWeeks(1).startOfWeek().format(),
                this.dateService.today().startOfWeek().format()
              ),
          },
          {
            label: 'Today',
            command: () =>
              this.dispatchDateRangeChange(this.dateService.today().format(), this.dateService.addDays(1).format()),
          },
          {
            label: 'Yesterday',
            command: () =>
              this.dispatchDateRangeChange(
                this.dateService.today().subtractDays(1).format(),
                this.dateService.today().format()
              ),
          },
          {
            label: 'Last 7 Days',
            command: () =>
              this.dispatchDateRangeChange(
                this.dateService.today().subtractDays(6).format(),
                this.dateService.today().addDays(1).format()
              ),
          },
          {
            label: 'Last 30 Days',
            command: () =>
              this.dispatchDateRangeChange(
                this.dateService.today().subtractDays(29).format(),
                this.dateService.today().addDays(1).format()
              ),
          },
          {
            label: 'Current Year',
            command: () =>
              this.dispatchDateRangeChange(
                this.dateService.today().startOfYear().format(),
                this.dateService.today().addYears(1).startOfYear().format()
              ),
          },
        ],
      },
    ];
  }

  onSelect() {
    if (!this.range[1]) {
      return; // Only process a selection if a range was selected
    }

    this.dispatchDateRangeChange(
      this.dateService.init(this.range[0]).format(),
      this.dateService.init(this.range[1]).addDays(1).format()
    );
  }

  // from/to format YYYY-MM-DD
  dispatchDateRangeChange(from: string, to: string) {
    this.dateRangeChange.emit({
      from,
      to,
    });
  }
}
