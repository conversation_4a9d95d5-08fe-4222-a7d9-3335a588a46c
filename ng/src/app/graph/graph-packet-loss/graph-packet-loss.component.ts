import { Component, OnChanges, Input } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { DateFormatterService } from 'src/app/services/date-formatter.service';

@Component({
  selector: 'app-graph-packet-loss',
  templateUrl: './graph-packet-loss.component.html',
  styleUrls: ['./graph-packet-loss.component.css'],
})
export class GraphPacketLossComponent implements OnChanges {
  @Input() from: any;
  @Input() to: any;
  @Input() measurement: any;
  @Input() country: any;
  @Input() probes: any;

  chart: any;
  options: any;
  hiddenProbe: any;
  granularity = 'HOUR';
  isUtcMode = false;
  graphData: any;

  constructor(
    private currentUserService: CurrentUserService,
    private httpClient: HttpClient,
    private dateService: DateFormatterService
  ) {}

  ngOnChanges() {
    this.fetchData();
  }

  detectGranularity(): string {
    const numberOfDays = this.dateService.durationDiffAsDays(this.to, this.from);
    if (numberOfDays === 1) {
      return '15MIN';
    }
    if (numberOfDays < 8) {
      return 'HOUR';
    }
    return 'DAY';
  }

  getPacketLoss(url: string, params: any) {
    params.aggregation = 'AVG';
    params.granularity = this.detectGranularity();
    this.granularity = params.granularity;

    let unit = 'day';
    let stepSize = 1;

    if (this.granularity === '15MIN') {
      unit = 'minute';
      stepSize = 15;
    } else if (this.granularity === 'HOUR') {
      unit = 'hour';
      stepSize = 1;
    } else {
      unit = 'day';
      stepSize = 1;
    }

    this.options = {
      stacked: true,
      hover: {
        mode: 'index',
        intersect: false,
      },
      interaction: {
        mode: 'index',
        intersect: false,
      },
      elements: {
        point: {
          radius: 0,
        },
      },
      scales: {
        x: {
          type: 'time',
          time: {
            unit,
            stepSize,
            tooltipFormat: 'MMM d HH:mma',
          },
          ticks: {
            major: {
              enabled: true,
              // TODO figure out how we can get daily ticks, without inceasing font size and using bold text
            },
          },
          grid: {
            color: '#ebedef',
          },
          gridLines: {
            drawOnChartArea: false,
          },
        },
        y: {
          suggestedMin: 0,
          scaleLabel: {
            display: true,
            labelString: 'Packet Loss (%)',
          },
          grid: {
            color: '#ebedef',
          },
          gridLines: {
            color: '#ebedef',
          },
          afterDataLimits: (scale) => {
            // add 1% to top
            const range = scale.max - scale.min;
            const grace = range * 0.01;
            scale.max += grace;
          },
        },
      },
      plugins: {
        legend: {
          position: 'bottom',
          onClick: (e, legendItem, legend) => {
            const index = legendItem.datasetIndex;
            const ci = legend.chart;
            if (ci.isDatasetVisible(index)) {
              ci.hide(index);
            } else {
              ci.show(index);
            }
          },
          labels: {
            usePointStyle: true,
            boxWidth: 20,
            padding: 10,
            fontSize: 17,
          },
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          callbacks: {
            label: (context) => context.raw + '%',
          },
        },
      },
    };

    this.httpClient.get<any>(url, { params }).subscribe((data: any) => {
      const packetLoss = data.packet_loss;
      this.graphData = packetLoss;
      this.buildChartData();
    });
  }

  buildChartData() {
    const colors = [
      '#3f0089',
      '#5d78c7',
      '#039be5',
      '#29b6f6',
      '#81d4fa',
      '#01579b',
      '#b3e5fc',
      '#3bffdc',
      '#b5e6eb',
      '#f9ffb9',
      '#f6d052',
      '#83e95d',
      '#ff9f4a',
      '#79e4d0',
      '#d42f23',
      '#773d7d',
      '#0082a7',
    ];

    const chartData = {
      labels: [],
      datasets: [],
    };

    const probes = [];
    const countryProbes = this.country.split(',');

    Object.entries(this.graphData).forEach(([key, probe]) => {
      if (countryProbes.includes('all')) {
        probes.push({ id: key, measurements: probe });
      }
      if (countryProbes.includes(key)) {
        probes.push({ id: key, measurements: probe });
      }
    });

    let labels = false;
    probes.forEach((probe, index) => {
      const measurementData = [];
      probe.measurements.forEach((measurement) => {
        measurementData.push(measurement.value);
        if (labels === false) {
          chartData.labels.push(
            this.isUtcMode
              ? this.dateService.localTimestampToUtc(measurement.timestamp)
              : this.dateService.utcToLocalTimestamp(measurement.timestamp)
          );
        }
      });
      labels = true;
      const dataset = {
        showLine: true,
        label: probe.id,
        borderColor: colors[index],
        backgroundColor: colors[index],
        data: measurementData,
        fill: false,
      };
      chartData.datasets.push(dataset);
    });

    if (this.probes === 'false') {
      chartData.datasets.forEach((ds) => {
        ds.hidden = !ds.hidden;
      });
    }

    this.chart = chartData;
  }

  fetchData() {
    const url =
      '/_/internal/networkperformanceapi/measurements/' + this.measurement.measurementId + '/metrics/packetloss';

    const params: any = {
      from: this.from,
      to: this.to,
    };

    this.getPacketLoss(url, { ...params });
  }
}
