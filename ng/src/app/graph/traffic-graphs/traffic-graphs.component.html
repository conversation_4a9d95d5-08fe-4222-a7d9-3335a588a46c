<div class="alert alert-info alert-dismissible" role="alert" *ngIf="equipmentType == 'networkDevices' && totalLinkSpeed > 0">The available capacity of this switch is {{ totalLinkSpeed >= 1000 ? totalLinkSpeed / 1000 : totalLinkSpeed }} {{ totalLinkSpeed >= 1000 ? 'Gbps' : 'Mbps' }}.</div>

<h3>
    <span class="fa fa-usage mr-1"></span>
    <span i18n="@@bma_traffic_graphs_title">{{ equipmentTitle }} Traffic Graphs</span>
</h3>

<div *ngIf="isGraphAvailable; else graphNotAvailable">
    <div class="row">
        <div class="col-12 graph-filter-block">
            <app-range-calendar [from]="from" [to]="to" (dateRangeChange)="onDateRangeChange($event)"></app-range-calendar>
        </div>
    </div>
    <div class="row mt-3">
        <div class="col-12 mt-3">
            <app-graph-bandwidth [equipmentType]="httpEquipmentType" [networkType]="networkType" [from]="from" [to]="to" [equipment]="equipment" [customerId]="customerId" [salesOrgId]="salesOrgId"></app-graph-bandwidth>
        </div>
    </div>
    <div class="row mt-3">
        <div class="col-12">
            <app-graph-datatraffic [equipmentType]="httpEquipmentType" [networkType]="networkType" [from]="from" [to]="to" [equipment]="equipment" [customerId]="customerId" [salesOrgId]="salesOrgId"></app-graph-datatraffic>
        </div>
    </div>
</div>

<ng-template #graphNotAvailable>
    <table class="table table-striped mt-2">
        <tbody>
            <tr>
                <td class="p-2 text-center">
                    Graphs are not available due to the unavailability of
                    {{ equipmentType == 'networkDevices' ? 'uplinks' : (networkType==='INTERNAL' ? 'Internal Network Interfaces' : 'Public Network Interfaces') }}
                </td>
            </tr>
        </tbody>
    </table>
</ng-template>
