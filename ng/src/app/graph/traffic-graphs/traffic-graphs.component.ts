import { AlertService } from 'src/app/services/alert.service';
import { Component, Input, OnInit } from '@angular/core';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { Router, ActivatedRoute } from '@angular/router';
import { DateFormatterService } from 'src/app/services/date-formatter.service';

@Component({
  selector: 'app-traffic-graphs',
  templateUrl: './traffic-graphs.component.html',
  styleUrls: ['./traffic-graphs.component.css'],
})
export class TrafficGraphsComponent implements OnInit {
  @Input() equipment: any;
  @Input() equipmentType: any;
  baseUrl = '/bare-metals/';
  isEmployee = false;
  scripts = [];
  networkType = '';
  isGraphAvailable = true;
  equipmentTitle = '';
  httpEquipmentType = '';
  uplinks = [];
  customerId = '';
  salesOrgId = '';
  totalLinkSpeed = 0;
  from = '';
  to = '';

  constructor(
    private activatedRoute: ActivatedRoute,
    private currentUserService: CurrentUserService,
    private router: Router,
    private alertService: AlertService,
    private dateService: DateFormatterService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();

    switch (this.equipmentType) {
      case 'servers':
        this.httpEquipmentType = this.equipmentType;
        this.equipmentTitle = 'Dedicated Server';
        this.customerId = this.equipment.contract ? this.equipment.contract.customerId : null;
        this.salesOrgId = this.equipment.contract ? this.equipment.contract.salesOrgId : null;
        break;
      case 'networkEquipments':
        this.httpEquipmentType = this.equipmentType;
        this.equipmentTitle = 'Network Equipment';
        this.customerId = this.equipment.contract ? this.equipment.contract.customerId : null;
        this.salesOrgId = this.equipment.contract ? this.equipment.contract.salesOrgId : null;
        break;
      case 'privateRacks':
        this.httpEquipmentType = 'privateRacks';
        this.equipmentTitle = 'Dedicated Rack';
        this.customerId = this.equipment.contract ? this.equipment.contract.customerId : null;
        this.salesOrgId = this.equipment.contract ? this.equipment.contract.salesOrgId : null;
        break;
      case 'ipTransits':
        this.httpEquipmentType = this.equipmentType;
        this.equipmentTitle = 'Ip Transit';
        this.customerId = this.equipment.contract ? this.equipment.contract.customerId : null;
        this.salesOrgId = this.equipment.contract ? this.equipment.contract.salesOrgId : null;
        break;
      case 'directInternetAccess':
        this.httpEquipmentType = this.equipmentType;
        this.equipmentTitle = 'Direct Internet Access';
        this.customerId = this.equipment.contract ? this.equipment.contract.customerId : null;
        this.salesOrgId = this.equipment.contract ? this.equipment.contract.salesOrgId : null;
        break;
      case 'aggregationPacks':
        this.httpEquipmentType = this.equipmentType;
        this.equipmentTitle = 'Aggregation Pack';
        this.customerId = this.equipment.customerId;
        this.salesOrgId = this.equipment.salesOrgId;
        break;
      case 'colocations':
        this.httpEquipmentType = this.equipmentType;
        this.equipmentTitle = 'Colocation';
        this.customerId = this.equipment.contract ? this.equipment.contract.customerId : null;
        this.salesOrgId = this.equipment.contract ? this.equipment.contract.salesOrgId : null;
        break;
      case 'networkDevices':
        this.httpEquipmentType = this.equipmentType;
        this.equipmentTitle = 'Network Device';
        this.networkType = this.equipment.networkType;
        this.equipment.interfaces.forEach((value) => {
          if (value.type === 'UPLINK') {
            this.uplinks.push(value);
            this.totalLinkSpeed = this.totalLinkSpeed + value.linkSpeed;
          }
        });
        break;
      case 'report':
        this.httpEquipmentType = 'metrics';
        this.customerId = this.equipment.customerId;
        this.salesOrgId = this.equipment.salesOrgId;
        break;
    }

    if (this.router.url.includes('privateNetwork')) {
      this.networkType = 'INTERNAL';
    } else {
      this.networkType = null;
    }

    if (this.equipmentType === 'networkDevices' && this.uplinks.length === 0) {
      this.isGraphAvailable = false;
      this.alertService.alert(
        {
          type: 'error',
          description: `Network device is not connected to uplinks.`,
        },
        true
      );
    } else if (
      this.equipmentType !== 'networkDevices' &&
      this.networkType === 'INTERNAL' &&
      (!this.equipment.networkInterfaces.internal ||
        typeof this.equipment.networkInterfaces.internal === 'undefined' ||
        typeof this.equipment.networkInterfaces.internal.ports === 'undefined')
    ) {
      this.isGraphAvailable = false;
      this.alertService.alert(
        {
          type: 'error',
          description: `${this.equipmentTitle} is not connected to any private network.`,
        },
        true
      );
    } else if (
      this.equipmentType !== 'aggregationPacks' &&
      this.equipmentType !== 'directInternetAccess' &&
      this.equipmentType !== 'ipTransits' &&
      this.equipmentType !== 'networkEquipments' &&
      this.equipmentType !== 'networkDevices' &&
      this.equipmentType !== 'report' &&
      this.networkType === '' &&
      (typeof this.equipment.networkInterfaces.public === 'undefined' ||
        typeof this.equipment.networkInterfaces.public.ports === 'undefined')
    ) {
      this.isGraphAvailable = false;
    }

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onDateRangeChange(range: any): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { from: range.from, to: range.to },
      queryParamsHandling: 'merge',
    });
  }

  onQueryParamsChanged(queryParams): void {
    this.from = queryParams.from || this.dateService.today().startOfMonth().format();
    this.to = queryParams.to || this.dateService.today().addMonths(1).startOfMonth().format();
  }
}
