/* eslint-disable no-param-reassign, no-underscore-dangle */
import { Component, OnInit, Input, OnChanges, ElementRef } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { ModalService } from 'src/app/services/modal.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { DateFormatterService } from 'src/app/services/date-formatter.service';
import { IpPerformanceMeasurementTracerouteModalComponent } from '../../ip-performance/ip-performance-measurement-traceroute-modal/ip-performance-measurement-traceroute-modal.component';
import CountriesJson from './../../../data/countries.json';

@Component({
  selector: 'app-graph-latency',
  templateUrl: './graph-latency.component.html',
  styleUrls: ['./graph-latency.component.css'],
})
export class GraphLatencyComponent implements OnInit, OnChanges {
  @Input() from: any;
  @Input() to: any;
  @Input() measurement: any;
  @Input() country: any;
  @Input() probes: any;

  chart: any;
  options: any;
  filters: UntypedFormGroup;
  avgLatency: any;
  totalGraphAvg: any;
  dynamicDialogRef: DynamicDialogRef;
  hiddenProbe: any;
  isEmployee = false;
  isUtcMode = false;
  graphData: any;

  constructor(
    private httpClient: HttpClient,
    private formBuilder: UntypedFormBuilder,
    private el: ElementRef,
    private modalService: ModalService,
    private currentUserService: CurrentUserService,
    private dateService: DateFormatterService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.filters = this.formBuilder.group({
      country: [null],
    });

    this.options = {
      stacked: true,
      hover: {
        mode: 'index',
        intersect: false,
        onHover: (event, chartElement) => {
          event.target.style.cursor = chartElement[0] ? 'pointer' : 'default';
        },
      },
      interaction: {
        mode: 'index',
        intersect: false,
      },
      elements: {
        point: {
          radius: 0,
        },
      },
      scales: {
        x: {
          type: 'time',
          time: {
            unit: 'hour',
            stepSize: 1,
            tooltipFormat: 'MMM d ha',
          },
          ticks: {
            major: {
              enabled: true,
              // TODO figure out how we can get daily ticks, without inceasing font size and using bold text
            },
          },
          grid: {
            color: '#ebedef',
          },
          gridLines: {
            drawOnChartArea: false,
          },
        },
        y: {
          suggestedMin: 0,
          scaleLabel: {
            display: true,
            labelString: 'RTT (ms)',
          },
          grid: {
            color: '#ebedef',
          },
          gridLines: {
            color: '#ebedef',
          },
          afterDataLimits: (scale) => {
            // add 1% to top
            const range = scale.max - scale.min;
            const grace = range * 0.01;
            scale.max += grace;
          },
        },
      },
      plugins: {
        legend: {
          position: 'bottom',
          onClick: (e, legendItem, legend) => {
            const index = legendItem.datasetIndex;
            const ci = legend.chart;
            if (ci.isDatasetVisible(index)) {
              ci.hide(index);
              this.hiddenProbe[parseInt(legendItem.text, 10)] = true;
            } else {
              ci.show(index);
              this.hiddenProbe[parseInt(legendItem.text, 10)] = false;
            }
          },
          labels: {
            usePointStyle: true,
            boxWidth: 20,
            padding: 10,
            fontSize: 17,
          },
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          callbacks: {
            label: (context) => context.raw.toFixed(3) + ' ms',
          },
        },
      },
      onClick: (event) => {
        const points = event.chart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, true);

        if (points.length) {
          const firstPoint = points[0];

          this.modalService.show(IpPerformanceMeasurementTracerouteModalComponent, {
            measurementId: this.measurement.measurementId,
            probeId: event.chart.data.datasets[firstPoint.datasetIndex].label,
            at: event.chart.data.labels[firstPoint.index].unix(),
          });
        }
      },
    };
  }

  ngOnChanges() {
    this.fetchData();
  }

  getLatency(url: string, params: any) {
    params.aggregation = 'AVG';
    params.granularity = this.detectGranularity();

    this.httpClient.get<any>(url, { params }).subscribe((data: any) => {
      const latency = data.latency;
      this.graphData = latency;
      this.buildChartData();
    });
  }

  buildChartData() {
    const colors = [
      '#3f0089',
      '#5d78c7',
      '#039be5',
      '#29b6f6',
      '#81d4fa',
      '#01579b',
      '#b3e5fc',
      '#3bffdc',
      '#b5e6eb',
      '#f9ffb9',
      '#f6d052',
      '#83e95d',
      '#ff9f4a',
      '#79e4d0',
      '#d42f23',
      '#773d7d',
      '#0082a7',
    ];

    const chartData = {
      labels: [],
      datasets: [],
    };

    const probes = [];
    const countryProbes = this.country.split(',');
    Object.entries(this.graphData).forEach(([key, probe]) => {
      if (countryProbes.includes('all')) {
        probes.push({ id: key, measurements: probe });
      }
      if (countryProbes.includes(key)) {
        probes.push({ id: key, measurements: probe });
      }
    });

    let labels = false;
    probes.forEach((probe, index) => {
      const measurementData = [];
      probe.measurements.forEach((measurement) => {
        measurementData.push(measurement.value);
        if (labels === false) {
          chartData.labels.push(
            this.isUtcMode
              ? this.dateService.localTimestampToUtc(measurement.timestamp)
              : this.dateService.utcToLocalTimestamp(measurement.timestamp)
          );
        }
      });
      labels = true;
      const dataset = {
        showLine: true,
        label: probe.id,
        borderColor: colors[index],
        backgroundColor: colors[index],
        data: measurementData,
        fill: false,
      };
      chartData.datasets.push(dataset);
    });

    if (this.probes === 'false') {
      chartData.datasets.forEach((ds) => {
        ds.hidden = !ds.hidden;
      });
    }

    this.chart = chartData;
  }

  getAvgLatency(url: string, params: any): any {
    params.aggregation = 'AVG';

    this.httpClient.get<any>(url, { params }).subscribe((data: any) => {
      if (typeof data.latency !== 'undefined') {
        this.avgLatency = data;

        const countryProbes = this.country.split(',');
        if (!countryProbes.includes('all')) {
          Object.keys(this.avgLatency._metadata.probes).forEach((key) => {
            if (!countryProbes.includes(key)) {
              delete this.avgLatency._metadata.probes[key];
            }
          });

          Object.keys(this.avgLatency.latency).forEach((key) => {
            if (!countryProbes.includes(key)) {
              delete this.avgLatency.latency[key];
            }
          });
        }

        // set for each probe a default visibility value for the table
        this.hiddenProbe = [];
        Object.keys(this.avgLatency._metadata.probes).forEach((key) => {
          this.hiddenProbe[key] = this.probes === 'false' ? true : false;
        });

        const avgSum: any = Object.values(this.avgLatency.latency).reduce(
          (total, metric) => total + metric[0].value,
          0
        );
        this.totalGraphAvg = (avgSum / Object.keys(data._metadata.probes).length).toFixed(2);
      }
    });
  }

  removeProbesFromResult(probe) {
    if (probe !== this.country) {
      delete this.avgLatency._metadata.probes[probe];
    }
  }

  getProbesPerCountry(probes: any) {
    const probesPerCountry = [];
    for (const [key, probe] of Object.entries(probes)) {
      const countryCode = 'country_code';
      const countryProbes = CountriesJson.find((country) => country.Code === probe[countryCode]);
      if (!(countryProbes.Name in probesPerCountry)) {
        probesPerCountry[countryProbes.Name] = [];
      }
      probesPerCountry[countryProbes.Name].push(key);
    }
    return probesPerCountry;
  }

  detectGranularity(): string {
    const numberOfDays = this.dateService.durationDiffAsDays(this.to, this.from);

    if (numberOfDays < 8) {
      return '5MIN';
    }
    if (numberOfDays > 90) {
      return 'DAY';
    }
    return 'HOUR';
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
  }

  fetchData() {
    const url = '/_/internal/networkperformanceapi/measurements/' + this.measurement.measurementId + '/metrics/latency';
    const params: any = {
      from: this.from,
      to: this.to,
    };

    this.getLatency(url, { ...params });
    this.getAvgLatency(url, { ...params });
  }
}
