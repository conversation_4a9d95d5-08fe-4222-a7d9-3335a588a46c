<div>
    <h4>
        <span i18n="@@bma_graphlatency">Latency:</span> {{ from|lswDate:"" }} <span i18n="@@bma_to">to</span> {{ to|lswDate:"" }}
        <small class="pull-right">
            <div class="d-flex align-items-center">
                <p-inputSwitch [(ngModel)]="isUtcMode" i18n-pTooltip="@@bma_tooltip_utc_localtime" pTooltip="Choose whether to display the graph in your local time or in UTC." tooltipPosition="top" (onChange)="buildChartData()"></p-inputSwitch>
                <span class="label">
                    <span *ngIf="isUtcMode">UTC</span>
                    <span *ngIf="!isUtcMode" i18n="@@bma_localtime">Local time</span>
                </span>
            </div>
        </small>
    </h4>
    <div class="margin-top-20">
        <p-chart type="line" id="test" [data]="chart" [options]="options"></p-chart>

        <b i18n="@@bma_graphlatency_averagelatency">Average Latency: {{ totalGraphAvg }} ms</b>
        <table class="table ipperformance-table" *ngIf="avgLatency">
            <thead>
                <tr>
                    <th i18n="@@bma_graphlatency_probeid" class="text-center" scope="col">Probe ID</th>
                    +
                    <th i18n="@@bma_graphlatency_name" class="text-center" scope="col">Name</th>
                    +
                    <th i18n="@@bma_graphlatency_asn" class="text-center" scope="col">ASN</th>
                    +
                    <th i18n="@@bma_graphlatency_averagelatencytable" class="text-center" scope="col">Average Latency</th>
                    +
                    <th i18n="@@bma_graphlatency_packetloss" class="text-center" scope="col">Packet Loss</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let probe of avgLatency._metadata.probes | keyvalue">
                    <td *ngIf="!hiddenProbe[probe.key]" class="text-center" scope="col">
                        <small>{{ probe.key }}</small>
                    </td>
                    <td *ngIf="!hiddenProbe[probe.key]" class="text-center" scope="col">
                        <app-country-flag countryCode="{{ probe.value.country_code }}"></app-country-flag><br />
                        <small>{{ probe.value.description }}</small>
                    </td>
                    <td *ngIf="!hiddenProbe[probe.key]" class="text-center" scope="col">
                        <small *ngIf="probe.value.asn_v4"
                            ><a href="https://stat.ripe.net/{{probe.value.asn_v4}}" target="_blank">{{ probe.value.asn_v4 }}</a></small
                        >
                    </td>
                    <td *ngIf="!hiddenProbe[probe.key]" class="text-center" scope="col">
                        <small>{{ avgLatency.latency[probe.key][0].value.toFixed(2) }} ms</small>
                    </td>
                    <td *ngIf="!hiddenProbe[probe.key]" class="text-center" scope="col">
                        <small>{{ avgLatency.packet_loss[probe.key][0].value.toFixed(2) }}%</small>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
