<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '500px' }">
    <ng-template pTemplate="header">
        <h3 class="modal-title">Toggle {{ type }} network interface</h3>
    </ng-template>

    <form [formGroup]="form" (ngSubmit)="doAction()" class="form-horizontal">
        <div class="modal-body">
            <p>
                Are you sure you want to {{ action }} the {{ type }} network interface for dedicated server <span class="text-monospace">{{ equipmentId }}</span
                >?
            </p>
            <ng-container *ngIf="isEmployee && action == 'close'">
                <label><strong>Message</strong></label>
                <textarea formControlName="comment" name="comment" class="form-control"></textarea>
            </ng-container>
        </div>
    </form>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button pButton type="button" class="p-button-secondary" label="Close" (click)="closeModal()"></button>
            <button pButton (click)="doAction()" [loading]="isSubmitting" label="{{ action|titlecase }} Interface"></button>
        </div>
    </ng-template>
</p-dialog>
