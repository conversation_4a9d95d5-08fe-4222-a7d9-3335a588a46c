import { Component, OnInit } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';

@Component({
  selector: 'app-switch-port-close-modal',
  templateUrl: './switch-port-close-modal.component.html',
  styleUrls: ['./switch-port-close-modal.component.css'],
})
export class SwitchPortCloseModalComponent implements OnInit {
  equipmentId: string;
  type: string;
  action: string;

  form: UntypedFormGroup;
  isSubmitting = false;
  isEmployee = false;
  showDialog = true;

  constructor(
    private alertService: AlertService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    private dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.equipmentId = this.dynamicDialogConfig.data.equipmentId;
    this.type = this.dynamicDialogConfig.data.type;
    this.action = this.dynamicDialogConfig.data.action;
    this.form = this.formBuilder.group({
      comment: [null],
    });
  }

  doAction(): void {
    this.isSubmitting = true;
    this.alertService.clear();
    const params: any = {};
    params.bareMetalId = this.equipmentId;

    if (this.isEmployee && this.currentUserService.hasPermission('networkdevice_ports')) {
      params.comment = this.form.get('comment').value;
    }

    this.httpClient
      .post<any>(
        `/_/internal/nseapi/v2/servers/${this.equipmentId}/networkInterfaces/${this.type}/${this.action}`,
        params
      )
      .subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.form.reset();
          this.alertService.alert({
            type: 'success',
            description: `Success! ${this.action} ${this.type} network interface for server ${this.equipmentId}`,
          });
          this.closeModal({ switchPortUpdated: true });
        },
        error: (error: any) => {
          this.isSubmitting = false;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ switchPortUpdated: false });
        },
      });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
