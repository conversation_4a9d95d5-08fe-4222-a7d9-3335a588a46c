import { Router } from '@angular/router';
import { Component, Input, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { NetworkDeviceManageSpeedModalComponent } from 'src/app/network-device/network-device-manage-speed-modal/network-device-manage-speed-modal.component';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { AlertService } from 'src/app/services/alert.service';
import { SwitchPortCloseModalComponent } from 'src/app/switch-port/switch-port-close-modal/switch-port-close-modal.component';
import { ModalService } from 'src/app/services/modal.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ServerRemoteManagementModalComponent } from 'src/app/server/server-remote-management-modal/server-remote-management-modal.component';

@Component({
  selector: 'app-switch-port-status',
  templateUrl: './switch-port-status.component.html',
  styleUrls: ['./switch-port-status.component.css'],
})
export class SwitchPortStatusComponent implements OnInit {
  @Input() equipment: any;
  @Input() equipmentId: any; //To pass equipmentId, in case of "show status" in list page
  @Input() equipmentType: any;
  @Input() networkInterfaceType: any;
  @Input() errorInformation: string[];

  switchPorts: any = {};
  networkDevice: any = {};
  closedPortStates: any = {};
  isEmployee = false;
  isLoading = false;
  isError = false;
  dynamicDialogRef: DynamicDialogRef;

  constructor(
    private httpClient: HttpClient,
    private router: Router,
    private currentUserService: CurrentUserService,
    private alertService: AlertService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    if (this.equipment.networkInterfaces[this.networkInterfaceType].ports.length >= 1) {
      this.getSwitchPortStatus(this.networkInterfaceType);
    } else {
      this.isError = true;
    }
  }

  updateSpeedHandler(speedUpdated): void {
    if (speedUpdated) {
      this.getSwitchPortStatus(this.networkInterfaceType);
    }
  }

  getSwitchPortStatus(type): void {
    this.alertService.clear();
    this.isLoading = true;
    this.httpClient
      .get(`/_/internal/nseapi/v2/${this.equipmentType}/${this.equipment.id}/networkInterfaces/${type}?all`)
      .subscribe({
        next: (response: any) => {
          if (response) {
            this.switchPorts = response.networkInterfaces;
            this.closedPortStates = response.closedPortStates ?? null;
          }
          this.isLoading = false;
        },
        error: (error: any) => {
          this.alertService.alert(
            {
              type: 'error',
              description: $localize`:@@bma_common_switchportprocesserror:Something went wrong while processing network interface request.`,
            },
            true
          );

          this.isLoading = false;
          this.isError = true;
        },
      });
  }

  openCloseSwitchPortModal(action) {
    this.dynamicDialogRef = this.modalService.show(SwitchPortCloseModalComponent, {
      equipmentId: this.equipmentId ? this.equipmentId : this.equipment.id,
      type: this.networkInterfaceType,
      action,
    });

    this.dynamicDialogRef.onClose.subscribe((event: any) => {
      if (event?.switchPortUpdated === true) {
        this.getSwitchPortStatus(this.networkInterfaceType);
      }
    });
  }

  openManagePortSpeedModal(switchName, switchInterface) {
    this.dynamicDialogRef = this.modalService.show(NetworkDeviceManageSpeedModalComponent, {
      networkDeviceName: switchName,
      port: switchInterface,
    });

    this.dynamicDialogRef.onClose.subscribe((data) => {
      if (data?.portSpeedUpdated === true) {
      }
    });
  }

  openRemoteManagementModal(serverId: string) {
    this.modalService.show(ServerRemoteManagementModalComponent, { serverId });
  }
}
