<div class="row">
    <div class="p-col-12 p-ml-4 pt-2">
        <app-loader *ngIf="isLoading"></app-loader>
    </div>
</div>

<ng-container *ngIf="!isLoading">
    <div class="row">
        <div class="p-col-12" [ngClass]="equipmentType == 'servers'  &&  ((!isEmployee && networkInterfaceType != 'remoteManagement') || (isEmployee && currentUserService.hasPermission('networkdevice_ports'))) ? 'p-md-10 border-right' : 'p-md-12'">
            <ng-container *ngIf="equipment.networkInterfaces[networkInterfaceType]">
                <h5 class="p-ml-6 pt-2" i18n="@@bma_switchportstatus_interfacedetails">Interface Details</h5>
                <div class="p-col-12 p-d-flex p-flex-col p-pb-0">
                    <ng-container *ngIf="equipmentType == 'servers'">
                        <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-2">
                            <div class="p-grid grid-row">
                                <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_macaddress">Mac Address</strong></div>
                                <div class="p-col-8 grid-content grid-content-border">{{ equipment.networkInterfaces[networkInterfaceType].mac }}</div>
                            </div>
                            <div class="p-grid grid-row">
                                <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_ipaddress">IP Address</strong></div>
                                <div class="p-col-8 grid-content grid-content-border">{{ equipment.networkInterfaces[networkInterfaceType].ip }}</div>
                            </div>
                            <div class="p-grid grid-row">
                                <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_gatewayip">Gateway IP</strong></div>
                                <div class="p-col-8 grid-content grid-content-border">{{ equipment.networkInterfaces[networkInterfaceType].gateway }}</div>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </ng-container>
            <ng-container *ngIf="equipment.networkInterfaces[networkInterfaceType] && equipment.networkInterfaces[networkInterfaceType].ports && equipment.networkInterfaces[networkInterfaceType].ports.length > 0 ;else notAvailable">
                <ng-container *ngIf="!isError;else switchPortError">
                    <div class="table-responsive p-col-12 p-d-flex p-flex-col p-pb-0 p-pl-6 p-mt-3 network-interface-data">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th class="text-left p-pl-0" width="30%" i18n="@@bma_common_networkdevice">Network Device</th>
                                    <th class="text-left" i18n="@@bma_common_port">Port</th>
                                    <th class="text-left" i18n="@@bma_common_linkspeed">Link Speed</th>
                                    <th class="text-left" i18n="@@bma_common_type">Type</th>
                                    <th class="text-left" i18n="@@bma_common_adminstatus">Admin Status</th>
                                    <th class="text-left" i18n="@@bma_common_operationalstatus">Operational Status</th>
                                    <th class="text-left p-pl-0" *ngIf="isEmployee" i18n="@@bma_common_actions">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let networkInterface of switchPorts">
                                    <td class="text-left  p-pl-0">
                                        <a pButton class="p-button-link p-pl-0" *ngIf="isEmployee" [routerLink]="['/networkDevices', networkInterface.switchName]"> {{networkInterface.switchName }}</a>
                                        <span *ngIf="!isEmployee">
                                            {{ networkInterface.switchName }}
                                        </span>
                                    </td>
                                    <td class="text-left">
                                        {{ networkInterface.switchInterface }}
                                    </td>
                                    <td class="text-left">
                                        <span>{{ networkInterface.linkSpeed }}</span>
                                    </td>
                                    <td class="text-left">
                                        <span>{{  networkInterface.type }}</span>
                                    </td>
                                    <td class="text-left">
                                        <ng-container *ngIf="networkInterface['status']">
                                            <ng-container *ngIf="networkInterface['status'] == 'OPEN';else adminStatusDown">
                                                <span i18n="@@bma_common_open" class="h5 badge badge-success">OPEN</span>
                                            </ng-container>
                                            <ng-template #adminStatusDown>
                                                <span i18n="@@bma_common_closed" class="h5 badge badge-warning"> CLOSED </span>
                                            </ng-template>
                                        </ng-container>
                                    </td>
                                    <td class="text-left">
                                        <ng-container *ngIf="networkInterface['operStatus']">
                                            <ng-container *ngIf="networkInterface['operStatus'] == 'OPEN';else statusDown">
                                                <span class="h5 badge badge-success">
                                                    {{ networkInterface.linkSpeed }}
                                                </span>
                                            </ng-container>
                                            <ng-template #statusDown>
                                                <span class="h5 badge badge-danger" i18n="@@bma_common_down">DOWN</span>
                                            </ng-template>
                                        </ng-container>
                                    </td>
                                    <td class="text-left p-pl-0" *ngIf="isEmployee">
                                        <a i18n="@@bma_common_capuncap" pButton href="javascript:void(0);" class="text-nowrap p-button-link p-pl-0" (click)="openManagePortSpeedModal(networkInterface['switchName'],networkInterface['switchInterface'])" [class.disabled]="equipment.isSharedEol"> Cap/Uncap </a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </ng-container>

                <ng-container *ngIf="isEmployee && closedPortStates.length > 0 && switchPorts && switchPorts[0] && switchPorts[0].status == 'CLOSED'">
                    <div class="p-col-12 p-pl-6">
                        <h5 class="pt-2" i18n="@@bma_common_internalcomment">Internal Comment</h5>
                        <ng-container *ngFor="let closedPortState of closedPortStates">
                            <div class="p-col-12  p-md-12 p-pb-0 p-mt-3 p-mb-2 p-py-0 grid-content grid-content-border">
                                <strong>{{closedPortState.type}}</strong
                                ><br />
                                <span class="text-monospace" *ngIf="!closedPortState.comment" i18n="@@bma_common_nomessage">No Message</span>
                                <span class="text-monospace" *ngIf="closedPortState.comment">{{closedPortState.comment}}</span>
                                <a i18n-title="@@bma_common_viewalllog" *ngIf="closedPortState.correlationId" class="float-right" [routerLink]="['/audit-logs']" [queryParams]="{filter: closedPortState.correlationId}" title="View all related log messages...">
                                    <i class="fa fa-lg fa-tag"></i>
                                </a>
                            </div>
                        </ng-container>
                    </div>
                </ng-container>
            </ng-container>
        </div>
        <div *ngIf="equipmentType == 'servers' &&  ((!isEmployee && networkInterfaceType != 'remoteManagement') || (isEmployee && currentUserService.hasPermission('networkdevice_ports')))" class="p-col-12 p-md-2 quick-actions">
            <h5 class="p-2" i18n="@@bma_common_quickactions">Quick Actions</h5>
            <div class="p-grid p-flex-column align-items-start">
                <ng-container *ngIf="switchPorts && switchPorts[0] && switchPorts[0].status == 'OPEN'">
                    <a i18n="@@bma_common_closeport" pButton *ngIf="switchPorts.length == 1" class="p-button-link" href="javascript:void(0);" (click)="openCloseSwitchPortModal('close')" [class.disabled]="equipment.isSharedEol"> Close Port </a>
                    <a i18n="@@bma_common_closeports" pButton *ngIf="switchPorts.length > 1" class="p-button-link" href="javascript:void(0);" (click)="openCloseSwitchPortModal('close')" [class.disabled]="equipment.isSharedEol"> Close Ports </a>
                </ng-container>
                <ng-container *ngIf="switchPorts && switchPorts[0] && switchPorts[0].status == 'CLOSED'">
                    <a i18n="@@bma_common_openport" pButton *ngIf="switchPorts.length == 1" class="p-button-link" href="javascript:void(0);" (click)="openCloseSwitchPortModal('open')" [class.disabled]="equipment.isSharedEol"> Open Port </a>
                    <a i18n="@@bma_common_openports" pButton *ngIf="switchPorts.length > 1" class="p-button-link" href="javascript:void(0);" (click)="openCloseSwitchPortModal('open')" [class.disabled]="equipment.isSharedEol"> Open Ports </a>
                </ng-container>
                <a i18n-title="@@bma_switchportstatus_manageprivatenetwork" pButton *ngIf="networkInterfaceType === 'internal' && equipment.contract && equipment.isPrivateNetworkCapable" class="p-button-link" label="Manage Private Network" href="/emp/privateNetwork/lookup?customerId={{ equipment.contract.customerId }}&salesOrgId={{ equipment.contract.salesOrgId }}"></a>
                <a i18n-title="@@bma_common_remotemanagement" pButton *ngIf="networkInterfaceType === 'remoteManagement'" class="p-button-link" label="Remote Management" href="javascript:void(0);" (click)="openRemoteManagementModal(equipment.id)"></a>
            </div>
        </div>
    </div>
    <ng-template #switchPortError>
        <div class="p-col-12 p-d-flex p-pl-6 p-flex-col p-pb-0 justify-content-center">
            <span i18n="@@bma_common_switchporterror" class="text-danger">There was an issue in loading interface details.</span>
        </div>
    </ng-template>
    <ng-template #notAvailable>
        <div class="p-col-12 p-d-flex p-pl-6 p-flex-col p-pb-0 justify-content-center mt-3">
            <span i18n="@@bma_common_networkinterfacenotavailable">Network Interface Not Available</span>
        </div>
    </ng-template>
</ng-container>
