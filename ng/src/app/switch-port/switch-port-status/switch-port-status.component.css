.fs-small {
  font-size: small;
}
.admin-status-title {
  width: 33%;
  max-width: 300px;
}
.admin-status-block {
  min-width: 200px;
}
.privatenetwork-message {
  margin-top: 9px;
}
.grid-content {
  margin-bottom: 5px;
  padding-bottom: 4px;
  padding-left: 2px;
  font-size: 0.8rem !important;
}
.grid-content-border {
  border-bottom: 1px solid #ddd;
}
.grid-row {
  padding-right: 10px;
}
.show-data {
  cursor: pointer;
}

.quick-actions a,
.quick-actions button {
  padding-top: 4px;
  padding-bottom: 4px;
  font-size: 0.9rem;
  text-align: left;
}
.network-interface-data .p-button-link {
  font-size: 0.9rem;
}
.disabled {
  color: #aaa !important;
  pointer-events: none;
  cursor: not-allowed;
}
.disabled * {
  color: #aaa !important;
  pointer-events: none;
  cursor: not-allowed;
}
