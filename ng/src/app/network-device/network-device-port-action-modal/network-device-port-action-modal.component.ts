import { AlertService } from 'src/app/services/alert.service';
import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-network-device-port-action-modal',
  templateUrl: './network-device-port-action-modal.component.html',
  styleUrls: ['./network-device-port-action-modal.component.css'],
})
export class NetworkDevicePortActionModalComponent implements OnInit {
  networkDeviceName: any;
  port: any;
  action: any;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.networkDeviceName = this.config.data.networkDeviceName;
    this.port = this.config.data.port;
    this.action = this.config.data.action;
  }

  openClosePort(): void {
    this.alertService.clear();
    this.isSubmitting = true;
    this.httpClient
      .post(`/_/internal/nseapi/v2/networkDevices/${this.networkDeviceName}/ports/${this.port}/${this.action}`, {})
      .subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.closeModal({ portStatusUpdated: true });
          this.alertService.alert(
            {
              type: 'success',
              description: $localize`:@@bma_networkdevice_successportaction:"Successfully completed action ${this.action} port ${this.port} for network device ${this.networkDeviceName}"`,
            },
            true
          );
        },
        error: (error: any) => {
          this.isSubmitting = false;
          this.closeModal({ portStatusUpdated: false });
          this.alertService.alertApiError(error.error, true);
        },
      });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
