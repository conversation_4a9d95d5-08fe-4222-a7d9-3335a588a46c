import { ActivatedRoute, Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Title } from '@angular/platform-browser';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { ModalService } from 'src/app/services/modal.service';
import { NetworkDeviceRemoveModalComponent } from '../network-device-remove-modal/network-device-remove-modal.component';
import { NetworkDeviceEditModalComponent } from '../network-device-edit-modal/network-device-edit-modal.component';

@Component({
  selector: 'app-network-device-tabs',
  templateUrl: './network-device-tabs.component.html',
  styleUrls: ['./network-device-tabs.component.css'],
})
export class NetworkDeviceTabsComponent implements OnInit {
  networkDevice: any;
  header: string;
  isLoading = false;
  errorInformation: string[] = [];

  constructor(
    private activatedRoute: ActivatedRoute,
    private httpClient: HttpClient,
    private router: Router,
    private titleService: Title,
    private modalService: ModalService,
    private currentUserService: CurrentUserService
  ) {}

  ngOnInit(): void {
    this.activatedRoute.url.subscribe((parameters) => this.onUrlParamsChanged(parameters));
  }

  onUrlParamsChanged(urlParams): void {
    this.isLoading = true;
    this.networkDevice = null;
    this.errorInformation = [];

    this.httpClient.get<any>(`/_/internal/nseapi/v2/networkDevices/${urlParams[1].path}`).subscribe(
      (networkDevice: any) => {
        this.isLoading = false;
        this.networkDevice = networkDevice;

        if (networkDevice.automationLevel === 0) {
          this.errorInformation.push(
            $localize`:@@bma_networkdevice_noautomationfeatures:Automation level for this device is 0. No Automation features will work on this device.`
          );
        }

        if (networkDevice.automationLevel > 0 && networkDevice.type === 'SWITCH') {
          if (
            networkDevice.networkType === 'INTERNAL' ||
            (networkDevice.networkType === 'PUBLIC' && networkDevice.isNgn === true)
          ) {
            let uplinks = 0;

            networkDevice.interfaces.forEach((value) => {
              if (value.type === 'UPLINK') {
                uplinks++;
              }

              if (value.connectedPrivateNetworkEquipment !== null) {
                switch (value.connectedPrivateNetworkEquipment.equipmentType) {
                  case 'DEDICATED_SERVER': {
                    value.connectedPrivateNetworkEquipment.equipmentUrl = '/servers';
                    break;
                  }
                  case 'PRIVATE_RACK': {
                    value.connectedPrivateNetworkEquipment.equipmentUrl = '/racks';
                    break;
                  }
                  case 'COLOCATION': {
                    value.connectedPrivateNetworkEquipment.equipmentUrl = '/colocations';
                    break;
                  }
                  case 'FIREWALL':
                  case 'LOAD_BALANCER': {
                    value.connectedPrivateNetworkEquipment.equipmentUrl = '/networkEquipments';
                    break;
                  }
                }
              }
            });

            if (uplinks < 2) {
              this.errorInformation.push(
                $localize`:@@bma_networkdevice_notenoughuplinkinterfaces:Less than 2 connected uplink interfaces found on device.`
              );
              if (networkDevice.brand === 'ARISTA') {
                this.errorInformation.push(
                  $localize`:@@bma_networkdevice_arista_redundancy_note:If this switch is configured for a redundant private network with 1Gbps or 10Gbps, ignore the message about uplinks.`
                );
              }
            }
          }
        }

        if (networkDevice.type === 'ROUTER' && !networkDevice.pairedNetworkDevice) {
          this.errorInformation.push(
            $localize`:@@bma_networkdevice_routerwithoutpairednetworkdevice:This router does not have paired network device.`
          );
        }
        this.titleService.setTitle(
          $localize`:@@bma_networkdevice_singlepagetitle:Network Device ${networkDevice.name} | Leaseweb`
        );
      },
      (error) => {
        this.isLoading = false;
        this.router.navigate(['**']);
      }
    );
  }

  openNetworkDeviceRemoveModal() {
    const dynamicDialogRef = this.modalService.show(NetworkDeviceRemoveModalComponent, {
      networkDevice: this.networkDevice,
    });

    dynamicDialogRef.onClose.subscribe((status) => {
      if (status?.networkDeviceRemoved === true) {
        this.router.navigate(['networkDevices']);
      }
    });
  }

  onNetworkDeviceLoaded(component): void {
    if (this.networkDevice) {
      component.networkDevice = this.networkDevice;
      component.equipment = this.networkDevice;
      component.equipmentType = 'networkDevices';
    }
    component.errorInformation = this.errorInformation;
  }

  openNetworkDeviceEditModal() {
    this.modalService.show(NetworkDeviceEditModalComponent, {
      networkDevice: this.networkDevice,
    });
  }
}
