<div id="network-device-orange-bar" *ngIf="networkDevice" [ngClass]="{'d-none' : errorInformation?.length === 0}" class="alert alert-warning alert-dismissible" role="alert">
    <p i18n="@@bma_networkdevice_missinginformation"><span class="fa fa-alert"></span> <strong> Device Missing Information</strong></p>
    <ul id="network-device-missing-info">
        <li *ngFor="let message of errorInformation">{{ message }}</li>
    </ul>
</div>

<app-customer-aware-header i18n="@@bma_networkdevice_networkdevicename" *ngIf="networkDevice" caption="Network Device {{networkDevice.name}}" isEmployee="true"></app-customer-aware-header>

<app-loader *ngIf="isLoading"></app-loader>

<ul class="nav nav-pills mb-4" *ngIf="!isLoading && networkDevice">
    <li class="nav-item dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-details mr-2"></span>
            <span i18n="@@bma_common_networkdevice_dropdowndetails">Details</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a [routerLink]="['.']" class="dropdown-item">
                <span class="text-muted fa fa-details mr-2"></span>
                <span i18n="@@bma_common_networkdevice_networkdevicedetails">Network Device Details</span>
            </a>
            <a [routerLink]="['lldp']" [ngClass]="{'disabled' : networkDevice.automationLevel === 0}" class="dropdown-item">
                <span class="text-muted fa fa-usage mr-2"></span>
                <span i18n="@@bma_common_networkdevice_lldpdetails">LLDP Details</span>
            </a>
            <a [routerLink]="['interfaces']" [ngClass]="{'disabled' : networkDevice.automationLevel === 0}" class="dropdown-item">
                <span class="text-muted fa fa-usage mr-2"></span>
                <span i18n="@@bma_common_networkdevice_networkinterfaces">Network Interfaces</span>
            </a>
        </div>
    </li>
    <li class="nav-item dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-details mr-2"></span>
            <span i18n="@@bma_common_networkdevice_dropdownactivities">Activities</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a [href]="networkDevice._links.logstorEntriesUrl" [ngClass]="{'disabled' : networkDevice.automationLevel !== 3}" class="dropdown-item">
                <span class="text-muted fa fa-search mr-2"></span>
                <span i18n="@@bma_common_networkdevice_syslog">Syslog</span>
            </a>
            <a [href]="networkDevice._links.icingaLogsUrl" [ngClass]="{'disabled' : networkDevice.automationLevel !== 3}" class="dropdown-item">
                <span class="text-muted fa fa-search mr-2"></span>
                <span i18n="@@bma_common_networkdevice_icingamonitoring">Icinga Monitoring</span>
            </a>
            <a [routerLink]="['/audit-logs']" [queryParams]="{filter: networkDevice.name}" class="dropdown-item">
                <span class="text-muted fa fa-search mr-2"></span>
                <span i18n="@@bma_common_networkdevice_auditlogs">Audit Logs</span>
            </a>
        </div>
    </li>
    <li class="nav-item navbar-right dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-actions mr-2"></span>
            <span i18n="@@bma_common_networkdevice_dropdownactions">Actions</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a href="#" [ngClass]="{'disabled' : 'ROUTER' === networkDevice.type && !currentUserService.hasPermission('networkdevice_modify_router')}" class="dropdown-item" (click)="openNetworkDeviceEditModal(); false">
                <span class="text-muted fa fa-change mr-2"></span>
                <span i18n="@@bma_common_networkdevicetabs_editnetworkdevice">Edit Network Device</span>
            </a>
            <a href="#" [ngClass]="{'disabled' : 'ROUTER' === networkDevice.type && !currentUserService.hasPermission('networkdevice_modify_router')}" class="dropdown-item" (click)="openNetworkDeviceRemoveModal(); false">
                <span class="text-muted fa fa-delete mr-2"></span>
                <span i18n="@@bma_common_networkdevicetabs_removenetworkdevice">Remove Network Device</span>
            </a>
            <a [routerLink]="['/networkDevices/generate-config']" [queryParams]="{ equipmentId: networkDevice.equipmentId }" class="dropdown-item" [ngClass]="{'disabled' : networkDevice['automationLevel'] === 0}">
                <span class="text-muted fa fa-administration mr-2"></span>
                <span i18n="@@bma_common_networkdevicetabs_generateconfiguration">Generate Configuration</span>
            </a>
            <a [routerLink]="['backupConfiguration']" class="dropdown-item" [ngClass]="{'disabled' : networkDevice['automationLevel'] === 0}">
                <span class="text-muted fa fa-save mr-2"></span>
                <span i18n="@@bma_common_networkdevicetabs_backupconfiguration">Backup Configuration</span>
            </a>
            <a [routerLink]="['downloadConfiguration']" class="dropdown-item" [ngClass]="{'disabled' : networkDevice['automationLevel'] === 0}">
                <span class="text-muted fa fa-save mr-2"></span>
                <span i18n="@@bma_common_networkdevicetabs_downloadconfiguration">Download Configuration</span>
            </a>
            <a [routerLink]="['restoreConfiguration']" class="dropdown-item" [ngClass]="{'disabled' : (networkDevice['automationLevel'] === 0 || !['JUNIPER', 'ARISTA'].includes(networkDevice['brand']))}">
                <span class="text-muted pi pi-refresh mr-2"></span>
                <span i18n="@@bma_common_networkdevicetabs_restoreconfiguration">Restore Configuration</span>
            </a>
        </div>
    </li>
    <li class="nav-item navbar-right dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-usage mr-2"></span>
            <span i18n="@@bma_common_networkdevicetabs_dropdownusage">Usage</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a [routerLink]="['graphs']" class="dropdown-item" [ngClass]="{'disabled' : networkDevice['type'] !== 'SWITCH' || networkDevice['networkType'] === 'RM'}">
                <span class="text-muted fa fa-usage mr-2"></span>
                <span i18n="@@bma_common_networkdevicetabs_dropdowngraphs">Graphs</span>
            </a>
        </div>
    </li>
    <li class="nav-item navbar-right dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-knowledge mr-2"></span>
            <span i18n="@@bma_common_networkdevicetabs_dropdownhelp">Help</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a href="https://wiki.ocom.com/display/PROG/Add+a+switch+to+automation" class="dropdown-item">
                <span class="text-muted fa fa-knowledge mr-2"></span>
                <span i18n="@@bma_common_networkdevicetabs_howtoswitchautomation">How to add a switch to automation</span>
            </a>
            <a href="https://wiki.ocom.com/display/KC/BMA+-+Examine+the+syslog+of+a+switch" class="dropdown-item">
                <span class="text-muted fa fa-knowledge mr-2"></span>
                <span i18n="@@bma_common_networkdevicetabs_howtosyslogswitch">How to examine the syslog of a switch</span>
            </a>
            <a href="https://wiki.ocom.com/display/KC/BMA+-+How-to+log+in+a+tacacs+enabled+switch" class="dropdown-item">
                <span class="text-muted fa fa-knowledge mr-2"></span>
                <span i18n="@@bma_common_networkdevicetabs_howtologtacacs">How to log in a tacacs enabled switch</span>
            </a>
            <a href="https://wiki.ocom.com/display/KC/BMA+-+Obtain+the+initial+configuration+for+a+new+switch" class="dropdown-item">
                <span class="text-muted fa fa-knowledge mr-2"></span>
                <span i18n="@@bma_common_networkdevicetabs_howtogetinitialconfig">How to obtain the initial configuration for a new switch</span>
            </a>
            <a href="https://wiki.ocom.com/display/KC/Remove+a+switch+from+automation" class="dropdown-item">
                <span class="text-muted fa fa-knowledge mr-2"></span>
                <span i18n="@@bma_common_networkdevicetabs_howtoremoveswitchfromautomation">How to remove a switch from automation</span>
            </a>
            <a href="https://wiki.ocom.com/display/KC/Replace+a+switch+managed+by+NSE-API" class="dropdown-item">
                <span class="text-muted fa fa-knowledge mr-2"></span>
                <span i18n="@@bma_common_networkdevicetabs_howtoreplacemanagedswitchnseapi">How to replace a switch managed by NSE-API</span>
            </a>
            <a href="https://wiki.ocom.com/display/KC/BMA+-+Verify+that+a+switch+works+correctly+with+our+automation" class="dropdown-item">
                <span class="text-muted fa fa-knowledge mr-2"></span>
                <span i18n="@@bma_common_networkdevicetabs_howtoverifyswitchautomation">How to verify that a switch works correctly with our automation</span>
            </a>
        </div>
    </li>
</ul>

<ng-container *ngIf="networkDevice">
    <router-outlet (activate)="onNetworkDeviceLoaded($event)"></router-outlet>
</ng-container>
