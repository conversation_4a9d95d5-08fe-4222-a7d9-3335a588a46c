import { Router } from '@angular/router';
import { Component, OnInit, Input } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { NetworkDeviceAttributeAddModalComponent } from '../network-device-attribute-add-modal/network-device-attribute-add-modal.component';
import { NetworkDeviceAttributeRemoveModalComponent } from '../network-device-attribute-remove-modal/network-device-attribute-remove-modal.component';

@Component({
  selector: 'app-network-device-details',
  templateUrl: './network-device-details.component.html',
  styleUrls: ['./network-device-details.component.css'],
})
export class NetworkDeviceDetailsComponent implements OnInit {
  @Input() networkDevice: any;
  @Input() errorInformation: string[];
  isLoadingSystemInformation = false;
  networkDeviceSystemInformation: any;
  isEmployee = false;
  networkDeviceSysInfoError: any;
  selectedAttributeKey = '';
  backupStatusText = 'Loading';
  backupStatusCss = '';
  automationLevels = [];
  dynamicDialogRef: DynamicDialogRef;

  constructor(
    private httpClient: HttpClient,
    private alertService: AlertService,
    private router: Router,
    private modalService: ModalService,
    private currentUserService: CurrentUserService
  ) {}

  ngOnInit(): void {
    this.getAutomationLevels();
    this.fetchQuerySystemInfo();
    this.fetchBackupStatus();
    this.isEmployee = this.currentUserService.isEmployee();
  }

  interfaceChangeHandler(isInterfaceUpdated: boolean): void {
    if (isInterfaceUpdated) {
      this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
        this.router.navigate([`networkDevices/${this.networkDevice.name}`]);
      });
    }
  }

  openNetworkDeviceAttributeAddModal(networkDeviceAttribute: any) {
    this.dynamicDialogRef = this.modalService.show(NetworkDeviceAttributeAddModalComponent, {
      networkDevice: this.networkDevice,
    });

    this.dynamicDialogRef.onClose.subscribe((status) => {
      if (status?.networkDeviceAttributeAdded === true) {
        this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
          this.router.navigate([`networkDevices/${this.networkDevice.name}`]);
        });
      }
    });
  }

  openNetworkDeviceAttributeRemoveModal(networkDeviceAttribute: any) {
    this.dynamicDialogRef = this.modalService.show(NetworkDeviceAttributeRemoveModalComponent, {
      networkDeviceName: this.networkDevice.name,
      networkDeviceAttribute,
    });

    this.dynamicDialogRef.onClose.subscribe((status) => {
      if (status?.networkDeviceAttributeRemoved === true) {
        this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
          this.router.navigate([`networkDevices/${this.networkDevice.name}`]);
        });
      }
    });
  }

  getAutomationLevels(): void {
    this.httpClient
      .get<any>(`/_/internal/nseapi/v2/networkDeviceAutomationLevels?limit=200`)
      .subscribe((response: any) => {
        if (response) {
          const automationLevels = [];
          response.networkDeviceAutomationLevels.forEach((item, index) => {
            automationLevels[item.name] = item.description;
          });
          this.automationLevels = automationLevels;
        }
      });
  }

  fetchQuerySystemInfo(): void {
    this.isLoadingSystemInformation = true;
    this.httpClient
      .get<any>(`/_/internal/nseapi/v2/networkDevices/${this.networkDevice.name}/querySystemInfo`)
      .subscribe(
        (networkDeviceSystemInfo: any) => {
          this.networkDeviceSystemInformation = networkDeviceSystemInfo;
          this.isLoadingSystemInformation = false;
          const networkDeviceName = this.networkDevice.chassisName
            ? this.networkDevice.chassisName
            : this.networkDevice.name;
          if (
            networkDeviceSystemInfo.system.sanitizedName &&
            networkDeviceSystemInfo.system.sanitizedName.toLowerCase() !== networkDeviceName.toLowerCase()
          ) {
            this.errorInformation.push(
              $localize`:@@bma_networkdevicedetails_devicenamemismatch:Device name mismatch.` +
                ' [DB Name : ' +
                networkDeviceName +
                ', SNMP Name : ' +
                networkDeviceSystemInfo.system.sanitizedName +
                ']'
            );
          }
          const networkDeviceSerialNumber = this.networkDevice.chassisName
            ? networkDeviceSystemInfo.virtualChassisMembers?.[this.networkDevice.chassisMemberId]?.serial_number
            : networkDeviceSystemInfo.serial_number;
          if (this.networkDevice.metadata) {
            const serialNumberObj = this.networkDevice.metadata.find((obj) => obj.key.toUpperCase() === 'SERIAL');
            if (
              serialNumberObj &&
              networkDeviceSerialNumber &&
              serialNumberObj.value.toLowerCase() !== networkDeviceSerialNumber.toLowerCase()
            ) {
              this.errorInformation.push(
                $localize`:@@bma_networkdevicedetails_deviceserialnumbermismatch:Device serial number mismatch.` +
                  ' [SAP : ' +
                  serialNumberObj.value +
                  ', SNMP : ' +
                  networkDeviceSerialNumber +
                  ']'
              );
            }
          }
        },
        (error) => {
          this.isLoadingSystemInformation = false;
          this.networkDeviceSysInfoError = error;
        }
      );
  }

  fetchBackupStatus(): void {
    this.httpClient
      .get(`/_/internal/nseapi/v2/networkDevices/${this.networkDevice.name}/backupStatus`)
      .subscribe((backupStatus: any) => {
        switch (backupStatus.status) {
          case 'SUCCESS':
            this.backupStatusText = backupStatus.timestamp;
            this.backupStatusCss = 'text-success';
            break;
          case 'NO_BACKUP':
            this.backupStatusText = $localize`:@@bma_networkdevicedetails_missingbackup:Missing Backup`;
            this.backupStatusCss = 'text-warning';
            break;
          case 'NOT_IN_BACKUP':
            this.backupStatusText = $localize`:@@bma_networkdevicedetails_backupdisabled:Backup Disabled`;
            this.backupStatusCss = 'text-danger';
            break;
          default:
            this.backupStatusText = $localize`:@@bma_networkdevicedetails_unknown:Unknown`;
            this.backupStatusCss = 'text-warning';
            break;
        }
      });
  }
}
