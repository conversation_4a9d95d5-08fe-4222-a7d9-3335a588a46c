<div class="row mb-4">
    <div class="col">
        <!-- Details -->
        <h3 class="mb-3" i18n="@@bma_common_details">Details</h3>

        <app-loader *ngIf="!networkDevice"></app-loader>

        <table class="table table-sm" *ngIf="networkDevice">
            <colgroup>
                <col class="table-col-30" />
                <col class="table-col-70" />
            </colgroup>

            <tbody>
                <tr>
                    <td i18n="@@bma_common_site">Site</td>
                    <td>
                        {{ networkDevice.site }}
                    </td>
                </tr>

                <tr>
                    <td i18n="@@bma_common_equipmentid">Equipment ID</td>
                    <td>
                        <span class="selectable">
                            {{ networkDevice.equipmentId }}
                        </span>
                    </td>
                </tr>

                <tr>
                    <td i18n="@@bma_common_name">Name</td>
                    <td>
                        <span class="selectable">
                            {{ networkDevice.name }}
                        </span>
                    </td>
                </tr>

                <tr *ngIf="networkDevice.chassisName">
                    <td i18n="@@bma_common_chassisname">Chassis Name</td>
                    <td>
                        <span class="selectable">
                            <a [routerLink]="['/networkDevices']" [queryParams]="{filter: networkDevice.chassisName}">
                                {{ networkDevice.chassisName }}
                            </a>
                        </span>
                    </td>
                </tr>

                <tr *ngIf="networkDevice.chassisName">
                    <td i18n="@@bma_common_chassismemberid">Chassis Member ID</td>
                    <td>
                        <span class="selectable">
                            {{ networkDevice.chassisMemberId }}
                        </span>
                    </td>
                </tr>

                <tr>
                    <td i18n="@@bma_common_automationlevel">Automation Level</td>
                    <td>
                        <span [ngClass]="{'text-warning' : networkDevice.automationLevel === 3 && networkDevice.type === 'SWITCH' && networkDevice.networkInterfacesCount === 0}">
                            {{ automationLevels[networkDevice.automationLevel] }}
                        </span>
                    </td>
                </tr>

                <tr>
                    <td i18n="@@bma_common_networktype">Network Type</td>
                    <td>
                        <span>
                            {{ networkDevice.networkType|titlecase }}
                        </span>
                    </td>
                </tr>

                <tr>
                    <td i18n="@@bma_common_devicetype">Device Type</td>
                    <td>
                        {{ networkDevice.type|titlecase }}
                    </td>
                </tr>

                <tr>
                    <td i18n="@@bma_common_brand">Brand</td>
                    <td>
                        {{ networkDevice.brand|titlecase }}
                    </td>
                </tr>

                <tr>
                    <td i18n="@@bma_common_ipaddress">IP Address</td>
                    <td>
                        <a *ngIf="isEmployee" href="/ipam/ips/List?SearchConfig.SubnetId={{ networkDevice.ip }}">
                            {{ networkDevice.ip }}
                        </a>
                        <span *ngIf="!isEmployee" class="text-monospace selectable">
                            {{ networkDevice.ip }}
                        </span>
                    </td>
                </tr>

                <tr>
                    <td class="py-2" i18n="@@bma_common_communitystring">Community String</td>
                    <td class="py-2">
                        <app-community-string [communityString]="networkDevice.community"></app-community-string>
                    </td>
                </tr>

                <tr *ngIf="networkDevice.type === 'ROUTER' && networkDevice.pairedNetworkDevice">
                    <td i18n="@@bma_common_pairednetworkdevice">Paired network device</td>
                    <td>
                        <a [routerLink]="['/networkDevices', networkDevice.pairedNetworkDevice.name]" aria-hidden="true">{{networkDevice.pairedNetworkDevice.name}}</a>
                    </td>
                </tr>

                <tr>
                    <td class="py-2" i18n="@@bma_common_created">Created</td>
                    <td class="py-2">
                        {{ networkDevice.createdAt|timeAgo }} &nbsp;<span i18n="@@bma_common_ago">ago</span>

                        <small class="text-muted pull-right">
                            {{ networkDevice.createdAt | lswDateTime }}
                        </small>
                    </td>
                </tr>

                <tr>
                    <td i18n="@@bma_common_updated">Updated</td>
                    <td>
                        {{ networkDevice.updatedAt|timeAgo }} &nbsp;<span i18n="@@bma_common_ago">ago</span>

                        <small class="text-muted pull-right">
                            {{ networkDevice.updatedAt | lswDateTime }}
                        </small>
                    </td>
                </tr>

                <tr>
                    <td i18n="@@bma_common_backupstatus">Backup Status</td>
                    <td>
                        <ng-container *ngIf="networkDevice.automationLevel > 0; else notAvailable">
                            <span id="backup_status" class="text-nowrap {{ backupStatusCss }}">
                                {{ backupStatusText }}
                            </span>
                        </ng-container>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Attributes -->
    <div class="col">
        <h3 class="mb-3" i18n="@@bma_common_attributes">Attributes</h3>

        <app-loader *ngIf="!networkDevice"></app-loader>

        <table class="table table-sm" *ngIf="networkDevice">
            <thead>
                <tr>
                    <th class="table-col-25" i18n="@@bma_common_attribute">Attribute</th>
                    <th class="table-col-50" i18n="@@bma_common_value">Value</th>
                    <th class="text-center table-col-20" i18n="@@bma_common_updated">Updated</th>
                    <th class="text-center table-col-5">&nbsp;</th>
                </tr>
            </thead>

            <tbody>
                <tr>
                    <td i18n="@@bma_networkdevicedetails_ngn">NGN</td>
                    <td>
                        <span i18n="@@bma_common_yes" class="selectable text-monospace" *ngIf="networkDevice.isNgn">Yes</span>
                        <span i18n="@@bma_common_no" class="selectable text-monospace" *ngIf="!networkDevice.isNgn">No</span>
                    </td>
                    <td class="text-center">
                        <small title="{{ networkDevice.updatedAt }}" data-toggle="tooltip" data-placement="bottom">
                            {{ networkDevice.updatedAt|timeAgo }}
                        </small>
                    </td>
                    <td>&nbsp;</td>
                </tr>
                <tr *ngFor="let item of networkDevice['metadata']">
                    <td>
                        {{ item.key }}
                    </td>
                    <td>
                        {{ item.value }}
                    </td>
                    <td class="text-center" title="{{ item.updatedAt|timeAgo }} ago">
                        <small i18n-title="@@bma_networkdevicedetails_timeago" title="{{ item.updatedAt|timeAgo }} ago" data-toggle="tooltip" data-placement="bottom"> {{ item.updatedAt|timeAgo }} &nbsp;<span i18n="@@bma_common_ago">ago</span></small>
                    </td>
                    <td>
                        <a title="Remove Attribute" [ngClass]="{'disabled' : !currentUserService.hasPermission('networkdevice_remove')}" (click)="openNetworkDeviceAttributeRemoveModal(item.key)">
                            <span class="fa fa-delete" aria-hidden="true"></span>
                        </a>
                    </td>
                </tr>
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="99" class="pt-4 text-right">
                        <a [ngClass]="{'disabled' : !currentUserService.hasPermission('networkdevice_remove')}" (click)="openNetworkDeviceAttributeAddModal()" class="btn btn-sm btn-success" i18n="@@bma_networkdevicedetails_addattribute">
                            <span class="fa fa-plus"></span>
                            Add Attribute
                        </a>
                    </td>
                </tr>
            </tfoot>
        </table>

        <br />

        <!-- System Information -->
        <h3 class="mb-3" i18n="@@bma_common_systeminformation">System information</h3>

        <app-loader *ngIf="isLoadingSystemInformation"></app-loader>

        <div id="network-device-system-error" *ngIf="networkDeviceSysInfoError">
            <p id="network-device-system-error-message">
                {{ networkDeviceSysInfoError.error.errorMessage | default: 'No Error' }}
            </p>
        </div>

        <table id="network-device-system-info" class="table table-sm" *ngIf="!isLoadingSystemInformation && networkDeviceSystemInformation && networkDevice">
            <colgroup>
                <col class="table-col-20" />
                <col class="table-col-80" />
            </colgroup>
            <tbody>
                <tr>
                    <td i18n="@@bma_common_hostname">Hostname</td>
                    <td id="name">{{ networkDeviceSystemInformation.system.name | default: '-' }}</td>
                </tr>
                <tr>
                    <td i18n="@@bma_common_description">Description</td>
                    <td id="description">
                        {{ networkDeviceSystemInformation.system.description |
                    default: '-' }}
                    </td>
                </tr>
                <tr>
                    <td i18n="@@bma_common_brand">Brand</td>
                    <td id="model">{{ networkDeviceSystemInformation.system.brand | default: '-' }}</td>
                </tr>
                <tr>
                    <td i18n="@@bma_common_objectid">Object ID</td>
                    <td id="object-id">{{ networkDeviceSystemInformation.system.object_id | default: '-' }}</td>
                </tr>
                <tr>
                    <td i18n="@@bma_common_uptime">Uptime</td>
                    <td id="uptime">{{ networkDeviceSystemInformation.system.uptime.label | default: '-' }}</td>
                </tr>
                <tr>
                    <td i18n="@@bma_common_contact">Contact</td>
                    <td id="contact">{{ networkDeviceSystemInformation.system.contact | default: '-' }}</td>
                </tr>
                <tr>
                    <td i18n="@@bma_common_location">Location</td>
                    <td id="location">{{ networkDeviceSystemInformation.system.location | default: '-' }}</td>
                </tr>
                <tr>
                    <td i18n="@@bma_common_services">Services</td>
                    <td id="services">{{ networkDeviceSystemInformation.system.services | default: '-' }}</td>
                </tr>
                <tr *ngIf="!networkDevice.chassisName">
                    <td i18n="@@bma_common_serialnumber">Serial Number</td>
                    <td id="serial">{{ networkDeviceSystemInformation.serial_number | default: '-' }}</td>
                </tr>
                <ng-container *ngFor="let member of networkDeviceSystemInformation.virtualChassisMembers; index as memberId">
                    <tr>
                        <th colspan="2">Virtual Chassis Member {{ memberId }}</th>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_role">Role</td>
                        <td>{{ member.role | default: '-' }}</td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_uptime">Uptime</td>
                        <td>{{ member.uptime.label | default: '-' }}</td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_location">Location</td>
                        <td>{{ member.location | default: '-' }}</td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_serialnumber">Serial Number</td>
                        <td>{{ member.serial_number | default: '-' }}</td>
                    </tr>
                </ng-container>
            </tbody>
        </table>
        <!-- End System Information -->
    </div>
</div>
<ng-template #notAvailable>
    <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
</ng-template>

<ng-container>
    <app-network-device-network-interfaces (onInterfaceChange)="interfaceChangeHandler($event)" [networkDevice]="networkDevice"></app-network-device-network-interfaces>
</ng-container>
