import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { SiteService } from 'src/app/services/site.service';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { CidrToIpPipe } from 'src/app/pipes/cidr-to-ip.pipe';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { AlertService } from 'src/app/services/alert.service';

@Component({
  selector: 'app-network-device-add-modal',
  templateUrl: './network-device-add-modal.component.html',
  styleUrls: ['./network-device-add-modal.component.css'],
})
export class NetworkDeviceAddModalComponent implements OnInit {
  form: UntypedFormGroup;
  isSubmitting = false;
  isEquipmentLoading = false;
  isEquipmentInSap: boolean;
  sites: any;
  brands = [];
  types = [];
  networkTypes = [];
  automationLevels = [];
  pairedNetworkDevices = [];
  showDialog = true;
  isVirtualChassis = false;

  constructor(
    private siteService: SiteService,
    private httpClient: HttpClient,
    private readonly formBuilder: UntypedFormBuilder,
    private router: Router,
    private cidrToIpPipe: CidrToIpPipe,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.sites = this.siteService.sites();

    this.form = this.formBuilder.group({
      name: [null, Validators.required],
      ip: [null, Validators.required],
      community: [null, Validators.required],
      type: [null, Validators.required],
      site: [null, Validators.required],
      networkType: [null, Validators.required],
      automationLevel: [null, Validators.required],
      brand: [null, Validators.required],
      isNgn: [true],
      equipmentId: [null, Validators.required],
      pairedNetworkDeviceName: [null],
      chassisName: [null, this.requiredIfValidator(() => this.isVirtualChassis)],
      chassisMemberId: [null, this.requiredIfValidator(() => this.isVirtualChassis)],
    });

    this.getBrands();
    this.getDeviceTypes();
    this.getNetworkTypes();
    this.getAutomationLevels();
  }

  getBrands(): void {
    this.httpClient.get<any>(`/_/internal/nseapi/v2/networkDeviceBrands?limit=200`).subscribe((brands: any) => {
      if (brands) {
        this.brands = brands.networkDeviceBrands;
      }
    });
  }

  getDeviceTypes(): void {
    this.httpClient.get<any>(`/_/internal/nseapi/v2/networkDeviceTypes?limit=200`).subscribe((types: any) => {
      if (types) {
        this.types = types.networkDeviceTypes;
      }
    });
  }

  getNetworkTypes(): void {
    this.httpClient
      .get<any>(`/_/internal/nseapi/v2/networkDeviceNetworkTypes?limit=200`)
      .subscribe((networkTypes: any) => {
        if (networkTypes) {
          this.networkTypes = networkTypes.networkDeviceNetworkTypes;
        }
      });
  }

  getAutomationLevels(): void {
    this.httpClient
      .get(`/_/internal/nseapi/v2/networkDeviceAutomationLevels?limit=200`)
      .subscribe((automationLevels: any) => {
        if (automationLevels) {
          this.automationLevels = automationLevels.networkDeviceAutomationLevels;
        }
      });
  }

  onDeviceTypeChange(event): void {
    this.form.patchValue({ pairedNetworkDeviceName: null });
    this.getPairedNetworkDevices();
  }

  onNetworkTypeChange(event): void {
    this.form.patchValue({ pairedNetworkDeviceName: null });
    this.getPairedNetworkDevices();

    if (['PUBLIC', 'INTERNAL', 'RM'].includes(this.form.get('networkType').value)) {
      this.form.patchValue({ automationLevel: '3' });
    } else {
      this.form.patchValue({ automationLevel: null });
    }
  }

  onSiteChange(event): void {
    this.form.patchValue({ pairedNetworkDeviceName: null });
    this.getPairedNetworkDevices();
  }

  getPairedNetworkDevices(): void {
    const deviceName = this.form.get('name').value;
    const deviceType = this.form.get('type').value;
    const networkType = this.form.get('networkType').value;
    const site = this.form.get('site').value;

    if (deviceType === 'SWITCH' && networkType === 'INTERNAL' && site && deviceName) {
      let switchNameFilter = deviceName;
      const index = deviceName.lastIndexOf('-SW');
      if (index !== -1) {
        // AMS-01-5-10-29-INT
        switchNameFilter = deviceName.substring(0, index + 3);
      }

      // trying to fetch switches in same rack
      this.httpClient
        .get(
          `/_/internal/nseapi/v2/networkDevices?limit=200&type=${deviceType}&networkType=${networkType}&site=${site}&name=${switchNameFilter}%`
        )
        .subscribe((pairedNetworkDevices: any) => {
          if (pairedNetworkDevices) {
            this.pairedNetworkDevices = pairedNetworkDevices.networkDevices;
          }
        });
    }

    if (deviceType === 'ROUTER' && networkType && site) {
      this.httpClient
        .get(
          `/_/internal/nseapi/v2/networkDevices?limit=200&type=${deviceType}&networkType=${networkType}&site=${site}`
        )
        .subscribe((pairedNetworkDevices: any) => {
          if (pairedNetworkDevices) {
            this.pairedNetworkDevices = pairedNetworkDevices.networkDevices;
          }
        });
    } else {
      this.pairedNetworkDevices = [];
    }
  }

  onNetworkDeviceSubmit() {
    // Hack to make Enter press work as expected for the first equipment search
    // Following presses will trigger the whole form submit
    if (this.isEquipmentInSap === undefined) {
      this.getEquipmentDetail(this.form.get('equipmentId').value);
      return;
    }

    if (this.form.invalid) {
      return;
    }

    this.isSubmitting = true;
    const body = this.form.getRawValue();

    if (
      this.form.get('type').value !== 'ROUTER' ||
      typeof this.form.get('pairedNetworkDeviceName').value != 'string' ||
      this.form.get('pairedNetworkDeviceName').value === null
    ) {
      delete body.pairedNetworkDeviceName;
    }

    this.httpClient.post('/_/internal/nseapi/v2/networkDevices', body).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;
        this.alertService.alert(
          {
            type: 'success',
            description:
              $localize`:@@bma_networkdeviceaddmodal_added:Successfully added the network device ` + `${data.name}`,
          },
          true
        );
        this.closeModal({ networkDeviceUpdated: true });

        if (data.name) {
          this.router.navigate([`networkDevices/${data.name}`]);
        } else {
          this.router.navigate(['networkDevices']);
        }
      },
      error: (error: any) => {
        this.isSubmitting = false;
        this.form.setErrors({ api: error.error });
        Object.entries(error.error.errorDetails || {}).forEach(([key, value]) => {
          this.form.get(key).setErrors({ api: value[0] });
        });
      },
    });
  }

  generateCommunityString(length: number = 11) {
    if (!length) {
      length = 11;
    }
    const charSet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let randomCommunityString = '';
    for (let i = 0; i < length; i++) {
      const randomPosition = Math.floor(Math.random() * charSet.length);
      randomCommunityString += charSet.substring(randomPosition, randomPosition + 1);
    }

    return randomCommunityString;
  }

  getEquipmentDetail(equipmentId): void {
    this.isEquipmentLoading = true;
    this.httpClient.get<any>(`/_legacy/networkDevices/${equipmentId}`).subscribe({
      next: (networkDevice: any) => {
        this.isEquipmentLoading = false;
        this.isEquipmentInSap = true;

        this.prePopulateForm(networkDevice);
      },
      error: (error: any) => {
        this.isEquipmentLoading = false;
        this.isEquipmentInSap = false;

        this.resetForm(equipmentId);
      },
    });
  }

  resetForm(equipmentId): void {
    this.form.reset({
      community: this.generateCommunityString(11),
      equipmentId,
      isNgn: true,
    });
  }

  prePopulateForm(networkDevice): void {
    this.form.setValue({
      name: networkDevice.description,
      ip: this.cidrToIpPipe.transform(networkDevice.ip),
      community: this.generateCommunityString(11),
      type: networkDevice.type,
      site: networkDevice.site,
      networkType: null,
      automationLevel: null,
      brand: networkDevice.brand,
      isNgn: true,
      equipmentId: networkDevice.id,
      pairedNetworkDeviceName: null,
      chassisName: null,
      chassisMemberId: null,
    });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }

  toggleIsVirtualChassis(event: any = null) {
    this.isVirtualChassis = !this.isVirtualChassis;

    this.form.patchValue({
      chassisName: null,
      chassisMemberId: null,
    });
  }

  isApplicableForVirtualChassis(): boolean {
    const formValue = this.form.value;
    const applicable =
      formValue.brand === 'JUNIPER' && formValue.networkType === 'INTERNAL' && formValue.type === 'SWITCH';

    if (!applicable) {
      this.isVirtualChassis = false;
    }

    return applicable;
  }

  requiredIfValidator(predicate) {
    return (formControl) => {
      if (!formControl.parent) {
        return null;
      }
      if (predicate()) {
        return Validators.required(formControl);
      }
      return null;
    };
  }
}
