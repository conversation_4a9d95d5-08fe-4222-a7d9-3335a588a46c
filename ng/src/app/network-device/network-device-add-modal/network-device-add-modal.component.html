<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{width: '100%', 'max-height': '550px'}">
    <ng-template pTemplate="header">
        <h3 class="modal-title">Add Network Device</h3>
    </ng-template>

    <form [formGroup]="form">
        <div class="modal-body">
            <div class="row form-group mt-1 mb-2">
                <label i18n="@@bma_common_equipmentid" class="col-3 col-form-label text-right" for="equipmentId">Equipment ID</label>
                <div class="col-8">
                    <div class="input-group">
                        <input #equipmentId type="text" id="equipmentId" (keyup.enter)="getEquipmentDetail(equipmentId.value)" class="form-control" formControlName="equipmentId" autofocus />
                        <div class="input-group-append">
                            <button i18n-label="@@bma_common_search" pButton type="button" (click)="getEquipmentDetail(equipmentId.value)" [loading]="isEquipmentLoading" label="Search" icon="pi pi-search" class="p-button-primary p-button-outlined p-button-without-padding"></button>
                        </div>
                    </div>

                    <span i18n="@@bma_common_equipmentnotfound" *ngIf="isEquipmentInSap === false" class="text-warning">Equipment not found in SAP or not supported yet. You can continue if you know what you are doing.<br /></span>
                    <small i18n="@@bma_common_equipmentfiledsap" class="text-muted font-italic">This field is the id of the equipment from SAP</small>
                    <small *ngIf="form.get('equipmentId').errors?.api" class="text-danger"><br />{{ form.get('equipmentId').errors.api }}</small>
                </div>
            </div>

            <div *ngIf="isEquipmentInSap !== undefined">
                <div class="row form-group mt-1 mb-2 pt-3 border-top">
                    <label i18n="@@bma_common_name" class="col-3 col-form-label text-right" for="name">Name</label>
                    <div class="col-8">
                        <input i18n-placeholder="@@bma_common_namingconvention" type="text" id="name" formControlName="name" class="form-control" placeholder="Please comply with the naming convention" />
                        <small i18n="@@bma_networkdeviceaddmodal_globalnamestructurehelp" class="text-muted font-italic">For automation to work, it is important that the name of the network device here matches the name in SAP. For more information see: <a href="https://wiki.ocom.com/display/PROV/Global+name+structure">Global Name Structure Explained</a></small>
                        <small *ngIf="form.get('name').errors?.api" class="text-danger"><br />{{ form.get('name').errors.api }}</small>
                    </div>
                </div>

                <div class="row form-group mt-1 mb-2">
                    <label i18n="@@bma_common_ipaddress" class="col-3 col-form-label text-right" for="ip">IP Address</label>
                    <div class="col-8">
                        <input i18n-placeholder="@@bma_common_networkdeviceip" type="text" id="ip" formControlName="ip" class="form-control" placeholder="The ipv4 address of the network device" />
                        <small *ngIf="form.get('ip').errors?.api" class="text-danger">{{ form.get('ip').errors.api }}</small>
                    </div>
                </div>

                <div class="row form-group mt-1 mb-2">
                    <label i18n="@@bma_common_community" class="col-3 col-form-label text-right" for="community">Community</label>
                    <div class="col-8">
                        <input type="text" id="community" formControlName="community" class="form-control" />
                        <small i18n="@@bma_networkdeviceaddmodal_communityhelp" class="text-muted font-italic">This should be the read/write community string of the switch. For juniper switches this should also be the password for the `provisioning` user.</small>
                        <small *ngIf="form.get('community').errors?.api" class="text-danger"><br />{{ form.get('community').errors.api }}</small>
                    </div>
                </div>

                <div class="row form-group mt-1 mb-2">
                    <label i18n="@@bma_common_type" class="col-3 col-form-label text-right" for="type">Type</label>
                    <div class="col-8">
                        <select class="form-control" id="type" formControlName="type" (change)="onDeviceTypeChange($event)">
                            <option i18n="@@bma_common_choosetype" value="null" disabled>Choose a type</option>
                            <option value="{{ type['name'] }}" *ngFor="let type of types">{{ type['description'] }}</option>
                        </select>
                        <small *ngIf="form.get('type').errors?.api" class="text-danger">{{ form.get('type').errors.api }}</small>
                    </div>
                </div>

                <div class="row form-group mt-1 mb-2">
                    <label i18n="@@bma_common_site" class="col-3 col-form-label text-right" for="site">Site</label>
                    <div class="col-8">
                        <select class="form-control" id="site" formControlName="site" (change)="onSiteChange($event)">
                            <option i18n="@@bma_common_choosesite" value="null" disabled>Choose a site for automation</option>
                            <option value="{{ site }}" *ngFor="let site of sites">{{ site }}</option>
                        </select>
                        <small i18n="@@bma_networkdevice_belongstosite" class="text-muted font-italic">Site this network device belongs to.</small>
                        <small *ngIf="form.get('site').errors?.api" class="text-danger"><br />{{ form.get('site').errors.api }}</small>
                    </div>
                </div>

                <div class="row form-group mt-1 mb-2">
                    <label i18n="@@bma_common_networktype" class="col-3 col-form-label text-right" for="networkType">Network Type</label>
                    <div class="col-8">
                        <select class="form-control" id="networkType" formControlName="networkType" (change)="onNetworkTypeChange($event)">
                            <option i18n="@@bma_common_choosenetworktype" value="null" disabled>Choose a network type</option>
                            <option value="{{ networkType['name'] }}" *ngFor="let networkType of networkTypes">{{ networkType['description'] }}</option>
                        </select>
                        <small *ngIf="form.get('networkType').errors?.api" class="text-danger">{{ form.get('networkType').errors.api }}</small>
                    </div>
                </div>

                <div class="row form-group mt-1 mb-2">
                    <label i18n="@@bma_common_automationlevel" class="col-3 col-form-label text-right" for="automationLevel">Automation Level</label>
                    <div class="col-8">
                        <select class="form-control" id="automationLevel" formControlName="automationLevel">
                            <option i18n="@@bma_common_chooseautomationlevel" value="null" disabled>Choose an automation level</option>
                            <option value="{{ automationLevel['name'] }}" *ngFor="let automationLevel of automationLevels">{{ automationLevel['description'] }}</option>
                        </select>
                        <small *ngIf="form.get('automationLevel').errors?.api" class="text-danger">{{ form.get('automationLevel').errors.api }}</small>
                    </div>
                </div>

                <div class="row form-group mt-1 mb-2">
                    <label i18n="@@bma_common_brand" class="col-3 col-form-label text-right" for="brand">Brand</label>
                    <div class="col-8">
                        <select class="form-control" id="brand" formControlName="brand">
                            <option i18n="@@bma_common_choosebrand" value="null" disabled>Choose a brand</option>
                            <option value="{{ brand['name'] }}" *ngFor="let brand of brands">{{ brand['description'] }}</option>
                        </select>
                        <small *ngIf="form.get('brand').errors?.api" class="text-danger">{{ form.get('brand').errors.api }}</small>
                    </div>
                </div>

                <div class="row form-group mt-1 mb-1">
                    <label i18n="@@bma_networkdeviceaddmodal_isngn" class="col-3 col-form-label text-right" fmr="isNgn">Is NGN</label>
                    <div class="col-8">
                        <input type="checkbox" id="isNgn" class="mt-2" formControlName="isNgn" />
                        <small *ngIf="form.get('isNgn').errors?.api" class="text-danger">{{ form.get('isNgn').errors.api }}</small>
                    </div>
                </div>

                <div class="row form-group mt-1 mb-2" *ngIf="pairedNetworkDevices.length > 0">
                    <label i18n="@@bma_common_pairednetworkdevice" class="col-3 col-form-label text-right" for="pairedNetworkDeviceName">Paired network device</label>
                    <div class="col-8">
                        <select class="form-control" id="pairedNetworkDeviceName" formControlName="pairedNetworkDeviceName">
                            <option i18n="@@bma_common_choosepairednetworkdevice" value="null" disabled>Choose a paired network device</option>
                            <option value="{{ pairedNetworkDevice['name'] }}" *ngFor="let pairedNetworkDevice of pairedNetworkDevices">{{ pairedNetworkDevice['name'] }}</option>
                        </select>
                        <small i18n="@@bma_common_pairednetworkdevicehelp" class="text-muted font-italic">Select the device that is paired with this network device. Note: please select network type and site first.</small>
                        <small *ngIf="form.get('pairedNetworkDeviceName').errors?.api" class="text-danger"><br />{{ form.get('pairedNetworkDeviceName').errors.api }}</small>
                    </div>
                </div>

                <div class="row form-group mt-1 mb-1" *ngIf="isApplicableForVirtualChassis()">
                    <label i18n="@@bma_networkdeviceaddmodal_isvirtualchassis" class="col-3 col-form-label text-right" fmr="isVirtualChassis">Is member of a Virtual Chassis</label>
                    <div class="col-8">
                        <input type="checkbox" id="isVirtualChassis" class="mt-2" (change)="toggleIsVirtualChassis()" [checked]="isVirtualChassis" />
                    </div>
                </div>

                <div class="row form-group mt-1 mb-2 pt-3" *ngIf="isVirtualChassis">
                    <label i18n="@@bma_common_chassisname" class="col-3 col-form-label text-right" for="chassisName">Chassis Name</label>
                    <div class="col-8">
                        <input i18n-placeholder="@@bma_common_namingconvention" type="text" id="chassisName" formControlName="chassisName" class="form-control" placeholder="Please comply with the naming convention" />
                        <small i18n="@@bma_networkdeviceaddmodal_virtualchassisnamehelp" class="text-muted font-italic">For automation to work, it is important that the name of the Virtual Chassis here matches the <a href="https://wiki.ocom.com/pages/viewpage.action?pageId=373261411">naming convention.</a></small>
                        <small *ngIf="form.get('chassisName').errors?.api" class="text-danger"><br />{{ form.get('chassisName').errors.api }}</small>
                    </div>
                </div>

                <div class="row form-group mt-1 mb-2 pt-3" *ngIf="isVirtualChassis">
                    <label i18n="@@bma_common_chassismemberid" class="col-3 col-form-label text-right" for="name">Chassis Member ID</label>
                    <div class="col-8">
                        <select class="form-control" id="chassisMemberId" formControlName="chassisMemberId">
                            <option i18n="@@bma_common_choosechassismemberid" value="null" disabled>Choose an id number</option>
                            <option>0</option>
                            <option>1</option>
                        </select>
                        <small i18n="@@bma_networkdeviceaddmodal_chassismemberidhelp" class="text-muted font-italic">The chassis member id must match the device id in the virtual chassis.</small>
                        <small *ngIf="form.get('chassisMemberId').errors?.api" class="text-danger"><br />{{ form.get('chassisMemberId').errors.api }}</small>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <div *ngIf="form.errors?.api?.errorMessage" class="alert alert-danger" role="alert">
        <span>{{ form.errors.api.errorMessage }}</span>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_close" pButton type="button" icon="pi pi-times" iconPos="left" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button i18n-label="@@bma_common_ok" pButton (click)="onNetworkDeviceSubmit()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" label="OK"></button>
        </div>
    </ng-template>
</p-dialog>
