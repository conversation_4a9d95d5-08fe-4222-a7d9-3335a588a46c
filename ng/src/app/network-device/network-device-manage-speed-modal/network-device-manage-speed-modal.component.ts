import { AlertService } from 'src/app/services/alert.service';
import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-network-device-manage-speed-modal',
  templateUrl: './network-device-manage-speed-modal.component.html',
  styleUrls: ['./network-device-manage-speed-modal.component.css'],
})
export class NetworkDeviceManageSpeedModalComponent implements OnInit {
  form: UntypedFormGroup;
  isSubmitting = false;
  networkDeviceName = '';
  port = '';
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    private readonly formBuilder: UntypedFormBuilder,
    private activatedRoute: ActivatedRoute,
    private alertService: AlertService,
    private dynamicDialogRef: DynamicDialogRef,
    public dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.form = this.formBuilder.group({
      linkSpeed: ['remove', Validators.required],
    });
    this.networkDeviceName = this.dynamicDialogConfig.data.networkDeviceName;
    this.port = this.dynamicDialogConfig.data.port;
  }

  setNetworkDevice(networkDeviceName) {
    this.networkDeviceName = networkDeviceName;
    this.httpClient
      .get<any>(`/_/internal/nseapi/v2/networkDevices/${networkDeviceName}`)
      .subscribe((networkDevice: any) => {});
  }

  onChangeSpeed() {
    this.alertService.clear();
    if (this.form.valid) {
      const body: any = {};
      body.linkSpeed = this.form.get('linkSpeed').value;

      this.activatedRoute.queryParams.subscribe((queryParams) => {
        body.serverId = queryParams.serverId;
      });

      let postUrl = '';

      if (body.linkSpeed === 'remove') {
        postUrl = `/_/internal/nseapi/v2/networkDevices/${this.networkDeviceName}/ports/${this.port}/uncap`;
      } else {
        postUrl = `/_/internal/nseapi/v2/networkDevices/${this.networkDeviceName}/ports/${this.port}/cap`;
      }
      this.isSubmitting = true;

      this.httpClient.post(postUrl, body).subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.closeModal({ portSpeedUpdated: true, linkSpeed: body.linkSpeed });
          this.alertService.alert(
            {
              type: 'success',
              description:
                body.linkSpeed === 'remove'
                  ? $localize`:@@bma_networkdevice_removecapsuccess:Successfully removed any existing cap from port '${this.port}' for network device '${this.networkDeviceName}'`
                  : $localize`:@@bma_networkdevice_linkspeedchanged:Successfully changed the link speed to ${body.linkSpeed} mbps for port '${this.port}' for network device '${this.networkDeviceName}'`,
            },
            true
          );
        },
        error: (error: any) => {
          this.isSubmitting = false;
          if (error.error.errorDetails) {
            Object.entries(error.error.errorDetails).forEach(([key, value]) => {
              this.form.get(key).setErrors({ server: value[0] });
            });
          }
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ portSpeedUpdated: false });
        },
      });
    } else {
      this.alertService.alert(
        {
          type: 'error',
          description: $localize`:@@bma_networkdevice_changespeedfailed:Failed to change speed`,
        },
        true
      );
    }
  }

  closeModal(event: any = null): void {
    this.dynamicDialogRef.close(event);
  }
}
