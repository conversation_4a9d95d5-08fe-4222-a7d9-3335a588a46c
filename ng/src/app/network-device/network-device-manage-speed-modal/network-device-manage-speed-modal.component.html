<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '800px' }">
    <ng-template pTemplate="header">
        <h3 class="modal-title" i18n="@@bma_common_switchportcapuncapoperation">Switch Port Cap/Uncap Operation</h3>
    </ng-template>
    <form [formGroup]="form" (ngSubmit)="onChangeSpeed()">
        <div class="modal-body">
            <p i18n="@@bma_common_selectlinkspeed">Select the desired link speed for {{ port }} on device {{ networkDeviceName }}?</p>
            <select id="linkSpeed" class="form-control" formControlName="linkSpeed" required="required" autofocus>
                <option value="remove" i18n="@@bma_common_removeportcap">Remove port cap</option>
                <option value="10">10 Mbps</option>
                <option value="100">100 Mbps</option>
                <option value="1000">1000 Mbps</option>
                <option value="10000">10000 Mbps</option>
                <option value="25000">25000 Mbps</option>
            </select>
        </div>
    </form>
    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button type="button" class="btn btn-dark" (click)="closeModal()" i18n="@@bma_common_cancel">Cancel</button>
            <button pButton [loading]="isSubmitting" (click)="onChangeSpeed()" icon="pi pi-check" iconPos="left" i18n-label="@@bma_common_execute" label="Execute"></button>
        </div>
    </ng-template>
</p-dialog>
