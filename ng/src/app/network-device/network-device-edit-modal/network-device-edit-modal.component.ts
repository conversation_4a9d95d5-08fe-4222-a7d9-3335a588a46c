import { AlertService } from 'src/app/services/alert.service';
import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { SiteService } from 'src/app/services/site.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-network-device-edit-modal',
  templateUrl: './network-device-edit-modal.component.html',
  styleUrls: ['./network-device-edit-modal.component.css'],
})
export class NetworkDeviceEditModalComponent implements OnInit {
  networkDevice: any;
  form: UntypedFormGroup;
  isSubmitting = false;
  isEquipmentLoading = false;
  isEquipmentInSap: boolean;
  sites: any;
  brands = [];
  types = [];
  networkTypes = [];
  automationLevels = [];
  pairedNetworkDevices = [];
  showDialog = true;
  isVirtualChassis: boolean;

  constructor(
    private siteService: SiteService,
    private httpClient: HttpClient,
    private readonly formBuilder: UntypedFormBuilder,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private alertService: AlertService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.networkDevice = this.config.data.networkDevice;
    this.sites = this.siteService.sites();

    if (this.networkDevice.chassisName) {
      this.isVirtualChassis = true;
    }

    this.form = this.formBuilder.group({
      name: [this.networkDevice.name, Validators.required],
      ip: [this.networkDevice.ip, Validators.required],
      community: [this.networkDevice.community, Validators.required],
      type: [this.networkDevice.type, Validators.required],
      site: [this.networkDevice.site, Validators.required],
      networkType: [this.networkDevice.networkType, Validators.required],
      automationLevel: [this.networkDevice.automationLevel, Validators.required],
      brand: [this.networkDevice.brand, Validators.required],
      isNgn: [this.networkDevice.isNgn],
      equipmentId: [this.networkDevice.equipmentId, Validators.required],
      pairedNetworkDeviceName: [this.networkDevice.pairedNetworkDeviceName],
      chassisName: [this.networkDevice.chassisName, this.requiredIfValidator(() => this.isVirtualChassis)],
      chassisMemberId: [this.networkDevice.chassisMemberId, this.requiredIfValidator(() => this.isVirtualChassis)],
    });

    this.getBrands();
    this.getDeviceTypes();
    this.getNetworkTypes();
    this.getAutomationLevels();
    this.getPairedNetworkDevices();
  }

  getBrands(): void {
    this.httpClient.get<any>(`/_/internal/nseapi/v2/networkDeviceBrands?limit=200`).subscribe((brands: any) => {
      if (brands) {
        this.brands = brands.networkDeviceBrands;
      }
    });
  }

  getDeviceTypes(): void {
    this.httpClient.get<any>(`/_/internal/nseapi/v2/networkDeviceTypes?limit=200`).subscribe((types: any) => {
      if (types) {
        this.types = types.networkDeviceTypes;
      }
    });
  }

  getNetworkTypes(): void {
    this.httpClient
      .get<any>(`/_/internal/nseapi/v2/networkDeviceNetworkTypes?limit=200`)
      .subscribe((networkTypes: any) => {
        if (networkTypes) {
          this.networkTypes = networkTypes.networkDeviceNetworkTypes;
        }
      });
  }

  getAutomationLevels(): void {
    this.httpClient
      .get(`/_/internal/nseapi/v2/networkDeviceAutomationLevels?limit=200`)
      .subscribe((automationLevels: any) => {
        if (automationLevels) {
          this.automationLevels = automationLevels.networkDeviceAutomationLevels;
        }
      });
  }

  onDeviceTypeChange(event): void {
    this.form.patchValue({ pairedNetworkDeviceName: null });
    this.getPairedNetworkDevices();
  }

  onNetworkTypeChange(event): void {
    this.form.patchValue({ pairedNetworkDeviceName: null });
    this.getPairedNetworkDevices();
  }

  onSiteChange(event): void {
    this.form.patchValue({ pairedNetworkDeviceName: null });
    this.getPairedNetworkDevices();
  }

  getPairedNetworkDevices(): void {
    const deviceType = this.form.get('type').value;
    const networkType = this.form.get('networkType').value;
    const site = this.form.get('site').value;

    if (deviceType === 'ROUTER' && networkType && site) {
      this.httpClient
        .get(
          `/_/internal/nseapi/v2/networkDevices?limit=200&type=${deviceType}&networkType=${networkType}&site=${site}`
        )
        .subscribe((pairedNetworkDevices: any) => {
          if (pairedNetworkDevices) {
            this.pairedNetworkDevices = pairedNetworkDevices.networkDevices;
          }
        });
    } else {
      this.pairedNetworkDevices = [];
    }
  }

  onNetworkDeviceEdit() {
    if (this.form.invalid) {
      return;
    }

    this.isSubmitting = true;
    const body = this.form.getRawValue();

    if (
      this.form.get('type').value !== 'ROUTER' ||
      typeof this.form.get('pairedNetworkDeviceName').value != 'string' ||
      this.form.get('pairedNetworkDeviceName').value === null
    ) {
      delete body.pairedNetworkDeviceName;
    }

    this.isSubmitting = true;
    this.alertService.clear();

    this.httpClient.put(`/_/internal/nseapi/v2/networkDevices/${this.networkDevice.name}`, body).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;
        this.alertService.alert(
          {
            type: 'success',
            description: $localize`:@@bma_networkdevice_updatesuccess:Successfully updated network device ${this.networkDevice.name}`,
          },
          true
        );
        this.closeModal({ networkDeviceUpdated: true });

        if (data.name) {
          this.router.navigate([`networkDevices/${data.name}`]);
        }
      },
      error: (error: any) => {
        this.isSubmitting = false;
        this.alertService.alertApiError(error.error, true);
        this.closeModal({ networkDeviceUpdated: false });
      },
    });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }

  toggleIsVirtualChassis(event: any = null) {
    this.isVirtualChassis = !this.isVirtualChassis;

    this.form.patchValue({
      chassisName: null,
      chassisMemberId: null,
    });
  }

  isApplicableForVirtualChassis(): boolean {
    const formValue = this.form.value;
    const applicable =
      formValue.brand === 'JUNIPER' && formValue.networkType === 'INTERNAL' && formValue.type === 'SWITCH';

    if (!applicable) {
      this.isVirtualChassis = false;
    }

    return applicable;
  }

  requiredIfValidator(predicate) {
    return (formControl) => {
      if (!formControl.parent) {
        return null;
      }
      if (predicate()) {
        return Validators.required(formControl);
      }
      return null;
    };
  }
}
