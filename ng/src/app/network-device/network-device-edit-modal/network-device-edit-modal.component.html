<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{width: '100%', 'max-height': '550px'}">
    <ng-template pTemplate="header">
        <h3 class="modal-title" i18n="@@bma_networkdeviceeditmodal_editnetworkdevice">Edit Network Device</h3>
    </ng-template>

    <form [formGroup]="form">
        <div class="modal-body">
            <div class="row form-group mt-1 mb-2 pt-3">
                <label class="col-3 col-form-label text-right" for="name" i18n="@@bma_common_name">Name</label>
                <div class="col-8">
                    <input type="text" id="name" formControlName="name" class="form-control" i18n-placeholder="@@bma_common_namingconvention" placeholder="Please comply with the naming convention" autofocus />
                    <small class="text-muted font-italic" i18n="@@bma_common_networkdevicenameautomation">For automation to work, it is important that the name of the network device here matches the name in SAP. For more information see: <a href="https://wiki.ocom.com/display/PROV/Global+name+structure" target="_blank">Global Name Structure Explained</a></small>
                    <small *ngIf="form.get('name').errors?.api" class="text-danger"><br />{{ form.get('name').errors.api }}</small>
                </div>
            </div>

            <div class="row form-group mt-1 mb-2">
                <label class="col-3 col-form-label text-right" for="ip" i18n="@@bma_common_ipaddress">IP Address</label>
                <div class="col-8">
                    <input type="text" id="ip" formControlName="ip" class="form-control" i18n-placeholder="@@bma_networkdevice_ipaddressdescription" placeholder="The ipv4 address of the network device" />
                    <small *ngIf="form.get('ip').errors?.api" class="text-danger">{{ form.get('ip').errors.api }}</small>
                </div>
            </div>

            <div class="row form-group mt-1 mb-2">
                <label class="col-3 col-form-label text-right" for="community" i18n="@@bma_common_community">Community</label>
                <div class="col-8">
                    <input type="text" id="community" formControlName="community" class="form-control" />
                    <small class="text-muted font-italic" i18n="@@bma_common_communityinstructions">This should be the read/write community string of the switch. For juniper switches this should also be the password for the `provisioning` user.</small>
                    <small *ngIf="form.get('community').errors?.api" class="text-danger"><br />{{ form.get('community').errors.api }}</small>
                </div>
            </div>

            <div class="row form-group mt-1 mb-2">
                <label class="col-3 col-form-label text-right" for="type" i18n="@@bma_common_type">Type</label>
                <div class="col-8">
                    <select class="form-control" id="type" formControlName="type" (change)="onDeviceTypeChange($event)">
                        <option value="{{ type['name'] }}" *ngFor="let type of types">{{ type['description'] }}</option>
                    </select>
                    <small *ngIf="form.get('type').errors?.api" class="text-danger">{{ form.get('type').errors.api }}</small>
                </div>
            </div>

            <div class="row form-group mt-1 mb-2">
                <label class="col-3 col-form-label text-right" for="site" i18n="@@bma_common_site">Site</label>
                <div class="col-8">
                    <select class="form-control" id="site" formControlName="site" (change)="onSiteChange($event)">
                        <option value="{{ site }}" *ngFor="let site of sites">{{ site }}</option>
                    </select>
                    <small class="text-muted font-italic" i18n="@@bma_networkdevice_belongstosite">Site this network device belongs to.</small>
                    <small *ngIf="form.get('site').errors?.api" class="text-danger"><br />{{ form.get('site').errors.api }}</small>
                </div>
            </div>

            <div class="row form-group mt-1 mb-2">
                <label class="col-3 col-form-label text-right" for="networkType" i18n="@@bma_common_networktype">Network Type</label>
                <div class="col-8">
                    <select class="form-control" id="networkType" formControlName="networkType" (change)="onNetworkTypeChange($event)">
                        <option value="{{ networkType['name'] }}" *ngFor="let networkType of networkTypes">{{ networkType['description'] }}</option>
                    </select>
                    <small *ngIf="form.get('networkType').errors?.api" class="text-danger">{{ form.get('networkType').errors.api }}</small>
                </div>
            </div>

            <div class="row form-group mt-1 mb-2">
                <label class="col-3 col-form-label text-right" for="automationLevel" i18n="@@bma_common_automationlevel">Automation Level</label>
                <div class="col-8">
                    <select class="form-control" id="automationLevel" formControlName="automationLevel">
                        <option value="{{ automationLevel['name'] }}" *ngFor="let automationLevel of automationLevels">{{ automationLevel['description'] }}</option>
                    </select>
                    <small *ngIf="form.get('automationLevel').errors?.api" class="text-danger">{{ form.get('automationLevel').errors.api }}</small>
                </div>
            </div>

            <div class="row form-group mt-1 mb-2">
                <label class="col-3 col-form-label text-right" for="brand" i18n="@@bma_common_brand">Brand</label>
                <div class="col-8">
                    <select class="form-control" id="brand" formControlName="brand">
                        <option value="{{ brand['name'] }}" *ngFor="let brand of brands">{{ brand['description'] }}</option>
                    </select>
                    <small *ngIf="form.get('brand').errors?.api" class="text-danger">{{ form.get('brand').errors.api }}</small>
                </div>
            </div>

            <div class="row form-group mt-1 mb-2">
                <label class="col-3 col-form-label text-right" for="equipmentId" i18n="@@bma_common_equipmentid">Equipment ID</label>
                <div class="col-8">
                    <input type="text" id="equipmentId" formControlName="equipmentId" class="form-control" />
                    <small class="text-muted font-italic" i18n="@@bma_common_equipmentfiledsap">This field is the id of the equipment from SAP</small>
                    <small *ngIf="form.get('equipmentId').errors?.api" class="text-danger"><br />{{ form.get('equipmentId').errors.api }}</small>
                </div>
            </div>

            <div class="row form-group mt-1 mb-1">
                <label class="col-3 col-form-label text-right" fmr="isNgn" i18n="@@bma_common_isngn">Is NGN</label>
                <div class="col-8">
                    <input type="checkbox" id="isNgn" class="mt-2" formControlName="isNgn" />
                    <small *ngIf="form.get('isNgn').errors?.api" class="text-danger">{{ form.get('isNgn').errors.api }}</small>
                </div>
            </div>

            <div class="row form-group mt-1 mb-2" *ngIf="pairedNetworkDevices.length > 0">
                <label class="col-3 col-form-label text-right" for="pairedNetworkDeviceName" i18n="@@bma_common_pairednetworkdevice">Paired network device</label>
                <div class="col-8">
                    <select class="form-control" id="pairedNetworkDeviceName" formControlName="pairedNetworkDeviceName">
                        <option value="null" disabled i18n="@@bma_common_choosepairednetworkdevice">Choose a paired network device</option>
                        <option value="{{ pairedNetworkDevice['name'] }}" *ngFor="let pairedNetworkDevice of pairedNetworkDevices">{{ pairedNetworkDevice['name'] }}</option>
                    </select>
                    <small class="text-muted font-italic" i18n="@@bma_common_pairednetworkdeviceinstructions">Select the device that is paired with this network device. Note: please select network type and site first.</small>
                    <small *ngIf="form.get('pairedNetworkDeviceName').errors?.api" class="text-danger"><br />{{ form.get('pairedNetworkDeviceName').errors.api }}</small>
                </div>
            </div>

            <div class="row form-group mt-1 mb-1" *ngIf="isApplicableForVirtualChassis()">
                <label i18n="@@bma_networkdeviceaddmodal_isvirtualchassis" class="col-3 col-form-label text-right" fmr="isVirtualChassis">Is member of a Virtual Chassis</label>
                <div class="col-8">
                    <input type="checkbox" id="isVirtualChassis" class="mt-2" (change)="toggleIsVirtualChassis()" [checked]="isVirtualChassis" />
                </div>
            </div>

            <div class="row form-group mt-1 mb-2 pt-3" *ngIf="isVirtualChassis">
                <label i18n="@@bma_common_chassisname" class="col-3 col-form-label text-right" for="chassisName">Chassis Name</label>
                <div class="col-8">
                    <input i18n-placeholder="@@bma_common_namingconvention" type="text" id="chassisName" formControlName="chassisName" class="form-control" placeholder="Please comply with the naming convention" />
                    <small i18n="@@bma_networkdeviceaddmodal_virtualchassisnamehelp" class="text-muted font-italic">For automation to work, it is important that the name of the Virtual Chassis here matches the <a href="https://wiki.ocom.com/pages/viewpage.action?pageId=373261411">naming convention.</a></small>
                    <small *ngIf="form.get('chassisName').errors?.api" class="text-danger"><br />{{ form.get('chassisName').errors.api }}</small>
                </div>
            </div>

            <div class="row form-group mt-1 mb-2 pt-3" *ngIf="isVirtualChassis">
                <label i18n="@@bma_common_chassismemberid" class="col-3 col-form-label text-right" for="name">Chassis Member ID</label>
                <div class="col-8">
                    <select class="form-control" id="chassisMemberId" formControlName="chassisMemberId">
                        <option i18n="@@bma_common_choosechassismemberid" value="null" disabled>Choose an id number</option>
                        <option>0</option>
                        <option>1</option>
                    </select>
                    <small i18n="@@bma_networkdeviceaddmodal_chassismemberidhelp" class="text-muted font-italic">The chassis member id must match the device id in the virtual chassis.</small>
                    <small *ngIf="form.get('chassisMemberId').errors?.api" class="text-danger"><br />{{ form.get('chassisMemberId').errors.api }}</small>
                </div>
            </div>
        </div>
    </form>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button pButton type="button" icon="pi pi-times" iconPos="left" label="Close" class="p-button-secondary" (click)="closeModal()" i18n-label="@@bma_common_close"></button>
            <button pButton (click)="onNetworkDeviceEdit()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" label="OK" i18n-label="@@bma_common_ok"></button>
        </div>
    </ng-template>
</p-dialog>
