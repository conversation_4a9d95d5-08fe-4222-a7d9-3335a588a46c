import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { SiteService } from 'src/app/services/site.service';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CidrToIpPipe } from 'src/app/pipes/cidr-to-ip.pipe';
import { AlertService } from 'src/app/services/alert.service';

@Component({
  selector: 'app-network-device-generate-config',
  templateUrl: './network-device-generate-config.component.html',
  styleUrls: ['./network-device-generate-config.component.css'],
})
export class NetworkDeviceGenerateConfigComponent implements OnInit {
  @ViewChild('generatedConfigElement') generatedConfigElement: ElementRef;

  form: UntypedFormGroup;
  generatedConfigs = [];
  isSubmitting = false;
  isEquipmentLoading = false;
  isEquipmentInSap: boolean;
  sites: any;
  brands = [];
  models = [];
  types = [];
  networkTypes = [];
  automationLevels = [];
  pairedNetworkDevices = [];
  showDialog = true;
  roles: any;
  availableRoles: any;
  availableSites: any;
  networkDevice: any;

  constructor(
    private alertService: AlertService,
    private siteService: SiteService,
    private httpClient: HttpClient,
    private readonly formBuilder: UntypedFormBuilder,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private cidrToIpPipe: CidrToIpPipe,
    public element: ElementRef
  ) {}

  ngOnInit(): void {
    this.sites = this.siteService.sites();

    const controls = {
      equipmentId: [null, Validators.required],
      name: [null, Validators.required],
      ip: [null, Validators.required],
      community: [null, Validators.required],
      type: [null, Validators.required],
      site: [null, Validators.required],
      networkType: [null, Validators.required],
      brand: [null, Validators.required],
      model: [null, this.requiredIfValidator(() => this.form.get('brand').value === 'JUNIPER')],
      uplinkPorts: [null, Validators.required],
      downlinkPorts: [null, Validators.required],
      vlanId: [null, this.requiredIfValidator(() => this.form.get('networkType').value !== 'PUBLIC')],
      role: [null, Validators.required],
      secondChassisMemberEquipmentId: [null, this.requiredIfValidator(() => this.isVirtualChassis())],
    };
    this.form = this.formBuilder.group(controls);

    this.form.get('type').valueChanges.subscribe((value) => {
      this.onTypeChange(value);
    });
    this.form.get('networkType').valueChanges.subscribe((value) => {
      this.onNetworkTypeChange(value);
    });

    this.getNetworkTypes();
    this.getAvailableSites();
    this.getDeviceTypes();
    this.getBrands();
    this.getRoles();

    this.getControlsFromUrl(controls);

    if (this.form.value.equipmentId) {
      this.getEquipmentDetail(this.form.value.equipmentId);
    }

    this.availableSites = [];
    this.availableRoles = [];

    this.getModels();
  }

  getNetworkDevice(deviceName): void {
    if (!deviceName) {
      return;
    }

    this.httpClient.get<any>(`/_/internal/nseapi/v2/networkDevices/${deviceName}`).subscribe((networkDevice: any) => {
      if (networkDevice) {
        this.form.patchValue({
          community: networkDevice.community,
          brand: networkDevice.brand,
          networkType: networkDevice.networkType,
          uplinkPorts: this.getUplinkPortCount(networkDevice.interfaces),
          downlinkPorts: this.getDownlinkPortCount(networkDevice.interfaces),
        });

        if (networkDevice.networkType === 'RM') {
          this.form.patchValue({
            uplinkPorts: null,
          });
        }

        if (networkDevice.brand === 'JUNIPER') {
          this.form.patchValue({
            model: this.getModelFromNetworkDeviceMetadata(networkDevice.metadata),
          });
        }
      }
    });
  }

  getControlsFromUrl(controls: object): void {
    for (const control in controls) {
      if (controls.hasOwnProperty(control)) {
        const parameterValue = this.getParameterFromUrl(control);
        if (parameterValue) {
          this.form.controls[control].setValue(parameterValue);
        }
      }
    }
  }

  getParameterFromUrl(parameterName: string): string | null {
    return this.activatedRoute.snapshot.queryParams[parameterName] ?? null;
  }

  getBrands(): void {
    this.httpClient.get<any>(`/_/internal/nseapi/v2/networkDeviceBrands?limit=200`).subscribe((brands: any) => {
      if (brands) {
        this.brands = brands.networkDeviceBrands;
      }
    });
  }

  getModels(): void {
    this.httpClient
      .get<any>(`/_/internal/nseapi/v2/networkDeviceModels?limit=200&brand=JUNIPER`)
      .subscribe((models: any) => {
        if (models) {
          this.models = models.networkDeviceModels;
        }
      });
  }

  getDeviceTypes(): void {
    this.httpClient.get<any>(`/_/internal/nseapi/v2/networkDeviceTypes?limit=200`).subscribe((types: any) => {
      if (types) {
        this.types = types.networkDeviceTypes;
      }
    });
  }

  getNetworkTypes(): void {
    this.httpClient
      .get<any>(`/_/internal/nseapi/v2/networkDeviceNetworkTypes?limit=200`)
      .subscribe((networkTypes: any) => {
        if (networkTypes) {
          this.networkTypes = networkTypes.networkDeviceNetworkTypes;
        }
      });
  }

  getRoles(): void {
    if (!this.roles) {
      this.roles = [];
    }

    this.httpClient.get<any>(`/_/internal/nseapi/v2/networkDeviceConfigRoles`).subscribe((roles: any) => {
      this.roles = [];

      if (roles) {
        this.roles = roles;
      }
    });
  }

  getAvailableSites(): void {
    this.httpClient.get<any>(`/_/internal/nseapi/v2/networkDeviceSites`).subscribe((sites: any) => {
      this.availableSites = [];

      if (sites) {
        this.availableSites = sites;
        if (!this.isSiteAvailable(this.form.get('site').value)) {
          this.form.patchValue({
            site: null,
          });
        }
      }
    });
  }

  onNetworkDeviceSubmit() {
    // Hack to make Enter press work as expected for the first equipment search
    // Following presses will trigger the whole form submit
    this.generatedConfigs = [];

    if (this.isEquipmentInSap === undefined) {
      this.getEquipmentDetail(this.form.get('equipmentId').value);
      return;
    }

    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    //Update the query string parameters.
    const url = new URL(window.location.href);
    url.search = '';
    window.history.pushState({}, '', url);

    for (const control in this.form.getRawValue()) {
      if (this.form.get(control).value) {
        if (control === 'community') {
          continue;
        }

        url.searchParams.set(control, this.form.get(control).value);
      }
    }
    window.history.pushState({}, '', url);

    this.isSubmitting = true;
    const body = {
      ...this.form.getRawValue(),
      uplinkPorts: +this.form.get('uplinkPorts').value,
      downlinkPorts: +this.form.get('downlinkPorts').value,
    };

    if (body.brand !== 'JUNIPER' && typeof body.model !== 'undefined') {
      delete body.model;
    }

    this.httpClient.post('/_/internal/nseapi/v2/networkDevices/generateConfig', body).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;

        if (!data) {
          return;
        }

        this.generatedConfigs = data;

        setTimeout(() => {
          this.generatedConfigElement.nativeElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }, 500);
      },
      error: (error: any) => {
        this.isSubmitting = false;
        let parsedError = error.error;

        if (typeof parsedError === 'string') {
          parsedError = JSON.parse(error.error);
          this.alertService.alert({
            type: 'error',
            description: parsedError.errorMessage,
          });
        }

        if (typeof parsedError.errorDetails === 'object') {
          this.form.setErrors({ api: parsedError });
          Object.entries(parsedError.errorDetails || {}).forEach(([key, value]) => {
            this.form.get(key)?.setErrors({ api: value[0] });
          });
        }
      },
    });
  }

  generateCommunityString(length: number = 11) {
    if (!length) {
      length = 11;
    }
    const charSet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let randomCommunityString = '';
    for (let i = 0; i < length; i++) {
      const randomPosition = Math.floor(Math.random() * charSet.length);
      randomCommunityString += charSet.substring(randomPosition, randomPosition + 1);
    }

    return randomCommunityString;
  }

  getEquipmentDetail(equipmentId): void {
    this.isEquipmentLoading = true;
    this.httpClient.get<any>(`/_legacy/networkDevices/${equipmentId}`).subscribe({
      next: (networkDevice: any) => {
        this.isEquipmentLoading = false;
        this.isEquipmentInSap = true;

        this.prePopulateForm(networkDevice);
        this.getNetworkDevice(networkDevice.description);
      },
      error: (error: any) => {
        this.isEquipmentLoading = false;
        this.isEquipmentInSap = false;

        this.resetForm(equipmentId);
      },
    });
  }

  resetForm(equipmentId): void {
    this.form.reset({
      community: this.generateCommunityString(11),
      equipmentId,
    });
  }

  prePopulateForm(networkDevice): void {
    const networkType = this.getNetworkTypeByName(networkDevice.description);

    if (networkType) {
      this.onNetworkTypeChange(networkType, networkDevice.type);
    }

    const networkDeviceSite = this.isSiteAvailable(networkDevice.site) ? networkDevice.site : null;
    const brand = this.getParameterFromUrl('brand') ?? networkDevice.brand;
    let model = null;
    if (brand === 'JUNIPER') {
      model = this.getParameterFromUrl('model');
    }

    this.form.setValue({
      equipmentId: this.getParameterFromUrl('equipmentId') ?? networkDevice.id,
      name: this.getParameterFromUrl('name') ?? networkDevice.description,
      ip: this.getParameterFromUrl('ip') ?? this.cidrToIpPipe.transform(networkDevice.ip),
      community: this.generateCommunityString(11),
      type: this.getParameterFromUrl('type') ?? networkDevice.type,
      site: this.getParameterFromUrl('site') ?? networkDeviceSite,
      networkType: this.getParameterFromUrl('networkType') ?? networkType,
      brand,
      model,
      uplinkPorts: this.getParameterFromUrl('uplinkPorts'),
      downlinkPorts: this.getParameterFromUrl('downlinkPorts'),
      vlanId: this.getParameterFromUrl('vlanId'),
      role: this.getParameterFromUrl('role'),
      secondChassisMemberEquipmentId: null,
    });
  }

  onTypeChange(type): void {
    this.availableRoles = this.getAvailableRoles(type, this.form.get('networkType').value);
  }

  onNetworkTypeChange(networkType, equipmentType?): void {
    this.form.patchValue({
      role: null,
    });
    const type = this.form.get('type').value ?? equipmentType;
    this.availableRoles = this.getAvailableRoles(type, networkType);
    this.form.get('vlanId').updateValueAndValidity();
  }

  onBrandChange(event): void {
    this.form.patchValue({
      uplinkPorts: null,
      downlinkPorts: null,
    });

    this.form.get('model').updateValueAndValidity();
  }

  onRoleChange(): void {
    this.form.patchValue({
      uplinkPorts: null,
      secondChassisMemberEquipmentId: null,
    });
  }

  onModelChange(): void {
    this.form.get('uplinkPorts').updateValueAndValidity();
  }

  getAvailableRoles(equipmentType, networkType) {
    if (!equipmentType || !networkType || !Object.keys(this.roles).length) {
      return [];
    }

    if (!(equipmentType in this.roles.available) || !(networkType in this.roles.available[equipmentType])) {
      return [];
    }

    return this.roles.available[equipmentType][networkType];
  }

  getRoleName(roleKey) {
    return this.roles.names[roleKey];
  }

  isTypeSupported(type) {
    const supported = ['SWITCH'];

    return supported.indexOf(type.name) > -1;
  }

  isNetworkTypeSupported(networkType) {
    const supported = ['INTERNAL', 'PUBLIC', 'RM'];

    return supported.indexOf(networkType.name) > -1;
  }

  isModelSupported(model) {
    const supported = ['EX2200', 'EX3200', 'EX3300', 'EX3400', 'QFX5100', 'QFX5120'];

    return supported.indexOf(model.name) > -1;
  }

  isBrandSupported(brand) {
    const supported = ['JUNIPER', 'ARISTA', 'HP'];

    return supported.indexOf(brand.name) > -1;
  }

  isSiteAvailable(site) {
    return this.availableSites.indexOf(site) > -1;
  }

  getAvailableUplinkPortCount(): Array<number> {
    if (this.form.get('role').value === 'ngn_rm') {
      return [1, 2];
    }

    if (
      (this.form.get('brand').value === 'ARISTA' && this.form.get('role').value === 'dedser_redundant_10ge') ||
      this.form.get('role').value === 'dedser_redundant_100ge'
    ) {
      return [1];
    }

    if (this.form.get('brand').value === 'ARISTA') {
      return [2, 4, 6, 8];
    }

    if (
      this.form.get('brand').value === 'JUNIPER' &&
      this.form.get('role').value === 'dedser_200ge' &&
      this.form.get('model').value === 'QFX5120'
    ) {
      return [4];
    }

    if (
      this.form.get('brand').value === 'HP' &&
      this.form.get('role').value === 'dedser_200ge' &&
      this.form.get('networkType').value === 'RM'
    ) {
      return [1];
    }

    return [2];
  }

  getAvailableDownlinkPortCount(): Array<number> {
    if (this.form.get('brand').value === 'ARISTA') {
      return [32, 48];
    }

    if (this.form.get('brand').value === 'HP') {
      return [24, 48];
    }

    if (this.form.get('brand').value === 'JUNIPER') {
      return [24, 48];
    }

    return [];
  }

  getNetworkTypeByName(name): string {
    if (!name) {
      return null;
    }

    const availableTypes = {
      internal: 'INT',
      public: 'PUB',
      rm: 'RM',
    };

    for (const type in availableTypes) {
      if (name.indexOf(`-${availableTypes[type]}-`) > -1) {
        return type.toUpperCase();
      }
    }

    return null;
  }

  requiredIfValidator(predicate) {
    return (formControl) => {
      if (!formControl.parent) {
        return null;
      }
      if (predicate()) {
        return Validators.required(formControl);
      }
      return null;
    };
  }

  isVirtualChassis() {
    return this.form.get('brand')?.value === 'JUNIPER' && this.form.get('role')?.value === 'dedser_redundant_2x200ge';
  }

  private getModelFromNetworkDeviceMetadata(metadata: Array<any>): string | null {
    const found = metadata.find((item) => item.key === 'MODEL');

    return found?.value.toUpperCase() ?? null;
  }

  private getUplinkPortCount(interfaces: Array<any>): number | null {
    const uplinks = interfaces.filter((item) => item.type === 'UPLINK').length;
    const availablePortCount = this.getAvailableUplinkPortCount();

    return this.findClosestMatch(uplinks, availablePortCount);
  }

  private getDownlinkPortCount(interfaces: Array<any>): number | null {
    const downlinks = interfaces.filter((item) => item.type === 'NORMAL').length;
    const availablePortCount = this.getAvailableDownlinkPortCount();

    return this.findClosestMatch(downlinks, availablePortCount);
  }

  private findClosestMatch(needle: number, haystack: Array<number>): number {
    const distances = haystack.map((k) => Math.abs(k - needle));
    const min = Math.min(...distances);
    return haystack[distances.indexOf(min)];
  }
}
