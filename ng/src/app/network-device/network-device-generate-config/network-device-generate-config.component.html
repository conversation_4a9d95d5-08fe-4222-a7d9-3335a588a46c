<div class="row mb-4">
    <div class="col">
        <h1 i18n="@@bma_common_networkdevices">Network Devices</h1>
    </div>
</div>

<div class="row">
    <div class="col">
        <h3 class="mb-3" i18n="@@bma_common_generateconfiguration">Generate Configuration</h3>
    </div>
</div>

<div class="row">
    <div class="col">
        <p i18n="@@bma_networkdevice_notapplyonswitch">This will not apply the configuration on the switch.</p>
    </div>
</div>

<div class="row">
    <div class="col">
        <form [formGroup]="form">
            <div class="row form-group mt-1 mb-2">
                <div class="col-3">
                    <div class="row">
                        <label i18n="@@bma_common_equipmentid" class="col col-form-label" for="equipmentId">Equipment ID</label>
                    </div>

                    <div class="row">
                        <div class="input-group col-12">
                            <input #equipmentId type="text" id="equipmentId" (keyup.enter)="getEquipmentDetail(equipmentId.value)" class="form-control" formControlName="equipmentId" autofocus tabindex="1" />
                            <div class="input-group-append">
                                <button i18n-label="@@bma_common_search" pButton type="button" (click)="getEquipmentDetail(equipmentId.value)" [loading]="isEquipmentLoading" label="Search" icon="pi pi-search" class="p-button-primary p-button-outlined p-button-without-padding form-control"></button>
                            </div>
                        </div>
                        <div class="col-12">
                            <span i18n="@@bma_common_equipmentnotfound" *ngIf="isEquipmentInSap === false" class="text-warning">Equipment not found in SAP or not supported yet. You can continue if you know what you are doing.<br /></span>
                            <small i18n="@@bma_common_equipmentfiledsap" class="text-muted font-italic">This field is the id of the equipment from SAP</small>
                            <small *ngIf="form.get('equipmentId').errors?.api" class="text-danger"><br />{{ form.get('equipmentId').errors.api }}</small>
                        </div>
                    </div>
                </div>
            </div>

            <div *ngIf="isEquipmentInSap !== undefined">
                <div class="row form-group mt-1 mb-2 pt-3 border-top">
                    <div class="col-4">
                        <div class="row">
                            <label i18n="@@bma_common_name" class="col-12 col-form-label" for="name">Name</label>
                            <div class="col-12">
                                <input i18n-placeholder="@@bma_common_namingconvention" type="text" id="name" formControlName="name" class="form-control" placeholder="Please comply with the naming convention" tabindex="2" />
                                <small *ngIf="form.get('name').errors?.api" class="text-danger"><br />{{ form.get('name').errors.api }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="row">
                            <label i18n="@@bma_common_ipaddress" class="col-12 col-form-label" for="ip">IP Address</label>
                            <div class="col-12">
                                <input i18n-placeholder="@@bma_common_networkdeviceip" type="text" id="ip" formControlName="ip" class="form-control" placeholder="The ipv4 address of the network device" tabindex="3" />
                                <small *ngIf="form.get('ip').errors?.api" class="text-danger">{{ form.get('ip').errors.api }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="row">
                            <label i18n="@@bma_common_community" class="col-12 col-form-label" for="community">Community</label>
                            <div class="col-12">
                                <input type="text" id="community" formControlName="community" class="form-control" tabindex="4" />
                                <small *ngIf="form.get('community').errors?.api" class="text-danger"><br />{{ form.get('community').errors.api }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="row">
                            <label i18n="@@bma_common_site" class="col-12 col-form-label" for="site">Site</label>
                            <div class="col-12">
                                <select class="form-control" id="site" formControlName="site" tabindex="6">
                                    <option i18n="@@bma_common_choosesite" value="null" disabled>Choose a site for automation</option>
                                    <option value="{{ site }}" [disabled]="!isSiteAvailable(site)" *ngFor="let site of sites">{{ site }}</option>
                                </select>
                                <small i18n="@@bma_networkdevice_belongstosite" class="text-muted font-italic">Site this network device belongs to.</small>
                                <small *ngIf="form.get('site').errors?.api" class="text-danger"><br />{{ form.get('site').errors.api }}</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row form-group mt-1 mb-2">
                    <div class="col-3">
                        <div class="row">
                            <label i18n="@@bma_common_networktype" class="col-12 col-form-label" for="networkType">Network Type</label>
                            <div class="col-12">
                                <select class="form-control" id="networkType" formControlName="networkType" tabindex="7" (change)="onNetworkTypeChange($event.target.value)">
                                    <option i18n="@@bma_common_choosenetworktype" value="null" disabled>Choose a network type</option>
                                    <option value="{{ networkType['name'] }}" [disabled]="!isNetworkTypeSupported(networkType)" *ngFor="let networkType of networkTypes">{{ networkType['description'] }}</option>
                                </select>
                                <small *ngIf="form.get('networkType').errors?.api" class="text-danger">{{ form.get('networkType').errors.api }}</small>
                                <small class="text-muted font-italic">&nbsp;</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="row">
                            <label i18n="@@bma_common_brand" class="col-12 col-form-label" for="brand">Brand</label>
                            <div class="col-12">
                                <select class="form-control" id="brand" formControlName="brand" tabindex="8" (change)="onBrandChange($event)">
                                    <option i18n="@@bma_common_choosebrand" value="null" disabled>Choose a brand</option>
                                    <option value="{{ brand['name'] }}" [disabled]="!isBrandSupported(brand)" *ngFor="let brand of brands">{{ brand['description'] }}</option>
                                </select>
                                <small *ngIf="form.get('brand').errors?.api" class="text-danger">{{ form.get('brand').errors.api }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-3" *ngIf="this.form.get('brand').value === 'JUNIPER'">
                        <div class="row">
                            <label i18n="@@bma_common_model" class="col-12 col-form-label" for="model">Model</label>
                            <div class="col-12">
                                <select class="form-control" id="model" formControlName="model" tabindex="8" (change)="onModelChange()">
                                    <option i18n="@@bma_common_choosemodel" value="null" disabled>Choose a model</option>
                                    <option value="{{ model['name'].toUpperCase() }}" *ngFor="let model of models">{{ model['name'] }}</option>
                                </select>
                                <small *ngIf="form.get('model').errors?.api" class="text-danger">{{ form.get('model').errors.api }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="row">
                            <label i18n="@@bma_common_type" class="col-12 col-form-label" for="type">Type</label>
                            <div class="col-12">
                                <select class="form-control" id="type" formControlName="type" tabindex="5" (change)="onTypeChange($event.target.value)">
                                    <option i18n="@@bma_common_choosetype" value="null" disabled>Choose a type</option>
                                    <option value="{{ type['name'] }}" [disabled]="!isTypeSupported(type)" *ngFor="let type of types">{{ type['description'] }}</option>
                                </select>
                                <small *ngIf="form.get('type').errors?.api" class="text-danger">{{ form.get('type').errors.api }}</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row form-group mt-1 mb-2">
                    <div class="col-3">
                        <div class="row">
                            <label i18n="@@bma_common_downlink_port_count" class="col-12 col-form-label" for="downlinkPorts">Downlink Port Count</label>
                            <div class="col-12">
                                <select class="form-control" id="downlinkPorts" formControlName="downlinkPorts" tabindex="8">
                                    <option i18n="@@bma_common_choosedownlinkports" value="null" disabled>Choose a downlink port count</option>
                                    <option value="{{ downlinkPortCount }}" *ngFor="let downlinkPortCount of getAvailableDownlinkPortCount()">{{ downlinkPortCount }} ports</option>
                                </select>
                                <small i18n="@@bma_networkdevice_downlinkportsinfo" class="text-muted font-italic">Total amount of downlink ports the device has.</small>
                                <small *ngIf="form.get('downlinkPorts').errors?.api" class="text-danger">{{ form.get('downlinkPorts').errors.api }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="row">
                            <label i18n="@@bma_common_role" class="col-12 col-form-label" for="role">Role</label>
                            <div class="col-12">
                                <select class="form-control" id="role" formControlName="role" tabindex="7" (change)="onRoleChange()">
                                    <option i18n="@@bma_common_chooserole" value="null" disabled>Choose an equipment role</option>
                                    <option value="{{ role }}" *ngFor="let role of availableRoles">{{ getRoleName(role) }}</option>
                                </select>
                                <small *ngIf="form.get('role').errors?.api" class="text-danger">{{ form.get('role').errors.api }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="row">
                            <label i18n="@@bma_common_uplinkportcount" class="col-12 col-form-label" for="uplinkPorts">Uplink Port Count</label>
                            <div class="col-12">
                                <select class="form-control" id="uplinkPorts" formControlName="uplinkPorts" tabindex="8">
                                    <option i18n="@@bma_common_uplinkportsused" value="null" disabled>Choose an uplink port count</option>
                                    <option value="{{ uplinkPortCount }}" *ngFor="let uplinkPortCount of getAvailableUplinkPortCount()">{{ uplinkPortCount }} ports</option>
                                </select>
                                <small i18n="@@bma_networkdevice_uplinkportsinfo" class="text-muted font-italic">Only the amount of ports actually used for uplink. e.g: 2 out of 8.</small>
                                <small *ngIf="form.get('uplinkPorts').errors?.api" class="text-danger">{{ form.get('uplinkPorts').errors.api }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="row" *ngIf="form.get('networkType').value !== 'PUBLIC'">
                            <label i18n="@@bma_common_vlanid" class="col-12 col-form-label" for="vlanId">Vlan ID</label>
                            <div class="col-12">
                                <input type="text" class="form-control" id="vlanId" formControlName="vlanId" tabindex="12" />
                                <small *ngIf="form.get('vlanId').errors?.api" class="text-danger">{{ form.get('vlanId').errors.api }}</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row form-group mt-1 mb-2" *ngIf="isVirtualChassis()">
                    <div class="col-3">
                        <div class="row">
                            <label i18n="@@bma_common_first_chassis_member_equipment_id" class="col-12 col-form-label" for="vlanId">Chassis Member #0 Equipment ID</label>
                            <div class="col-12">
                                <input type="text" readonly class="form-control" tabindex="12" value="{{ form.get('equipmentId')?.value }}" />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <small i18n="@@bma_common_vcprimarychassismember" class="text-muted font-italic">This field represents the equipment ID of the <strong>first</strong> Virtual Chassis member, also known as Primary Member.</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="row">
                            <label i18n="@@bma_common_second_chassis_member_equipment_id" class="col-12 col-form-label" for="vlanId">Chassis Member #1 Equipment ID</label>
                            <div class="col-12">
                                <input type="text" class="form-control" id="secondChassisMemberEquipmentId" formControlName="secondChassisMemberEquipmentId" tabindex="12" />
                                <small *ngIf="form.get('secondChassisMemberEquipmentId').errors?.api" class="text-danger">{{ form.get('secondChassisMemberEquipmentId').errors.api }}</small>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <small i18n="@@bma_common_vcbackupchassismember" class="text-muted font-italic">This field represents the equipment ID of the <strong>second</strong> Virtual Chassis member, also known as Backup Member.</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <div class="row mt-1 pt-3 border-top">
            <div class="col-8">
                <button i18n-label="@@bma_common_generate" pButton (click)="onNetworkDeviceSubmit()" [loading]="isSubmitting" label="Generate"></button>
            </div>
        </div>
    </div>
</div>

<div *ngIf="generatedConfigs.length > 1" class="row" id="generated-config-container" #generatedConfigElement>
    <p-tabView class="col-12 mt-3">
        <p-tabPanel [header]="'Chassis Member #'+i" *ngFor="let generatedConfig of generatedConfigs; let i = index" [selected]="i == 0" class="display-config pt-3">{{ generatedConfig?.content }}</p-tabPanel>
    </p-tabView>
</div>
<div *ngIf="generatedConfigs.length == 1" class="row" id="generated-config-container" #generatedConfigElement>
    <p-tabView class="col-12 mt-3">
        <p-tabPanel [header]="'Equipment #'+(i+1)" *ngFor="let generatedConfig of generatedConfigs; let i = index" [selected]="i == 0" class="display-config pt-3">{{ generatedConfig?.content }}</p-tabPanel>
    </p-tabView>
</div>
