textarea {
  width: 100%;
  font-family: monospace;
  border: none;
  overflow: auto;
  outline: none;
  background-color: transparent;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.copy-button-container {
  position: relative;
}
.copy-button {
  position: absolute;
  right: 0;
}
.p-button-without-padding {
  padding-top: 0;
  padding-bottom: 0;
}
input.ng-invalid.ng-touched,
select.ng-invalid.ng-touched {
  border-color: #c4153d !important;
}
.display-config {
  white-space: break-spaces;
  font-family: monospace;
}
