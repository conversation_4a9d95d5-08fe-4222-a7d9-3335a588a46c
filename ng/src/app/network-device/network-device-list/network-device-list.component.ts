import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { SiteService } from 'src/app/services/site.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { ModalService } from 'src/app/services/modal.service';
import { NetworkDeviceAddModalComponent } from '../network-device-add-modal/network-device-add-modal.component';

@Component({
  selector: 'app-network-device-list',
  templateUrl: './network-device-list.component.html',
  styleUrls: ['./network-device-list.component.css'],
})
export class NetworkDeviceListComponent implements OnInit {
  isLoading = false;
  error = false;
  networkDevices: any;

  automationLevels: any;
  brands: any;
  metadataModels: any;
  metadataFullModels: any;
  metadataVersions: any;
  selectedMetadataVersions = null;
  networkTypes: string;
  sites: string[];
  types: any[];

  filters: UntypedFormGroup;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private httpClient: HttpClient,
    private formBuilder: UntypedFormBuilder,
    private siteService: SiteService,
    private titleService: Title,
    public currentUserService: CurrentUserService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.filters = this.formBuilder.group({
      offset: [0],
      limit: [10],
      filter: [null],
      site: null,
      automationLevel: null,
      networkType: null,
      type: null,
      brand: null,
      metadataModel: null,
      metadataVersion: null,
      metadataFullModel: null,
    });

    this.titleService.setTitle('Network Devices | Leaseweb');
    this.sites = this.siteService.sites();

    this.httpClient.get<any>('/_/internal/nseapi/v2/networkDeviceNetworkAttributes').subscribe((data: any) => {
      this.automationLevels = data.networkDeviceAutomationLevels;
      this.networkTypes = data.networkDeviceNetworkTypes;
      this.types = data.networkDeviceTypes;
      this.brands = data.networkDeviceBrands;
      this.metadataModels = data.networkDeviceModels;
      this.metadataFullModels = data.networkDeviceFullModels;
      this.metadataVersions = data.networkDeviceVersions;
      this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
    });
  }

  onQueryParamsChanged(queryParams): void {
    this.isLoading = true;
    if (Object.keys(queryParams).length > 0) {
      for (const key in queryParams) {
        if (queryParams.hasOwnProperty(key)) {
          let value = null;
          switch (key) {
            case 'metadataVersion':
              value = queryParams[key].split(',');
              break;
            case 'metadataModel':
              value = { name: queryParams[key] };
              break;
            case 'metadataFullModel':
              if (this.filters.get('brand').value.name === 'JUNIPER') {
                value = { name: queryParams[key] };
              } else {
                continue;
              }
              break;
            case 'automationLevel':
              value = +queryParams[key];
              break;
            case 'brand':
              value = this.brands.find((brand) => brand.name === queryParams[key]);
              break;
            case 'type':
              value = this.types.find((type) => type.name === queryParams[key]);
              break;
            default:
              value = queryParams[key];
              break;
          }

          this.filters.get(key).setValue(value);
        }
      }
    }

    const params = this.serializeFilters();

    if (params.metadataVersion) {
      this.selectedMetadataVersions = params.metadataVersion.split(',');
    }

    this.httpClient.get<any>('/_/internal/nseapi/v2/networkDevices', { params }).subscribe(
      (data: any) => {
        this.isLoading = false;
        this.error = false;
        this.networkDevices = data;
      },
      (error: any) => {
        this.isLoading = false;
        this.error = true;
        this.networkDevices = { _metadata: { totalCount: 0 }, networkDevices: [] };
      }
    );

    this.onBrandChange();
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.serializeFilters(),
    });
  }

  serializeFilters(): any {
    const queryParam = this.sanitise(this.filters.getRawValue());

    if (queryParam.metadataVersion) {
      queryParam.metadataVersion = queryParam.metadataVersion.join(',');
    }
    if (queryParam.brand) {
      queryParam.brand = queryParam.brand.name;
    }
    if (queryParam.metadataModel) {
      queryParam.metadataModel = queryParam.metadataModel.name;
    }
    if (queryParam.metadataFullModel && this.filters.get('brand').value.name === 'JUNIPER') {
      queryParam.metadataFullModel = queryParam.metadataFullModel.name;
    } else {
      delete queryParam.metadataFullModel;
    }
    if (queryParam.type) {
      queryParam.type = queryParam.type.name;
    }
    if (this.filters.get('automationLevel').value === 0) {
      queryParam.automationLevel = 0;
    }

    return queryParam;
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  onBrandChange(): void {
    const brand = this.filters.get('brand').value;

    if (brand) {
      this.httpClient
        .get<any>(`/_/internal/nseapi/v2/networkDeviceModels?limit=2000&brand=${brand.name}`)
        .subscribe((data: any) => {
          this.metadataModels = data.networkDeviceModels;
        });

      this.httpClient
        .get<any>(`/_/internal/nseapi/v2/networkDeviceFullModels?limit=2000&brand=${brand.name}`)
        .subscribe((data: any) => {
          this.metadataFullModels = data.networkDeviceFullModels;
        });

      this.httpClient
        .get<any>(`/_/internal/nseapi/v2/networkDeviceVersions?limit=2000&brand=${brand.name}`)
        .subscribe((data: any) => {
          this.metadataVersions = data.networkDeviceVersions;
        });
    }
  }

  clearFilters(): void {
    this.filters.reset();
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
    });
  }

  getMetadataByKey(networkDeviceAttributes: any[], key: string): string {
    if (!networkDeviceAttributes || networkDeviceAttributes.length === 0) {
      return '-';
    }

    const metadata = networkDeviceAttributes.find((value) => value.key === key);

    return metadata ? metadata.value : '-';
  }

  getAutomationLevelDescription(automationLevel: string): string {
    if (!this.automationLevels || this.automationLevels.length === 0) {
      return $localize`:@@bma_common_unknown:UNKNOWN`;
    }

    const level = this.automationLevels.find((value) => value.name === automationLevel);

    const description = level ? level.description : $localize`:@@bma_common_unknown:UNKNOWN`;
    return description.replace(/\d \- /g, '');
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (!val || val === 'null') {
        return acc;
      }
      if (Array.isArray(val) && val.length === 0) {
        return acc;
      }
      acc[key] = val;
      return acc;
    }, {});
  }

  openNetworkDeviceAddModal() {
    const dynamicDialogRef = this.modalService.show(NetworkDeviceAddModalComponent, {});

    dynamicDialogRef.onClose.subscribe((status) => {
      if (status?.networkDeviceUpdated === true) {
        this.router.navigate(['networkDevices']);
      }
    });
  }
}
