<div class="row mb-4">
    <div class="col">
        <div class="float-right">
            <a pButton href="#" [routerLink]="['/networkDevices/generate-config']" type="button" i18n-label="@@bma_common_generateconfiguration" label="Generate Configuration" class="p-button-primary mr-1"></a>
            <a pButton href="#" (click)="openNetworkDeviceAddModal(); false" [ngClass]="{'disabled' : !currentUserService.hasPermission('networkdevice_add')}" type="button" i18n-label="@@bma_networkdevice_addnetworkdevice" label="Add Network Device" icon="pi pi-plus" class="p-button-success"></a>
        </div>
        <h1 i18n="@@bma_common_networkdevices">Network Devices</h1>
    </div>
</div>

<form role="form" [formGroup]="filters" (ngSubmit)="submitFilters()">
    <div class="row mb-3">
        <div class="col">
            <input type="text" id="filter" formControlName="filter" class="form-control filter-field" value="" i18n-placeholder="@@bma_common_filter" placeholder="Filter..." autofocus />
        </div>

        <div class="col">
            <p-dropdown i18n-placeholder="@@bma_common_site" formControlName="site" [options]="sites" placeholder="Site" [showClear]="true"></p-dropdown>
        </div>

        <div class="col">
            <p-dropdown i18n-placeholder="@@bma_common_automationlevel" placeholder="Automation Level" formControlName="automationLevel" [options]="automationLevels" optionLabel="description" optionValue="name" [showClear]="true"></p-dropdown>
        </div>

        <div class="col">
            <p-dropdown i18n-placeholder="@@bma_common_networktype" placeholder="Network Type" formControlName="networkType" [options]="networkTypes" optionLabel="name" optionValue="name" [showClear]="true"></p-dropdown>
        </div>
    </div>

    <div class="row">
        <div class="col">
            <p-dropdown i18n-placeholder="@@bma_common_type" placeholder="Type" formControlName="type" [options]="types" optionLabel="name" [showClear]="true"></p-dropdown>
        </div>

        <div class="col">
            <p-dropdown i18n-placeholder="@@bma_common_brand" placeholder="Brand" formControlName="brand" [options]="brands" optionLabel="name" [showClear]="true" (onChange)="onBrandChange()"></p-dropdown>
        </div>

        <div class="col">
            <p-dropdown i18n-placeholder="@@bma_common_model" placeholder="Model" formControlName="metadataModel" [options]="metadataModels" optionLabel="name" [showClear]="true"></p-dropdown>
        </div>
        <div class="col" *ngIf=" this.filters.get('brand').value && this.filters.get('brand').value.name === 'JUNIPER'">
            <p-dropdown i18n-placeholder="@@bma_common_fullmodel" placeholder="Full Model" formControlName="metadataFullModel" [options]="metadataFullModels" optionLabel="name" [showClear]="true"></p-dropdown>
        </div>
        <div class="col">
            <p-multiSelect formControlName="metadataVersion" [options]="metadataVersions" optionLabel="name" optionValue="name" [maxSelectedLabels]="1" i18n-defaultLabel="@@bma_common_version" defaultLabel="Version" [selectedItemsLabel]="'{0} versions'" dropdownIcon="pi pi-chevron-down"></p-multiSelect>
        </div>
    </div>

    <div class="row mb-3 mt-3">
        <div class="col text-right">
            <button pButton (click)="clearFilters()" type="button" i18n-label="@@bma_common_reset" label="Reset" icon="pi pi-refresh" class="p-button-secondary mr-2"></button>
            <button pButton [disabled]="isLoading" i18n-label="@@bma_common_search" label="Search" icon="pi pi-search"></button>
        </div>
    </div>
</form>

<app-loader *ngIf="isLoading"></app-loader>

<div *ngIf="!isLoading && networkDevices">
    <p-accordion [multiple]="true" *ngIf="networkDevices.networkDevices.length > 0 && !error; else noData">
        <p-accordionTab *ngFor="let networkDevice of networkDevices.networkDevices">
            <ng-template pTemplate="header">
                <div class="row col-sm-12 align-items-center">
                    <div class="col-sm-2">
                        <h3 class="mt-2">{{ networkDevice.site }}</h3>
                    </div>
                    <div class="col-sm-4">
                        <span class="h5">
                            {{ networkDevice.name }}
                        </span>
                    </div>
                    <div class="col-sm-2">
                        <span class="h5">
                            {{ networkDevice.ip }}
                        </span>
                    </div>
                    <div class="col-sm-2">
                        <span class="h5">
                            {{ networkDevice.networkType }}
                            <br />
                            {{ networkDevice.type }}
                        </span>
                    </div>
                    <div class="col-sm-2">
                        <span class="h5">
                            {{ getMetadataByKey(networkDevice.metadata, 'MODEL') }}
                            <br />
                            {{ getMetadataByKey(networkDevice.metadata, 'OS') }}
                        </span>
                    </div>
                </div>
            </ng-template>
            <ng-template pTemplate="content" class="p-p-0">
                <div class="p-grid border-bottom">
                    <div class="p-col-12 p-md-10 border-right">
                        <h5 class="p-ml-6 pt-2" i18n="@@bma_networkdevice_details">Network Device Details</h5>
                        <div class="p-col-12 p-d-flex p-flex-col p-pb-0">
                            <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-2">
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_name">Name</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <span class="selectable">{{ networkDevice.name }}</span>
                                    </div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_equipmentid">Equipment ID</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <span class="selectable">{{ networkDevice.equipmentId|default:'-' }}</span>
                                    </div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_ipaddress">IP Address</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <span class="selectable">{{ networkDevice.ip|default:'-' }}</span>
                                    </div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_networktype">Network Type</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ networkDevice.networkType }}</div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_devicetype">Device Type</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ networkDevice.brand }} {{ networkDevice.type }}</div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_automationlevel">Automation Level</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ getAutomationLevelDescription(networkDevice.automationLevel) }}</div>
                                </div>
                            </div>
                            <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-5">
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_site">Site</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ networkDevice.site }}</div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_community">Community</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <app-community-string [communityString]="networkDevice.community"></app-community-string>
                                    </div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_fullmodel">Full Model</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ getMetadataByKey(networkDevice.metadata, 'FULL_MODEL') }}</div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_networkdeviceeditmodal_os">OS</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ getMetadataByKey(networkDevice.metadata, 'OS') }}</div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_serialnumber">Serial Number</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ getMetadataByKey(networkDevice.metadata, 'SERIAL') }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-col-12 p-md-2 quick-actions">
                        <h5 class="p-2" i18n="@@bma_common_quickactions">Quick Actions</h5>
                        <div class="p-grid p-flex-column align-items-start">
                            <a pButton target="_blank" [routerLink]="['/networkDevices',networkDevice.name,'lldp']" class="p-button-link" i18n-label="@@bma_common_lldpdetails" label="LLDP Details" i18n-title="@@bma_common_lldpdetails" title="LLDP Details" [ngClass]="{'disabled' : networkDevice.automationLevel === 0}"></a>
                            <a pButton target="_blank" [routerLink]="['/networkDevices',networkDevice.name,'interfaces']" class="p-button-link" i18n-label="@@bma_common_networkinterfaces" label="Network Interfaces" i18n-title="@@bma_common_networkinterfaces" title="Network Interfaces" [ngClass]="{'disabled' : networkDevice.automationLevel === 0}"></a>
                            <a pButton target="_blank" [routerLink]="['/networkDevices/generate-config']" [queryParams]="{ equipmentId: networkDevice.equipmentId }" class="p-button-link" i18n-label="@@bma_common_generateconfiguration" label="Generate Configuration" i18n-title="@@bma_common_generateconfiguration" title="Generate Configuration" [ngClass]="{'disabled' : networkDevice['automationLevel'] === 0}"></a>
                            <a pButton target="_blank" [routerLink]="['/networkDevices',networkDevice.name,'graphs']" class="p-button-link" [ngClass]="{'disabled' : networkDevice['type'] !== 'SWITCH' || networkDevice['type'] === 'RM'}" i18n="@@bma_common_graphs">Graphs</a>
                        </div>
                    </div>
                </div>
                <div class="p-grid p-pr-5 p-pt-3">
                    <a pButton [routerLink]="['/networkDevices', networkDevice.name]" class="p-button-primary ml-auto" i18n-label="@@bma_common_manage" label="Manage" i18n-title="@@bma_common_manage" title="Manage"></a>
                </div>
            </ng-template>
        </p-accordionTab>
    </p-accordion>
</div>

<div *ngIf="!isLoading && networkDevices">
    <app-pagination [totalCount]="networkDevices._metadata.totalCount" [limit]="networkDevices._metadata.limit" [offset]="networkDevices._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
</div>

<ng-template #noData>
    <div class="align-middle bg-white p-mb-2 p-py-4">
        <span i18n="@@bma_networkdevice_notfound" *ngIf="!error">No Network Device found</span>
        <span i18n="@@bma_common_errorfetchingdata" class="text-danger" *ngIf="error">Error fetching the data</span>
    </div>
</ng-template>
