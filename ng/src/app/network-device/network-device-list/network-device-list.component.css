:host ::ng-deep .p-accordion .p-accordion-content {
  padding: 0.5rem;
}

.grid-content {
  margin-bottom: 5px;
  padding-bottom: 4px;
  padding-left: 2px;
  font-size: 0.8rem !important;
}

.grid-content-border {
  border-bottom: 1px solid #ddd;
}

.grid-row {
  padding-right: 10px;
}

.quick-actions a,
.quick-actions button {
  padding-top: 4px;
  padding-bottom: 4px;
  font-size: 0.9rem;
  text-align: left;
}

.p-grid.grid-row:last-of-type .grid-content-border {
  border-bottom: none;
}

.disabled {
  color: currentColor;
  cursor: not-allowed;
  opacity: 0.5;
  pointer-events: none;
  text-decoration: none;
}

.filter-field {
  height: auto !important;
  line-height: 1.975 !important;
  border: 1px solid #ced4da !important;
  border-radius: 3px !important;
}

.align-middle {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
