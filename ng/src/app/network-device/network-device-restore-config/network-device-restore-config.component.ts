import { Component, OnInit, Input } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';

@Component({
  selector: 'app-network-device-restore-config',
  templateUrl: './network-device-restore-config.component.html',
  styleUrls: ['./network-device-restore-config.component.css'],
})
export class NetworkDeviceRestoreConfigComponent implements OnInit {
  @Input() networkDevice: any;

  config = '';
  isLoading = false;

  constructor(
    private httpClient: HttpClient,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.isLoading = true;
    this.httpClient
      .get(`/_/internal/nseapi/v2/networkDevices/${this.networkDevice.name}/restoreConfig`, {
        responseType: 'text',
      })
      .subscribe(
        (data) => {
          this.isLoading = false;
          this.config = data;
        },
        (error) => {
          this.isLoading = false;
          this.config = error.error;
        }
      );
  }
}
