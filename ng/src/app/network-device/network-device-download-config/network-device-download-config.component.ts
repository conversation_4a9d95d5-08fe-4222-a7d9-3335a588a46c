import { Component, OnInit, Input } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';

@Component({
  selector: 'app-network-device-download-config',
  templateUrl: './network-device-download-config.component.html',
  styleUrls: ['./network-device-download-config.component.css'],
})
export class NetworkDeviceDownloadConfigComponent implements OnInit {
  @Input() networkDevice: any;

  config = '';
  isLoading = false;

  constructor(
    private httpClient: HttpClient,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.isLoading = true;
    this.httpClient
      .get(`/_/internal/nseapi/v2/networkDevices/${this.networkDevice.name}/latestConfig`, {
        responseType: 'text',
      })
      .subscribe(
        (data) => {
          this.isLoading = false;
          this.config = data;
        },
        (error) => {
          this.isLoading = false;
          this.config = error.error;
        }
      );
  }
}
