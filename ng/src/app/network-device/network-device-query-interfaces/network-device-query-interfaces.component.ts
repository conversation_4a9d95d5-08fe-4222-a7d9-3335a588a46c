import { Component, Input, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Title } from '@angular/platform-browser';
import { ModalService } from 'src/app/services/modal.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ServerInfoPopupModalComponent } from 'src/app/server/server-info-popup-modal/server-info-popup-modal.component';

@Component({
  selector: 'app-network-device-query-interfaces',
  templateUrl: './network-device-query-interfaces.component.html',
  styleUrls: ['./network-device-query-interfaces.component.css'],
})
export class NetworkDeviceQueryInterfacesComponent implements OnInit {
  @Input() networkDevice: any;
  networkInterfaces: any = [];
  isLoading = false;
  dynamicDialogRef: DynamicDialogRef;
  vlanInformationUnavailable = false;

  constructor(
    private httpClient: HttpClient,
    private titleService: Title,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.isLoading = true;
    if (this.networkDevice) {
      this.httpClient
        .get<any>(`/_/internal/nseapi/v2/networkDevices/${this.networkDevice.name}/ports/queryStatistics`)
        .subscribe(
          (networkInterfaces) => {
            this.isLoading = false;
            if (networkInterfaces) {
              this.networkInterfaces = Object.keys(networkInterfaces).map((key) => ({
                key: Number(key),
                value: networkInterfaces[key],
              }));
            }
          },
          (error) => {
            this.isLoading = false;
          }
        );
      if (this.networkDevice.metadata) {
        const modelObj = this.networkDevice.metadata.find((obj) => obj.key.toUpperCase() === 'MODEL');
        if (
          modelObj &&
          (modelObj.value.toLowerCase().startsWith('qfx') || modelObj.value.toLowerCase().startsWith('ex3400'))
        ) {
          this.vlanInformationUnavailable = true;
        }
      }
      this.titleService.setTitle(
        $localize`:@@bma_networkdevice_pagetitle:Network Device ${this.networkDevice.name} | Leaseweb`
      );
    }
  }

  openServerInfoPopupModal(macAddress: string) {
    this.modalService.show(ServerInfoPopupModalComponent, { macAddress });
  }
}
