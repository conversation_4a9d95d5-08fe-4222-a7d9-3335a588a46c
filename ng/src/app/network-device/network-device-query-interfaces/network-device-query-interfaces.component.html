<app-loader *ngIf="isLoading"></app-loader>
<div class="row" *ngIf="!isLoading && networkInterfaces">
    <div class="col-12 mt-3">
        <blockquote class="border-left pl-4">
            <p i18n="@@bma_networkdevice_errorcounterswarning">
                <sup>*</sup>Please note that the <strong>error counters</strong>
                presented contain historical data. A high value doesn't necessarily mean there is a actual problem, it could come from an event in the past. Try a couple of samples over a period of time to see if the value is increasing at this moment.
            </p>
            <p i18n="@@bma_networkdevice_vlaninformationunavailablewarning" *ngIf="vlanInformationUnavailable"><sup>*</sup>Please note that the <strong>VLAN</strong> cannot be retrieved from QFX and EX34000 network devices due to technical limitations.</p>
            <footer i18n="@@bma_common_leasewebdevelopmentteam">&mdash; The Leaseweb Development Team</footer>
        </blockquote>
        <hr />

        <div class="table-responsive">
            <table class="table table-sm">
                <thead class="multiple-row-header">
                    <tr>
                        <th class="text-center" i18n="@@bma_common_ifindex">If Index</th>
                        <th i18n="@@bma_common_status">Status</th>
                        <th i18n="@@bma_common_linkspeed">Link Speed</th>
                        <th i18n="@@bma_common_ifname">If Name</th>
                        <th i18n="@@bma_common_connectedmacs">Connected MAC(s)</th>
                        <th i18n="@@bma_common_vlan">Vlan</th>
                        <th i18n="@@bma_common_mtu">MTU</th>
                        <th i18n="@@bma_common_errors">Errors</th>
                        <th i18n="@@bma_common_discards">Discards</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let networkInterface of networkInterfaces" [ngClass]="{'table-success' : ((networkInterface.value.adminstatus ? networkInterface.value.adminstatus.label : '') === 'up'),'table-danger' : ((networkInterface.value.adminstatus ? networkInterface.value.adminstatus.label : '') === 'down')}">
                        <td class="text-center" title="{{ networkInterface.value.type ?   networkInterface.value.type.name : ''}}">
                            {{ networkInterface.key }}
                        </td>
                        <td i18n="@@bma_common_operationaladminstatus">
                            Oper: {{ networkInterface.value.operstatus ? networkInterface.value.operstatus.label : '' }}
                            <br />
                            Admin:
                            {{ networkInterface.value.adminstatus ? networkInterface.value.adminstatus.label : '' }}
                        </td>
                        <td>
                            {{ networkInterface.value.link_speed ? networkInterface.value.link_speed.name : '' }}
                            <span *ngIf="networkInterface.value.link_speed && networkInterface.value.duplexstatus">({{ networkInterface.value.duplexstatus.label}})</span>
                        </td>
                        <td>
                            <strong>{{ networkInterface.value.description ? networkInterface.value.description : '' }}</strong>
                            <br />
                            {{ networkInterface.value.alias   ? networkInterface.value.alias : ''}}
                        </td>
                        <td>
                            <div *ngFor="let mac of networkInterface.value.mac_addresses">
                                <a href (click)="openServerInfoPopupModal(mac); false">{{ mac }}</a>
                            </div>
                        </td>
                        <td>{{ networkInterface.value.vlan ?  networkInterface.value.vlan : '-'}}</td>
                        <td>{{  networkInterface.value.mtu }}</td>
                        <td>
                            In: {{ networkInterface.value.errors ? networkInterface.value.errors.in : '' }} <br />
                            Out:{{ networkInterface.value.errors ? networkInterface.value.errors.out : '' }}
                        </td>
                        <td>
                            In: {{ networkInterface.value.discards ? networkInterface.value.discards.in : '' }} <br />
                            Out:{{ networkInterface.value.discards ? networkInterface.value.discards.out : '' }}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
