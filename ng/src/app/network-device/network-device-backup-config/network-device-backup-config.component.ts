import { AlertService } from 'src/app/services/alert.service';
import { Component, OnInit, Input } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';

@Component({
  selector: 'app-network-device-backup-config',
  templateUrl: './network-device-backup-config.component.html',
  styleUrls: ['./network-device-backup-config.component.css'],
})
export class NetworkDeviceBackupConfigComponent implements OnInit {
  @Input() networkDevice: any;

  constructor(
    private httpClient: HttpClient,
    private router: Router,
    private alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.alertService.clear();
    this.httpClient
      .post(`/_/internal/nseapi/v2/networkDevices/${this.networkDevice.name}/backupConfiguration`, [])
      .subscribe({
        next: (data: any) => {
          this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
            this.router.navigate([`networkDevices/${this.networkDevice.name}`]);
          });
          this.alertService.alert(
            {
              type: 'success',
              description: $localize`:@@bma_networkdevicebackupconfig_success:Successfully started configuration backup for network device  '${this.networkDevice.name}'`,
            },
            true
          );
        },
        error: (error: any) => {
          this.alertService.alertApiError(error.error, true);
        },
      });
  }
}
