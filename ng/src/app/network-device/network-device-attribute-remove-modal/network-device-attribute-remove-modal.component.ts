import { AlertService } from 'src/app/services/alert.service';
import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-network-device-attribute-remove-modal',
  templateUrl: './network-device-attribute-remove-modal.component.html',
  styleUrls: ['./network-device-attribute-remove-modal.component.css'],
})
export class NetworkDeviceAttributeRemoveModalComponent implements OnInit {
  networkDeviceName: any;
  networkDeviceAttribute: any;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.networkDeviceName = this.config.data.networkDeviceName;
    this.networkDeviceAttribute = this.config.data.networkDeviceAttribute;
  }

  onNetworkDeviceAttributeRemove(): void {
    this.isSubmitting = true;
    this.alertService.clear();
    this.httpClient
      .delete(
        `/_/internal/nseapi/v2/networkDevices/${this.networkDeviceName}/attributes/${this.networkDeviceAttribute}`,
        {}
      )
      .subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.alertService.alert(
            {
              type: 'success',
              description: $localize`:@@bma_networkdeviceremoveattribute_success:Successfully removed Attribute '${this.networkDeviceAttribute}'`,
            },
            true
          );
          this.closeModal({ networkDeviceAttributeRemoved: true });
        },
        error: (error: any) => {
          this.isSubmitting = false;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ networkDeviceAttributeRemoved: false });
        },
      });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
