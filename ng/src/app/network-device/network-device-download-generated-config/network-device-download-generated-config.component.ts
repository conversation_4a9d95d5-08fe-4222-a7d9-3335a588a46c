import { Component, ElementRef } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';

@Component({
  selector: 'app-network-device-download-generated-config',
  templateUrl: './network-device-download-generated-config.component.html',
  styleUrls: ['./network-device-download-generated-config.component.css'],
})
export class NetworkDeviceDownloadGeneratedConfigComponent {
  config = '';
  deviceName = '';

  constructor(
    private httpClient: HttpClient,
    private router: Router,
    public element: ElementRef
  ) {
    if (!this.router.getCurrentNavigation().extras.state) {
      this.router.navigate(['networkDevices']);
      return;
    }

    this.config = this.router.getCurrentNavigation().extras.state.config;
    this.deviceName = this.router.getCurrentNavigation().extras.state.deviceName;
  }

  copyToClipboard() {
    if (navigator.clipboard) {
      navigator.clipboard.writeText(this.config);
      return;
    }

    //Fallback behaviour if the clipboard API is not available.
    const generatedConfig = this.element.nativeElement.children.namedItem('generated-config');
    if (generatedConfig) {
      try {
        generatedConfig.focus();
        generatedConfig.select();
        document.execCommand('copy');
      } catch (error) {
        return;
      }
    }
  }
}
