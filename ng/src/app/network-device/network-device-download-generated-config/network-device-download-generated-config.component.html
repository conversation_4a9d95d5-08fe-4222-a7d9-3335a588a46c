<div class="row">
    <div class="col">
        <h1 class="overflow-ellipsis">Network Device {{ deviceName }}</h1>
    </div>
</div>
<div class="copy-button-container">
    <hr />
    <button pButton type="button" (click)="copyToClipboard()" label="Reset" icon="pi pi-refresh" class="p-button text-white p-button-primary p-button-sm copy-button">Copy</button>
</div>
<textarea [rows]="config.split('\n').length + 5" id="generated-config" readonly>{{ config }}</textarea>
