import { AlertService } from 'src/app/services/alert.service';
import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-network-device-remove-modal',
  templateUrl: './network-device-remove-modal.component.html',
  styleUrls: ['./network-device-remove-modal.component.css'],
})
export class NetworkDeviceRemoveModalComponent implements OnInit {
  networkDevice: any;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.networkDevice = this.config.data.networkDevice;
  }

  onNetworkDeviceRemove() {
    this.isSubmitting = true;
    this.alertService.clear();
    this.httpClient.delete(`/_/internal/nseapi/v2/networkDevices/${this.networkDevice.name}`, {}).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;
        this.alertService.alert(
          {
            type: 'success',
            description: $localize`:@@bma_networkdevice_successremove:Successfully removed network device ${this.networkDevice.name}`,
          },
          true
        );
        this.closeModal({ networkDeviceRemoved: true });
      },
      error: (error: any) => {
        this.isSubmitting = false;
        this.alertService.alertApiError(error.error, true);
        this.closeModal({ networkDeviceRemoved: false });
      },
    });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
