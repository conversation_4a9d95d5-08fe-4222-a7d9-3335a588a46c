import { AlertService } from 'src/app/services/alert.service';
import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
@Component({
  selector: 'app-network-device-attribute-add-modal',
  templateUrl: './network-device-attribute-add-modal.component.html',
  styleUrls: ['./network-device-attribute-add-modal.component.css'],
})
export class NetworkDeviceAttributeAddModalComponent implements OnInit {
  networkDevice: any;

  form: UntypedFormGroup;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    private readonly formBuilder: UntypedFormBuilder,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.networkDevice = this.config.data.networkDevice;
    this.form = this.formBuilder.group({
      key: [null, Validators.required],
      value: [null, Validators.required],
    });
  }

  onAttributeCreate() {
    this.alertService.clear();
    if (this.form.valid) {
      const params: any = {};
      params.key = this.form.get('key').value;
      params.value = this.form.get('value').value;
      this.isSubmitting = true;

      this.httpClient
        .post(`/_/internal/nseapi/v2/networkDevices/${this.networkDevice.name}/attributes`, params)
        .subscribe({
          next: (data: any) => {
            this.isSubmitting = false;
            this.closeModal({ networkDeviceAttributeAdded: true });
            this.alertService.alert(
              {
                type: 'success',
                description: $localize`:@@bma_networkdeviceaddattribute_success:Successfully created attribute '${data.key}' on network device '${this.networkDevice.name}'`,
              },
              true
            );
          },
          error: (error: any) => {
            this.isSubmitting = false;
            if (error.error.errorDetails) {
              Object.entries(error.error.errorDetails).forEach(([key, value]) => {
                this.form.get(key).setErrors({ server: value[0] });
              });
            }
            this.closeModal({ networkDeviceAttributeAdded: false });
            this.alertService.alertApiError(error.error, true);
          },
        });
    } else {
      this.alertService.alert(
        {
          type: 'error',
          description: $localize`:@@bma_networkdeviceaddattribute_failed:Failed to create attribute`,
        },
        true
      );
    }
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
