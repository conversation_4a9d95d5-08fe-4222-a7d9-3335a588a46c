<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '800px'}">
    <ng-template pTemplate="header">
        <h3 class="modal-title" i18n="@@bma_networkdeviceattribute_create">Create New Attribute</h3>
    </ng-template>
    <form [formGroup]="form" (ngSubmit)="onAttributeCreate()">
        <div class="modal-body">
            <div class="form-group">
                <label for="key" i18n="@@bma_common_key" class="col-sm-3 control-label">Key</label>
                <div class="col-12">
                    <input type="text" class="form-control" id="key" formControlName="key" required="required" autofocus />
                    <span class="text-danger" *ngIf="form.get('key').errors && form.get('key').errors.server">{{ form.get('key').errors.server }}<br /></span>
                    <small class="text-muted font-italic"></small>
                </div>
            </div>
            <div class="form-group">
                <label for="value" i18n="@@bma_common_value" class="col-sm-3 control-label">Value</label>
                <div class="col-12">
                    <input type="text" class="form-control" id="value" formControlName="value" required="required" />
                    <span class="text-danger" *ngIf="form.get('value').errors && form.get('value').errors.server">{{ form.get('value').errors.server }}<br /></span>
                    <small class="text-muted font-italic"></small>
                </div>
            </div>
        </div>
    </form>
    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button pButton type="button" icon="pi pi-times" iconPos="left" i18n-label="@@bma_common_cancel" label="Cancel" class="p-button-secondary" (click)="closeModal()"></button>
            <button pButton [loading]="isSubmitting" icon="pi pi-check" iconPos="left" (click)="onAttributeCreate()" i18n-label="@@bma_common_create" label="Create"></button>
        </div>
    </ng-template>
</p-dialog>
