import { Component, Input, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Title } from '@angular/platform-browser';
import { AlertService } from 'src/app/services/alert.service';

@Component({
  selector: 'app-network-device-lldp',
  templateUrl: './network-device-lldp.component.html',
  styleUrls: ['./network-device-lldp.component.css'],
})
export class NetworkDeviceLldpComponent implements OnInit {
  @Input() networkDevice: any;
  networkDeviceLlldpInformation: any;
  isLoading = false;

  constructor(
    private alertService: AlertService,
    private httpClient: HttpClient,
    private titleService: Title
  ) {}

  ngOnInit(): void {
    this.isLoading = true;
    this.titleService.setTitle(`Network Device ${this.networkDevice.name} | Leaseweb`);
    if (this.networkDevice) {
      this.httpClient.get<any>(`/_/internal/nseapi/v2/networkDevices/${this.networkDevice.name}/queryLldp`).subscribe({
        next: (networkDeviceLlldpInformation: any) => {
          if (networkDeviceLlldpInformation) {
            this.isLoading = false;
            this.networkDeviceLlldpInformation = networkDeviceLlldpInformation;
          }
        },
        error: (error: any) => {
          this.isLoading = false;
          this.alertService.alertApiError(error.error, true);
        },
      });
    }
  }
}
