<app-loader *ngIf="!networkDevice || isLoading"></app-loader>
<div class="row mb-4" *ngIf="networkDevice">
    <div class="col-12">
        <table *ngFor="let lldpInfo of networkDeviceLlldpInformation | keyvalue" class="table table-sm mt-5">
            <colgroup>
                <col class="table-col-20" />
                <col class="table-col-80" />
            </colgroup>
            <tbody>
                <tr>
                    <td><span i18n="@@bma_common_chassisid">Chassis ID</span>:</td>
                    <td>{{ lldpInfo.value.remoteChassisId ? lldpInfo.value.remoteChassisId : '-' }}</td>
                </tr>
                <tr>
                    <td><span i18n="@@bma_common_interfaceid">Interface ID</span>:</td>
                    <td>{{ lldpInfo.value.remoteIfId ? lldpInfo.value.remoteIfId : '-' }}</td>
                </tr>
                <tr>
                    <td><span i18n="@@bma_common_interfacetype">Interface Type</span>:</td>
                    <td>{{ lldpInfo.value.remoteIfIdType.name ? lldpInfo.value.remoteIfIdType.name : "-"}}</td>
                </tr>
                <tr>
                    <td><span i18n="@@bma_common_interfacedesc">Interface Desc</span>:</td>
                    <td>{{ lldpInfo.value.remoteIfDescription ? lldpInfo.value.remoteIfDescription : '-' }}</td>
                </tr>
                <tr>
                    <td><span i18n="@@bma_common_systemname">System Name</span>:</td>
                    <td>
                        <a [routerLink]="['/networkDevices', lldpInfo.value.remoteSystemName]">{{ lldpInfo.value.remoteSystemName ? lldpInfo.value.remoteSystemName  : '-'}}</a>
                    </td>
                </tr>
                <tr>
                    <td><span i18n="@@bma_common_description">Description</span>:</td>
                    <td>{{ lldpInfo.value.remoteDescription ? lldpInfo.value.remoteDescription : '-' }}</td>
                </tr>
                <tr>
                    <td><span i18n="@@bma_common_localinterface">Local Interface</span>:</td>
                    <td>{{ lldpInfo.value.localIfDescription ? lldpInfo.value.localIfDescription : '-' }}</td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="col-12" *ngIf="!isLoading && !networkDeviceLlldpInformation" i18n="@@bma_networkdevice_lldpinfonotavailable">Could not get LLDP information for this network device.</div>
</div>
