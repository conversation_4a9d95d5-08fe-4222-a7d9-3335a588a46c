<div class="row mb-4">
    <h3 i18n="@@bma_common_networkinferfaces">Network Interfaces</h3>
    <br />

    <div class="col-12 mb-2 mt-2 pl-0" i18n="@@bma_networkdevice_datafromnseapialert">The data you see on this page comes from the <i>NSE-API</i> database, and may be outdated.<br /></div>
    <table class="table table-striped table-sm network-interfaces-list">
        <thead>
            <tr>
                <th class="text-center table-col-size-1" scope="col" i18n="@@bma_common_port">Port</th>
                <th class="text-center table-col-size-1" scope="col" i18n="@@bma_common_ifindex">If Index</th>
                <th class="text-center table-col-size-1" scope="col" i18n="@@bma_common_ifdescription">If Description</th>
                <th class="text-center table-col-size-1" scope="col" i18n="@@bma_common_type">Type</th>
                <th class="text-center table-col-size-1" scope="col" i18n="@@bma_common_linkspeed">Link Speed</th>
                <th class="text-center table-col-size-1" scope="col" i18n="@@bma_common_status">Status</th>
                <th class="text-center table-col-size-3" scope="col" i18n="@@bma_common_connectedinterface">Connected Interface</th>
                <th class="text-center table-col-size-2" scope="col" i18n="@@bma_common_macaddresses">MAC Addresses</th>
                <th class="text-center table-col-size-1" scope="col" i18n="@@bma_common_errorcounters">Error Counters</th>
            </tr>
        </thead>

        <tbody>
            <tr *ngFor="let interface of networkDevice['interfaces']" [class.table-info]="interface.type === 'UPLINK'">
                <td class="text-center">
                    {{ interface.portId }}
                </td>

                <td class="text-center">
                    {{ interface.ifindex|default: '-' }}
                </td>

                <td class="text-center">
                    {{ interface.description|default: '-' }}
                </td>

                <td class="text-center">
                    {{ interface.type|default: '-' }}
                </td>

                <td class="text-center" *ngIf="interface.linkSpeed;else notAvailable">
                    <a href="javascript:void(0);" [ngClass]="{'disabled' : !currentUserService.hasPermission('networkdevice_ports_speed')}" (click)="openManagePortSpeedModal(interface.portId)" *ngIf="interface.type === 'NORMAL'" class="text-nowrap">
                        {{ (interface.linkSpeed >= 1000 ? interface.linkSpeed / 1000 : interface.linkSpeed) }}
                        {{
                    (interface.linkSpeed >= 1000 ? 'gbps' : 'mbps')}}
                    </a>
                    <span class="text-nowrap" *ngIf="interface.type !== 'NORMAL'">
                        {{ (interface.linkSpeed >= 1000 ? interface.linkSpeed / 1000 : interface.linkSpeed) }}
                        {{ (interface.linkSpeed >= 1000 ? 'gbps' : 'mbps') }}
                    </span>
                </td>
                <ng-template #notAvailable>
                    <td class="text-center"><span class="text-muted">-</span></td>
                </ng-template>

                <td class="text-center switchport_status">
                    <a href="javascript:void(0);" *ngIf="!isLoadingPortStatus[interface.portId] && interface.type === 'NORMAL' && !showButton[interface.portId]" (click)="getPortStatus(interface.portId)" class="switchport_status_link" i18n="@@bma_common_show">Show</a>

                    <app-loader inline="true" *ngIf="isLoadingPortStatus[interface.portId]"></app-loader>

                    <ng-container *ngIf="!isLoadingPortStatus[interface.portId] && showButton[interface.portId]">
                        <div class="text-nowrap md-0 btn-group btn-group-toggle" *ngIf="showButton[interface.portId] !== 'Error'" role="group" aria-label="Port Status">
                            <span class="btn-cap-network-interface btn btn-sm btn-primary active" role="button" *ngIf="showButton[interface.portId] && showButton[interface.portId] === 'UP'" i18n="@@bma_common_open">OPEN</span>
                            <a href="javascript:void(0);" *ngIf="showButton[interface.portId] && showButton[interface.portId] === 'UP'" (click)="openPortActionModal(interface.portId, 'close')" [ngClass]="{'disabled' : !currentUserService.hasPermission('networkdevice_ports')}" class="btn-cap-network-interface btn btn-sm btn-outline-danger" i18n="@@bma_common_close">Close</a>

                            <a href="javascript:void(0);" *ngIf="showButton[interface.portId] && showButton[interface.portId] === 'DOWN'" (click)="openPortActionModal(interface.portId, 'open')" [ngClass]="{'disabled' : !currentUserService.hasPermission('networkdevice_ports')}" class="btn-cap-network-interface btn btn-sm btn-outline-primary" i18n="@@bma_common_open">OPEN</a>
                            <span class="btn-cap-network-interface btn btn-sm btn-danger active" role="button" *ngIf="showButton[interface.portId] && showButton[interface.portId] === 'DOWN'" i18n="@@bma_common_close">Close</span>
                        </div>

                        <span class="btn-cap-network-interface btn btn-outline-warning btn-sm disabled" role="button" *ngIf="showButton[interface.portId] === 'Error'" i18n="@@bma_common_error">error</span>
                    </ng-container>
                </td>

                <td class="text-center">
                    <a [routerLink]="['/networkDevices', interface.connectedNetworkDevice.deviceName]" *ngIf="interface.connectedNetworkDevice">
                        {{ interface.connectedNetworkDevice.deviceName }}
                        {{ interface.connectedNetworkDevice.interfaceDescription }}
                    </a>
                    <a [routerLink]="[interface.connectedPrivateNetworkEquipment.equipmentUrl, interface.connectedPrivateNetworkEquipment.equipmentId]" *ngIf="interface.connectedPrivateNetworkEquipment"> Dedicated Server - {{ interface.connectedPrivateNetworkEquipment.equipmentId }} </a>
                </td>

                <td class="text-center">
                    <ul class="list-unstyled mb-0">
                        <li *ngFor="let mac of interface.macAddresses|default: []">
                            <a href (click)="openServerInfoPopupModal(mac); false">{{ mac }}</a>
                        </li>
                    </ul>
                </td>

                <td class="text-right pr-3">
                    {{ interface.errorCounters|default: '-' }}
                </td>
            </tr>
            <tr *ngIf="!networkDevice['interfaces'].length">
                <td colspan="9" class="text-center p-5 h5" i18n="@@bma_networkdevice_nointerfacescachedindatabase">
                    No network interfaces are cached in the database.
                    <i>This is required for automation to work.</i><br />

                    If you just added this network device, try refreshing the page in a couple of minutes.<br />

                    Contact the network automation team if you expected network interfaces to be here.
                </td>
            </tr>
        </tbody>
    </table>
</div>
