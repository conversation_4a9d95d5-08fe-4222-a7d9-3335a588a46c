import { Component, Input } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ModalService } from 'src/app/services/modal.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { ServerInfoPopupModalComponent } from 'src/app/server/server-info-popup-modal/server-info-popup-modal.component';
import { NetworkDevicePortActionModalComponent } from 'src/app/network-device/network-device-port-action-modal/network-device-port-action-modal.component';
import { NetworkDeviceManageSpeedModalComponent } from 'src/app/network-device/network-device-manage-speed-modal/network-device-manage-speed-modal.component';

@Component({
  selector: 'app-network-device-network-interfaces',
  templateUrl: './network-device-network-interfaces.component.html',
  styleUrls: ['./network-device-network-interfaces.component.css'],
})
export class NetworkDeviceNetworkInterfacesComponent {
  @Input() networkDevice: any;

  isSubmitting = false;
  isLoadingPortStatus = [];
  showButton = [];
  dynamicDialogRef: DynamicDialogRef;

  constructor(
    private httpClient: HttpClient,
    private modalService: ModalService,
    private currentUserService: CurrentUserService
  ) {}

  openServerInfoPopupModal(macAddress: string) {
    this.modalService.show(ServerInfoPopupModalComponent, { macAddress });
  }

  getPortStatus(port): void {
    this.isLoadingPortStatus[port] = true;
    this.httpClient
      .get(`/_/internal/nseapi/v2/networkDevices/${this.networkDevice.name}/ports/${port}/queryStatistics`)
      .subscribe(
        (portStatus: any) => {
          this.isLoadingPortStatus[port] = false;
          if (!portStatus) {
            this.showButton[port] = 'Error';
            return;
          }
          if (portStatus.adminstatus.label === 'up') {
            this.showButton[port] = 'UP';
            return;
          }
          this.showButton[port] = 'DOWN';
        },
        (error: any) => {
          this.isLoadingPortStatus[port] = false;
          this.showButton[port] = 'Error';
        }
      );
  }

  openPortActionModal(port: any, action: any) {
    this.dynamicDialogRef = this.modalService.show(NetworkDevicePortActionModalComponent, {
      networkDeviceName: this.networkDevice.name,
      port,
      action,
    });

    this.dynamicDialogRef.onClose.subscribe((event: any) => {
      if (event?.portStatusUpdated === true) {
        if (action === 'close') {
          this.showButton[port] = 'DOWN';
        } else if (action === 'open') {
          this.showButton[port] = 'UP';
        }
      } else if (event?.portStatusUpdated === false) {
        this.showButton[port] = 'Error';
      }
    });
  }

  openManagePortSpeedModal(port: any) {
    this.dynamicDialogRef = this.modalService.show(NetworkDeviceManageSpeedModalComponent, {
      networkDeviceName: this.networkDevice.name,
      port,
    });

    this.dynamicDialogRef.onClose.subscribe((data) => {
      if (data?.portSpeedUpdated === true && data?.linkSpeed && this.networkDevice.interfaces) {
        for (const key in this.networkDevice.interfaces) {
          if (this.networkDevice.interfaces[key].portId === port) {
            this.networkDevice.interfaces[key].linkSpeed = data?.linkSpeed;
          }
        }
      }
    });
  }
}
