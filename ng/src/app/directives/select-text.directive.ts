import { DOCUMENT } from '@angular/common';
import { Directive, ElementRef, HostListener, Inject } from '@angular/core';

/*eslint-disable */
@Directive({
  selector: '.selectable',
})
/*eslint-enable */
export class SelectTextDirective {
  constructor(
    @Inject(DOCUMENT) private document: Document,
    private element: ElementRef
  ) {}

  @HostListener('dblclick') onClick() {
    this.selectText(this.element.nativeElement);
  }

  selectText(element) {
    const el = element;
    const selection = window.getSelection();
    const range = this.document.createRange();
    range.selectNodeContents(el);
    selection.removeAllRanges();
    selection.addRange(range);
  }
}
