import { Directive, OnInit, TemplateRef, ViewContainerRef } from '@angular/core';
import { CurrentUserService } from 'src/app/services/current-user.service';

@Directive({
  selector: '[appIsCustomer]',
})
export class IsCustomerDirective implements OnInit {
  constructor(
    private currentUserService: CurrentUserService,
    private tRef: TemplateRef<any>,
    private vcRef: ViewContainerRef
  ) {}

  ngOnInit(): void {
    if (this.currentUserService.isCustomer()) {
      this.vcRef.createEmbeddedView(this.tRef);
    } else {
      this.vcRef.clear();
    }
  }
}
