<app-customer-aware-header [caption]="'Disk ' + disk.serial" [isEmployee]="isEmployee" [reference]="disk.device_model" *ngIf="disk"> </app-customer-aware-header>

<div class="row">
    <div class="col">
        <h3>Details</h3>
        <app-loader *ngIf="isLoadingDetails"></app-loader>
        <p-table [scrollable]="true" [value]="[disk]" *ngIf="!isLoadingDetails && disk">
            <ng-template pTemplate="body">
                <tr>
                    <td i18n="@@bma_common_serial">Serial</td>
                    <td>
                        <span class="selectable">
                            {{ disk.serial }}
                        </span>
                    </td>
                </tr>
                <tr>
                    <td i18n="@@bma_common_modelfamily">Model family</td>
                    <td>
                        {{ disk.model_family }}
                    </td>
                </tr>
                <tr>
                    <td i18n="@@bma_common_manufacturingpartnumber">Manufacturing part number</td>
                    <td>
                        {{ disk.device_model }}
                    </td>
                </tr>
                <tr>
                    <td i18n="@@bma_common_size">Size</td>
                    <td>
                        {{ disk.size }}
                    </td>
                </tr>
                <tr>
                    <td i18n="@@bma_common_type">Type</td>
                    <td>
                        <span>
                            {{ disk.type }}
                        </span>
                    </td>
                </tr>
                <tr>
                    <td i18n="@@bma_common_connection">Connection</td>
                    <td>
                        <span>
                            {{ disk.connection }}
                        </span>
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>
<div class="row mt-4">
    <div class="col">
        <h3>History</h3>
        <app-loader *ngIf="isLoadingHistory"></app-loader>
        <div *ngIf="!isLoadingHistory && !errorHistory; else noData">
            <p-accordion class="w-full" [multiple]="true" *ngIf="scans.scans.length > 0; else noData">
                <p-accordionTab *ngFor="let scan of scans.scans">
                    <ng-template pTemplate="header">
                        <div class="row col-sm-12 align-items-center">
                            <div class="col">
                                <small class="text-muted" i18n="@@bma_common_source"> Source </small>
                                {{ this.getSource(scan.source) }}
                            </div>
                            <div class="col">
                                <small class="text-muted" i18n="@@bma_common_job">Job</small>
                                {{ this.getJobNames(scan.source) }}
                            </div>
                            <div class="col">
                                <small class="ml-auto text-muted" i18n="@@bma_common_serverid">Server ID</small>
                                <a [routerLink]="['/']" [queryParams]="{equipmentId: scan.data.server_id}">
                                    {{ scan.data.server_id }}
                                </a>
                            </div>
                            <div class="col">
                                <small class="ml-auto text-muted" i18n="@@bma_common_createdat">Created at</small>
                                {{ scan.createdAt | date:'longDate' }}
                            </div>
                        </div>
                    </ng-template>
                    <ng-template pTemplate="content" class="p-p-0">
                        <div class="p-grid">
                            <div class="p-col-12 p-md-10 border-right">
                                <div class="p-col-12 p-d-flex p-flex-col ">
                                    <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-2">
                                        <div class="p-grid">
                                            <div class="p-col-4 grid-content"><strong i18n="@@bma_common_overallhealth">Overall health</strong></div>
                                            <div class="p-col-8 grid-content">
                                                {{ scan.data.smartctl.overall_health ?? '-' }}
                                            </div>
                                        </div>
                                        <div class="p-grid">
                                            <div class="p-col-4 grid-content"><strong i18n="@@bma_common_diskbay">Diskbay</strong></div>
                                            <div class="p-col-8 grid-content">
                                                {{ scan.data.diskbay ?? '-' }}
                                            </div>
                                        </div>
                                        <div class="p-grid">
                                            <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_colocationlist_firmware">Firmware</strong></div>
                                            <div class="p-col-8 grid-content grid-content-border">
                                                {{ scan.data.firmware ?? '-' }}
                                            </div>
                                        </div>
                                        <div class="p-grid">
                                            <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_colocationlist_logicaldrive">Logical drive</strong></div>
                                            <div class="p-col-8 grid-content grid-content-border">
                                                {{ scan.data.logicaldrive ?? '-' }}
                                            </div>
                                        </div>
                                        <div class="p-grid">
                                            <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_raidarray">Raid array</strong></div>
                                            <div class="p-col-8 grid-content grid-content-border">
                                                {{ scan.data.raid_array ?? '-' }}
                                            </div>
                                        </div>
                                        <div class="p-grid">
                                            <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_diskdetails_raidlevel">Raid Level</strong></div>
                                            <div class="p-col-8 grid-content grid-content-border">
                                                {{ scan.data.raid_level ?? '-' }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-2">
                                        <div class="p-grid">
                                            <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_minlatency">Min latency</strong></div>
                                            <div class="p-col-8 grid-content grid-content-border">
                                                {{ scan.data.tests.latency.min ?? '-' }}
                                            </div>
                                        </div>
                                        <div class="p-grid">
                                            <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_maxlatency">Max latency</strong></div>
                                            <div class="p-col-8 grid-content grid-content-border">
                                                {{ scan.data.tests.latency.max ?? '-' }}
                                            </div>
                                        </div>
                                        <div class="p-grid">
                                            <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_avglatency">Avg latency</strong></div>
                                            <div class="p-col-8 grid-content grid-content-border">
                                                {{ scan.data.tests.latency.avg ?? '-' }}
                                            </div>
                                        </div>
                                        <div class="p-grid">
                                            <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_read">Read</strong></div>
                                            <div class="p-col-8 grid-content grid-content-border">
                                                {{ scan.data.tests.read ?? '-' }}
                                            </div>
                                        </div>
                                        <div class="p-grid">
                                            <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_write">Write</strong></div>
                                            <div class="p-col-8 grid-content grid-content-border">
                                                {{ scan.data.tests.write ?? '-' }}
                                            </div>
                                        </div>
                                        <div class="p-grid">
                                            <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_rpm">RPM</strong></div>
                                            <div class="p-col-8 grid-content grid-content-border">
                                                {{ scan.data.rpm ?? '-' }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="p-col-12 p-md-2 quick-actions">
                                <h5 class="p-2" i18n="@@bma_common_quickactions">Quick Actions</h5>
                                <div class="p-grid p-flex-column align-items-start">
                                    <button i18n-label="@@bma_common_smartctlattributes" pButton label="Smartctl attributes" class="p-button-link" (click)="openScanSmartctlAttributesModal(scan.data.smartctl.attributes)"></button>
                                </div>
                            </div>
                        </div>
                    </ng-template>
                </p-accordionTab>
            </p-accordion>
        </div>
    </div>
</div>
<div *ngIf="scans">
    <app-pagination [totalCount]="scans._metadata.totalCount" [limit]="scans._metadata.limit" [offset]="scans._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
</div>
<ng-template #notAvailable>
    <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
</ng-template>
<ng-template #noData>
    <div class="align-middle bg-white p-mb-2 p-py-4">
        <span i18n="@@bma_common_nodatafound" *ngIf="!errorHistory">No data found</span>
        <span i18n="@@bma_common_errorfetchingdata" class="text-danger" *ngIf="errorHistory">Error fetching the data</span>
    </div>
</ng-template>
