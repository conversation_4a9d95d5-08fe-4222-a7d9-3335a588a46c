import { Component, OnInit, Input } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { ModalService } from 'src/app/services/modal.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { DiskJobSmartctlAttributesModalComponent } from '../disk-job-smartctl-attributes-modal/disk-job-smartctl-attributes-modal.component';

@Component({
  selector: 'app-disk-details',
  templateUrl: './disk-details.component.html',
  styleUrls: ['./disk-details.component.css'],
})
export class DiskDetailsComponent implements OnInit {
  @Input() isEmployee: boolean;
  isLoadingDetails = false;
  isLoadingHistory = false;
  errorHistory = false;
  serial = null;
  disk = null;
  scans = null;
  dynamicDialogRef: DynamicDialogRef;
  filters = {
    offset: 0,
    limit: 20,
  };

  constructor(
    private activatedRoute: ActivatedRoute,
    private httpClient: HttpClient,
    private router: Router,
    private currentUserService: CurrentUserService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();

    this.activatedRoute.url.subscribe((parameters) => this.onUrlParamsChanged(parameters));
  }

  onUrlParamsChanged(urlParams): void {
    this.serial = urlParams[1].path;
    this.isLoadingDetails = true;

    this.httpClient.get<any>(`/_/internal/bmsdb/v2/hardDisks/${this.serial}`).subscribe({
      next: (data: any) => {
        if (data.hardDisks.length === 0) {
          this.redirectToLookup();
        }

        this.disk = data.hardDisks;
        this.isLoadingDetails = false;
      },
      error: (err: any) => {
        this.redirectToLookup();
      },
    });

    this.getHardDiskScans();
  }

  getHardDiskScans(): void {
    let params = new HttpParams();
    params = params.set('limit', this.filters.limit);
    params = params.set('offset', this.filters.offset);

    this.isLoadingHistory = true;

    this.httpClient.get<any>(`/_/internal/bmsdb/v2/hardDisks/${this.serial}/scans`, { params }).subscribe({
      next: (data: any) => {
        this.scans = data;
        this.isLoadingHistory = false;
        this.errorHistory = false;
      },
      error: (error: any) => {
        this.isLoadingHistory = false;
        this.errorHistory = true;
      },
    });
  }

  redirectToLookup(): void {
    this.router.navigate(['/'], {
      queryParams: {
        error: 404,
        type: 'diskSerial',
        search: this.serial,
      },
    });
  }

  onPageChange(event): void {
    this.filters = { offset: event.offset, limit: event.limit };
    this.getHardDiskScans();
  }

  getSource(source: string): string {
    const splitedSource = source.split('.');
    return splitedSource[0].toUpperCase();
  }

  getJobNames(source: string): string {
    const splitedSource = source.split('.');
    const formatedNames = [];

    splitedSource.shift();
    splitedSource.forEach((text) => formatedNames.push(this.formatJobNames(text)));

    return formatedNames.toString().replace(/,/, ' > ');
  }

  formatJobNames(name: string): string {
    return name
      .replace(/([A-Z])/g, (match) => ' ' + match)
      .replace(/^./, (match) => match.toUpperCase())
      .replace(/\-/g, ' ');
  }

  openScanSmartctlAttributesModal(attributes: any) {
    this.dynamicDialogRef = this.modalService.show(DiskJobSmartctlAttributesModalComponent, {
      attributes,
    });
  }

  sanitize(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
