<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_smartctlattributesmodal_title" class="modal-title">Smartctl Attributes</h3>
    </ng-template>
    <p-table [scrollable]="true" [value]="attributes">
        <ng-template pTemplate="header">
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Flag</th>
                <th>Value</th>
                <th>Worst</th>
                <th>Thresh</th>
                <th>Type</th>
                <th>Updated</th>
                <th>When Failed</th>
                <th>Raw Value</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-attribute>
            <tr>
                <td class="white-space-nowrap">{{ attribute.id }}</td>
                <td class="white-space-nowrap">{{ attribute.name }}</td>
                <td class="white-space-nowrap">{{ attribute.flag }}</td>
                <td class="white-space-nowrap">{{ attribute.value }}</td>
                <td class="white-space-nowrap">{{ attribute.worst }}</td>
                <td class="white-space-nowrap">{{ attribute.thresh }}</td>
                <td class="white-space-nowrap">{{ attribute.type }}</td>
                <td class="white-space-nowrap">{{ attribute.updated }}</td>
                <td class="white-space-nowrap">{{ attribute.when_failed }}</td>
                <td class="white-space-nowrap">{{ attribute.raw_value }}</td>
            </tr>
        </ng-template>
    </p-table>
    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button pButton i18n-label="@@bma_common_close" type="button" icon="pi pi-times" iconPos="left" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
        </div>
    </ng-template>
</p-dialog>
