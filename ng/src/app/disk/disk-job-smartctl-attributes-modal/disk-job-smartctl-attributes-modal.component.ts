import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-disk-job-smartctl-attributes-modal',
  templateUrl: './disk-job-smartctl-attributes-modal.component.html',
  styleUrls: ['./disk-job-smartctl-attributes-modal.component.css'],
})
export class DiskJobSmartctlAttributesModalComponent implements OnInit {
  attributes = [];
  showDialog = true;

  constructor(
    private dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    const attributes = this.dynamicDialogConfig.data.attributes;

    for (const key in attributes) {
      if (!key) {
        continue;
      }

      attributes[key].name = key;
      this.attributes.push(attributes[key]);
    }
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }
}
