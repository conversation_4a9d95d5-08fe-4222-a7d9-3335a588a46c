<app-loader *ngIf="isLoading"></app-loader>

<div class="dedicated-network-equipment-alerts">
    <div *ngIf="isEmployee && (missingInformation|default:'') && (missingInformation|default:'').length > 0" id="dedicated-network-equipment-missing-information-alert" class="alert alert-warning" role="alert">
        <p><span class="fa fa-alert"></span> <strong i18n="@@bma_dedicatedstoragetabs_missinginformation">Dedicated Storage Missing Information</strong></p>
        <ul>
            <li *ngFor="let message of missingInformation">{{ message }}</li>
        </ul>
    </div>
</div>

<app-customer-aware-header *ngIf="dedicatedStorage" [caption]="'Dedicated Storage ' + dedicatedStorage.id" [reference]="dedicatedStorage.contract?.reference" [customerId]="dedicatedStorage.contract?.customerId" [salesOrgId]="dedicatedStorage.contract?.salesOrgId" [isEmployee]="isEmployee"></app-customer-aware-header>

<ul class="nav nav-pills mb-4 powerbar-navigation" *ngIf="dedicatedStorage">
    <li class="nav-item dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-details"></span>&nbsp;<span i18n="@@bma_common_details">Details</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a [routerLink]="['.']" class="dropdown-item">
                <span class="text-muted fa fa-details mr-2"></span>
                <span i18n="@@bma_common_equipmentdetails">Equipment Details</span>
            </a>
            <a [routerLink]="['network']" class="dropdown-item">
                <span class="text-muted fa fa-transfer mr-2"></span>
                <span i18n="@@bma_common_networkdetails">Network Details</span>
            </a>
            <a [routerLink]="['ips']" class="dropdown-item">
                <span class="text-muted fa fa-ip mr-2"></span>
                <span i18n="@@bma_common_ipaddresses">IP Addresses</span>
            </a>
            <a [routerLink]="['credentials']" class="dropdown-item">
                <span class="text-muted fa fa-lock mr-2"></span>
                <span i18n="@@bma_common_credentials">Credentials</span>
            </a>
        </div>
    </li>
    <li class="nav-item dropdown">
        <a href="#" class="nav-link dropdown-toggle disabled" data-toggle="dropdown">
            <span class="text-muted fa fa-usage"></span>&nbsp; <span i18n="@@bma_common_usage">Usage</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a [routerLink]="['graphs']" class="dropdown-item">
                <span class="text-muted fa fa-usage"></span>
                <span i18n="@@bma_common_graphs">Graphs</span>
            </a>
        </div>
    </li>
    <li class="nav-item navbar-right dropdown">
        <a href="#" class="nav-link dropdown-toggle disabled" data-toggle="dropdown">
            <span class="text-muted fa fa-tasks"></span>&nbsp;<span i18n="@@bma_common_activities">Activities</span>
            <span class="caret"></span>
        </a>
    </li>
    <li class="nav-item">
        <a href="#" class="nav-link disabled"> <span class="text-muted fa fa-privatenetwork"></span>&nbsp; <span i18n="@@bma_common_privatenetwork">Private Network</span> </a>
    </li>
    <li class="nav-item navbar-right dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-administration"></span>&nbsp;<span i18n="@@bma_common_actions">Actions</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a href="javascript:void(0);" class="dropdown-item disabled" (click)="openRemoteManagementModal(dedicatedStorageId)">
                <span class="text-muted fa fa-remotemanagement mr-2"></span>
                <span i18n="@@bma_common_remotemanagement">Remote Management</span>
            </a>
            <a href="javascript:void(0);" class="dropdown-item disabled" (click)="openPowerOperationModal()">
                <span class="text-muted fa fa-powercycle mr-2"></span>
                <span i18n="@@bma_common_poweroperation">Power Operation</span>
            </a>
        </div>
    </li>
</ul>

<ng-container *ngIf="dedicatedStorage && !isLoading">
    <router-outlet (activate)="onDedicatedStorageLoaded($event)"></router-outlet>
</ng-container>
