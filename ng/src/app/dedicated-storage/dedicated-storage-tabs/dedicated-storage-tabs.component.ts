import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { HttpClient } from '@angular/common/http';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { SiteService } from 'src/app/services/site.service';

@Component({
  selector: 'app-dedicated-storage-tabs',
  templateUrl: './dedicated-storage-tabs.component.html',
  styleUrls: ['./dedicated-storage-tabs.component.css'],
})
export class DedicatedStorageTabsComponent implements OnInit {
  isLoading = false;
  dedicatedStorage: any;
  dedicatedStorageId: string;
  isEmployee = false;
  missingInformation: any;
  ips: any;
  dynamicDialogRef: DynamicDialogRef;

  constructor(
    private activatedRoute: ActivatedRoute,
    private titleService: Title,
    private currentUserService: CurrentUserService,
    private httpClient: HttpClient,
    private modalService: ModalService,
    private router: Router,
    private siteService: SiteService
  ) {}

  ngOnInit(): void {
    this.dedicatedStorageId = this.activatedRoute.snapshot.url[1].path;
    this.isEmployee = this.currentUserService.isEmployee();

    this.isLoading = true;

    this.httpClient
      .get<any>(`/_/internal/dedicatedserverapi/v2/dedicatedStorages/${this.dedicatedStorageId}`)
      .subscribe({
        next: (data: any) => {
          this.isLoading = false;
          this.dedicatedStorage = data;
          this.titleService.setTitle(
            `Dedicated Storage ${this.dedicatedStorage.id} ${
              this.dedicatedStorage.contract?.reference ? this.dedicatedStorage.contract.reference : ''
            }| Leaseweb`
          );
          /*eslint-disable */
          const event = new Event('update_product_navbar');
          event['customerId'] = this.dedicatedStorage.contract?.customerId;
          event['salesOrgId'] = this.dedicatedStorage.contract?.salesOrgId;
          event['country'] = this.siteService.getCountry(this.dedicatedStorage.contract?.salesOrgId);
          window.dispatchEvent(event);
          /*eslint-enable */
        },
        error: (error: any) => {
          this.isLoading = false;
          this.router.navigate(['**']);
        },
      });
  }

  onDedicatedStorageLoaded(component) {
    if (this.dedicatedStorage) {
      component.equipment = this.dedicatedStorage;
      component.dedicatedStorage = this.dedicatedStorage;
      component.equipmentId = this.dedicatedStorage.id;
      component.equipmentType = 'dedicatedStorages';
    }
  }
}
