import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-dedicated-storage-network-details',
  templateUrl: './dedicated-storage-network-details.component.html',
  styleUrls: ['./dedicated-storage-network-details.component.css'],
})
export class DedicatedStorageNetworkDetailsComponent {
  @Input() equipment: any;
  @Input() errorInformation: string[];
  equipmentType = 'dedicatedStorages';
  networkInterfaceOrder = ['public', 'internal', 'remoteManagement'];
}
