.bmp-task-list THEAD,
.bmp-task-list TFOOT {
  background-color: #efefef;
}
.bmp-task-list TBODY TD,
.bmp-task-list TBODY TH {
  line-height: 2em !important;
  min-height: 2em !important;
}
.bmp-task-list TBODY TH {
  color: black;
  background-color: #eeeeee80;
  border-bottom: solid 1px black !important;
}
/* inprogress & pending tasks */
.bmp-task-list TR.bmp-task-inprogress {
  background-color: #eee;
}
.bmp-task-list TR.bmp-task-failed {
  background-color: #d9534f10;
}
.bmp-task-list TR.bmp-task-warning {
  background-color: #f0ad4e10;
}
.bmp-task-list TR.bmp-task-disabled,
.bmp-task-list TR.bmp-task-disabled TD,
.bmp-task-list TR.bmp-task-disabled TH {
  color: #bbb !important;
}
.bmp-task-list TR.bmp-task-disabled A {
  color: #00336650 !important;
}

.bmp-task-list TBODY .bmp-task-message CODE {
  display: block;
  padding: 0 !important;
  line-height: 1.5em;
  margin: 0.75em 0 !important;
}

.bmp-task-message SPAN {
  white-space: pre-wrap;
}

.bmp-task-actor SPAN {
  font-size: 1.2em;
}
.bmp-task-status {
  font-size: 0.9em;
}
/* canceled, expired and warning tasks */
.bmp-task-status.bmp-task-status-canceled,
.bmp-task-status.bmp-task-status-expired,
.bmp-task-status.bmp-task-status-warning {
  color: #f0ad4e;
  font-weight: bold;
}
/* failed tasks */
.bmp-task-status.bmp-task-status-failed {
  color: #d9534f;
  font-weight: bold;
}

.bmp-task-list TFOOT UL.bmp-task-progress LI {
  margin-left: 1em !important;
  font-size: 16px;
}
.table-col-8 {
  width: 8%;
}
.table-col-20 {
  width: 20%;
}
.table-col-25 {
  width: 25%;
}
.table-col-75 {
  width: 75%;
}
.table-col-33 {
  width: 33%;
}
.table-col-66 {
  width: 66%;
}

button:disabled {
  background-color: gray;
  border-color: gray;
}

.p-button-primary {
  font-weight: 500;
  color: #fff;
  background: #5685c4;
  border: 1px solid #5685c4;
}

.p-button-primary:hover {
  color: #fff;
  background: #3f71b5 !important;
}
