<app-loader *ngIf="isLoadingJob"></app-loader>

<div class="job-info" *ngIf="!isLoadingJob">
    <div class="row">
        <div class="col-md-6">
            <h3>
                <span class="fa fa-tasks mr-1"></span>
                <span i18n="@@bma_serverjobdetails_overview">Job Overview</span>
            </h3>

            <table class="table table-sm">
                <colgroup>
                    <col class="table-col-25" />
                    <col class="table-col-75" />
                </colgroup>

                <tbody>
                    <tr>
                        <td i18n="@@bma_common_type">Type</td>
                        <td>
                            {{ job.type }}

                            <small class="pull-right">
                                <ng-container *ngIf="job.type == 'install'">
                                    {{ job.payload.operatingSystemId }}
                                </ng-container>
                                <ng-container *ngIf="job.type == 'rescueMode'">
                                    {{ job.payload.rescueImageId }}
                                </ng-container>
                            </small>
                        </td>
                    </tr>

                    <tr>
                        <td i18n="@@bma_common_node">Node</td>
                        <td>
                            <span class="selectable text-monospace">{{ job.node }}</span>

                            <small *ngIf="job.isRunning" class="pull-right">
                                <a i18n="@@bma_common_bootfile" href="https://api.leaseweb.com/v1/lswasp/nodes/{{ job.node }}/boot" target="_blank">Bootfile</a>
                                <br />
                                <a i18n="@@bma_common_answerfile" *ngIf="job.type == 'install' && (['wimboot', 'answerFile']).indexOf(job.payload.os.engine) > -1" href="https://api.leaseweb.com/v1/lswasp/nodes/{{ job.node }}/answerFile" target="_blank">Answer file</a>
                            </small>
                        </td>
                    </tr>

                    <tr>
                        <td i18n="@@bma_common_initiatedby">Initiated By</td>
                        <td>
                            {{ job.payload.initiatedBy|default:'Unknown' }}
                        </td>
                    </tr>

                    <tr>
                        <td i18n="@@bma_common_status">Status</td>
                        <td>
                            <span class="job-status btn btn-sm py-0 disabled" [ngClass]="getJobStatusClass(job.status)">
                                {{ job.status }}
                            </span>
                        </td>
                    </tr>

                    <tr>
                        <td i18n="@@bma_common_created">Created</td>
                        <td>
                            {{ job.createdAt|timeAgo }}

                            <small class="text-muted pull-right">
                                {{ job.createdAt|date: 'MMM d, yyyy HH:mm:ss (zzzz)' }}
                            </small>
                        </td>
                    </tr>

                    <tr>
                        <td i18n="@@bma_common_updated">Updated</td>
                        <td>
                            {{ job.updatedAt|timeAgo }}

                            <small class="text-muted pull-right">
                                {{ job.updatedAt|date: 'MMM d, yyyy HH:mm:ss (zzzz)'}}
                            </small>
                        </td>
                    </tr>

                    <tr>
                        <td i18n="@@bma_serverjobdetails_isjobrunning">Is job running</td>
                        <td>
                            <span class="btn btn-sm btn-outline-{{ job.isRunning ? 'success' : 'danger' }} disabled py-0">
                                {{ job.isRunning ? 'Yes' : 'No' }}
                            </span>
                        </td>
                    </tr>

                    <tr>
                        <td i18n="@@bma_serverjobdetails_currenttask">Current Task</td>
                        <td>
                            <ng-container *ngIf="job.currentTask|default; else notAvailable"> {{ job.currentTask.flow|default }}::{{ job.currentTask.type|default }} </ng-container>
                        </td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_batch">Batch</td>
                        <td>
                            <a *ngIf="job.metadata.BATCH_ID|default; else notAvailable" [routerLink]="['/jobs']" [queryParams]="{ batchId: job.metadata.BATCH_ID }">{{ job.metadata.BATCH_ID }}</a>
                        </td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_featureflags">Feature Flags</td>
                        <td>
                            <ul class="list-unstyled mb-0">
                                <li *ngFor="let feature of job.payload.features|default:[]" class="{{ job.payload.featuresUtilized?.includes(feature) ? '' : 'text-muted' }}">
                                    {{ feature }}
                                </li>
                            </ul>
                        </td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_viewlogs">View Logs</td>
                        <td>
                            <a [routerLink]="['/audit-logs']" [queryParams]="{filter: job.uuid}">{{ job.uuid }}</a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="col-md-6">
            <h3>
                <span class="fa fa-server mr-1"></span>
                <span i18n="@@bma_serverjobdetails_dedicatederveroverview">Dedicated Server Overview</span>
            </h3>

            <table class="table table-sm">
                <colgroup>
                    <col class="table-col-33" />
                    <col class="table-col-66" />
                </colgroup>

                <tbody>
                    <tr>
                        <td i18n="@@bma_common_id">ID</td>
                        <td>
                            <a href="#" (click)="openServerInfoPopupModal(server.id); false">{{ server.id }}</a>
                        </td>
                    </tr>

                    <tr>
                        <td i18n="@@bma_common_model">Model</td>
                        <td>
                            {{ job.payload.serverBrand|default:'Unknown' }} -
                            {{ job.payload.serverChassis|default:'Unknown' }}
                        </td>
                    </tr>

                    <tr>
                        <td i18n="@@bma_common_location">Location</td>
                        <td>
                            <app-equipment-location [location]="server.location"></app-equipment-location>
                        </td>
                    </tr>

                    <tr>
                        <td i18n="@@bma_common_macadress">Mac Address</td>
                        <td>
                            <span *ngIf="server.networkInterfaces.public?.mac|default; else notAvailable" class="selectable text-monospace">
                                {{ server.networkInterfaces.public.mac }}
                            </span>
                        </td>
                    </tr>

                    <tr>
                        <td i18n="@@bma_common_installip">Installation IP Address</td>
                        <td>
                            <span *ngIf="(job.payload.network.public.cidr|default) && (server.networkInterfaces.public?.ip|default) != (job.payload.network.public.cidr|default); else notAvailable" class="selectable text-monospace">
                                {{ job.payload.network.public.ip }}
                            </span>
                        </td>
                    </tr>

                    <tr>
                        <td i18n="@@bma_common_mainip">Main IP Address</td>
                        <td>
                            <span *ngIf="server.networkInterfaces.public?.ip|default; else notAvailable" class="selectable text-monospace">
                                {{ server.networkInterfaces.public.ip|cidrToIp }}
                            </span>
                            <ng-container *ngIf="server.networkInterfaces.public?.nullRouted|default"> &nbsp;<span class="badge badge-danger">NULL ROUTED</span> </ng-container>
                        </td>
                    </tr>

                    <tr *ngIf="(['install', 'rescueMode']).indexOf(job.type) > -1">
                        <td>
                            <span i18n="@@bma_common_password">Password</span>
                            <small class="text-muted"> (<span i18n="@@bma_common_mightbeold">Might be old</span>)</small>
                        </td>
                        <td>
                            <app-copy-password [equipmentId]="equipmentId" [credential]="operatingSystemPassword"></app-copy-password>
                        </td>
                    </tr>

                    <tr>
                        <td i18n="@@bma_common_serialnumber">Serial Number</td>
                        <td>
                            <span *ngIf="server.serialNumber|default; else notAvailable" class="selectable text-monospace">
                                {{ server.serialNumber }}
                            </span>
                        </td>
                    </tr>

                    <tr *ngIf="server.featureAvailability.remoteManagement && server.networkInterfaces.remoteManagement?.ip; else default">
                        <td i18n="@@bma_common_remotemanagementaddress">Remote Management Address</td>
                        <td>
                            <a href="http://{{ server.networkInterfaces.remoteManagement.ip|cidrToIp }}" target="_blank">
                                <span class="fa fa-login" aria-hidden="true"> </span>
                                {{ server.networkInterfaces.remoteManagement.ip|cidrToIp }}
                            </a>
                        </td>
                    </tr>

                    <tr *ngIf="server.featureAvailability.remoteManagement|default">
                        <td i18n="@@bma_common_remotemanagementuser">Remote Management User</td>
                        <td>lswadmin</td>
                    </tr>

                    <tr *ngIf="server.featureAvailability.remoteManagement|default">
                        <td i18n="@@bma_common_remotemanagementpassword">Remote Management Password</td>
                        <td>
                            <app-copy-password [equipmentId]="server.id" credentialType="REMOTE_MANAGEMENT" [credential]="remoteManagementPassword"></app-copy-password>
                        </td>
                    </tr>
                </tbody>
            </table>
            <ng-container *ngIf="job.status == 'ACTIVE'">
                <h3 class="mt-5">
                    <i class="fa fa-server mr-1"></i>
                    <span i18n="@@bma_serverjobdetails_reservationstate">Reservation State</span>
                </h3>

                <table id="reservation" class="table table-sm">
                    <colgroup>
                        <col class="table-col-33" />
                        <col class="table-col-66" />
                    </colgroup>

                    <tbody>
                        <tr *ngIf="dhcpState">
                            <td i18n="@@bma_common_state">State</td>
                            <td id="state">{{ dhcpState }}</td>
                        </tr>
                        <tr *ngIf="dhcpLastUsed">
                            <td i18n="@@bma_common_lastused">Last Used</td>
                            <td id="last-used">{{ dhcpLastUsed }}</td>
                        </tr>
                        <tr *ngIf="dhcpRequestType">
                            <td i18n="@@bma_common_requesttype">Request Type</td>
                            <td id="request-type">{{ dhcpRequestType }}</td>
                        </tr>
                        <tr *ngIf="dhcpRelayAgent">
                            <td i18n="@@bma_common_relayagent">Relay Agent</td>
                            <td id="relay-agent">{{ dhcpRelayAgent }}</td>
                        </tr>
                        <tr *ngIf="dhcpUserAgent">
                            <td i18n="@@bma_common_useragent">User Agent</td>
                            <td id="user-agent">{{ dhcpUserAgent }}</td>
                        </tr>
                    </tbody>
                </table>
            </ng-container>
        </div>
    </div>

    <div class="text-right">
        <div class="btn-toolbar d-inline-block my-3" role="toolbar">
            <div class="btn-group p-buttonset mr-2" role="group">
                <button pButton pRipple i18n-label="@@bma_common_cancel" type="button" [disabled]="!currentUserService.hasPermission('job_expire_and_cancel') || job.status !== 'ACTIVE'" (click)="openJobCancelModal()" label="Cancel" icon="pi pi-trash" class="p-button-danger"></button>
                <button pButton pRipple i18n-label="@@bma_common_expire" type="button" [disabled]="!currentUserService.hasPermission('job_expire_and_cancel') || !job.isRunning || ('FINALLY' === job.currentTask?.flow)" (click)="openJobExpireModal()" label="Expire" icon="pi pi-pause" class="p-button-warning"></button>
                <button pButton pRipple i18n-label="@@bma_common_retry" type="button" [disabled]="job.isRunning || !allowJobRetry" (click)="openJobRetryModal()" label="Retry" icon="pi pi-refresh" class="p-button-success"></button>
            </div>

            <div class="btn-group p-buttonset mr-2" role="group">
                <button pButton pRipple type="button" label="{{ showPayload ? 'Hide Payload' : 'Show Payload' }}" (click)="togglePayloadShow()" icon="pi pi-refresh" class="p-button-primary border-0"></button>
                <button pButton pRipple type="button" i18n-label="@@bma_common_openlogstor" label="Open Logstor" (click)="openLogstor(job)" icon="pi pi-search" class="p-button-primary"></button>
            </div>
        </div>
    </div>

    <aside id="jobPayload" *ngIf="showPayload">
        <h3>
            <i class="fa fa-details mr-1"></i>
            <span i18n="@@bma_common_payload">Payload</span>
        </h3>

        <div class="card mb-5 mt-2">
            <div class="card-body">
                <button pButton type="button" icon="pi pi-copy" class="p-button-sm float-right" (click)="doCopy(job.payload)"></button>
                <pre id="payload-json" [innerHtml]="job.payload|json"></pre>
            </div>
        </div>
    </aside>

    <h3 class="mt-5">
        <i class="fa fa-dashboard mr-1"></i>
        <span i18n="@@bma_common_progress">Progress</span>
    </h3>

    <p-progressBar [value]="job.progress.percentage"></p-progressBar>

    <h3 class="mt-5"><i class="fa fa-survey"></i>&nbsp;<span i18n="@@bma_common_tasks">Tasks</span></h3>

    <div class="table-responsive">
        <table class="table table-striped bmp-task-list">
            <thead>
                <tr>
                    <th i18n="@@bma_common_updated" class="text-center table-col-20">Updated</th>
                    <th i18n="@@bma_common_status" class="text-center table-col-8">Status</th>
                    <th i18n="@@bma_common_task" class="text-left">Task</th>
                </tr>
            </thead>

            <tbody>
                <ng-container *ngFor="let task of job.tasks">
                    <tr *ngIf="task.flow != currentFlow" class="bmp-task-flow">
                        <th colspan="2">&nbsp;</th>
                        <th>{{ task.flow }} flow</th>
                    </tr>

                    <tr data-id="{{ task.uuid }}" class="{{ taskStatusClass(task.status) }}">
                        <td class="text-center">
                            <span *ngIf="(['SKIPPED', 'WAITING']).indexOf(task.status) === -1">{{ task.updatedAt|date: 'MMM d, yyyy HH:mm:ss (zzzz)' }}</span>
                        </td>

                        <td class="text-center">
                            <span class="bmp-task-status bmp-task-status-{{ task.status|lowercase }}">
                                {{ task.status }}
                            </span>
                        </td>

                        <td class="text-left">
                            <strong class="bmp-task-type">
                                <a *ngIf="task.actor == 'node'; else showTaskType" href="#" (click)="openJobTaskDetailsModal(task.uuid); false">
                                    {{ task.type }}
                                </a>
                                <ng-template #showTaskType>
                                    {{ task.type }}
                                </ng-template>
                            </strong>

                            <ng-container *ngIf="(task.errorMessage|default) && (task.errorMessage|default) != currentErrorMessage">
                                <div class="bmp-task-message">
                                    <span class="text-monospace {{ (['FAILED','FINISHED','WARNING']).indexOf(task.status) == -1 ? 'text-muted' : ''}}">{{ task.errorMessage|default:'' }}</span>
                                </div>
                                <div *ngIf="task.type === 'firmware/update' && (['FAILED','FINISHED','WARNING']).indexOf(task.status) !== -1" class="text-right">
                                    <a [href]="renderLinkToFirmwareUpdatesOnLogstor(task)" target="_blank">Check logstor to find out what was updated</a>
                                </div>
                            </ng-container>
                        </td>
                    </tr>
                    {{ updateCurrentErrorMessage(task.errorMessage|default) }}
                    {{ updateCurrentFlow(task.flow) }}
                </ng-container>
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="999">
                        <ul class="list-inline text-right bmp-task-progress">
                            <li *ngIf="job.progress.waiting > 0" class="list-inline-item">{{ job.progress.waiting }} <span i18n="@@bma_common_waiting">waiting</span></li>
                            <li *ngIf="job.progress.pending > 0" class="list-inline-item">{{ job.progress.pending }} <span i18n="@@bma_serverjobdetails_pending">pending</span></li>
                            <li *ngIf="job.progress.inprogress > 0" class="list-inline-item">{{ job.progress.inprogress }} <span i18n="@@bma_serverjobdetails_inprogress">in progress</span></li>
                            <li *ngIf="job.progress.expired > 0" class="list-inline-item">{{ job.progress.expired }} <span i18n="@@bma_serverjobdetails_expired">expired</span></li>
                            <li *ngIf="job.progress.canceled > 0" class="list-inline-item">{{ job.progress.canceled }} <span i18n="@@bma_common_canceled">canceled</span></li>
                            <li *ngIf="job.progress.skipped > 0" class="list-inline-item">{{ job.progress.skipped }} <span i18n="@@bma_common_skipped">skipped</span></li>
                            <li *ngIf="job.progress.warning > 0" class="list-inline-item">{{ job.progress.warning }} <span i18n="@@bma_serverjobdetails_warning">warning</span></li>
                            <li *ngIf="job.progress.failed > 0" class="list-inline-item">{{ job.progress.failed }} <span i18n="@@bma_common_failed">failed</span></li>
                            <li *ngIf="job.progress.finished > 0" class="list-inline-item">{{ job.progress.finished }} <span i18n="@@bma_common_finished">finished</span></li>
                            <li class="list-inline-item">
                                <strong>{{ job.tasks.length }} <span i18n="@@bma_common_tasks">Tasks</span></strong>
                            </li>
                        </ul>
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>
</div>
<ng-template #notAvailable>
    <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
</ng-template>
