import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';
import { Clipboard } from '@angular/cdk/clipboard';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { JobRetryModalComponent } from 'src/app/job/job-retry-modal/job-retry-modal.component';
import { JobExpireModalComponent } from 'src/app/job/job-expire-modal/job-expire-modal.component';
import { JobCancelModalComponent } from 'src/app/job/job-cancel-modal/job-cancel-modal.component';
import { JobTaskDetailsModalComponent } from 'src/app/job/job-task-details-modal/job-task-details-modal.component';
import { LswNotificationHubService } from '@lsw/notification-hub';
import { first } from 'rxjs';
import { ServerEvent } from 'src/app/models/server-event.model';
import { SiteService } from 'src/app/services/site.service';
import { MercureService } from 'src/app/services/mercure.service';
import { ServerInfoPopupModalComponent } from '../server-info-popup-modal/server-info-popup-modal.component';

@Component({
  selector: 'app-server-job-details',
  templateUrl: './server-job-details.component.html',
  styleUrls: ['./server-job-details.component.css'],
})
export class ServerJobDetailsComponent implements OnInit {
  @Input() server: any;
  allowJobRetry = false;
  jobUuid: any;
  job: any;
  isSubmitting = false;
  isLoadingJob = false;
  currentErrorMessage = '';
  currentFlow = '';
  operatingSystemPassword: any;
  remoteManagementPassword: any;
  showPayload = false;
  isLoadingTaskAction = false;
  taskAction: any;
  dhcpState: any;
  dhcpLastUsed: any;
  dhcpRequestType: any;
  dhcpRelayAgent: any;
  dhcpUserAgent: any;
  dynamicDialogRef: DynamicDialogRef;

  constructor(
    private activatedRoute: ActivatedRoute,
    private alertService: AlertService,
    private modalService: ModalService,
    private currentUserService: CurrentUserService,
    private httpClient: HttpClient,
    private router: Router,
    private clipboard: Clipboard,
    private siteService: SiteService,
    private notificationHubService: LswNotificationHubService,
    private mercureService: MercureService,
    private ref: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.activatedRoute.url.subscribe((parameters) => this.onUrlParamsChanged(parameters));
  }

  onUrlParamsChanged(urlParams): void {
    this.jobUuid = urlParams[1].path;
    this.getJobDetails();
    this.getDhcpReservation();
    this.getRemoteManagementPasswordInfo();
    this.getOperatingSystemPasswordInfo();
  }

  getRemoteManagementPasswordInfo(): void {
    this.remoteManagementPassword = {
      type: 'REMOTE_MANAGEMENT',
      username: 'lswadmin',
    };
  }

  getOperatingSystemPasswordInfo(): void {
    let passwordType = 'RESCUE_MODE';
    let userType = 'root';

    if (this.job && this.job.type !== 'rescueMode') {
      userType = this.job.payload.os.family === 'windows' ? 'Administrator' : 'root';
      passwordType = 'TEMPORARY_OPERATING_SYSTEM';

      if (this.job.status === 'FINISHED') {
        passwordType = 'OPERATING_SYSTEM';
      }
    }

    this.operatingSystemPassword = {
      type: passwordType,
      username: userType,
    };
  }

  getJobDetails(): void {
    this.isLoadingJob = true;
    this.httpClient.get(`/_/internal/bmpapi/v2/jobs/${this.jobUuid}`).subscribe(
      (data: any) => {
        this.isLoadingJob = false;
        if (data) {
          this.job = data;
          const createdAtDateDifference =
            (new Date().getTime() - new Date(this.job.createdAt).getTime()) / (1000 * 60 * 60 * 24);
          this.allowJobRetry = createdAtDateDifference <= 7;
          if (this.job.progress.percentage < 100) {
            /* eslint-disable */
            const event = new Event('update');
            event['customerId'] = this.currentUserService.getCustomerId();
            event['salesOrgId'] = this.currentUserService.getSalesOrgId();
            event['country'] = this.siteService.getCountry(this.currentUserService.getSalesOrgId());
            window.dispatchEvent(event);
            /* eslint-enable */
            this.subscribeForUpdate();
          }
        } else {
          this.job = {};
        }
        this.getOperatingSystemPasswordInfo();
      },
      (error: any) => {
        this.isLoadingJob = false;
        this.job = {};
        if (error.status === 404) {
          this.router.navigate(['**']);
        }
      }
    );
  }

  updateCurrentErrorMessage(errorMessage: any): void {
    this.currentErrorMessage = errorMessage;
  }

  updateCurrentFlow(flow: any): void {
    this.currentFlow = flow;
  }

  togglePayloadShow(): void {
    this.showPayload = !this.showPayload;
  }

  taskStatusClass(status: string): string {
    if (['SKIPPED', 'WAITING'].find((statuses) => statuses === status)) {
      return 'bmp-task-disabled';
    }

    if (['INPROGRESS', 'PENDING'].find((statuses) => statuses === status)) {
      return 'bmp-task-inprogress';
    }

    if (['FAILED'].find((statuses) => statuses === status)) {
      return 'bmp-task-failed';
    }

    if (['WARNING'].find((statuses) => statuses === status)) {
      return 'bmp-task-warning';
    }

    return '';
  }

  openServerInfoPopupModal(serverId: string) {
    this.modalService.show(ServerInfoPopupModalComponent, { serverId });
  }

  getDhcpReservation(): void {
    this.httpClient.get<any>(`/_/bareMetals/v2/servers/${this.server.id}/leases`).subscribe({
      next: (data: any) => {
        const reservation = data.leases.length > 0 ? data.leases[0] : null;
        if (!reservation) {
          this.dhcpState = 'No DHCP Reservation';
        } else if (reservation.lastClientRequest) {
          this.dhcpState = 'DHCP Reservation used';
          this.dhcpLastUsed = reservation.updatedAt;
          this.dhcpRequestType = reservation.lastClientRequest.type;
          this.dhcpRelayAgent = reservation.lastClientRequest.relayAgent;
          this.dhcpUserAgent = reservation.lastClientRequest.userAgent;
        } else {
          this.dhcpState = 'DHCP Reservation unused';
        }
      },
      error: (error: any) => {
        this.dhcpState = 'No DHCP Reservation';
      },
    });
  }

  showTaskAction(uuid: string): void {
    this.isLoadingTaskAction = true;
    this.httpClient.get<any>(`/_/internal/bmpapi/v2/tasks/${uuid}`).subscribe({
      next: (data: any) => {
        this.taskAction = data;
        this.isLoadingTaskAction = false;
      },
      error: (error: any) => {
        this.taskAction = {};
        this.isLoadingTaskAction = false;
      },
    });
  }

  openJobRetryModal() {
    this.dynamicDialogRef = this.modalService.show(JobRetryModalComponent, {
      uuid: this.jobUuid,
      oldPowerCycle: this.job.payload.powerCycle,
    });

    this.dynamicDialogRef.onClose.subscribe((event) => {
      if (event?.jobRetried === true) {
        this.router.navigate([`/servers/${this.server.id}/jobs/${event?.newJobUuid}`]);
      }
    });
  }

  openJobExpireModal() {
    this.dynamicDialogRef = this.modalService.show(JobExpireModalComponent, {
      uuid: this.jobUuid,
    });

    this.dynamicDialogRef.onClose.subscribe((event) => {
      if (event?.jobExpired === true) {
        this.getJobDetails();
        this.getDhcpReservation();
      }
    });
  }

  openJobCancelModal() {
    this.dynamicDialogRef = this.modalService.show(JobCancelModalComponent, {
      uuid: this.jobUuid,
    });

    this.dynamicDialogRef.onClose.subscribe((event) => {
      if (event?.jobCancelled === true) {
        this.getJobDetails();
        this.getDhcpReservation();
      }
    });
  }

  openJobTaskDetailsModal(taskUuid: string) {
    this.dynamicDialogRef = this.modalService.show(JobTaskDetailsModalComponent, {
      uuid: taskUuid,
      serverId: this.server.id,
    });
  }

  selectText(event): void {
    window.getSelection().selectAllChildren(event.target);
  }

  getJobStatusClass(status) {
    switch (status) {
      case 'ACTIVE':
        return 'btn-outline-primary';
      case 'FINISHED':
        return 'btn-outline-success';
      case 'CANCELED':
      case 'EXPIRED':
        return 'btn-outline-warning';
      case 'FAILED':
        return 'btn-outline-danger';
      default:
        return '';
    }
  }

  openLogstor(job: any) {
    window.open(this.renderLinkToFirmwareUpdatesOnLogstor(job), '_blank').focus();
  }

  renderLinkToFirmwareUpdatesOnLogstor(task: any): string {
    if (!task?.updatedAt) {
      return '';
    }
    const folder = task.updatedAt.slice(0, 7).replace(/-/g, '/');
    const filename = task.updatedAt.slice(0, 10).replace(/-/g, '.');
    const today = '0'.concat(new Date().getUTCDate().toString(10)).slice(-2);
    let url = `https://logstor.ams1.nl.leaseweb.net/lswasp/s${this.server.id}/${folder}/${filename}.log`;
    if (today !== task.updatedAt.slice(8, 10)) {
      url += '.bz2';
    }
    return url;
  }

  doCopy(payload: string): void {
    const pending = this.clipboard.beginCopy(JSON.stringify(payload));
    let remainingAttempts = 3;
    const attempt = () => {
      const result = pending.copy();
      if (remainingAttempts === 0) {
        this.alertService.alert({
          type: 'error',
          description: $localize`:@@bma_common_errorcopyingpayload:Sorry, there was an error copying payload.`,
        });
      } else if (!result && --remainingAttempts) {
        setTimeout(attempt);
      } else {
        pending.destroy();
      }
    };
    attempt();

    this.alertService.alert({
      type: 'success',
      description: $localize`:@@bma_common_payloadcopiedtoclipboard:Payload copied to clipboard`,
    });
  }

  subscribeForUpdate() {
    this.notificationHubService.setTokenParams({
      serverId: this.job.serverId,
    });
    this.notificationHubService
      .getNotificationHubToken(this.mercureService.getTokenURL('bmpapi'))
      .pipe(first())
      .subscribe({
        next: () => {
          this.notificationHubService.sync<ServerEvent>(`${this.job.serverId}/jobs`).subscribe({
            next: (event) => {
              if (this.job.progress.percentage < 100) {
                this.job = event.data;
                this.ref.detectChanges();
              }
            },
            error: (error) => console.log({ error }),
          });
        },
      });
  }
}
