import { Component, Input, ViewChild } from '@angular/core';
import { NetworkDeviceManageSpeedModalComponent } from 'src/app/network-device/network-device-manage-speed-modal/network-device-manage-speed-modal.component';

@Component({
  selector: 'app-server-network-details',
  templateUrl: './server-network-details.component.html',
  styleUrls: ['./server-network-details.component.css'],
})
export class ServerNetworkDetailsComponent {
  @Input() equipment: any;
  @Input() errorInformation: string[];
  @ViewChild(NetworkDeviceManageSpeedModalComponent, { static: false })
  capUncapComponent: NetworkDeviceManageSpeedModalComponent;

  equipmentType = 'servers';
  networkInterfaceOrder = ['public', 'internal', 'remoteManagement'];
}
