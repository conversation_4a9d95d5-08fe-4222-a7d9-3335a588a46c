import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-server-new-features-modal',
  templateUrl: './server-new-features-modal.component.html',
  styleUrls: ['./server-new-features-modal.component.css'],
})
export class ServerNewFeaturesModalComponent implements OnInit {
  serverId: any;
  showDialog = true;

  constructor(
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.serverId = this.config.data.serverId;
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }
}
