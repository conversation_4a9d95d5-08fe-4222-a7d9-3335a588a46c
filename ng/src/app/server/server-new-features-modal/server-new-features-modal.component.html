<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '800px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_common_newfeatures" class="modal-title">New Features</h3>
    </ng-template>

    <div class="modal-body">
        <div>
            <h4 i18n="@@bma_servernewfeaturesmodal_feature1_description1">Terraform provider plugin</h4>
            <p i18n="@@bma_servernewfeaturesmodal_feature1_description2">You can now manage your dedicated servers using Terraform.</p>
            <p i18n="@@bma_servernewfeaturesmodal_feature1_description3">We've released the first version of our provider plugin which includes basic functionalities for dedicated servers.</p>
            <a i18n="@@bma_servernewfeaturesmodal_feature1_description4" target="_blank" href="https://registry.terraform.io/providers/LeaseWeb/leaseweb/latest/docs">Find out more in the documentation</a>
        </div>
        <div>
            <h4 i18n="@@bma_servernewfeaturesmodal_feature2_description1">List of jobs</h4>
            <p i18n="@@bma_servernewfeaturesmodal_feature2_description2">You can now see a list of the jobs run on your server.</p>
            <p i18n="@@bma_servernewfeaturesmodal_feature2_description3">From there you can check the current job status, cancel it and retry some of the previous jobs.</p>
            <a i18n="@@bma_servernewfeaturesmodal_feature2_description4" target="_blank" href="/bare-metals/servers/{{serverId}}/jobs">See the list of jobs</a>
        </div>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n="@@bma_common_close" pButton label="Close" icon="pi pi-times" (click)="closeModal()" type="button" class="p-button-secondary"></button>
        </div>
    </ng-template>
</p-dialog>
