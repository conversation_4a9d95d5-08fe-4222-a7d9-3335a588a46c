<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '800px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_common_newfeatures" class="modal-title">New Features</h3>
    </ng-template>

    <div class="modal-body">
        <div>
            <h4 i18n="@@bma_servernewfeaturesmodal_feature_aiframeworks">AI Frameworks</h4>
            <p i18n="@@bma_servernewfeaturesmodal_feature_aiframeworks_description">For Ubuntu 24.04, Ubuntu 22.04, Debian 12, AlmaLinux 9 and Rocky Linux 9, you can now select frameworks that will be installed on your server (<PERSON>y<PERSON>or<PERSON>, Tensor<PERSON>low, Scikit-learn, XGboost). In addition you can select model libraries to be installed (Hugging face, Nvidia NGC).</p>
        </div>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n="@@bma_common_close" pButton label="Close" icon="pi pi-times" (click)="closeModal()" type="button" class="p-button-secondary"></button>
        </div>
    </ng-template>
</p-dialog>
