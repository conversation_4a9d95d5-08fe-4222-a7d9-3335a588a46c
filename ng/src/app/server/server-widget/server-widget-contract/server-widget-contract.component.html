<span i18n-title="@@bma_common_contract" class="fa fa-spla {{ hasWarning ? 'text-danger' : 'text-muted' }}" (click)="hasWarning && panel.toggle($event)" title="Contract"></span>
<p-overlayPanel #panel [dismissable]="true" [style]="{'width':'300px'}" [appendTo]="'body'">
    <ng-template pTemplate>
        <h3 i18n="@@bma_common_contractstatus">Contract Status</h3>
        <p i18n="@@bma_serverwidgetcontract_description1">Contract in modification. Some automation features have been disabled.</p>
        <p i18n="@@bma_serverwidgetcontract_description2" *ngIf="server.contract?.endsAt">This Server's contract will expire on "{{ server.contract.endsAt }}"</p>
    </ng-template>
</p-overlayPanel>
