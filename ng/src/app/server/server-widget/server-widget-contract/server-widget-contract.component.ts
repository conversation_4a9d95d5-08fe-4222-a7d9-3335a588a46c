import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-server-widget-contract',
  templateUrl: './server-widget-contract.component.html',
  styleUrls: ['./server-widget-contract.component.css', '../server-widget-shared.css'],
})
export class ServerWidgetContractComponent implements OnInit {
  @Input() server: any;

  hasWarning = false;

  ngOnInit(): void {
    this.checkContractStatus();
  }

  checkContractStatus(): void {
    if (this.server.contract && this.server.contract.status !== 'ACTIVE') {
      this.hasWarning = true;
    }
  }
}
