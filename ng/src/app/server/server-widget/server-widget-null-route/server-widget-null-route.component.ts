import { Component, Input, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { CidrToIpPipe } from 'src/app/pipes/cidr-to-ip.pipe';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { NullRouteIpUnnullModalComponent } from 'src/app/null-route-ip/null-route-ip-unnull-modal/null-route-ip-unnull-modal.component';

@Component({
  selector: 'app-server-widget-null-route',
  templateUrl: './server-widget-null-route.component.html',
  styleUrls: ['./server-widget-null-route.component.css', '../server-widget-shared.css'],
})
export class ServerWidgetNullRouteComponent implements OnInit {
  @Input() server: any;
  isEmployee = false;
  dynamicDialogRef: DynamicDialogRef;

  hasWarning = false;
  primaryIp: string;

  constructor(
    private httpClient: HttpClient,
    private modalService: ModalService,
    private cidrToIpPipe: CidrToIpPipe,
    private currentUserService: CurrentUserService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.checkIpNullRouted();
  }

  checkIpNullRouted(): void {
    if (this.server.networkInterfaces.public?.ip && this.server.networkInterfaces.public?.nullRouted) {
      this.hasWarning = true;
      this.primaryIp = this.cidrToIpPipe.transform(this.server.networkInterfaces.public.ip);
    }
  }

  openUnnullIpModal() {
    this.dynamicDialogRef = this.modalService.show(NullRouteIpUnnullModalComponent, {
      equipment: this.server,
      equipmentType: 'servers',
      ipAddress: this.primaryIp,
    });

    this.dynamicDialogRef.onClose.subscribe((event: any) => {
      if (event?.unnulled === true) {
        this.hasWarning = false;
      }
    });
  }
}
