<span i18n-title="@@bma_serverwidgetnullroute_title" class="fa fa-ip {{ hasWarning ? 'text-danger' : 'text-muted' }}" (click)="hasWarning && panel.toggle($event)" title="Null Routed Primary IP Address"></span>
<p-overlayPanel #panel [dismissable]="true" [style]="{'width':'300px'}" [appendTo]="'body'">
    <ng-template pTemplate>
        <h3 i18n="@@bma_common_nullroute">Null Route</h3>
        <p i18n="@@bma_serverwidgetnullroute_description1">This Server's Primary IP address "{{ primaryIp }}" is null routed.</p>
        <ul *ngIf="!isEmployee" class="list-inline">
            <li>
                <a i18n-title="@@bma_common_removenullroute" href="javascript:void(0);" (click)="openUnnullIpModal()"> Remove Null Route </a>
            </li>
        </ul>
    </ng-template>
</p-overlayPanel>
