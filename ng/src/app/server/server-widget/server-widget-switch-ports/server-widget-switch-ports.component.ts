import { Component, Input, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { SwitchPortCloseModalComponent } from 'src/app/switch-port/switch-port-close-modal/switch-port-close-modal.component';

@Component({
  selector: 'app-server-widget-switch-ports',
  templateUrl: './server-widget-switch-ports.component.html',
  styleUrls: ['./server-widget-switch-ports.component.css', '../server-widget-shared.css'],
})
export class ServerWidgetSwitchPortsComponent implements OnInit {
  @Input() server: any;
  dynamicDialogRef: DynamicDialogRef;

  hasWarning = false;

  constructor(
    private httpClient: HttpClient,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.checkSwitchPortStatus();
  }

  checkSwitchPortStatus(): void {
    this.httpClient
      .get<any>(`/_/internal/nseapi/v2/servers/${this.server.id}/networkInterfaces/public?all`, {})
      .subscribe((data: any) => {
        const closedInterfaces = data.networkInterfaces.filter(
          (networkInterface) => networkInterface.status !== 'OPEN'
        );
        if (closedInterfaces.length > 0) {
          this.hasWarning = true;
        }
      });
  }

  openSwitchPortModal() {
    this.dynamicDialogRef = this.modalService.show(SwitchPortCloseModalComponent, {
      equipmentId: this.server.id,
      type: 'public',
      action: 'open',
    });

    this.dynamicDialogRef.onClose.subscribe((event: any) => {
      if (event?.switchPortUpdated === true) {
        this.hasWarning = false;
      }
    });
  }
}
