import { Component, Input, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-server-widget-custom-installation',
  templateUrl: './server-widget-custom-installation.component.html',
  styleUrls: ['./server-widget-custom-installation.component.css', '../server-widget-shared.css'],
})
export class ServerWidgetCustomInstallationComponent implements OnInit {
  @Input() server: any;

  hasWarning = false;

  constructor(private httpClient: HttpClient) {}

  ngOnInit(): void {
    this.checkCustomInstallation();
  }

  checkCustomInstallation(): void {
    this.httpClient.get<any>(`/_/internal/bmpapi/v2/servers/${this.server.id}/leases`).subscribe((data: any) => {
      if (data.leases?.length > 0) {
        this.hasWarning = !data.leases[0].bootfile.includes('ipxe.leaseweb.com') ? true : false;
      }
    });
  }
}
