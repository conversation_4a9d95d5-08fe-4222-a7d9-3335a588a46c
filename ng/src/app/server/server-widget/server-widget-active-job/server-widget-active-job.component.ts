import { Component, Input, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { JobCancelModalComponent } from 'src/app/job/job-cancel-modal/job-cancel-modal.component';

@Component({
  selector: 'app-server-widget-active-job',
  templateUrl: './server-widget-active-job.component.html',
  styleUrls: ['./server-widget-active-job.component.css', '../server-widget-shared.css'],
})
export class ServerWidgetActiveJobComponent implements OnInit {
  @Input() server: any;
  isEmployee = false;
  dynamicDialogRef: DynamicDialogRef;

  hasWarning = false;
  activeJob: any;
  isCancelingJob = false;

  constructor(
    private httpClient: HttpClient,
    private modalService: ModalService,
    private currentUserService: CurrentUserService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.checkActiveJob();
  }

  checkActiveJob(): void {
    this.httpClient
      .get<any>(`/_/internal/bmpapi/v2/servers/${this.server.id}/jobs?status=ACTIVE`, {})
      .subscribe((data: any) => {
        if (data.jobs?.length > 0) {
          this.activeJob = data.jobs[0];
          this.hasWarning = true;
        }
      });
  }

  openJobCancelModal() {
    this.dynamicDialogRef = this.modalService.show(JobCancelModalComponent, {
      uuid: this.activeJob.uuid,
      serverId: this.server.id,
    });

    this.dynamicDialogRef.onClose.subscribe((event) => {
      if (event?.jobCancelled === true) {
        this.hasWarning = false;
      }
    });
  }
}
