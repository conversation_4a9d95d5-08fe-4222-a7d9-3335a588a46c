<span i18n-title="@@bma_common_activejob" class="fa fa-tasks {{ hasWarning ? 'text-danger' : 'text-muted' }}" (click)="hasWarning && panel.toggle($event)" title="Active Job"></span>
<p-overlayPanel #panel [dismissable]="true" [style]="{'width':'300px'}" [appendTo]="'body'">
    <ng-template pTemplate>
        <h3 i18n="@@bma_common_activejob">Active Job</h3>
        <div class="active-job-block">
            <span i18n="@@bma_common_runningjob">There is an active job running.</span><br />

            <span class="text-monospace">
                {{ activeJob.type }}<br />
                <span *ngIf="isEmployee" class="active-job-type"> {{ activeJob.currentTask?.flow }} :: {{ activeJob.currentTask?.type }} </span>
            </span>

            <p-progressBar [value]="activeJob.progress.percentage"></p-progressBar>

            <ul class="list-inline mb-0">
                <li *ngIf="isEmployee" class="list-inline-item">
                    <a i18n="@@bma_common_viewjob" [routerLink]="['/servers', server.id, 'jobs', activeJob.uuid]"> View Job </a>
                </li>

                <li class="list-inline-item">
                    <a i18n="@@bma_common_canceljob" href="javascript:void(0);" (click)="openJobCancelModal()"> Cancel Job </a>
                </li>
            </ul>
        </div>
    </ng-template>
</p-overlayPanel>
