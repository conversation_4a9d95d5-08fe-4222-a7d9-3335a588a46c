import { Component, Input, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-server-widget-power',
  templateUrl: './server-widget-power.component.html',
  styleUrls: ['./server-widget-power.component.css', '../server-widget-shared.css'],
})
export class ServerWidgetPowerComponent implements OnInit {
  @Input() server: any;

  hasWarning = false;
  poweredOffByPDU = false;
  poweredOffByIPMI = false;

  constructor(private httpClient: HttpClient) {}

  ngOnInit(): void {
    this.checkPowerStatus();
  }

  checkPowerStatus(): void {
    if (this.server.featureAvailability.powerCycle) {
      this.httpClient
        .get<any>(`/_/internal/bmpapi/v2/servers/${this.server.id}/powerInfo`, {})
        .subscribe((data: any) => {
          this.poweredOffByPDU = data.pdu?.status === 'off';
          this.poweredOffByIPMI = data.ipmi?.status === 'off';
          if (this.poweredOffByPDU || this.poweredOffByIPMI) {
            this.hasWarning = true;
          }
        });
    }
  }
}
