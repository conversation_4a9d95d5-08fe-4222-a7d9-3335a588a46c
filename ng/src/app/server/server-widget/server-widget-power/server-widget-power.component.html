<span i18n-title="@@bma_common_powerstatus" class="fa fa-powercycle {{ hasWarning ? 'text-danger' : 'text-muted' }}" (click)="hasWarning && panel.toggle($event)" title="Power Status"></span>
<p-overlayPanel #panel [dismissable]="true" [style]="{'width':'300px'}" [appendTo]="'body'">
    <ng-template pTemplate>
        <h3 i18n="@@bma_common_powerstatus">Power Status</h3>
        <ul class="list-inline">
            <li i18n="@@bma_common_pdupoweredoff" *ngIf="poweredOffByPDU">Server is powered off by PDU</li>
            <li i18n="@@bma_common_ipmipoweredoff" *ngIf="poweredOffByIPMI">Server is powered off by IPMI</li>
        </ul>
    </ng-template>
</p-overlayPanel>
