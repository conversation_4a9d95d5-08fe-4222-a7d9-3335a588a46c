<app-loader *ngIf="isLoading"></app-loader>

<div *ngIf="!isLoading" class="hardware-scan-wrapper">
    <h3 class="mb-5">
        <span class="fa fa-compute"></span>
        <span i18n="@@bma_common_hardwaredetails" class="mx-1">Hardware Details</span>
        <sup>
            <a href="https://kb.leaseweb.com/kb/dedicated-server/dedicated-server-viewing-dedicated-server-hardware-details/#ViewingDedicatedServerhardwaredetails-Hardwaredetails" target="_blank">
                <i i18n-title="@@bma_common_kb" title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i>
            </a>
        </sup>
    </h3>

    <div *ngIf="!currentHardwareScan">
        <table class="table table-striped">
            <tbody>
                <tr>
                    <td i18n="@@bma_serverhardwaredetails_none" class="text-center p-3">No Hardware Details available.</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div *ngIf="currentHardwareScan">
        <ul *ngIf="isEmployee" class="list-inline row md-5 mb-5">
            <li class="col text-center">
                <span class="fa fa-date mr-1"></span>
                <strong i18n="@@bma_serverhardwaredetails_scannedat">Scanned At</strong><br />
                {{ currentHardwareScan.scannedAt|lswDateTime }}
            </li>
            <li class="col text-center">
                <span class="fa fa-dashboard mr-1"></span>
                <strong i18n="@@bma_common_version">Version</strong><br />
                {{ currentHardwareScan.parserVersion }}
            </li>
            <li class="col text-center">
                <a *ngIf="currentHardwareScanRaw" class="btn btn-primary mr-1" role="button" href (click)="showDetails(); false"><span class="fa fa-log"></span><span i18n="@@bma_serverhardwaredetails_scandetails">Scan Details</span></a>
                <a *ngIf="!currentHardwareScanRaw" class="btn btn-primary mr-1" role="button" href (click)="showRawXml(); false"><span class="fa fa-log mr-1"></span><span i18n="@@bma_serverhardwaredetails_rawxml">Raw XML</span></a>
                <a *ngIf="!currentHardwareScanRaw" class="btn btn-primary" role="button" href (click)="showRawJson(); false"><span class="fa fa-log mr-1"></span><span i18n="@@bma_serverhardwaredetails_parseddata">Parsed Data</span></a>
            </li>
        </ul>

        <app-loader *ngIf="isLoadingXml"></app-loader>
        <div *ngIf="!isLoadingXml && currentHardwareScanRaw">
            <textarea [(ngModel)]="currentHardwareScanRaw" [rows]="currentHardwareScanRaw.split('\n').length + 5" readonly></textarea>
        </div>
        <div *ngIf="!isLoadingXml && !currentHardwareScanRaw">
            <div class="card mb-5">
                <div i18n="@@bma_common_chassis" class="card-header">Chassis</div>

                <div class="card-body">
                    <dl class="row mb-0">
                        <dt i18n="@@bma_common_product" class="col text-muted">Product</dt>
                        <dd class="col-4">
                            <span class="text-monospace">{{ currentHardwareScan.result?.chassis?.product|default: '-'}}</span>
                        </dd>

                        <dt i18n="@@bma_common_serialnumber" class="text-muted" style="min-width: 14%;">Serial Number</dt>
                        <dd class="col-5">
                            <span class="text-monospace">{{ currentHardwareScan.result?.chassis?.serial|default:'-' }}</span>
                        </dd>

                        <dt i18n="@@bma_common_vendor" class="col text-muted">Vendor</dt>
                        <dd class="col-4">
                            <span class="text-monospace">{{ currentHardwareScan.result?.chassis?.vendor|default:'-' }}</span>
                        </dd>

                        <dt i18n="@@bma_common_firmware" class="text-muted" style="min-width: 14%;">Firmware</dt>
                        <dd class="col-5">
                            <span class="text-monospace"> {{ currentHardwareScan.result?.chassis?.firmware?.description|default:'-' }} </span>&nbsp;

                            <app-firmware-version-check *ngIf="isEmployee && currentHardwareScan.result?.chassis?.firmwareVersionCheck" [check]="currentHardwareScan.result.chassis.firmwareVersionCheck"></app-firmware-version-check>
                            <strong i18n="@@bma_serverhardwaredetails_version" class="text-muted"> Version: </strong>

                            <span class="text-monospace">
                                {{ currentHardwareScan.result?.chassis?.firmware?.version|default:'-' }}
                            </span>

                            <strong i18n="@@bma_serverhardwaredetails_date" class="text-muted"> Date: </strong>
                            <span class="text-monospace">
                                {{ currentHardwareScan.result?.chassis?.firmware?.date ? currentHardwareScan.result.chassis.firmware.date : 'Unknown' }}
                            </span>
                        </dd>
                    </dl>
                </div>
            </div>

            <div class="card mb-5">
                <div class="card-header">
                    <span i18n="@@bma_serverhardwaredetails_cpu" class="mr-1">CPUs</span>
                    <small>({{ currentHardwareScan.result.cpu.length }})</small>
                </div>

                <div class="card-body">
                    <table class="table table-striped mb-0">
                        <thead>
                            <tr>
                                <th i18n="@@bma_common_slot" scope="col">Slot</th>
                                <th i18n="@@bma_common_vendor" scope="col">Vendor</th>
                                <th i18n="@@bma_common_description" scope="col">Description</th>
                                <th i18n="@@bma_common_cores" scope="col">Cores</th>
                                <th i18n="@@bma_common_threads" scope="col">Threads</th>
                                <th i18n="@@bma_common_virtualization" scope="col">Virtualization</th>
                            </tr>
                        </thead>

                        <tbody>
                            <tr *ngFor="let cpu of currentHardwareScan.result?.cpu">
                                <td>
                                    <span class="text-monospace">{{ cpu.slot|default:'-' }}</span>
                                </td>
                                <td>
                                    <span class="text-monospace">{{ cpu.vendor|default:'-' }}</span>
                                </td>
                                <td>
                                    <span class="text-monospace">{{ cpu.description|default:'-' }}</span>
                                </td>
                                <td>
                                    <span class="text-monospace">{{ cpu?.settings?.cores|default:'-' }}</span>
                                </td>
                                <td>
                                    <span class="text-monospace">{{ cpu?.settings?.threads|default:'-' }}</span>
                                </td>
                                <td>
                                    <ul>
                                        <li *ngIf="cpu?.capabilities?.ht">{{ cpu.capabilities.ht }}</li>
                                        <li *ngIf="cpu?.capabilities?.vmx">{{ cpu.capabilities.vmx }}</li>
                                        <li *ngIf="cpu?.capabilities?.cpufreq">{{ cpu.capabilities.cpufreq }}</li>
                                    </ul>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card mb-5">
                <div class="card-header">
                    <span i18n="@@bma_serverhardwaredetails_gpu" class="mr-1">GPUs</span>
                    <small>({{ currentHardwareScan.result.gpus.length  }})</small>
                </div>

                <div class="card-body">
                    <table class="table table-striped mb-0">
                        <thead>
                            <tr>
                                <th i18n="@@bma_common_product" scope="col">Product</th>
                                <th i18n="@@bma_common_vendor" scope="col">Vendor</th>
                                <th i18n="@@bma_common_description" scope="col">Description</th>
                                <th i18n="@@bma_common_mhz" scope="col">Mhz</th>
                            </tr>
                        </thead>

                        <tbody>
                            <tr *ngFor="let gpu of currentHardwareScan.result.gpus">
                                <td>
                                    <span class="text-monospace">{{ gpu.product|default:'-' }}</span>
                                </td>
                                <td>
                                    <span class="text-monospace">{{ gpu.vendor|default:'-'}}</span>
                                </td>
                                <td>
                                    <span class="text-monospace">{{ gpu.description|default:'-' }}</span>
                                </td>
                                <td>
                                    <span class="text-monospace">{{ gpu.hz * 0.000001 }}</span>
                                    <span>Mhz</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card mb-5">
                <div i18n="@@bma_serverhardwaredetails_memorybanks" class="card-header">
                    Memory Banks

                    <small> ({{ getUsedMemoryBanks(currentHardwareScan.result.memory) }} of {{ currentHardwareScan.result.memory.length }} in use, total {{ getTotalMemory(currentHardwareScan.result.memory) / 1024 / 1024 / 1024 }} GB) </small>
                </div>

                <div class="card-body">
                    <table class="table table-striped mb-0">
                        <thead>
                            <tr>
                                <th i18n="@@bma_common_memorybank" scope="col">Memory Bank</th>
                                <th i18n="@@bma_common_description" scope="col">Description</th>
                                <th i18n="@@bma_common_size" scope="col">Size</th>
                                <th i18n="@@bma_common_speed" scope="col">Speed</th>
                                <th i18n="@@bma_common_product" scope="col">Product</th>
                                <th i18n="@@bma_common_serialnumber" scope="col">Serial Number</th>
                            </tr>
                        </thead>

                        <tbody>
                            <tr *ngFor="let bank of currentHardwareScan.result.memory" [ngClass]="{'d-none': !bank.size_bytes && !currentHardwareScan.showEmptyMemoryBanks}">
                                <td>
                                    <span class="text-monospace">{{ bank.slot ? bank.slot : bank.id }}</span>
                                </td>
                                <td>
                                    <span>{{ bank.description }}</span>
                                </td>
                                <td>
                                    <span *ngIf="bank.size_bytes" class="text-monospace">{{ (bank.size_bytes / 1024 / 1024 / 1024) }} GB</span>
                                    <span *ngIf="!bank.size_bytes">-</span>
                                </td>
                                <td>
                                    <span *ngIf="bank.size_bytes" class="text-monospace">{{ getBankSpeed(bank.clock_hz) }}</span>
                                    <span *ngIf="!bank.size_bytes">-</span>
                                </td>
                                <td>
                                    <span *ngIf="bank.size_bytes" class="text-monospace">{{ bank.product}}</span>
                                    <span *ngIf="!bank.size_bytes">-</span>
                                </td>
                                <td>
                                    <span *ngIf="bank.size_bytes" class="text-monospace">{{ bank.serial_number }}</span>
                                    <span *ngIf="!bank.size_bytes">-</span>
                                </td>
                            </tr>
                        </tbody>

                        <tfoot>
                            <tr>
                                <td colspan="999" class="text-right">
                                    <a href="javascript:void(0);" (click)="currentHardwareScan.showEmptyMemoryBanks = !currentHardwareScan.showEmptyMemoryBanks">
                                        <span class="fa fa-show mr-1"></span>
                                        <span i18n="@@bma_common_availblebanks">Available banks</span>
                                    </a>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>

            <div *ngFor="let raidController of currentHardwareScan.result.raid_controllers" class="card mb-5">
                <div class="card-header">
                    <span i18n="@@bma_common_raidcontroller" class="mr-1">Raid Controller</span>
                    <small i18n="@@bma_common_disks_amount">({{ raidController.disks.length }} disks)</small>
                </div>

                <div class="card-body">
                    <dl class="row">
                        <dt i18n="@@bma_common_model" class="col-md-2 text-right text-muted">Model</dt>
                        <dd class="col-md-10">
                            <span class="text-monospace">{{ raidController.model|default:'-' }}</span>
                        </dd>

                        <dt i18n="@@bma_common_serialnumber" class="col-md-2 text-right text-muted">Serial Number</dt>
                        <dd class="col-md-10">
                            <span class="text-monospace">{{ raidController.serial|default:'-' }}</span>
                        </dd>

                        <dt i18n="@@bma_common_firmware" class="col-md-2 text-right text-muted">Firmware</dt>
                        <dd class="col-md-10">
                            <app-firmware-version-check *ngIf="isEmployee" [check]="raidController.firmwareVersionCheck"></app-firmware-version-check>
                            <span class="text-monospace">{{ raidController.firmware|default:'-' }}</span>
                        </dd>

                        <dt i18n="@@bma_common_batterystatus" class="col-md-2 text-right text-muted">Battery Status</dt>
                        <dd class="col-md-10">
                            <span class="text-monospace" [style.color]="getBatteryStatusColor(raidController.battery)">{{ raidController.battery|default:'-' }}</span>
                        </dd>

                        <dt i18n="@@bma_common_cachemodulestatus" class="col-md-2 text-right text-muted">Cache Module Status</dt>
                        <dd class="col-md-10">
                            <span class="text-monospace" [style.color]="getCacheModuleStatusColor(raidController.cachemodule)">{{ raidController.cachemodule|default:'-' }}</span>
                        </dd>
                    </dl>

                    <app-server-hardware-details-disks [raidController]="raidController" [disks]="raidController.disks" [isEmployee]="isEmployee"></app-server-hardware-details-disks>
                </div>
            </div>

            <div class="card mb-5">
                <div class="card-header">
                    <span i18n="@@bma_common_disks" class="mr-1">Disks</span>
                    <small>({{ disks.length }})</small>
                </div>

                <div class="card-body">
                    <app-server-hardware-details-disks *ngIf="disks.length" [disks]="disks" [isEmployee]="isEmployee"></app-server-hardware-details-disks>
                </div>
            </div>

            <div *ngIf="currentHardwareScan.result.disks.length" class="card mb-5">
                <div class="card-header">
                    <span i18n="@@bma_common_logicalvolumes" class="mr-1">Logical Volumes</span>
                    <small>({{ currentHardwareScan.result.disks.length }})</small>
                </div>

                <div class="card-body">
                    <table class="table table-collapsible table-striped mb-0">
                        <thead>
                            <tr>
                                <th i18n="@@bma_common_product">Product</th>
                                <th i18n="@@bma_common_size">Size</th>
                                <th i18n="@@bma_common_logicaldrive">Logical Drive</th>
                            </tr>
                        </thead>

                        <tbody>
                            <ng-container *ngFor="let disk of currentHardwareScan.result.disks">
                                <tr>
                                    <td>
                                        <span class="text-monospace">
                                            {{ disk.product|default:'unknown' }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-monospace">
                                            {{ ((disk.smartctl?.user_capacity|default:disk.size) / 1000 / 1000 / 1000)|round }}
                                            GB
                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-monospace">
                                            {{ disk.logicaldrive|default:'unknown' }}
                                        </span>
                                    </td>
                                </tr>
                            </ng-container>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card mb-5">
                <div class="card-header">
                    <span i18n="@@bma_common_networkinterfaces" class="mr-1">Network Interfaces</span>

                    <small>({{ currentHardwareScan.result.network.length }})</small>
                </div>

                <div class="card-body">
                    <table class="table table-collapsible table-striped mb-0">
                        <thead>
                            <tr>
                                <th i18n="@@bma_common_name" scope="col">Name</th>
                                <th i18n="@@bma_common_macaddress" scope="col">Mac Address</th>
                                <th i18n="@@bma_common_ipaddress" scope="col">IP Address</th>
                                <th i18n="@@bma_common_switchname" scope="col" *ngIf="isEmployee">Switch name</th>
                                <th i18n="@@bma_common_switchport" scope="col" *ngIf="isEmployee">Switch port</th>
                                <th i18n="@@bma_common_activelinkspeed" scope="col">Active Link speed</th>
                                <th i18n="@@bma_common_duples" scope="col">Duplex</th>
                                <th scope="col"></th>
                            </tr>
                        </thead>

                        <tbody>
                            <ng-container *ngFor="let nic of currentHardwareScan.result.network">
                                <tr>
                                    <td>
                                        <span class="text-monospace">{{ nic.logical_name|default:'-' }}</span>
                                    </td>

                                    <td>
                                        <span class="text-monospace">{{ nic.mac_address|default:'-' }}</span>
                                    </td>

                                    <td>
                                        <span class="text-monospace">{{ nic?.settings?.ip|default:'-' }}</span>
                                    </td>

                                    <td class="text-nowrap" *ngIf="isEmployee">
                                        <a *ngIf="nic.lldp?.chassis?.name" [routerLink]="['/networkDevices', nic.lldp.chassis.name]">
                                            <span class="text-monospace">
                                                {{ nic.lldp.chassis.name }}
                                            </span>
                                        </a>
                                        <span *ngIf="!nic.lldp?.chassis?.name">-</span>
                                    </td>

                                    <td *ngIf="isEmployee">
                                        <span class="text-monospace">{{ nic.lldp?.port?.description|default:'-' }}</span>
                                    </td>

                                    <td>
                                        <span class="text-monospace">{{ nic?.settings?.speed|default:'-' }}</span>
                                    </td>

                                    <td>
                                        <span class="text-monospace">{{ nic?.settings?.duplex|default:'-' }}</span>
                                    </td>
                                    <td class="text-right text-nowrap">
                                        <a href="javascript:void(0);" (click)="nic.viewMore = !nic.viewMore">
                                            <span class="fa fa-show mr-1"></span>
                                            <span i18n="@@bma_common_view_more">View more</span>
                                        </a>
                                    </td>
                                </tr>
                                <tr *ngIf="nic.viewMore">
                                    <td colspan="999">
                                        <div class="row">
                                            <div class="col-sm-3">
                                                <b i18n="@@bma_common_product">Product</b>
                                                <p>{{ nic.product|default:'-' }}</p>
                                            </div>
                                            <div class="col-sm-3">
                                                <b i18n="@@bma_common_vendor">Vendor</b>
                                                <p>{{ nic.vendor|default:'-' }}</p>
                                            </div>
                                            <div class="col-sm-3">
                                                <b i18n="@@bma_common_firmwareversion">Firmware Version</b>
                                                <p><app-firmware-version-check *ngIf="isEmployee" [check]="nic.firmwareVersionCheck"></app-firmware-version-check>{{ nic.settings.firmware }}</p>
                                            </div>
                                            <div class="col-sm-3">
                                                <b i18n="@@bma_common_connected">Connected</b>
                                                <p>{{ nic?.settings?.link|default:'-'}}</p>
                                            </div>
                                            <div class="col-sm-3">
                                                <b i18n="@@bma_common_lldp">LLDP</b>
                                                <p>{{ nic.lldp ? 'Available' : 'Not Available' }}</p>
                                            </div>
                                            <div class="col-sm-3">
                                                <b i18n="@@bma_common_availablelinkspeeds">Available Link speeds</b>
                                                <p>{{ nic.capabilities.link_speeds|values|join:', ' }}</p>
                                            </div>
                                            <div class="col-sm-3">
                                                <b i18n="@@bma_common_switchdescription">Switch Description</b>
                                                <p>{{ nic.lldp?.chassis?.description|default:"-" }}</p>
                                            </div>
                                            <div class="col-sm-3">
                                                <b i18n="@@bma_common_autonegotiation">Auto Negotiation</b>
                                                <p i18n="@@bma_common_networkinterfacesenabled">Enabled: {{ nic.lldp?.port?.auto_negotiation?.enabled|default:" " }}</p>
                                                <p i18n="@@bma_common_networkinterfacessupported">Supported: {{ nic.lldp?.port?.auto_negotiation?.supported|default:" " }}</p>
                                            </div>
                                            <div class="col-sm-3">
                                                <b i18n="@@bma_common_vlan">Vlan</b>
                                                <p>{{ nic.lldp?.vlan?.id }} ({{ nic.lldp?.vlan?.name }})</p>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </ng-container>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card mb-5" *ngIf="isEmployee">
                <div i18n="@@bma_common_ipmi" class="card-header">IPMI</div>

                <div class="card-body">
                    <dl class="row mb-0">
                        <dt i18n="@@bma_common_vendor" class="col-2 text-muted">Vendor</dt>
                        <dd class="col-2">
                            <span class="text-monospace">{{ currentHardwareScan.result?.ipmi?.vendor|default:'-' }}</span>
                        </dd>

                        <dt i18n="@@bma_common_macaddress" class="col-2 text-muted">Mac Address</dt>
                        <dd class="col-2">
                            <span class="text-monospace">{{ currentHardwareScan.result?.ipmi?.macaddress|default:'-' }}</span>
                        </dd>

                        <dt i18n="@@bma_common_gateway" class="col-2 text-muted">Gateway</dt>
                        <dd class="col-2">
                            <span class="text-monospace">{{ currentHardwareScan.result?.ipmi?.defgateway|default:'-' }}</span>
                        </dd>

                        <dt i18n="@@bma_common_firmwareversion" class="col-2 text-muted">Firmware Version</dt>
                        <dd class="col-2">
                            <app-firmware-version-check *ngIf="isEmployee" [check]="currentHardwareScan.result?.ipmi?.firmwareVersionCheck"></app-firmware-version-check>
                            <span class="text-monospace">{{ currentHardwareScan.result?.ipmi?.firmware|default:'-' }}</span>
                        </dd>

                        <dt i18n="@@bma_common_ipaddress" class="col-2 text-muted">IP Address</dt>
                        <dd class="col-2">
                            <span class="text-monospace">{{ currentHardwareScan.result?.ipmi?.ipaddress|default:'-' }}</span
                            ><br />
                            <span class="text-muted" *ngIf="currentHardwareScan.result?.ipmi?.ipaddress"> ({{ currentHardwareScan.result?.ipmi?.ipsource|default:'-' }}) </span>
                        </dd>

                        <dt i18n="@@bma_common_subnet" class="col-2 text-muted">Subnet</dt>
                        <dd class="col-2">
                            <span class="text-monospace">{{ currentHardwareScan.result?.ipmi?.subnetmask|default:'-' }}</span>
                        </dd>
                    </dl>
                </div>
            </div>

            <div *ngIf="isEmployee" class="card mb-5">
                <div class="card-header">
                    <span class="fa fa-compute mr-1"></span>
                    <span i18n="@@bma_common_hardwarescanhistory">Hardware Scan History</span>
                </div>
                <app-loader *ngIf="isLoadingList"></app-loader>
                <div class="card-body table-responsive" *ngIf="!isLoadingList && hardwareScans">
                    <table class="table hardwarescan-history-table table-striped mb-0">
                        <thead>
                            <tr>
                                <th i18n="@@bma_serverhardwaredetails_scannedat">Scanned At</th>
                                <th>&nbsp;</th>
                            </tr>
                        </thead>

                        <tbody>
                            <tr *ngFor="let hardwareScan of hardwareScans">
                                <td>{{ hardwareScan.scannedAt|timeAgo }} ({{ hardwareScan.scannedAt|lswDateTime }})</td>
                                <td class="text-right" *ngIf="hardwareScan.id != currentHardwareScan.id">
                                    <a class="btn btn-primary" role="button" [routerLink]="['.']" [queryParams]="{historyId: hardwareScan.id}"><span class="fa fa-log"></span> Scan Details</a>
                                </td>
                                <td i18n="@@bma_common_currenthardwarescan" class="text-right" *ngIf="hardwareScan.id == currentHardwareScan.id">Current Hardware Scan</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
