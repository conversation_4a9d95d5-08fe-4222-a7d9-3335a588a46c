import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-server-hardware-details-disks',
  templateUrl: './server-hardware-details-disks.component.html',
  styleUrls: ['./server-hardware-details-disks.component.css'],
})
export class ServerHardwareDetailsDisksComponent {
  @Input() disks: Array<any>;
  @Input() raidController: any;
  @Input() isEmployee = false;

  getSmartAttribute(disk: any, id: number): any {
    if (!disk.smartctl.attributes) {
      return null;
    }
    return Object.values(disk.smartctl.attributes)
      .filter((attribute: any) => +attribute.id === id)
      .pop();
  }
}
