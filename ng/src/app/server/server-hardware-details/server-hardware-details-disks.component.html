<div class="table-responsive">
    <table class="table table-collapsible table-striped mb-0">
        <thead>
            <tr>
                <th i18n="@@bma_common_product">Product</th>
                <th i18n="@@bma_common_serialnumber">Serial Number</th>
                <th i18n="@@bma_serverhardwaredetailsdisks_raidarray" *ngIf="raidController">RAID array</th>
                <th i18n="@@bma_serverhardwaredetailsdisks_raidlevel" *ngIf="raidController">RAID level</th>
                <th i18n="@@bma_common_firmware">Firmware</th>
                <th i18n="@@bma_common_size" class="text-right">Size</th>
                <th i18n="@@bma_common_diskbay" *ngIf="raidController">Diskbay</th>
                <th i18n="@@bma_common_rpm">RPM</th>
                <th i18n="@@bma_common_description" *ngIf="!raidController">Description</th>
                <th *ngIf="isEmployee">&nbsp;</th>
            </tr>
        </thead>
        <tbody>
            <ng-container *ngFor="let disk of disks">
                <tr [ngClass]="{'table-danger': isEmployee && disk.errors?.length > 0}">
                    <td>
                        <span class="text-monospace">
                            {{ disk.product|default:'unknown' }}
                        </span>
                    </td>
                    <td>
                        <span class="text-monospace">
                            {{ disk.serial_number|default:'unknown' }}
                        </span>
                    </td>
                    <td *ngIf="raidController">
                        <span class="text-monospace">
                            {{ disk.raid_array|default:'unknown' }}
                        </span>
                    </td>
                    <td *ngIf="raidController">
                        <span class="text-monospace">
                            {{ disk.raid_level|default:'unknown' }}
                        </span>
                    </td>
                    <td>
                        <app-firmware-version-check *ngIf="isEmployee" [check]="disk.firmwareVersionCheck" [keepIfEmpty]="true"></app-firmware-version-check>
                        <span class="text-monospace">{{ disk.smartctl?.firmware_version|default:'Not Available' }}</span>
                    </td>
                    <td class="text-right">
                        <span class="text-monospace">
                            {{ ((disk.smartctl?.user_capacity|default:disk.size) / 1000 / 1000 / 1000)|round }}
                            GB
                        </span>
                    </td>
                    <td *ngIf="raidController">
                        <span class="text-monospace">
                            {{ disk.diskbay|default:'unknown' }}
                        </span>
                    </td>
                    <td>
                        <span class="text-monospace">
                            {{ disk.smartctl?.is_ssd ? 'SSD' : disk.smartctl?.rpm }}
                        </span>
                    </td>
                    <td *ngIf="!raidController">
                        <span class="text-monospace">
                            {{ disk.description }}
                        </span>
                    </td>
                    <td *ngIf="isEmployee" class="text-right text-nowrap">
                        <a *ngIf="disk.smartctl" href="javascript:void(0);" (click)="disk.showSmartctl = !disk.showSmartctl">
                            <span class="fa fa-show mr-1"></span>
                            <span i18n="@@bma_common_view_more">View more</span>
                        </a>
                        <span i18n="@@bma_common_view_more" *ngIf="!disk.smartctl">View more</span>
                    </td>
                </tr>
                <tr *ngIf="disk.showSmartctl">
                    <td colspan="999">
                        <div class="row">
                            <div class="col-sm-3">
                                <b i18n="@@bma_common_smart">SMART</b>
                                <p>
                                    {{ disk.smartctl.smart_support.available ? 'Available' : 'Not available' }}
                                    /
                                    {{ disk.smartctl.smart_support.enabled ? 'Enabled' : 'Disabled' }}
                                    (v{{ disk.smartctl.smartctl_version|default:'' }})
                                </p>
                            </div>
                            <div class="col-sm-3">
                                <b i18n="@@bma_common_smarthealth">SMART Health</b>
                                <p>{{ disk.smartctl.overall_health|default:'n/a' }}</p>
                            </div>
                            <div class="col-sm-3">
                                <b i18n="@@bma_common_smarterror">Smart Error Log</b>
                                <p>{{ disk.smartctl.smart_error_log|default:'n/a' }}</p>
                            </div>
                            <div class="col-sm-3" *ngIf="!disk.smartctl.is_nvme">
                                <b i18n="@@bma_common_sataversion">SATA Version</b>
                                <p>{{ disk.smartctl.sata_version|default:"n/a" }}</p>
                            </div>
                            <div class="col-sm-3">
                                <b i18n="@@bma_common_currentpendingsector">Current Pending Sector</b>
                                <p>
                                    {{ disk.smartctl.attributes.Current_Pending_Sector ? disk.smartctl.attributes.Current_Pending_Sector.raw_value : 'n/a' }}
                                </p>
                            </div>
                            <div class="col-sm-3">
                                <b i18n="@@bma_common_reallocatedsectors">Reallocated Sectors</b>
                                <p>
                                    {{ disk.smartctl.attributes.Reallocated_Sector_Ct ? disk.smartctl.attributes.Reallocated_Sector_Ct.raw_value : 'n/a' }}
                                </p>
                            </div>
                            <div class="col-sm-3">
                                <b i18n="@@bma_common_usercapacity">User Capacity</b>
                                <p>
                                    {{ ((disk.smartctl.user_capacity|default:disk.size) / 1000 / 1000 / 1000)|round }}
                                    GB
                                </p>
                            </div>
                            <div class="col-sm-3">
                                <b i18n="@@bma_common_rpm">RPM</b>
                                <p>
                                    {{ disk.smartctl.is_ssd ? 'SSD' : disk.smartctl.rpm }}
                                </p>
                            </div>
                            <div class="col-sm-3" *ngIf="!disk.smartctl.is_ssd">
                                <b i18n="@@bma_common_offlinceuncorrectible">Offline Uncorrectable</b>
                                <p>
                                    {{ disk.smartctl.attributes.Offline_Uncorrectable ? disk.smartctl.attributes.Offline_Uncorrectable.raw_value : 'n/a' }}
                                </p>
                            </div>
                            <div class="col-sm-3">
                                <b i18n="@@bma_common_poweronhours">Power-on Hours</b>
                                <p *ngIf="disk.smartctl.attributes.Power_On_Hours">
                                    {{ disk.smartctl.attributes.Power_On_Hours.raw_value }}
                                </p>
                                <p *ngIf="disk.smartctl.attributes.Power_On_Hours_and_Msec">
                                    {{ disk.smartctl.attributes.Power_On_Hours_and_Msec.raw_value }}
                                </p>
                                <p *ngIf="!disk.smartctl.attributes.Power_On_Hours && !disk.smartctl.attributes.Power_On_Hours_and_Msec">n/a</p>
                            </div>
                            <div class="col-sm-3">
                                <b i18n="@@bma_common_latencytest">Latency Test</b>
                                <ul>
                                    <li i18n="@@bma_serverhardwaredetailsdisks_minimumlatency">Minimum: "{{ disk.tests.latency.min|default:'n/a' }}"</li>
                                    <li i18n="@@bma_serverhardwaredetailsdisks_maximumlatency">Maximum: "{{ disk.tests.latency.max|default:'n/a' }}"</li>
                                    <li i18n="@@bma_serverhardwaredetailsdisks_averagelatency">Average: "{{ disk.tests.latency.avg|default:'n/a' }}"</li>
                                </ul>
                            </div>
                            <div class="col-sm-3">
                                <b i18n="@@bma_serverhardwaredetailsdisks_selftest">Self Test</b>
                                <p>{{ disk.tests.self|default:'n/a' }}</p>
                            </div>
                            <div class="col-sm-3">
                                <b i18n="@@bma_serverhardwaredetailsdisks_readtest">Read Test</b>
                                <p>{{ disk.tests.read|default:'n/a' }}</p>
                            </div>
                            <div class="col-sm-3">
                                <b i18n="@@bma_serverhardwaredetailsdisks_writetest">Write Test</b>
                                <p>{{ disk.tests.write|default:'n/a' }}</p>
                            </div>
                            <div class="col-sm-3" *ngIf="disk.smartctl.is_nvme">
                                <b i18n="@@bma_serverhardwaredetailsdisks_criticalwarning">Critical Warning</b>
                                <p>{{ disk.smartctl.critical_warning ? 'Yes' : 'No' }}</p>
                            </div>
                            <div class="col-sm-3" *ngIf="disk.smartctl.is_nvme">
                                <b i18n="@@bma_serverhardwaredetailsdisks_availablespare">Available Spare</b>
                                <p>{{ disk.smartctl.available_spare|default:'n/a' }}</p>
                            </div>
                            <div class="col-sm-3" *ngIf="disk.smartctl.is_nvme">
                                <b i18n="@@bma_common_percetageused">Percentage Used</b>
                                <p>{{ disk.smartctl.percentage_used|default:'n/a' }}</p>
                            </div>
                            <div class="col-sm-6" *ngIf="disk.smartctl.is_ssd">
                                <b i18n="@@bma_serverhardwaredetailsdisks_dwpd">DWPD</b>
                                <p i18n="@@bma_serverhardwaredetailsdisks_dwpdpolicy" class="alert alert-warning" *ngIf="disk.smartctl.actualDwpd/disk.smartctl.guaranteedDwpd > 1.15">
                                    The actual DWPD is unusually high.<br />
                                    Check our
                                    <a href="https://wiki.ocom.com/pages/viewpage.action?pageId=250912041">Hard Disk Policy</a>.
                                </p>
                                <ul>
                                    <li i18n="@@bma_serverhardwaredetailsdisks_actual">Actual: {{ disk.smartctl.actualDwpd|round:2 }}</li>
                                    <li i18n="@@bma_serverhardwaredetailsdisks_guaranteed">Guaranteed: {{ disk.smartctl.guaranteedDwpd|round:2 }}</li>
                                    <li i18n="@@bma_serverhardwaredetailsdisks_percentage">Percentage: {{ (disk.smartctl.actualDwpd / disk.smartctl.guaranteedDwpd * 100)|round:2 }} %</li>
                                </ul>
                            </div>
                        </div>
                        <div class="alert alert-danger" *ngIf="isEmployee && disk.errors?.length > 0">
                            <div class="alert-icon">
                                <i class="fa fa-information fa-lg" aria-hidden="true"></i>
                            </div>
                            <h4 i18n="@@bma_serverhardwaredetailsdisks_diskerrors" class="alert-heading">Disk error(s)</h4>
                            <ul class="alert-body">
                                <li *ngFor="let diskError of disk.errors">{{ diskError }}</li>
                            </ul>
                        </div>
                    </td>
                </tr>
            </ng-container>
        </tbody>
    </table>
</div>
