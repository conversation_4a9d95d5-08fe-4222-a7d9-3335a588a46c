import { ActivatedRoute } from '@angular/router';
import { Component, OnInit, Input } from '@angular/core';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { HttpClient } from '@angular/common/http';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-server-hardware-details',
  templateUrl: './server-hardware-details.component.html',
  styleUrls: ['./server-hardware-details.component.css'],
})
export class ServerHardwareDetailsComponent implements OnInit {
  @Input() server: any;

  currentHardwareScan: any;
  disks: any;
  currentHardwareScanRaw: string;
  hardwareScans: Array<any> = [];
  isEmployee = false;
  isLoading = true;
  isLoadingList = false;
  isLoadingXml = false;

  constructor(
    private activatedRoute: ActivatedRoute,
    private httpClient: HttpClient,
    private titleService: Title,
    private currentUserService: CurrentUserService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.isLoading = true; // TODO this is temporary because the parent route sets it to false

    this.titleService.setTitle(
      `${this.server.id} - Hardware Details ${
        this.server.contract && this.server.contract.reference ? this.server.contract.reference : ''
      }| Leaseweb`
    );

    if (this.isEmployee) {
      this.isLoadingList = true;
      this.httpClient.get<any>(`/_/internal/bmsdb/v2/servers/${this.server.id}/hardwareScans`).subscribe({
        next: (data: any) => {
          this.isLoadingList = false;
          this.hardwareScans = data.hardwareScans;
        },
        error: (error: any) => {
          this.isLoadingList = false;
          this.hardwareScans = [];
        },
      });
    }

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    this.isLoading = true;
    this.currentHardwareScan = null;
    this.currentHardwareScanRaw = null;

    let url = `/_/internal/bmsdb/v2/servers/${this.server.id}/hardwareInfo`;
    if (queryParams.historyId) {
      url = `/_/internal/bmsdb/v2/servers/${this.server.id}/hardwareScans/${queryParams.historyId}`;
    }
    this.disks = [];
    this.httpClient.get(url).subscribe({
      next: (data: any) => {
        this.isLoading = false;
        this.currentHardwareScan = data;
        // Filtering disks on smartctl output to display non raid disks.
        this.disks = this.currentHardwareScan.result.disks.filter((disk) => disk.smartctl);
      },
      error: (error: any) => {
        this.isLoading = false;
        this.currentHardwareScan = null;
      },
    });
  }

  getTotalMemory(banks: Array<any>): number {
    let totalBytes = 0;
    for (const bank of banks) {
      totalBytes += +bank.size_bytes;
    }
    return totalBytes;
  }

  getUsedMemoryBanks(banks: Array<any>): number {
    return banks.filter((bank) => bank.description.indexOf('empty') === -1).length;
  }

  getBatteryStatusColor(status: string): string {
    switch (status) {
      case 'Failed':
        return 'red';
      case 'Recharging':
        return 'orange';
      default:
        return '';
    }
  }

  getCacheModuleStatusColor(status: string): string {
    switch (status) {
      case 'Failed':
        return 'red';
      case 'Degraded':
        return 'orange';
      default:
        return '';
    }
  }

  showRawJson() {
    this.currentHardwareScanRaw = JSON.stringify(this.currentHardwareScan, null, 2);
  }

  showRawXml() {
    this.isLoadingXml = true;
    this.httpClient
      .get(`/_/internal/bmsdb/v2/servers/${this.server.id}/hardwareScans/${this.currentHardwareScan.id}?format=xml`, {
        responseType: 'text',
      })
      .subscribe({
        next: (data: any) => {
          this.isLoadingXml = false;
          this.currentHardwareScanRaw = data;
        },
        error: (error: any) => {
          this.isLoadingXml = false;
          this.currentHardwareScanRaw = null;
        },
      });
  }

  showDetails() {
    this.currentHardwareScanRaw = null;
  }

  getBankSpeed(value) {
    if (/^\d+$/.test(value)) {
      return (value / 1000000).toString() + ' Mhz';
    }
    return value;
  }
}
