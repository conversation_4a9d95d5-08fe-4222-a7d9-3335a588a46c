import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-firmware-version-check',
  templateUrl: './firmware-version-check.component.html',
  styleUrls: ['./firmware-version-check.component.css'],
})
export class FirmwareVersionCheckComponent {
  @Input() check!: string;
  @Input() keepIfEmpty: boolean;

  getIcon(): string {
    switch (this.check) {
      case 'whitelisted':
        return 'fa-check';
      case 'blacklisted':
        return 'fa-alert';
      case 'unknown':
        return 'fa-information';
      default:
        return '';
    }
  }

  getTitle(): string {
    switch (this.check) {
      case 'unknown':
      case 'whitelisted':
        return $localize`:@@bma_firwareversioncheck_info:Info!`;
      case 'blacklisted':
        return $localize`:@@bma_firwareversioncheck_warning:Warning!`;
      default:
        return '';
    }
  }

  getText(): string {
    switch (this.check) {
      case 'blacklisted':
        return $localize`:@@bma_firwareversioncheck_blacklisted:This version is known to cause problems!<br/>If you have some useful information about this version, please send an email to the <a href="mailto:<EMAIL>">BMA Team</a>`;
      default:
        return $localize`:@@bma_firwareversioncheck_whitelisted:This version has no known outstanding issues to BMA Team and is considered ok.<br/>If you have some useful information about this version, please send an email to the <a href="mailto:<EMAIL>">BMA Team</a>.`;
    }
  }
}
