<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '800px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_common_configurehardwareraid" class="modal-title">Configure Hardware RAID</h3>
    </ng-template>

    <form [formGroup]="form" (ngSubmit)="configureHardwareRaid()" class="form-horizontal">
        <div class="modal-body">
            <div *ngIf="server.specs.brand == 'SUPERMICRO'" class="alert alert-warning" role="alert">
                <div class="alert-icon">
                    <i class="fa fa-information fa-lg" aria-hidden="true"></i>
                </div>
                <h4 i18n="@@bma_common_attention" class="alert-heading">Attention!</h4>
                <p i18n="@@bma_serverconfigurehardwareraidmodal_description1" class="alert-body">Configuring hardware RAID on Supermicro is only supported when target disk set is attached to the RAID controller.</p>
            </div>

            <p i18n="@@bma_common_assignedserver" *ngIf="server.contract">Dedicated server "{{ server.id }}" is used by customer "{{ server.contract.customerId }}".</p>
            <p i18n="@@bma_common_unassignedserver" *ngIf="!server.contract">Dedicated server "{{ server.id }}" is unassigned.</p>
            <div class="form-group">
                <label i18n="@@bma_common_raidlevel" class="col-form-label" for="raidLevel">Raid Level:</label>
                <select formControlName="raidLevel" class="form-control" id="raidLevel" autofocus>
                    <option i18n="@@bma_common_singledrivearray" value="single" selected>Single Drive Array</option>
                    <option i18n="@@bma_common_raid0" value="0">RAID 0</option>
                    <option i18n="@@bma_common_raid1" value="1">RAID 1</option>
                    <option i18n="@@bma_common_raid5" value="5">RAID 5</option>
                    <option i18n="@@bma_common_raid10" value="10">RAID 10</option>
                </select>
            </div>
            <div class="form-group">
                <label i18n="@@bma_common_diskset" class="col-form-label" for="diskSet">Disk set:</label>
                <select class="form-control" formControlName="diskSet" id="diskSet">
                    <option *ngFor="let diskSet of diskSets" value="{{ diskSet.id }}">{{ diskSet.id }} ( {{ diskSet.amount }}x {{ diskSet.size }}{{ diskSet.unit }} {{ diskSet.type }} {{ diskSet.performanceType|formatDiskPerformanceType }} )</option>
                </select>
            </div>
            <div class="form-group" *ngIf="form.get('raidLevel').value !== 'single'">
                <label i18n="@@bma_common_diskstouse" class="col-form-label" for="numberOfDisks">Number of disks to use:</label>
                <input class="form-control" formControlName="numberOfDisks" type="number" id="numberOfDisks" />
                <small i18n="@@bma_common_numberofdisksnone" class="text-muted font-italic">if no number of disks is provided, all disks in the disk set will be used</small>
            </div>

            <div class="power-cycle">
                <strong i18n="@@bma_common_rebootalert">Please note that you need to reboot the dedicated server for the process to complete</strong>
                <div class="form-check">
                    <input class="form-check-input" formControlName="powerCycle" type="checkbox" id="powerCycle" />
                    <label i18n="@@bma_common_powercycle" class="form-check-label" for="powerCycle">Power Cycle</label>
                </div>
            </div>

            <div i18n="@@bma_common_dataerasealert" class="alert alert-danger" role="alert">Starting the process will cause all data on all disks to be lost.</div>

            <div class="form-group" *ngIf="server.contract">
                <label i18n="@@bma_common_customerconfirmation" for="customerIdConfirmation">Please type in the customer number to confirm</label>
                <input type="text" formControlName="customerIdConfirmation" class="form-control" id="customerIdConfirmation" autocomplete="off" autofocus />
            </div>
        </div>
    </form>

    <div *ngIf="form.errors?.api?.errorMessage" class="alert alert-danger" role="alert">
        <span>{{ form.errors.api.errorMessage }}</span>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_cancel" pButton (click)="closeModal()" type="button" icon="pi pi-times" iconPos="left" label="Cancel" class="p-button-secondary"></button>
            <button i18n-label="@@bma_common_launch" pButton (click)="configureHardwareRaid()" label="Launch" icon="pi pi-check" [loading]="isSubmitting" [disabled]="form.invalid || !isCustomerConfirmationValid()"></button>
        </div>
    </ng-template>
</p-dialog>
