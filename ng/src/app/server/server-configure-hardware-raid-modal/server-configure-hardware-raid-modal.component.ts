import { Component, OnInit } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-server-configure-hardware-raid-modal',
  templateUrl: './server-configure-hardware-raid-modal.component.html',
  styleUrls: ['./server-configure-hardware-raid-modal.component.css'],
})
export class ServerConfigureHardwareRaidModalComponent implements OnInit {
  server: any;
  diskSets: Array<any>;
  form: UntypedFormGroup;
  isSubmitting = false;
  isEmployee = false;
  showDialog = true;

  constructor(
    private alertService: AlertService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.server = this.config.data.server;
    this.diskSets = this.getUsableDiskSets();
    this.isEmployee = this.currentUserService.isEmployee();
    this.form = this.formBuilder.group({
      raidLevel: ['single', Validators.required],
      diskSet: [this.diskSets[0].id, Validators.required],
      numberOfDisks: [],
      powerCycle: [true, Validators.required],
      customerIdConfirmation: [],
    });
  }

  getUsableDiskSets(): Array<any> {
    return this.server.specs.hdd.filter((hdd) => hdd.type !== 'NVME');
  }

  configureHardwareRaid(): void {
    if (this.form.invalid) {
      return;
    }

    this.isSubmitting = true;
    const raidLevel = this.form.get('raidLevel').value;

    const body: any = {
      powerCycle: this.form.get('powerCycle').value,
      type: raidLevel === 'single' ? 'NONE' : 'HW',
      device: this.form.get('diskSet').value,
    };

    if (raidLevel !== 'single') {
      body.level = parseInt(raidLevel, 10);

      const numberOfDisks = parseInt(this.form.get('numberOfDisks').value, 10);
      if (numberOfDisks) {
        body.numberOfDisks = numberOfDisks;
      }
    }

    this.httpClient.post<any>(`/_/internal/bmpapi/v2/servers/${this.server.id}/configureHardwareRaid`, body).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;
        if (this.isEmployee) {
          this.router.navigate([`/servers/${this.server.id}/jobs/${data.uuid}`], { skipLocationChange: false });
        }
        this.alertService.alert({
          type: 'success',
          description: $localize`:@@bma_serverconfigurehardwareraidmodal_success:Scheduled a hardware RAID configuration for dedicated server ${this.server.id}.`,
        });
        this.closeModal();
      },
      error: (error: any) => {
        this.isSubmitting = false;
        this.form.setErrors({ api: error.error });
      },
    });
  }

  isCustomerConfirmationValid(): boolean {
    if (!this.server.contract) {
      return true;
    }
    return this.server.contract.customerId === this.form.get('customerIdConfirmation').value;
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }
}
