<app-loader *ngIf="isLoading || isRunningInstallation"></app-loader>

<ng-container *ngIf="!isLoading && !isRunningInstallation">
    <div *ngIf="hasRunningJob === true">
        <div class="alert alert-danger" role="alert">
            <div class="alert-icon">
                <i class="fa fa-information fa-lg" aria-hidden="true"></i>
            </div>
            <h4 i18n="@@bma_common_activejob" class="alert-heading">Active Job</h4>
            <span class="alert-body">
                <p i18n="@@bma_serverinstallwizard_warning1">There is another running job at the moment.</p>
                <p i18n="@@bma_serverinstallwizard_warning2">You can start installation once this job is finished.</p>
            </span>
        </div>
    </div>

    <p-accordion multiple="true" *ngIf="!installationLaunched && hasRunningJob === false">
        <p-accordionTab i18n-header="@@bma_common_software" header="Software" [(selected)]="formOpenStatus.software">
            <app-server-install-wizard-software [server]="server" (dataUpdated)="onSoftwareDataUpdated($event)" (submitted)="onSoftwareSubmitted($event)"></app-server-install-wizard-software>
        </p-accordionTab>
        <p-accordionTab id="partitioningAccordion" i18n-header="@@bma_common_raid_and_disk_partitioning" header="RAID and Disk Partitioning" [(selected)]="formOpenStatus.partitioning">
            <app-server-install-wizard-partitioning [server]="server" [operatingSystemDefaults]="operatingSystemDefaults" (dataUpdated)="onPartitioningDataUpdated($event)" (submitted)="onPartitioningSubmitted($event)"></app-server-install-wizard-partitioning>
        </p-accordionTab>
        <p-accordionTab id="settingsAccordion" i18n-header="@@bma_common_settings" header="Settings" [(selected)]="formOpenStatus.settings">
            <app-server-install-wizard-settings [server]="server" [operatingSystemDefaults]="operatingSystemDefaults" (dataUpdated)="onSettingsDataUpdated($event)" (submitted)="onSettingsSubmitted($event)"></app-server-install-wizard-settings>
        </p-accordionTab>
        <p-accordionTab id="confirmationAccordion" i18n-header="@@bma_common_confirmation" header="Confirmation" [(selected)]="formOpenStatus.confirmation">
            <app-server-install-wizard-confirmation [server]="server" [payload]="getPayload()" [configs]="configs" [operatingSystem]="selectedOperatingSystem" (submitted)="onConfirmationSubmitted($event)"></app-server-install-wizard-confirmation>
        </p-accordionTab>
    </p-accordion>

    <div class="alert alert-success" role="alert" *ngIf="jobUuid">
        <div class="alert-icon">
            <i class="fa fa-information fa-lg" aria-hidden="true"></i>
        </div>
        <h4 i18n="@@bma_common_success" class="alert-heading">Success!</h4>
        <p i18n="@@bma_serverinstallwizard_description1" class="alert-body">You have successfully requested installation of "{{ selectedOperatingSystem.name }}".</p>

        <p i18n="@@bma_serverinstallwizard_description2" *ngIf="isEmployee" class="alert-body">
            Go to
            <a [routerLink]="['/servers', server.id, 'jobs', jobUuid]"> installation job page </a>
            to see more details.
        </p>
    </div>

    <div class="alert alert-danger" role="alert" *ngIf="error">
        <p i18n="@@bma_common_contactsupport" *ngIf="!error.correlationId">Something went wrong, please contact support.</p>

        <div *ngIf="error.correlationId">
            <p i18n="@@bma_serverinstallwizard_description3" class="text-danger">An error occurred during installation, due to the following reason:</p>
            <p>
                <strong>{{ error.errorMessage }}</strong>
            </p>
            <p i18n="@@bma_serverinstallwizard_description4">Please use the following error reference ID while contacting support: "{{ error.correlationId }}".</p>
        </div>
    </div>
</ng-container>
