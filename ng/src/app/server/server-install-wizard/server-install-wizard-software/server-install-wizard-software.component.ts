import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, UntypedFormControl, Validators } from '@angular/forms';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { FormatDatabaseTypePipe } from 'src/app/pipes/format-database-type.pipe';

@Component({
  selector: 'app-server-install-wizard-software',
  templateUrl: './server-install-wizard-software.component.html',
  styleUrls: ['./server-install-wizard-software.component.css'],
})
export class ServerInstallWizardSoftwareComponent implements OnInit {
  @Input() server: any;
  @Output() dataUpdated = new EventEmitter<any>();
  @Output() submitted = new EventEmitter<any>();
  operatingSystems: any;
  isEmployee = false;
  isLoadingOperatingSystems = true;
  loadingOperatingSystemDefaults = true;
  operatingSystemDefaults: any;
  selectedOperatingSystem: any;
  controlPanels = [];
  form: UntypedFormGroup;
  dbForm: UntypedFormGroup;
  databaseTypes: any;
  aiFrameworks: any;
  shownAiFrameworks: any;
  networkTypes = [];
  extraSoftware = [];

  constructor(
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    private formatDatabaseTypePipe: FormatDatabaseTypePipe
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();

    this.form = this.formBuilder.group({
      operatingSystem: [null, Validators.required],
      extra: [null],
    });

    this.buildNetworkTypes();
    this.dbForm = this.formBuilder.group({
      password: [null, Validators.required],
      networkType: [this.networkTypes[0].value, Validators.required],
    });

    this.dbForm.valueChanges.subscribe((value) => {
      this.dataUpdated.emit({
        selectedOperatingSystem: this.selectedOperatingSystem,
        payload: this.getPayload(),
      });
    });

    this.getOperatingSystems();
  }

  onExtraSoftwareChange(event): void {
    if (this.databaseTypes?.includes(this.form.get('extra').value)) {
      this.modifyDbFormControls();
    }
    this.dataUpdated.emit({ payload: this.getPayload() });
  }

  modifyDbFormControls(): void {
    if (this.form.get('extra').value === 'REDIS') {
      this.dbForm.removeControl('dbName');
      this.dbForm.removeControl('username');
    } else {
      this.dbForm.addControl('dbName', new UntypedFormControl('', Validators.required));
      this.dbForm.addControl('username', new UntypedFormControl('', Validators.required));
    }
  }

  getOperatingSystems(): void {
    this.isLoadingOperatingSystems = true;
    this.httpClient.get(`/_/bareMetals/v2/operatingSystems`, {}).subscribe({
      next: (data: any) => {
        this.isLoadingOperatingSystems = false;
        if (data.operatingSystems) {
          this.operatingSystems = this.filterLicensedWindowsOs(data.operatingSystems);
          this.form.get('operatingSystem').setValue(this.operatingSystems[0].id);
          this.getOperatingSystemDefaults();
        }
      },
      error: (error: any) => {
        this.isLoadingOperatingSystems = false;
        this.operatingSystems = [];
      },
    });
  }

  filterLicensedWindowsOs(operatingSystems: any[]): any[] {
    const serverSoftwareLicenses = this.server.contract?.softwareLicenses ?? [];

    // Prepare the list of licensed operating systems
    const licensedWindowsOS = serverSoftwareLicenses.map((license) => {
      if (license.name.toLowerCase().includes('windows')) {
        const licenseNameParts = license.name.split('-');
        const licenseName = licenseNameParts[0]
          .replace(/[^a-zA-Z0-9]/g, '')
          .replace(/server/gi, '')
          .toLowerCase()
          .replace(/(standard|datacenter).*/i, '$1');
        return licenseName;
      }
    });

    // Filter the operating systems to include only those that are licensed
    return operatingSystems.filter((os) => {
      if (os.name.toLowerCase().includes('windows')) {
        const osName = os.name
          .split('(')[0]
          .replace(/server/gi, '')
          .replace(/[^a-zA-Z0-9]/g, '')
          .toLowerCase();
        return licensedWindowsOS.includes(osName);
      } else {
        return true; // keep non-Windows OSes
      }
    });
  }

  getOperatingSystemDefaults(): void {
    this.form.get('extra').reset();
    this.dbForm.reset({ networkType: this.networkTypes[0].value });

    this.loadingOperatingSystemDefaults = true;
    this.selectedOperatingSystem = this.operatingSystems.filter(
      (operatingSystem) => operatingSystem.id === this.form.get('operatingSystem').value
    )[0];
    this.httpClient
      .get<any>(`/_/bareMetals/v2/operatingSystems/${this.form.get('operatingSystem').value}`, {})
      .subscribe(
        (data: any) => {
          this.loadingOperatingSystemDefaults = false;
          this.operatingSystemDefaults = data;
          if (data.defaults) {
            this.operatingSystemDefaults.timezone = 'UTC';
            this.operatingSystemDefaults.powerCycle = true;
            this.operatingSystemDefaults.doEmailNotification = true;
            for (const osDefault in data.defaults) {
              if (data.defaults[osDefault]) {
                this.operatingSystemDefaults[osDefault] = data.defaults[osDefault];
              }
            }
            this.databaseTypes = this.operatingSystemDefaults.features
              .filter((feature) => feature.startsWith('DB_'))
              .map((feature) => feature.split('_').pop());
            this.aiFrameworks = this.operatingSystemDefaults.features
              .filter((feature) => feature.startsWith('AI_FRAMEWORK_'))
              .map((feature) => {
                let displayName = feature.split('FRAMEWORK_').pop().replace('_', ' ');
                if (displayName === 'HUGGING FACE') {
                  displayName = 'HUGGING FACE (PYTORCH, TENSORFLOW, KERAS, FLAX)';
                }
                return {
                  displayName,
                  value: feature.split('FRAMEWORK_').pop(),
                };
              });
            this.shownAiFrameworks = this.aiFrameworks;
            this.getControlPanels();

            this.operatingSystemDefaults.filesystems = this.operatingSystemDefaults.supportedFileSystems;
            this.dataUpdated.emit({
              selectedOperatingSystem: this.selectedOperatingSystem,
              operatingSystemDefaults: this.operatingSystemDefaults,
              payload: this.getPayload(),
            });
          }
        },
        (error: any) => {
          this.loadingOperatingSystemDefaults = false;
          this.operatingSystemDefaults = [];
        }
      );
  }

  getControlPanels(): void {
    // Control panels are not supported on ARM servers
    if (/HP TODO ARM/i.test(this.server.specs.chassis)) {
      this.buildExtraSoftwareList();
      return;
    }

    let customerCpLicenses = [];
    if (this.server.contract) {
      customerCpLicenses = this.server.contract.softwareLicenses.map((el) => el.name);
    }

    this.httpClient
      .get(`/_/bareMetals/v2/controlPanels?operatingSystemId=${this.form.get('operatingSystem').value}`, {})
      .subscribe(
        (data: any) => {
          if (data.controlPanels) {
            const controlPanelCheck = (cpLicenses) => (element, index, array) => cpLicenses.includes(element.name);
            this.controlPanels = data.controlPanels.filter(controlPanelCheck(customerCpLicenses));
          } else {
            this.controlPanels = [];
          }
          this.buildExtraSoftwareList();
        },
        (error: any) => {
          this.loadingOperatingSystemDefaults = false;
          this.controlPanels = [];
        }
      );
  }

  buildExtraSoftwareList(): void {
    this.extraSoftware = [];

    if (this.controlPanels.length > 0) {
      const controlPanelItems = [];
      for (const controlPanel of this.controlPanels) {
        controlPanelItems.push({ label: controlPanel.name, value: 'controlPanel.' + controlPanel.id });
      }
      this.extraSoftware.push({ label: 'Control panels', items: controlPanelItems });
    }

    if (this.databaseTypes.length > 0) {
      const databases = [];
      for (const databaseType of this.databaseTypes) {
        databases.push({ label: this.formatDatabaseTypePipe.transform(databaseType), value: databaseType });
      }
      this.extraSoftware.push({ label: 'Databases', items: databases });
    }
  }

  buildNetworkTypes(): void {
    this.networkTypes = [];

    if (this.server.isPrivateNetworkEnabled) {
      // If available we put INTERNAL as first option as this will be the default
      this.networkTypes.push({ label: 'Internal', value: 'INTERNAL' });
    }

    this.networkTypes.push({ label: 'Public', value: 'PUBLIC' });
  }

  getPayload(): any {
    const payload: any = {
      operatingSystemId: this.selectedOperatingSystem?.id,
    };

    if (this.form.get('extra').value?.startsWith('controlPanel.')) {
      payload.controlPanelId = this.form.get('extra').value.split('.')[1];
    }

    if (this.databaseTypes?.includes(this.form.get('extra').value)) {
      payload.database = this.dbForm.getRawValue();
      payload.database.type = this.form.get('extra').value;
    }

    return payload;
  }

  onAiFrameworksChange(event) {
    const selectedFrameworks = event.value;
    const payload = this.getPayload();
    if (selectedFrameworks.length === 0) {
      this.shownAiFrameworks = this.aiFrameworks;
      delete payload.aiFrameworks;
      this.dataUpdated.emit({ payload });
      return;
    } else if (selectedFrameworks.includes('NVIDIA_NGC') || selectedFrameworks.includes('HUGGING_FACE')) {
      this.shownAiFrameworks = this.aiFrameworks.filter((framework) => framework.value === selectedFrameworks[0]);
    } else {
      this.shownAiFrameworks = this.aiFrameworks.filter(
        (framework) => framework.value !== 'NVIDIA_NGC' && framework.value !== 'HUGGING_FACE'
      );
    }
    payload.aiFrameworks = selectedFrameworks;
    this.dataUpdated.emit({ payload });
  }

  next() {
    this.submitted.emit();
  }
}
