<app-loader *ngIf="isLoadingOperatingSystems"></app-loader>
<form *ngIf="!isLoadingOperatingSystems" [formGroup]="form" class="row">
    <div class="col-sm-12">
        <div class="alert alert-warning" role="alert">
            <div class="alert-icon">
                <i class="fa fa-information fa-lg" aria-hidden="true"></i>
            </div>
            <h4 i18n="@@bma_common_attention" class="alert-heading">Attention!</h4>
            <p i18n="@@bma_serverinstallwizardsoftware_warning" class="alert-body">Only control panels with existing licenses and compatible for this operating system are listed in the dropdown. If you want another control panel for this installation, please order it from the dedicated server details page.</p>
            <p i18n="@@bma_common_buy_spla_for_windows" class="alert-body">For the Windows operating system, it's necessary to purchase an SPLA (Service Provider License Agreement) separately.</p>
            <div *ngIf="operatingSystemDefaults?.endOfLife">
                <h4 i18n="@@bma_common_endoflife" class="alert-heading">End of life</h4>
                <p class="alert-body">
                    <b>{{ operatingSystemDefaults.name }} </b>
                    <span i18n="@@bma_common_willbeeol">will be end of life in</span>
                    <b> {{ operatingSystemDefaults.endOfLife|lswDate:"" }}. </b>
                    <span i18n="@@bma_common_endsupport">Be aware that we will be dropping support for this operating system after this date.</span>
                </p>
            </div>
        </div>
        <div class="form-group">
            <label i18n="@@bma_common_os" for="operatingSystem" class="mr-1">Operating System</label>
            <sup>
                <i class="buy-spla-for-windows fa fa-knowledge" aria-hidden="true">
                    <span class="tooltip-text">
                        <p i18n="@@bma_common_buy_spla_for_windows_instruction">You can purchase SPLA on the Server Details page, under the Administrative Details section. There, you'll find a link labelled 'Order Windows License' located next to the Software Licenses. Please note, only the Windows versions that already have an SPLA are listed below.</p>
                        <p i18n="@@bma_common_cancel_spla_for_windows" class="alert-body">It is crucial to remember to cancel the SPLA separately when it is no longer needed. This can be done by opening a support ticket. It is important because if this step is not taken, you will continue to be billed for the SPLA, even if you are no longer using it.</p>
                    </span>
                </i>
            </sup>
            <select formControlName="operatingSystem" id="operatingSystem" class="form-control" (change)="getOperatingSystemDefaults()" autofocus>
                <option *ngFor="let operatingSystem of operatingSystems" value="{{ operatingSystem.id }}">
                    {{ operatingSystem.name }}
                </option>
            </select>
        </div>

        <div *ngIf="extraSoftware.length > 0" class="form-group">
            <label for="extra">
                <span i18n="@@bma_serverinstallwizardsoftware_extrasoftware" class="mr-1">Extra software</span>
                <sup>
                    <a target="_blank" href="https://kb.leaseweb.com/products/dedicated-server/reinstalling-your-dedicated-server#extraSoftware">
                        <i title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i>
                    </a>
                </sup>
            </label>
            <p-dropdown i18n-placeholder="@@bma_serverinstallwizardsoftware_selectextrasoftware" id="extra" (onChange)="onExtraSoftwareChange()" formControlName="extra" [options]="extraSoftware" [showClear]="true" placeholder="Select an extra software" [group]="true"></p-dropdown>
        </div>

        <div *ngIf="databaseTypes?.includes(form.get('extra').value)" [formGroup]="dbForm">
            <div *ngIf="dbForm.get('dbName')" class="row form-group">
                <label i18n="@@bma_serverinstallwizard_databasename" class="col-2 col-form-label text-right" for="dbName">Database name</label>
                <input type="text" id="dbName" formControlName="dbName" class="col-4 form-control" />
            </div>

            <div *ngIf="dbForm.get('username')" class="row form-group">
                <label i18n="@@bma_common_username" class="col-2 col-form-label text-right" for="username">Username</label>
                <input type="text" id="username" formControlName="username" class="col-4 form-control" />
            </div>

            <div class="row form-group">
                <label i18n="@@bma_common_password" class="col-2 col-form-label text-right" for="password">Password</label>
                <input type="text" id="password" formControlName="password" class="col-4 form-control" />
            </div>

            <div class="row form-group">
                <ng-container *ngIf="networkTypes.length > 1; else singleNetworkType">
                    <label i18n="@@bma_common_networktype" class="col-2 col-form-label text-right" for="networkType">Network Type</label>
                    <select id="networkType" formControlName="networkType" class="col-4 form-control">
                        <option *ngFor="let networkType of networkTypes" value="{{ networkType.value }}">{{ networkType.label }}</option>
                    </select>
                </ng-container>
                <ng-template #singleNetworkType>
                    <label i18n="@@bma_common_networktype" class="col-2 text-right" for="networkType">Network Type</label>
                    <span class="col-6">{{networkTypes[0].label}}</span>
                    <input type="hidden" id="networkType" formControlName="networkType" value="{{networkTypes[0].value}}" />
                </ng-template>
            </div>
        </div>

        <div *ngIf="!loadingOperatingSystemDefaults && shownAiFrameworks.length > 0" class="form-group">
            <p-multiSelect [options]="shownAiFrameworks" display="chip" placeholder="AI Frameworks" (onChange)="onAiFrameworksChange($event)" optionLabel="displayName" optionValue="value" [maxSelectedLabels]="aiFrameworks.length" />
        </div>

        <button i18n-label="@@bma_common_nextstep" pButton type="button" label="Next step" icon="pi pi-chevron-right" iconPos="right" class="p-button-primary float-right" (click)="next()"></button>
    </div>
</form>
