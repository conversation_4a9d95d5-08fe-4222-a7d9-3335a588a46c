import { Component, Input, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { CurrentUserService } from 'src/app/services/current-user.service';

@Component({
  selector: 'app-server-install-wizard',
  templateUrl: './server-install-wizard.component.html',
  styleUrls: ['./server-install-wizard.component.css'],
})
export class ServerInstallWizardComponent implements OnInit {
  @Input() server: any;
  isEmployee = false;
  selectedOperatingSystem: any;
  operatingSystemDefaults: any;
  formOpenStatus = {
    software: true,
    partitioning: false,
    settings: false,
    confirmation: false,
  };
  payloads = {
    software: {},
    partitioning: {},
    settings: {},
  };
  configs = {
    partitioning: {},
    settings: {},
  };
  error: any;
  jobUuid: any;
  hasRunningJob = null;
  installationLaunched = false;
  isRunningInstallation = false;
  isLoading = false;

  constructor(
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.checkForRunningJob();
  }

  getPayload(): any {
    return {
      ...this.payloads.software,
      ...this.payloads.partitioning,
      ...this.payloads.settings,
    };
  }

  checkForRunningJob(): void {
    this.isLoading = true;
    this.httpClient.get<any>(`/_/internal/bmpapi/v2/servers/${this.server.id}/jobs?isRunning=true`).subscribe({
      next: (data: any) => {
        this.isLoading = false;
        this.hasRunningJob = data._metadata.totalCount !== 0;
      },
      error: (error: any) => {
        this.isLoading = false;
      },
    });
  }

  onSoftwareSubmitted(event) {
    this.formOpenStatus.partitioning = true;
    this.scrollToAccordion('partitioningAccordion');
  }

  onPartitioningSubmitted(event) {
    this.formOpenStatus.settings = true;
    this.scrollToAccordion('settingsAccordion');
  }

  onSettingsSubmitted(event) {
    this.formOpenStatus.confirmation = true;
    this.scrollToAccordion('confirmationAccordion');
  }

  onConfirmationSubmitted(event) {
    this.startInstallation();
  }

  onSoftwareDataUpdated(event) {
    if (event?.selectedOperatingSystem) {
      this.selectedOperatingSystem = event.selectedOperatingSystem;
    }
    if (event?.operatingSystemDefaults) {
      this.operatingSystemDefaults = event.operatingSystemDefaults;
    }
    this.updateData('software', event);
  }

  onPartitioningDataUpdated(event) {
    this.updateData('partitioning', event);
  }

  onSettingsDataUpdated(event) {
    this.updateData('settings', event);
  }

  updateData(tab, event) {
    if (event?.config) {
      this.configs[tab] = event.config;
    }
    if (event?.payload) {
      this.payloads[tab] = event.payload;
    }
  }

  scrollToAccordion(accordionId: string): void {
    setTimeout(() => {
      document.getElementById(accordionId).scrollIntoView({ behavior: 'smooth' });
    }, 500);
  }

  startInstallation(): void {
    this.installationLaunched = true;
    this.isRunningInstallation = true;
    this.httpClient.post<any>(`/_/bareMetals/v2/servers/${this.server.id}/install`, this.getPayload()).subscribe({
      next: (data: any) => {
        this.isRunningInstallation = false;
        this.jobUuid = data.uuid;
      },
      error: (error: any) => {
        this.isRunningInstallation = false;
        this.error = error.error;
      },
    });
  }
}
