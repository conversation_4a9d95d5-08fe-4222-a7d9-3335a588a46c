import { Component, Input, Output, EventEmitter } from '@angular/core';
import { ModalService } from 'src/app/services/modal.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ServerInstallWizardConfirmationModalComponent } from '../server-install-wizard-confirmation-modal/server-install-wizard-confirmation-modal.component';

@Component({
  selector: 'app-server-install-wizard-confirmation',
  templateUrl: './server-install-wizard-confirmation.component.html',
  styleUrls: ['./server-install-wizard-confirmation.component.css'],
})
export class ServerInstallWizardConfirmationComponent {
  @Input() server: any;
  @Input() operatingSystem: any;
  @Input() payload: any;
  @Input() configs: any;
  @Input() partitionMode: any;
  @Output() submitted = new EventEmitter<any>();
  dynamicDialogRef: DynamicDialogRef;

  constructor(private modalService: ModalService) {}

  openInstallConfirmationModal() {
    this.dynamicDialogRef = this.modalService.show(ServerInstallWizardConfirmationModalComponent, {
      serverId: this.server.id,
      customerId: this.server.contract?.customerId,
      doEmailNotification: this.payload.doEmailNotification,
    });

    this.dynamicDialogRef.onClose.subscribe((data: any) => {
      if (data?.startInstallation === true) {
        this.submitted.emit();
      }
    });
  }
}
