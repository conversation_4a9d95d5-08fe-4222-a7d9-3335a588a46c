<div *ngIf="operatingSystem && payload" class="row">
    <div class="col-sm-12">
        <h3 i18n="@@bma_common_software">Software</h3>
        <div class="row">
            <div i18n="@@bma_common_os" class="col-sm-3 text-right text-primary">Operating System</div>
            <div class="col-sm-6">{{ operatingSystem.name }}</div>
        </div>
        <div class="row" *ngIf="payload?.controlPanelId">
            <div i18n="@@bma_common_cpsoftware" class="col-sm-3 text-right text-primary">Control Panel Software</div>
            <div class="col-sm-6">{{ payload.controlPanelId }}</div>
        </div>
        <div class="row" *ngIf="payload?.database">
            <div i18n="@@bma_common_database" class="col-sm-3 text-right text-primary">Database</div>
            <div class="col-sm-6">
                <div i18n="@@bma_serverinstallwizardconfirmation_type">Type: {{ payload.database.type|formatDatabaseType }}</div>
                <div i18n="@@bma_serverinstallwizardconfirmation_databasename" *ngIf="payload.database?.dbName">Database name: {{ payload.database.dbName }}</div>
                <div i18n="@@bma_serverinstallwizardconfirmation_username" *ngIf="payload.database?.username">Username: {{ payload.database.username }}</div>
                <div i18n="@@bma_serverinstallwizardconfirmation_password">Password: {{ payload.database.password }}</div>
                <div i18n="@@bma_serverinstallwizardconfirmation_networktype">Network type: {{ payload.database.networkType }}</div>
            </div>
        </div>

        <h3 i18n="@@bma_serverinstallwizardconfirmation_diskpartitioning">Disk partitioning</h3>
        <div class="row" *ngIf="payload.device && !configs?.partitioning?.useExistingHardwareRaidConfig">
            <div i18n="@@bma_common_device" class="col-sm-3 text-right text-primary">Device</div>
            <div class="col-sm-6">{{ payload.device }}</div>
        </div>
        <div class="row" *ngIf="configs?.partitioning?.useExistingHardwareRaidConfig">
            <div class="col-sm-3 text-right text-primary">Hardware RAID</div>
            <div i18n="@@bma_common_useexistinghardwareconfig" class="col-sm-6">Use existing configuration</div>
        </div>
        <ng-container *ngIf="payload?.raid && !configs?.partitioning?.useExistingHardwareRaidConfig">
            <div class="row">
                <div i18n="@@bma_serverinstallwizardconfirmation_raidtype" class="col-sm-3 text-right text-primary">Type</div>
                <div class="col-sm-6">{{ payload.raid.type == 'SW' ? 'Software' : 'Hardware' }} RAID</div>
            </div>
            <div class="row">
                <div i18n="@@bma_serverinstallwizardconfirmation_raidlevel" class="col-sm-3 text-right text-primary">Level</div>
                <div class="col-sm-6">{{ payload.raid.type == 'NONE' ? 'Single Drive Array (equivalent to pass-through mode)' : 'RAID ' + payload.raid.level }}</div>
            </div>
            <div class="row" *ngIf="payload.raid?.numberOfDisks">
                <div i18n="@@bma_serverinstallwizardconfirmation_raidnumberofdisks" class="col-sm-3 text-right text-primary">Number of disks</div>
                <div class="col-sm-6">{{ payload.raid.numberOfDisks }}</div>
            </div>
        </ng-container>
        <div class="row" *ngIf="configs?.partitioning?.useDefaultPartitioning !== null">
            <div i18n="@@bma_serverinstallwizardconfirmation_partitionmode" class="col-sm-3 text-right text-primary">Partition mode</div>
            <div class="col-sm-6">{{ configs?.partitioning?.useDefaultPartitioning ? 'Use default partitioning' : 'Customize partitioning' }}</div>
        </div>
        <div class="row" *ngIf="payload?.partitions">
            <div i18n="@@bma_common_partitions" class="col-sm-3 text-right text-primary">Partitions</div>
            <div class="col-sm-6">
                <table class="table table-sm table-stripped">
                    <thead>
                        <tr>
                            <th i18n="@@bma_common_mountpoint">Mount point</th>
                            <th i18n="@@bma_common_type">Type</th>
                            <th i18n="@@bma_common_sizemb">Size MB</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let partition of payload?.partitions">
                            <td>
                                {{ partition.mountpoint }}
                            </td>
                            <td>
                                {{ partition.filesystem }}
                            </td>
                            <td>
                                {{ partition.size }}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <h3>Settings</h3>

        <div class="row" *ngIf="payload.hostname && payload.hostname.length > 0">
            <div i18n="@@bma_common_hostname" class="col-sm-3 text-right text-primary">Hostname</div>
            <div class="col-sm-6">{{ payload.hostname }}</div>
        </div>

        <div class="row" *ngIf="payload.timezone && payload.timezone.length > 0">
            <div i18n="@@bma_common_timezone" class="col-sm-3 text-right text-primary">Timezone</div>
            <div class="col-sm-6">{{ payload.timezone }}</div>
        </div>

        <div class="row" *ngIf="payload.sshKeys && payload.sshKeys?.length > 0">
            <div i18n="@@bma_common_sshkeys" class="col-sm-3 text-right text-primary">SSH keys</div>
            <div class="col-sm-6">
                <pre><span class="text-monospace">{{ payload.sshKeys }}</span></pre>
            </div>
        </div>

        <div class="row" *ngIf="payload.postInstallScript && payload.postInstallScript?.length > 0">
            <div i18n="@@bma_common_postinstallscript" class="col-sm-3 text-right text-primary">Post-installation script</div>
            <div class="col-sm-6">
                <pre><span class="text-monospace" [innerHtml]="payload.postInstallScript"></span></pre>
            </div>
        </div>

        <div class="row">
            <div i18n="@@bma_common_powercycle" class="col-sm-3 text-right text-primary">Power Cycle</div>
            <div class="col-sm-6">
                <span i18n="@@bma_common_yes" *ngIf="payload.powerCycle === true">Yes</span>
                <span i18n="@@bma_common_no" *ngIf="!payload.powerCycle === true">No</span>
            </div>
        </div>

        <div class="row" *ngIf="configs?.settings.canSendEmailNotification">
            <div i18n="@@bma_common_doemailnotification" class="col-sm-3 text-right text-primary">Send email notification</div>
            <div class="col-sm-6">
                <span i18n="@@bma_common_yes" *ngIf="payload.doEmailNotification === true">Yes</span>
                <span i18n="@@bma_common_no" *ngIf="!payload.doEmailNotification === true">No</span>
            </div>
        </div>

        <div class="row" *ngIf="payload.callbackUrl && payload.callbackUrl.length > 0">
            <div i18n="@@bma_common_callbackurl" class="col-sm-3 text-right text-primary">Callback URL</div>
            <div class="col-sm-6">{{ payload.callbackUrl }}</div>
        </div>

        <button i18n-label="@@bma_common_install" pButton type="button" label="Install" icon="pi pi-check" (click)="openInstallConfirmationModal()" class="p-button-success float-right"></button>
    </div>
</div>
