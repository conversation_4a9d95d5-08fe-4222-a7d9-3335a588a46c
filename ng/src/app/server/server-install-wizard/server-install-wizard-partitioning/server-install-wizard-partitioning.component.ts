import { Component, Input, OnInit, OnChanges, Output, EventEmitter } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormArray } from '@angular/forms';

@Component({
  selector: 'app-server-install-wizard-partitioning',
  templateUrl: './server-install-wizard-partitioning.component.html',
  styleUrls: ['./server-install-wizard-partitioning.component.css'],
})
export class ServerInstallWizardPartitioningComponent implements OnInit, OnChanges {
  @Input() server: any;
  @Input() operatingSystemDefaults: any;
  @Input() nextButtonEnabled = true;
  @Output() dataUpdated = new EventEmitter<any>();
  @Output() submitted = new EventEmitter<any>();

  // partitioning part
  partitioningForm: FormGroup;

  // RAID part
  diskSets: Array<any>;
  raidForm: FormGroup;
  useOptions: Array<any>;

  constructor(private formBuilder: FormBuilder) {}

  get partitions() {
    return this.partitioningForm.get('partitions') as FormArray;
  }

  ngOnInit(): void {
    // partitioning part
    this.partitioningForm = this.formBuilder.group({
      default: [true],
      partitions: this.formBuilder.array([]),
    });

    this.partitioningForm.valueChanges.subscribe((value) => {
      this.dataUpdated.emit({
        config: this.getConfig(),
        payload: this.getPayload(),
      });
    });
    this.monitorPartitionChanges();

    // RAID part
    this.raidForm = this.formBuilder.group({
      use: [],
      diskSet: [],
      raidLevel: [],
      numberOfDisks: [],
    });

    this.raidForm.valueChanges.subscribe((value) => {
      this.dataUpdated.emit({
        config: this.getConfig(),
        payload: this.getPayload(),
      });
    });

    this.raidForm.get('use').valueChanges.subscribe((value) => {
      this.diskSets = this.getUsableDiskSets();
      this.raidForm.get('diskSet').setValue(this.diskSets[0].id);
    });
  }

  ngOnChanges() {
    if (!this.operatingSystemDefaults) {
      return;
    }

    if (!this.partitioningForm) {
      this.ngOnInit();
    }

    // partitioning part
    this.partitioningForm.get('default').setValue(true);
    this.setDefaultPartitions();

    // RAID part
    this.useOptions = [];

    if (this.isHardwareRaidCapable()) {
      this.useOptions.push(
        {
          value: 'existing_hardware_config',
          text: $localize`:@@bma_common_existingraidconfig:Existing Hardware RAID configuration`,
        },
        {
          value: 'hardware',
          text: $localize`:@@bma_common_hardware_raid:Hardware RAID`,
        }
      );

      if (this.isFeatureSupportedByOS('SW_RAID')) {
        this.useOptions.push({ value: 'software', text: $localize`:@@bma_common_softwareraid:Software RAID` });
      }

      this.useOptions.push({
        value: 'no_raid',
        text: $localize`:@@bma_common_noraid:No RAID`,
      });

      this.raidForm.get('use').setValue('existing_hardware_config');
      this.raidForm.get('raidLevel').setValue('single');
    }

    if (this.isSoftwareRaidCapable()) {
      if (!this.isHardwareRaidCapable()) {
        this.useOptions.push({ value: 'no_raid', text: $localize`:@@bma_common_noraid:No RAID` });
        this.raidForm.get('use').setValue('no_raid');
        this.raidForm.get('raidLevel').setValue('0');
      }

      if (!this.useOptions.find((option) => option.value === 'software')) {
        this.useOptions.push({ value: 'software', text: $localize`:@@bma_common_softwareraid:Software RAID` });
      }
    }

    this.dataUpdated.emit({
      config: this.getConfig(),
      payload: this.getPayload(),
    });
  }

  // common part

  isFeatureSupportedByOS(feature: string) {
    return this.operatingSystemDefaults?.features.includes(feature);
  }

  getConfig(): any {
    return {
      useExistingHardwareRaidConfig: this.isExistingHardwareRaidConfigSelected(),
      useDefaultPartitioning: this.isFeatureSupportedByOS('PARTITIONING')
        ? this.partitioningForm.get('default').value
        : null,
    };
  }

  getPayload() {
    return {
      ...this.getPartitioningPayload(),
      ...this.getRaidPayload(),
    };
  }

  next() {
    this.submitted.emit();
  }

  // partitioning part

  setDefaultPartitions(): void {
    this.partitions.clear();

    if (!this.operatingSystemDefaults?.partitions) {
      return;
    }

    for (const partition of this.operatingSystemDefaults.partitions) {
      this.addPartition(partition);
    }

    this.applyInitialValidations();
    if (this.partitioningForm.get('default').value) {
      this.partitions.disable();
    }
  }

  addPartition(partition = null): void {
    const partitionForm = this.formBuilder.group({
      mountpoint: [partition?.mountpoint],
      filesystem: [partition?.filesystem, Validators.required],
      size: [partition?.size, Validators.required],
      supportedFileSystems: [this.operatingSystemDefaults.supportedFileSystems],
    });

    this.partitions.push(partitionForm);
    this.monitorPartitionChanges();
  }

  removePartition(partitionIndex: number): void {
    this.partitions.removeAt(partitionIndex);
  }

  getPartitioningPayload(): any {
    if (this.partitions.length === 0) {
      return {};
    }
    const partitions = this.partitions.getRawValue();
    partitions.forEach((element) => delete element.supportedFileSystems);
    return { partitions };
  }

  monitorPartitionChanges() {
    if (this.partitioningForm.get('default').value === true) {
      return;
    }

    this.partitions.controls.forEach((partitionForm) => {
      partitionForm
        .get('mountpoint')
        ?.valueChanges.subscribe((mountpointValue: string) =>
          this.updateSupportedFileSystems(partitionForm, mountpointValue)
        );
      partitionForm
        .get('filesystem')
        ?.valueChanges.subscribe((filesystemValue: string) =>
          this.updateMountpointState(partitionForm, filesystemValue)
        );
    });
  }

  updateSupportedFileSystems(partitionForm, mountpointValue: string) {
    let supportedFileSystems = this.operatingSystemDefaults.supportedFileSystems;

    if (mountpointValue === '/boot') {
      supportedFileSystems = supportedFileSystems.filter((filesystem) => filesystem !== 'xfs');
    }

    partitionForm.get('supportedFileSystems')?.setValue(supportedFileSystems);
  }

  updateMountpointState(partitionForm, filesystemValue: string) {
    if (filesystemValue === 'swap') {
      partitionForm.get('mountpoint')?.setValue('');
      partitionForm.get('mountpoint')?.disable();
      return;
    }
    partitionForm.get('mountpoint')?.enable();
  }

  // Aplica as validações iniciais quando o formulário é carregado
  applyInitialValidations() {
    this.partitions.controls.forEach((partitionForm) => {
      const mountpointValue = partitionForm.get('mountpoint')?.value;
      const filesystemValue = partitionForm.get('filesystem')?.value;

      // Executa a validação do mountpoint para remover 'xfs' se necessário
      this.updateSupportedFileSystems(partitionForm, mountpointValue);

      // Executa a validação do filesystem para desabilitar mountpoint se necessário
      this.updateMountpointState(partitionForm, filesystemValue);
    });
  }

  // RAID part

  hasOnlyNvmeDisks() {
    return this.server.specs.hdd.findIndex((hdd) => hdd.type !== 'NVME') === -1;
  }

  isSuperMicro(): boolean {
    return this.server.specs.brand === 'SUPERMICRO';
  }

  getUsableDiskSets(): Array<any> {
    if (
      !this.isSuperMicro() &&
      this.isHardwareRaidCapable() &&
      (this.raidForm.get('use').value === 'software' || this.raidForm.get('use').value === 'no_raid')
    ) {
      return this.server.specs.hdd.filter((hdd) => hdd.type === 'NVME');
    }
    if (this.raidForm.get('use').value === 'hardware') {
      return this.server.specs.hdd.filter((hdd) => hdd.type !== 'NVME');
    }
    return this.server.specs.hdd;
  }

  isExistingHardwareRaidConfigSelected() {
    return this.raidForm.get('use').value === 'existing_hardware_config';
  }

  isRaidToBeConfigured() {
    return (
      (this.isHardwareRaidCapable() || this.isSoftwareRaidCapable()) &&
      ['hardware', 'software'].includes(this.raidForm.get('use').value)
    );
  }

  isHardwareRaidCapable() {
    return (
      this.server.specs.hardwareRaidCapable &&
      !this.hasOnlyNvmeDisks() &&
      !(this.server.specs.chassis === 'SM 846TQ (24xLFF)' && this.server.specs.cpu.type !== 'Intel Xeon E5-2620v4')
    );
  }

  isSoftwareRaidCapable() {
    return (
      this.isFeatureSupportedByOS('SW_RAID') &&
      (!this.isHardwareRaidCapable() || this.server.specs.brand === 'SUPERMICRO')
    );
  }

  getRaidPayload(): any {
    if (this.isExistingHardwareRaidConfigSelected()) {
      return { device: 'SATA_SAS' };
    }

    const payload: any = {
      device: this.raidForm.get('diskSet').value,
    };

    if (this.isRaidToBeConfigured()) {
      const raidLevel = this.raidForm.get('raidLevel').value;

      if (raidLevel === 'single') {
        payload.raid = { type: 'NONE' };
        return payload;
      }

      payload.raid = {
        type: this.raidForm.get('use').value === 'hardware' ? 'HW' : 'SW',
        level: parseInt(raidLevel, 10),
      };

      const numberOfDisks = parseInt(this.raidForm.get('numberOfDisks').value, 10);
      if (numberOfDisks) {
        payload.raid.numberOfDisks = numberOfDisks;
      }
    }

    return payload;
  }
}
