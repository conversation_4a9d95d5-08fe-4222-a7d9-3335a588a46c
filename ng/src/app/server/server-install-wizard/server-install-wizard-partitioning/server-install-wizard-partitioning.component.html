<form *ngIf="operatingSystemDefaults" class="row">
    <div class="col-sm-12">
        <div [formGroup]="raidForm">
            <div *ngIf="server.specs.brand == 'SUPERMICRO' && server.specs.hardwareRaidCapable" class="alert alert-warning" role="alert">
                <div class="alert-icon">
                    <i class="fa fa-information fa-lg" aria-hidden="true"></i>
                </div>
                <h4 i18n="@@bma_common_attention" class="alert-heading">Attention!</h4>
                <p i18n="@@bma_serverinstallwizardpartitioning_warning" class="alert-body">For Supermicros with hardware RAID controller, please make sure that the chosen disk set is compatible with the chosen RAID type.</p>
            </div>

            <p i18n="@@bma_serverinstallwizardpartitioning_description1">
                RAID Controller: <strong>{{ server.specs.hardwareRaidCapable ? 'yes' : 'no' }}</strong>
            </p>

            <ng-container *ngIf="isHardwareRaidCapable() || isSoftwareRaidCapable()">
                <label i18n="@@bma_serverinstallwizardpartitioning_use" for="use">Use:</label>
                <select id="use" formControlName="use" class="form-control mb-3">
                    <option *ngFor="let opt of useOptions" value="{{ opt.value }}">{{ opt.text }}</option>
                </select>
            </ng-container>

            <ng-container *ngIf="!isExistingHardwareRaidConfigSelected()">
                <label i18n="@@bma_serverinstallwizardpartitioning_selectdiskset" for="diskSet">Select Disk set:</label>
                <select id="diskSet" formControlName="diskSet" class="form-control mb-3">
                    <option *ngFor="let diskSet of diskSets" value="{{ diskSet.id }}">{{ diskSet.id }} ( {{ diskSet.amount }}x {{ diskSet.size }}{{ diskSet.unit }} {{ diskSet.type }} {{ diskSet.performanceType|formatDiskPerformanceType }} )</option>
                </select>
            </ng-container>

            <ng-container *ngIf="isRaidToBeConfigured()">
                <div class="form-group">
                    <label i18n="@@bma_common_raidlevel" class="col-form-label" for="raidLevel">Raid Level:</label>
                    <select formControlName="raidLevel" class="form-control" id="raidLevel">
                        <option *ngIf="raidForm.get('use').value === 'hardware'" i18n="@@bma_common_singledrivearrayequivalent" value="single">Single Drive Array (equivalent to pass-through mode)</option>
                        <option i18n="@@bma_common_raid0" value="0">RAID 0</option>
                        <option i18n="@@bma_common_raid1" value="1">RAID 1</option>
                        <option i18n="@@bma_common_raid5" value="5">RAID 5</option>
                        <option i18n="@@bma_common_raid10" value="10">RAID 10</option>
                    </select>
                </div>

                <div class="form-group" *ngIf="raidForm.get('raidLevel').value !== 'single'">
                    <label i18n="@@bma_common_diskstouse" for="numberOfDisks">Number of disks to use:</label>
                    <input formControlName="numberOfDisks" type="number" class="form-control" id="numberOfDisks" min="0" />
                    <small i18n="@@bma_common_numberofdisksnone" class="text-muted font-italic">if no number of disks is provided, all disks in the disk set will be used</small>
                </div>
            </ng-container>
        </div>

        <div *ngIf="isFeatureSupportedByOS('PARTITIONING')" [formGroup]="partitioningForm" class="mt-5">
            <div class="form-check">
                <input formControlName="default" class="form-check-input" type="checkbox" id="default" (change)="setDefaultPartitions()" />
                <label i18n="@@bma_serverinstallwizardpartitioning_usedefaultpartitioning" class="form-check-label" for="default">Use default partitioning</label>
            </div>

            <table *ngIf="partitions.length > 0" class="table">
                <thead>
                    <tr>
                        <td i18n="@@bma_serverinstallwizardpartitioning_mountpoint">Mount point</td>
                        <td i18n="@@bma_common_type">Type</td>
                        <td i18n="@@bma_common_sizemib">Size MiB</td>
                        <td></td>
                    </tr>
                </thead>
                <tbody>
                    <ng-container formArrayName="partitions">
                        <tr *ngFor="let partitionForm of partitions.controls; let index = index">
                            <ng-container [formGroup]="partitionForm">
                                <td width="28%">
                                    <input type="text" formControlName="mountpoint" class="form-control" />
                                </td>
                                <td width="28%">
                                    <select formControlName="filesystem" class="form-control">
                                        <option *ngFor="let option of partitionForm.get('supportedFileSystems').value" [value]="option">
                                            {{ option }}
                                        </option>
                                    </select>
                                </td>
                                <td width="28%">
                                    <input type="text" formControlName="size" class="form-control" />
                                </td>
                                <td width="8%">
                                    <button type="button" pButton *ngIf="!partitioningForm.get('default').value" (click)="removePartition(index)" icon="fa fa-cancel" class="p-button-sm p-button-text p-button-danger"></button>
                                </td>
                            </ng-container>
                        </tr>
                    </ng-container>
                </tbody>
            </table>

            <div class="text-center" *ngIf="!partitioningForm.get('default').value">
                <button i18n-label="@@bma_serverinstallwizardpartitioning_addpartition" pButton type="button" label="Add a partition" (click)="addPartition()" icon="pi pi-plus" class="p-button-sm p-button-success"></button>
            </div>
        </div>

        <button *ngIf="nextButtonEnabled" i18n-label="@@bma_common_nextstep" pButton type="button" label="Next step" icon="pi pi-chevron-right" iconPos="right" class="p-button-primary float-right" (click)="next()"></button>
    </div>
</form>
