import { Component, OnInit } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { CurrentUserService } from 'src/app/services/current-user.service';

@Component({
  selector: 'app-server-install-wizard-confirmation-modal',
  templateUrl: './server-install-wizard-confirmation-modal.component.html',
  styleUrls: ['./server-install-wizard-confirmation-modal.component.css'],
})
export class ServerInstallWizardConfirmationModalComponent implements OnInit {
  serverId: any;
  customerId: any;
  doEmailNotification = false;
  showDialog = true;
  isEmployee = false;
  form: UntypedFormGroup;

  constructor(
    private currentUserService: CurrentUserService,
    private formBuilder: UntypedFormBuilder,
    public config: DynamicDialogConfig,
    private dynamicDialogRef: DynamicDialogRef
  ) {}

  ngOnInit(): void {
    this.serverId = this.config.data.serverId;
    this.customerId = this.config.data.customerId;
    this.doEmailNotification = this.config.data.doEmailNotification;
    this.isEmployee = this.currentUserService.isEmployee();
    this.form = this.formBuilder.group({
      customerIdConfirmation: [null, Validators.required],
    });
  }

  isCustomerConfirmationValid(): boolean {
    if (!this.customerId) {
      return true;
    }
    return this.customerId === this.form.get('customerIdConfirmation').value;
  }

  doConfirmInstallation(): void {
    if (!this.isCustomerConfirmationValid()) {
      return;
    }
    this.closeModal({ startInstallation: true });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
