<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_serverinstallwizardconfirmationmodal_header" class="modal-title">Installation Confirmation</h3>
    </ng-template>

    <div class="modal-body">
        <p i18n="@@bma_serverinstallwizardconfirmationmodal_serverwarning" *ngIf="customerId">Dedicated server "{{ serverId }}" is used by customer "{{ customerId }}".</p>

        <div i18n="@@bma_serverinstallwizardconfirmationmodal_installdataerasealert" class="alert alert-danger" role="alert">During installation, all existing data on all disks on your server will be wiped and cannot be recovered.</div>

        <ng-container *ngIf="customerId">
            <p i18n="@@bma_serverinstallwizardconfirmationmodal_description" *ngIf="doEmailNotification">{{ isEmployee ? 'The customer' : 'You' }} will be notified by e-mail when the installation is started and when it has finished.</p>

            <form [formGroup]="form" class="form-horizontal" (ngSubmit)="doConfirmInstallation()">
                <div class="form-group">
                    <label i18n="@@bma_serverinstallwizardconfirmationmodal_customernumberconfirmation" for="customerIdConfirmation">Please type in {{ isEmployee ? 'the' : 'your' }} customer number to confirm</label>
                    <input type="text" formControlName="customerIdConfirmation" class="form-control" id="customerIdConfirmation" autocomplete="off" autofocus />
                </div>
            </form>
        </ng-container>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_serverinstallwizardconfirmationmodal_installconfirmation" pButton type="button" (click)="doConfirmInstallation()" [disabled]="!isCustomerConfirmationValid()" label="I understand the consequences, start installation"></button>
        </div>
    </ng-template>
</p-dialog>
