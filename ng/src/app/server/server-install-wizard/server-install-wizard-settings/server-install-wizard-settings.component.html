<form *ngIf="operatingSystemDefaults" [formGroup]="form" class="row">
    <div class="col-sm-12">
        <div class="form-group" *ngIf="isFeatureSupportedByOS('HOSTNAME')">
            <label i18n="@@bma_common_hostname" for="settings-hostname">Hostname</label>
            <input type="text" formControlName="hostname" class="form-control" id="settings-hostname" placeholder="localhost.localdomain.tld" />
            <p i18n="@@bma_serverinstallwizardsettings_hostnamewarning" class="help-block" *ngIf="!isHostnameValid()">Please provide valid Hostname e.g localhost.localdomain.com</p>
        </div>

        <div class="form-group" *ngIf="isFeatureSupportedByOS('TIMEZONE')">
            <label i18n="@@bma_common_timezone" for="settings-timezone">Timezone</label>
            <select formControlName="timezone" id="settings-timezone" class="form-control">
                <option value="GMT-13">GMT-13</option>
                <option value="GMT-12">GMT-12</option>
                <option value="GMT-14">GMT-14</option>
                <option value="GMT-11">GMT-11</option>
                <option value="GMT-10">GMT-10</option>
                <option value="GMT-9">GMT-9</option>
                <option value="GMT-8">GMT-8</option>
                <option value="GMT-7">GMT-7</option>
                <option value="GMT-6">GMT-6</option>
                <option value="GMT-5">GMT-5</option>
                <option value="GMT-4">GMT-4</option>
                <option value="GMT-3">GMT-3</option>
                <option value="GMT-2">GMT-2</option>
                <option value="GMT-1">GMT-1</option>
                <option value="UTC">UTC</option>
                <option value="GMT+1">GMT+1</option>
                <option value="GMT+2">GMT+2</option>
                <option value="GMT+3">GMT+3</option>
                <option value="GMT+4">GMT+4</option>
                <option value="GMT+5">GMT+5</option>
                <option value="GMT+6">GMT+6</option>
                <option value="GMT+7">GMT+7</option>
                <option value="GMT+8">GMT+8</option>
                <option value="GMT+9">GMT+9</option>
                <option value="GMT+10">GMT+10</option>
                <option value="GMT+11">GMT+11</option>
                <option value="GMT+12">GMT+12</option>
            </select>
        </div>

        <app-ssh-keys-selector *ngIf="isFeatureSupportedByOS('SSH_KEYS')" [(content)]="sshKeys" (contentChange)="emitPayloadUpdated()"></app-ssh-keys-selector>

        <app-local-storage-editor *ngIf="isFeatureSupportedByOS('POST_INSTALL_SCRIPTS')" key="postInstall" [(content)]="postInstallScript" (contentChange)="emitPayloadUpdated()" label="Post installation script" [clearButton]="true" [uploadButton]="true" [dragAndDrop]="true" [uploadAllowedTypes]="['.sh']"></app-local-storage-editor>

        <div class="form-check">
            <input formControlName="doPowerCycle" class="form-check-input" type="checkbox" id="settings-power-cycle" />
            <label class="form-check-label" for="settings-power-cycle">
                <span i18n="@@bma_serverinstallwizardsettings_powercyclecheck" class="mr-1">Check this box if you want to power cycle your dedicated server</span>
                <sup *ngIf="isEmployee">
                    <a target="_blank" href="https://wiki.ocom.com/display/KC/Install+an+OS+using+EMP#InstallanOSusingEMP-Step-by-stepguide">
                        <i title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i>
                    </a>
                </sup>
            </label>
        </div>

        <div class="form-check" *ngIf="canSendEmailNotification">
            <input formControlName="doEmailNotification" class="form-check-input" type="checkbox" id="settings-do-email-notifications" />
            <label i18n="@@bma_common_doemailnotification" class="form-check-label" for="settings-do-email-notifications">Send email notification</label>
        </div>

        <div *ngIf="isEmployee" class="form-group">
            <label for="settings-callback-url">
                <span i18n="@@bma_common_callbackurl" class="mr-1">Callback URL</span>
                <sup>
                    <a target="_blank" href="https://wiki.ocom.com/display/KC/Install+an+OS+using+EMP#InstallanOSusingEMP-Step-by-stepguide">
                        <i i18n-title="@@bma_common_kb" title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i>
                    </a>
                </sup>
            </label>
            <input type="text" formControlName="callbackUrl" class="form-control" placeholder="http://hostname/script" />
            <p i18n="@@bma_serverinstallwizardsettings_callbackurlwarning" class="help-block" *ngIf="!isCallbackUrlValid()">Please provide valid URL</p>
        </div>

        <button i18n-label="@@bma_common_nextstep" pButton type="button" label="Next step" icon="pi pi-chevron-right" iconPos="right" class="p-button-primary float-right" (click)="next()"></button>
    </div>
</form>
