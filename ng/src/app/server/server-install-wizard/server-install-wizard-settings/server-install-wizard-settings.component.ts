import { Component, Input, OnInit, OnChanges, Output, EventEmitter } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { CustomValidator } from 'src/app/validators/custom.validator';

@Component({
  selector: 'app-server-install-wizard-settings',
  templateUrl: './server-install-wizard-settings.component.html',
  styleUrls: ['./server-install-wizard-settings.component.css'],
})
export class ServerInstallWizardSettingsComponent implements OnInit, OnChanges {
  @Input() server: any;
  @Input() operatingSystemDefaults: any;
  @Output() dataUpdated = new EventEmitter<any>();
  @Output() submitted = new EventEmitter<any>();
  isEmployee = false;
  postInstallScript = '';
  sshKeys = '';
  form: UntypedFormGroup;
  canSendEmailNotification: any;

  constructor(
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    private customValidator: CustomValidator
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.canSendEmailNotification = this.server.contract?.deliveryStatus === 'ACTIVE';

    this.form = this.formBuilder.group({
      hostname: ['s' + this.server.id + '.dedi.leaseweb.net'],
      timezone: ['UTC'],
      doEmailNotification: [this.canSendEmailNotification],
      doPowerCycle: [true],
      callbackUrl: [''],
    });

    this.form.valueChanges.subscribe((value) => {
      this.dataUpdated.emit({ payload: this.getPayload() });
    });

    this.dataUpdated.emit({ config: this.getConfig() });
  }

  ngOnChanges() {
    if (!this.operatingSystemDefaults) {
      return;
    }

    this.dataUpdated.emit({ payload: this.getPayload() });
  }

  emitPayloadUpdated() {
    this.dataUpdated.emit({ payload: this.getPayload() });
  }

  isFeatureSupportedByOS(feature: string) {
    return this.operatingSystemDefaults?.features.includes(feature);
  }

  isCallbackUrlValid() {
    const callbackUrl = this.form.get('callbackUrl').value;
    return callbackUrl.trim() === '' || this.customValidator.isValidHttpUrl(callbackUrl);
  }

  isHostnameValid() {
    const hostname = this.form.get('hostname').value;
    return hostname.trim() === '' || this.customValidator.isValidFqdn(hostname, 3);
  }

  getConfig(): any {
    return { canSendEmailNotification: this.canSendEmailNotification };
  }

  getPayload(): any {
    const payload: any = {
      powerCycle: this.form.get('doPowerCycle').value,
      doEmailNotification: this.form.get('doEmailNotification').value,
    };

    if (this.isFeatureSupportedByOS('TIMEZONE')) {
      payload.timezone = this.form.get('timezone').value;
    }

    const callbackUrl = this.form.get('callbackUrl').value;
    if (this.isEmployee && callbackUrl.trim().length > 0) {
      payload.callbackUrl = callbackUrl.trim();
    }

    const hostname = this.form.get('hostname').value;
    if (hostname.trim().length > 0) {
      payload.hostname = hostname.trim();
    }

    if (this.postInstallScript.length > 0) {
      payload.postInstallScript = btoa(this.postInstallScript);
    }

    if (this.sshKeys.length > 0) {
      payload.sshKeys = this.sshKeys.trim();
    }

    return payload;
  }

  next() {
    this.submitted.emit();
  }
}
