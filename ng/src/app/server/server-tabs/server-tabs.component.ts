import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { HttpClient } from '@angular/common/http';
import { ModalService } from 'src/app/services/modal.service';
import { SiteService } from 'src/app/services/site.service';
import { CommerceService } from 'src/app/services/commerce.service';
import { IpV6RequestModalComponent } from 'src/app/ip/ip-v6-request-modal/ip-v6-request-modal.component';
import { PowerOperationModalComponent } from 'src/app/power-operation-modal/power-operation-modal.component';
import { ServerNewFeaturesModalComponent } from 'src/app/server/server-new-features-modal/server-new-features-modal.component';
import { DateFormatterService } from 'src/app/services/date-formatter.service';
import { PrivateNetworkRedundancyOrderModalComponent } from 'src/app/private-network/private-network-redundancy-order-modal/private-network-redundancy-order-modal.component';
import { PrivateNetworkService } from 'src/app/services/private-network.service';
import { ServerRemoteManagementModalComponent } from '../server-remote-management-modal/server-remote-management-modal.component';
import { ServerConfigureHardwareRaidModalComponent } from '../server-configure-hardware-raid-modal/server-configure-hardware-raid-modal.component';
import { ServerCustomInstallationModalComponent } from '../server-custom-installation-modal/server-custom-installation-modal.component';
import { ServerFirmwareUpdateModalComponent } from '../server-firmware-update-modal/server-firmware-update-modal.component';
import { ServerHardwareScanModalComponent } from '../server-hardware-scan-modal/server-hardware-scan-modal.component';
import { ServerPrepareForSaleModalComponent } from '../server-prepare-for-sale-modal/server-prepare-for-sale-modal.component';
import { ServerRescueModeModalComponent } from '../server-rescue-mode-modal/server-rescue-mode-modal.component';

@Component({
  selector: 'app-server-tabs',
  templateUrl: './server-tabs.component.html',
  styleUrls: ['./server-tabs.component.css'],
})
export class ServerTabsComponent implements OnInit {
  isLoading = false;
  server: any;
  serverId: string;
  isEmployee = false;
  missingInformation: any;
  tickets: any;

  constructor(
    private activatedRoute: ActivatedRoute,
    private titleService: Title,
    private currentUserService: CurrentUserService,
    private httpClient: HttpClient,
    private modalService: ModalService,
    private router: Router,
    private siteService: SiteService,
    private commerceService: CommerceService,
    private dateService: DateFormatterService,
    private privateNetworkService: PrivateNetworkService
  ) {}

  ngOnInit(): void {
    this.serverId = this.activatedRoute.snapshot.url[1].path;
    this.isEmployee = this.currentUserService.isEmployee();

    this.isLoading = true;

    this.httpClient.get<any>(`/_/internal/dedicatedserverapi/v2/servers/${this.serverId}`).subscribe({
      next: (server: any) => {
        this.server = server;
        this.server.isSharedEol = this.isServerSharedEol(this.server);
        this.titleService.setTitle(
          `${this.server.id} - Dedicated Server ${
            this.server.contract && this.server.contract.reference ? this.server.contract.reference : ''
          }| Leaseweb`
        );

        /*eslint-disable */
        if (this.server.contract) {
          const event = new Event('update_product_navbar');
          event['customerId'] = this.server.contract.customerId;
          event['salesOrgId'] = this.server.contract.salesOrgId;
          event['country'] = this.siteService.getCountry(this.server.contract.salesOrgId);
          window.dispatchEvent(event);
        }
        /*eslint-enable */
        this.isLoading = false;
        if (this.isEmployee) {
          this.getServerMissingInformationForOrangeBar(server);
        }
        if (this.server.contract) {
          this.getOpenTickets();
        }
      },
      error: (error: any) => {
        this.isLoading = false;
        this.router.navigate(['**']);
      },
    });
  }

  getServerMissingInformationForOrangeBar(server: any): void {
    this.missingInformation = [];

    if (this.isServerSharedEol(server)) {
      this.missingInformation.push('This server is in a EOL shared rack without automation');
      return;
    }

    if (!server.location?.rack) {
      if (!server.location?.suite) {
        this.missingInformation.push('Dedicated server is not scanned in a rack.');
      } else {
        this.missingInformation.push('Dedicated server is scanned on a storage shelf.');
      }
    }
    if (!server.location?.site) {
      this.missingInformation.push(
        'We do not know in which data center this dedicated server is in. Automation is disabled, but you can still use the Job Wizard.'
      );
    }
    if ('DEDICATED' === server.rack?.type && !server.featureAvailability.automation) {
      this.missingInformation.push(
        'This dedicated server is in a dedicated rack without automation. Some automation operations will not be available.'
      );
    }
    if (!server.specs?.ram?.size) {
      this.missingInformation.push('No RAM in this dedicated server according to administration.');
    }
    if (server.specs?.hdd?.length === 0) {
      this.missingInformation.push('No disks in this dedicated server according to administration.');
    }
    if (!server.serialNumber || ['0123456789', '1234567890'].indexOf(server.serialNumber) !== -1) {
      this.missingInformation.push(
        'Invalid or no serial number in administration. Automation may not work as expected.'
      );
    }
    if (!server.networkInterfaces?.public?.mac || server.networkInterfaces?.public?.mac === '00:00:00:00:00:00') {
      this.missingInformation.push(
        'Invalid or missing public MAC address for this dedicated server in administration. Automation is disabled, but you can still use the Job Wizard.'
      );
    }
    if (!server.networkInterfaces?.public?.ip) {
      this.missingInformation.push(
        'No public IP address found in IPAM. Automation is disabled, but you can still use the Job Wizard.'
      );
    }
    if (server.networkInterfaces?.public?.ports?.length === 0) {
      this.missingInformation.push('No public switch port set. Switch port automation is unavailable.');
    }
    if (!server.featureAvailability?.powerCycle && !server.featureAvailability?.ipmiReboot) {
      this.missingInformation.push(
        'This dedicated server is not assigned to a power outlet, nor is capable of IPMI reboot. Automation can still work if you reboot manually.'
      );
    }
    if (!server.featureAvailability?.remoteManagement) {
      this.missingInformation.push('This dedicated server has no remote management, or has legacy remote management.');
    } else if (
      !server.networkInterfaces?.remoteManagement?.mac ||
      server.networkInterfaces?.remoteManagement?.mac === '00:00:00:00:00:00'
    ) {
      this.missingInformation.push(
        'Invalid or missing remote management MAC address for this dedicated server in administration. If you can, please schedule a hardware scan.'
      );
    }
    if (server.rack?.id) {
      if (
        server.networkInterfaces?.public?.locationId &&
        server.networkInterfaces?.public?.locationId !== server.rack?.id
      ) {
        this.missingInformation.push('Location of subnet of public IP address is different than location of server.');
      }
      if (
        server.networkInterfaces?.remoteManagement?.locationId &&
        server.networkInterfaces?.remoteManagement?.locationId !== server.rack?.id
      ) {
        this.missingInformation.push(
          'Location of subnet of remote management IP address is different than location of server.'
        );
      }
    }
    if (
      server.privateNetworks?.length > 0 &&
      server.privateNetworks[0].switch &&
      server.networkInterfaces?.internal?.ports?.length > 0
    ) {
      if (server.privateNetworks[0].switch.name !== server.networkInterfaces.internal.ports[0].name) {
        this.missingInformation.push(
          `Potential Private Network issue: switch mismatch - SAP admin has ${server.networkInterfaces.internal.ports[0].name} and NSE-API admin has ${server.privateNetworks[0].switch.name}. Remove and re-add to fix this.`
        );
      }
      if (server.privateNetworks[0].switch.port !== server.networkInterfaces.internal.ports[0].port) {
        this.missingInformation.push(
          `Potential Private Network issue: port mismatch - SAP admin has ${server.networkInterfaces.internal.ports[0].port} and NSE-API admin has ${server.privateNetworks[0].switch.port}. Remove and re-add to fix this.`
        );
      }
    }
    const pdusFromServer = Array.from(new Set(server.powerPorts.map((powerbar) => powerbar.name)));
    if (pdusFromServer.length > 0) {
      const params = { name: pdusFromServer.join(',') };
      this.httpClient.get<any>('/_/internal/bmpapi/v1/powerbars', { params }).subscribe({
        next: (data: any) => {
          const pdusFromBmp = data.powerbars?.map((powerbar) => powerbar.name);
          pdusFromServer.forEach((name) => {
            if (-1 === pdusFromBmp.indexOf(name)) {
              this.missingInformation.push(
                `PDU ${name} is missing in EMP. If you expect power automation, you should consider adding it.`
              );
            }
          });
        },
      });
    }
    const switchesFromServer = Array.from(
      new Set(
        []
          .concat(
            server.networkInterfaces.public?.ports ?? [],
            server.networkInterfaces.internal?.ports ?? [],
            server.networkInterfaces.remoteManagement?.ports ?? []
          )
          .map((device) => device.name)
      )
    );
    if (switchesFromServer.length > 0) {
      const params = { name: switchesFromServer.join(',') };
      this.httpClient.get<any>('/_/internal/nseapi/v2/networkDevices', { params }).subscribe({
        next: (data: any) => {
          const devicesFromNse = data.networkDevices?.map((device) => device.name);
          switchesFromServer.forEach((name) => {
            if (-1 === devicesFromNse.indexOf(name)) {
              this.missingInformation.push(
                `Switch ${name} is missing in EMP. If you expect switch automation, you should consider adding it.`
              );
            }
          });
        },
      });
    }
    if (server.networkInterfaces?.public?.ip) {
      const params = {
        ip: server.networkInterfaces.public.ip.split('/')[0],
        createdAt: this.dateService.utc().subtractHours(2).format(`yyyy-MM-dd'T'HH:mm:ss'.0Z'`), // TODO remove hack once nse-api uses DateTimeISO8601Validator
      };
      this.httpClient.get<any>('/_/internal/nseapi/v2/arpwatchEntries', { params }).subscribe({
        next: (data: any) => {
          const macs = Array.from(new Set(data.arpwatchEntries?.map((entry) => entry.mac)));
          if (macs.length > 1) {
            const listOfMacs = macs.join(', ');
            this.missingInformation.push(
              `There is an active IP conflict. IP address ${params.ip} was recently used by ${listOfMacs}.`
            );
          }
        },
      });
    }
  }

  getOpenTickets(): void {
    let customerHeaders = {};
    if (this.isEmployee) {
      /*eslint-disable */
      customerHeaders = {
        'APIGW-Impersonated-Customer-ID': this.server.contract.customerId,
        'APIGW-Impersonated-Customer-Sales-Org-ID': this.server.contract.salesOrgId,
      };
      /*eslint-enable */
    }
    this.httpClient
      .get<any>(
        `/_/tickets/v2/tickets?limit=10&offset=0&sortBy=creationDate&sortDirection=DESC&status=NEW%2CIN_PROGRESS%2CCUSTOMER_INPUT_REQUIRED%2CSCHEDULED&subject=${this.server.id}`,
        {
          headers: customerHeaders,
        }
      )
      .subscribe({
        next: (data: any) => {
          this.tickets = data.tickets;
          console.log(this.tickets);
        },
      });
  }

  isServerSharedEol(server: any): boolean {
    if (!server.contract) {
      return false;
    }

    return server.contract.salesOrgId === '1700' && server.rack.type === 'SHARED_EOL';
  }

  openRemoteManagementModal(serverId: string) {
    this.modalService.show(ServerRemoteManagementModalComponent, { serverId });
  }

  openConfigureHardwareRaidModal(server: any) {
    this.modalService.show(ServerConfigureHardwareRaidModalComponent, { server });
  }

  openFirmwareUpdateModal(serverId: string) {
    this.modalService.show(ServerFirmwareUpdateModalComponent, { serverId });
  }

  openRescueModeModal(serverId: string) {
    this.modalService.show(ServerRescueModeModalComponent, { server: this.server });
  }

  openHardwareScanModal(serverId: string) {
    this.modalService.show(ServerHardwareScanModalComponent, { serverId });
  }

  openPrepareForSaleModal(serverId: string) {
    this.modalService.show(ServerPrepareForSaleModalComponent, { serverId });
  }

  openCustomInstallModal(serverId: string) {
    this.modalService.show(ServerCustomInstallationModalComponent, { serverId });
  }

  openIpV6RequestModal() {
    this.modalService.show(IpV6RequestModalComponent, { equipment: this.server, equipmentType: 'servers' });
  }

  getCommerceRequestMoreIpsUrl(): string {
    return this.commerceService.getCommerceConfigureProductUrl(this.server.contract.id, 'DEDSER02_MOD_IPV4');
  }

  openPowerOperationModal() {
    this.modalService.show(PowerOperationModalComponent, {
      equipment: this.server,
      equipmentType: 'servers',
    });
  }

  openServerNewFeaturesModal() {
    this.modalService.show(ServerNewFeaturesModalComponent, {
      serverId: this.server.id,
    });
  }

  onServerLoaded(component) {
    if (this.server) {
      component.equipment = this.server;
      component.server = this.server;
      component.equipmentId = this.server.id;
      component.equipmentType = 'servers';
    }
  }

  openOrderRedundantPrivateNetworkModal() {
    this.modalService.show(PrivateNetworkRedundancyOrderModalComponent, {
      equipment: this.server,
      equipmentType: 'dedicatedServer',
    });
  }
}
