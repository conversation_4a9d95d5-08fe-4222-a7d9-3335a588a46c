<div class="server-alerts">
    <div *ngIf="isEmployee && (missingInformation|default:'') && (missingInformation|default:'').length > 0" id="server-missing-information-alert" class="alert alert-warning" role="alert">
        <p><span class="fa fa-alert mr-1"></span><strong i18n="@@bma_servertabs_missinginformation">Server Missing Information</strong></p>
        <ul>
            <li *ngFor="let message of missingInformation" [innerHTML]="message"></li>
        </ul>
    </div>
    <div *ngIf="tickets && tickets.length > 0" class="alert alert-warning" role="alert">
        <p><span class="fa fa-alert mr-1"></span><strong i18n="@@bma_servertabs_opentickets">There are open tickets for this server</strong></p>
        <ul>
            <li *ngFor="let ticket of tickets">
                <a *ngIf="!isEmployee" [href]="'/tickets/' + ticket.id" target="_blank"
                    ><b>{{ ticket.id }}</b></a
                >
                <a *ngIf="isEmployee" [href]="'/emp/_legacy/redirect-ticket/' + ticket.id" target="_blank"
                    ><b>{{ ticket.id }}</b></a
                >
                {{ ticket.subject }}
            </li>
        </ul>
    </div>
</div>

<app-server-widget *ngIf="server" [server]="server" class="float-right"></app-server-widget>

<app-customer-aware-header *ngIf="server" i18n-caption="@@bma_dedicatedserver_title" caption="Dedicated Server {{server.id}}" [reference]="server.contract?.reference" [customerId]="server.contract?.customerId" [salesOrgId]="server.contract?.salesOrgId" [isEmployee]="isEmployee"></app-customer-aware-header>

<div *ngIf="!isEmployee && server && server.isRedundantPrivateNetworkCapable" class="float-right server-private-network-link">
    <a i18n="@@bma_common_order_redundantprivatenetwork" class="btn btn-success" (click)="openOrderRedundantPrivateNetworkModal(); false" href="#">Order Redundant Private Network</a>
</div>

<app-loader *ngIf="isLoading"></app-loader>

<ul class="nav nav-pills mb-4" *ngIf="server">
    <li class="nav-item dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-details mr-2"></span>
            <span i18n="@@bma_common_details">Details</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a [routerLink]="['.']" class="dropdown-item">
                <span class="text-muted fa fa-details mr-2"></span>
                <span i18n="@@bma_common_serverdetails">Server Details</span>
            </a>
            <a [routerLink]="['hardware']" class="dropdown-item">
                <span class="text-muted fa fa-compute mr-2"></span>
                <span i18n="@@bma_common_hardwaredetails">Hardware Details</span>
            </a>
            <a [routerLink]="['network']" class="dropdown-item">
                <span class="text-muted fa fa-transfer mr-2"></span>
                <span i18n="@@bma_common_networkdetails">Network Details</span>
            </a>
            <a [routerLink]="['ips']" class="dropdown-item">
                <span class="text-muted fa fa-ip mr-2"></span>
                <span i18n="@@bma_common_ipaddresses">IP Addresses</span>
            </a>
            <a [routerLink]="['credentials']" class="dropdown-item">
                <span class="text-muted fa fa-lock mr-2"></span>
                <span i18n="@@bma_common_credentials">Credentials</span>
            </a>
        </div>
    </li>
    <li class="nav-item dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-usage mr-2"></span>
            <span i18n="@@bma_common_usage">Usage</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a [routerLink]="['notifications']" [class.disabled]="!server.networkInterfaces.public?.ports || server.isSharedEol|default:''" class="dropdown-item">
                <span class="text-muted fa fa-notification mr-2"></span>
                <span i18n="@@bma_common_notifications">Notifications</span>
            </a>
            <a [routerLink]="['graphs']" [class.disabled]="!server.networkInterfaces.public?.ports || server.isSharedEol|default:''" class="dropdown-item">
                <span class="text-muted fa fa-usage mr-2"></span>
                <span i18n="@@bma_common_graphs">Graphs</span>
            </a>
            <a [routerLink]="['graphs', 'privateNetwork']" [class.disabled]="!server.networkInterfaces.internal?.ports || server.isSharedEol|default:''" class="dropdown-item">
                <span class="text-muted fa fa-usage mr-2"></span>
                <span i18n="@@bma_common_pngraphs">Private Network Graphs</span>
            </a>
        </div>
    </li>
    <li class="nav-item navbar-right dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-tasks mr-2"></span>
            <span i18n="@@bma_common_activities">Activities</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a [routerLink]="['jobs']" href="#" [class.disabled]="server.isSharedEol" class="dropdown-item">
                <span class="text-muted fa fa-tasks mr-2"></span>
                <span i18n="@@bma_common_jobs">Jobs</span>
            </a>
            <a [routerLink]="['nullRouteHistory']" class="dropdown-item">
                <span class="text-muted fa fa-transfer mr-2"></span>
                <span i18n="@@bma_common_nullroutehistory">Null Route History</span>
            </a>
            <a *ngIf="isEmployee" href="https://logstor.ams1.nl.leaseweb.net/lswasp/s{{ server.id }}/" target="_blank" [class.disabled]="server.isSharedEol" class="dropdown-item">
                <span class="text-muted fa fa-search mr-2"></span>
                <span i18n="@@bma_common_openlogstor">Open Logstor</span>
            </a>
        </div>
    </li>
    <li class="nav-item">
        <a *ngIf="!isEmployee" [class.disabled]="!server.contract|default:''" href="/bare-metals/privateNetwork/lookup" class="nav-link">
            <span class="text-muted fa fa-privatenetwork mr-2"></span>
            <span i18n="@@bma_common_privatenetwork">Private Network</span>
        </a>
        <a *ngIf="isEmployee" [class.disabled]="!server.contract || server.isSharedEol |default:''" href="/emp/privateNetwork/lookup?customerId={{ server.contract?.customerId }}&salesOrgId={{ server.contract?.salesOrgId }}" class="nav-link">
            <span class="text-muted fa fa-privatenetwork mr-2"></span>
            <span i18n="@@bma_common_privatenetwork">Private Network</span>
        </a>
    </li>
    <li class="nav-item navbar-right dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-administration mr-2"></span>
            <span i18n="@@bma_common_actions">Actions</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a href="javascript:void(0);" (click)="openRemoteManagementModal(server.id)" class="dropdown-item" [class.disabled]="server.isSharedEol">
                <span class="text-muted fa fa-remotemanagement mr-2"></span>
                <span i18n="@@bma_common_remotemanagement">Remote Management</span>
            </a>
            <a href="#" [class.disabled]="(!server.featureAvailability.powerCycle|default:'') && (!server.featureAvailability.ipmiReboot|default:'')" class="dropdown-item" (click)="openPowerOperationModal(); false">
                <span class="text-muted fa fa-powercycle mr-2"></span>
                <span i18n="@@bma_common_poweroperation">Power Operation</span>
            </a>
            <a href="#" *ngIf="!isEmployee || currentUserService.hasPermission('hardwarescan_schedule')" (click)="openHardwareScanModal(server.id); false" class="dropdown-item" [class.disabled]="server.isSharedEol">
                <span class="text-muted fa fa-show mr-2"></span>
                <span i18n="@@bma_common_schedulehardwarescan">Schedule Hardware Scan</span>
            </a>
            <a *ngIf="isEmployee" [class.disabled]="server.isSharedEol || (!currentUserService.hasPermission('hardware_raid_schedule') || server.networkInterfaces.public?.nullRouted|default:'') || (!server.specs.hardwareRaidCapable)" href="javascript:void(0);" (click)="openConfigureHardwareRaidModal(server)" class="dropdown-item">
                <span class="text-muted fa fa-compute mr-2"></span>
                <span i18n="@@bma_common_configurehardwareraid">Configure Hardware RAID</span>
            </a>
            <a [routerLink]="['install']" *ngIf="isEmployee" [class.disabled]="server.isSharedEol || server.networkInterfaces.public?.nullRouted|default:''" class="dropdown-item">
                <span class="text-muted fa fa-cart mr-2"></span>
                <span i18n="@@bma_common_installwizard">Install Wizard</span>
            </a>
            <a href="#" [class.disabled]="(server.networkInterfaces.public?.nullRouted|default:'') || (server.isSharedEol)" class="dropdown-item" (click)="openRescueModeModal(server.id); false">
                <span class="text-muted fa fa-rescue mr-2"></span>
                <span i18n="@@bma_common_rescuemode">Rescue Mode</span>
            </a>
            <a href="#" *ngIf="isEmployee" [class.disabled]="(server.networkInterfaces.public?.nullRouted|default:'') || (server.contract?.deliveryStatus|default:'') == 'ACTIVE'" class="dropdown-item" (click)="openPrepareForSaleModal(server.id); false">
                <span class="text-muted fa fa-caretright mr-2"></span>
                <span i18n="@@bma_common_pfs">Prepare for Sale</span>
            </a>
            <a href="#" *ngIf="isEmployee" [class.disabled]="(server.networkInterfaces.public?.nullRouted|default:'') || (server.isSharedEol)" class="dropdown-item" (click)="openFirmwareUpdateModal(server.id); false">
                <span class="text-muted fa fa-caretright mr-2"></span>
                <span i18n="@@bma_common_firmwareupdate">Firmware update</span>
            </a>
            <a *ngIf="!isEmployee" href="/tickets/new?equipmentId={{ server.id }}&category=technical-assistance" class="dropdown-item">
                <span class="text-muted fa fa-ticket mr-2"></span>
                <span i18n="@@bma_common_createticket">Create Ticket</span>
            </a>
            <a *ngIf="!isEmployee" href="/backup/#/order/servers" [class.disabled]="server.isSharedEol" class="dropdown-item">
                <span class="text-muted fa fa-acronis mr-2"></span>
                <span i18n="@@bma_common_acronisbackup">Acronis Backup</span>
            </a>
            <a href="#" [class.disabled]="(!server.featureAvailability.automation|default:'') || (server.networkInterfaces.public?.nullRouted|default:'') || (server.isSharedEol)" class="dropdown-item" (click)="openCustomInstallModal(server.id); false">
                <span class="text-muted fa fa-customize mr-2"></span>
                <span i18n="@@bma_common_custominstallation">Custom Installation</span>
            </a>
            <a href="#" [routerLink]="['install']" *ngIf="!isEmployee" [class.disabled]="(!server.featureAvailability.automation|default:'') || (server.networkInterfaces.public?.nullRouted|default:'') || (server.isSharedEol)" class="dropdown-item">
                <span class="text-muted fa fa-reinstall mr-2"></span>
                <span i18n="@@bma_common_reinstall">Reinstall</span>
            </a>
            <div i18n-pTooltip="@@bma_ipv4tooltip" pTooltip="You have exceeded the limit (32) of public IP addresses" [tooltipDisabled]="!(server.contract?.ipv4Quantity >= 32)">
                <a *ngIf="!isEmployee && server.contract && server.contract?.status == 'ACTIVE'" [class.disabled]="((server.contract|default:'') && server.contract?.status != 'ACTIVE' || ('DEDICATED' == (server.rack.type|default:''|uppercase) && server.rack.id|default:'')) || server.isSharedEol || server.contract?.ipv4Quantity >= 32" class="dropdown-item" target="_blank" [href]="getCommerceRequestMoreIpsUrl()">
                    <span class="text-muted fa fa-ip mr-2"></span>
                    <span i18n="@@bma_common_requestmoreipv4">Request More IPv4 Addresses</span>
                </a>
            </div>
            <a *ngIf="(!isEmployee && server.contract && server.contract?.status == 'ACTIVE')" [class.disabled]="server.isSharedEol" href="#" class="dropdown-item" (click)="openIpV6RequestModal(); false">
                <span class="text-muted fa fa-ip mr-2"></span>
                <span i18n="@@bma_common_requestipv6">Request IPv6</span>
                <div i18n="@@bma_common_free" class="badge pull-right">Free</div>
            </a>
        </div>
    </li>
    <li class="new-features nav-item" *ngIf="!isEmployee">
        <a href="#" class="nav-link" (click)="openServerNewFeaturesModal(); false">
            <span class="fa fa-exclamation mr-2"></span>
            <span i18n="@@bma_common_newfeatures">New Features</span>
        </a>
    </li>
</ul>

<ng-container *ngIf="server">
    <router-outlet (activate)="onServerLoaded($event)"></router-outlet>
</ng-container>
