import { Component, OnInit } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-server-firmware-update-modal',
  templateUrl: './server-firmware-update-modal.component.html',
  styleUrls: ['./server-firmware-update-modal.component.css'],
})
export class ServerFirmwareUpdateModalComponent implements OnInit {
  serverId: any;
  form: UntypedFormGroup;
  isSubmitting = false;
  isEmployee = false;
  showDialog = true;

  constructor(
    private alertService: AlertService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.serverId = this.config.data.serverId;
    this.form = this.formBuilder.group({
      powerCycle: [true, Validators.required],
    });
  }

  updateFirmware(): void {
    if (this.form.invalid) {
      return;
    }

    this.isSubmitting = true;

    this.httpClient
      .post<any>(`/_/internal/bmpapi/v2/servers/${this.serverId}/firmwareUpdate`, {
        powerCycle: this.form.get('powerCycle').value,
      })
      .subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          if (this.isEmployee) {
            this.router.navigate([`/servers/${this.serverId}/jobs/${data.uuid}`], { skipLocationChange: false });
          }
          this.alertService.alert({
            type: 'success',
            description: $localize`:@@bma_serverfirmwareupgrademodal_success:Firmware update successfully launched for dedicated server ${this.serverId}.`,
          });
          this.closeModal();
        },
        error: (error: any) => {
          this.isSubmitting = false;
          this.form.setErrors({ api: error.error });
        },
      });
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }
}
