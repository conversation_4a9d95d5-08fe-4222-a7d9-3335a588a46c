<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '800px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_common_firmwareupdate" class="modal-title">Firmware update</h3>
    </ng-template>
    <form [formGroup]="form" (ngSubmit)="updateFirmware()" class="form-horizontal">
        <div class="modal-body">
            <p i18n="@@bma_serverfirmwareupdatemodal_confirmation">
                Are you sure you want to update the firmware on dedicated server
                <span class="text-monospace">{{ serverId }}</span
                >?
            </p>

            <p>
                <strong i18n="@@bma_common_rebootprocess"> Be aware that the dedicated server needs to be rebooted for this process to complete. </strong>
            </p>
            <div class="form-group">
                <div class="checkbox">
                    <input type="checkbox" id="powerCycle" formControlName="powerCycle" value="true" checked="checked" autofocus />
                    <label i18n="@@bma_common_powercycleserver" for="powerCycle" class="ml-1">Power cycle the dedicated server</label>
                </div>
            </div>
            <div class="alert alert-info">
                <div class="alert-icon"><i class="fa fa-information fa-lg" aria-hidden="true"></i></div>
                <h4 i18n="@@bma_common_information" class="alert-heading">Information</h4>
                <p i18n="@@bma_common_leasewebreboot" class="alert-body small">Leaseweb can perform a power cycle for you, or you can choose to reboot the dedicated server yourself. If you choose to reboot the dedicated server yourself, you need to do so within the next 4 hours.</p>
            </div>
        </div>
    </form>

    <div *ngIf="form.errors?.api?.errorMessage" class="alert alert-danger" role="alert">
        <span>{{ form.errors.api.errorMessage }}</span>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_cancel" pButton (click)="closeModal()" type="button" icon="pi pi-times" iconPos="left" label="Cancel" class="p-button-secondary"></button>
            <button i18n-label="@@bma_common_launch" pButton (click)="updateFirmware()" label="Launch" icon="pi pi-check" [loading]="isSubmitting" [disabled]="form.invalid"></button>
        </div>
    </ng-template>
</p-dialog>
