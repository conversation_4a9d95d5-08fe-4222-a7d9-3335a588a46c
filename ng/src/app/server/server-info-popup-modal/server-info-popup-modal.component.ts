import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-server-info-popup-modal',
  templateUrl: './server-info-popup-modal.component.html',
  styleUrls: ['./server-info-popup-modal.component.css'],
})
export class ServerInfoPopupModalComponent implements OnInit {
  serverId: string;
  macAddress: string;
  isLoading = false;
  server: any;
  error: boolean;
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    public dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.serverId = this.config.data?.serverId;
    this.server = this.config.data?.server;
    this.macAddress = this.config.data?.macAddress;
    if (this.server) {
      this.error = false;
      return;
    } else if (this.serverId) {
      this.getServerByEquipmentId();
    } else if (this.macAddress) {
      this.getServerByMacAddress();
    }
  }

  getServerByEquipmentId(): void {
    this.isLoading = true;
    this.httpClient.get<any>(`/_/internal/dedicatedserverapi/v2/servers/${this.serverId}`).subscribe(
      (server: any) => {
        this.isLoading = false;
        this.server = server;
        this.error = false;
      },
      (error) => {
        this.isLoading = false;
        this.error = true;
      }
    );
  }

  getServerByMacAddress(): void {
    this.isLoading = true;
    this.httpClient.get<any>(`/_/internal/dedicatedserverapi/v2/servers?macAddress=${this.macAddress}`).subscribe(
      (servers: any) => {
        this.isLoading = false;
        if (servers._metadata.totalCount === 0) {
          this.error = true;
          return;
        }
        this.serverId = servers.servers[0].id;
        this.getServerByEquipmentId();
        this.error = false;
      },
      (error) => {
        this.isLoading = false;
        this.error = true;
      }
    );
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }
}
