<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '800px'}">
    <ng-template pTemplate="header">
        <h3 class="modal-title pull-left">
            <span>Dedicated Server</span>
            <span *ngIf="server">{{ server.id }}</span>
        </h3>
    </ng-template>
    <div class="modal-body">
        <app-loader *ngIf="isLoading"></app-loader>
        <div class="table-responsive" *ngIf="!isLoading && server">
            <table class="table table-striped">
                <tbody>
                    <tr>
                        <th i18n="@@bma_common_location" scope="col">Location</th>
                        <td colspan="2">
                            <app-equipment-location [location]="server.location"></app-equipment-location>
                        </td>
                    </tr>
                    <tr>
                        <th i18n="@@bma_common_id">ID</th>
                        <td>
                            {{ server.id }}
                        </td>
                        <td>
                            <a [routerLink]="['/servers', server.id]" (click)="closeModal()">
                                <span class="fa fa-caretright"></span>
                                <span i18n="@@bma_common_emp">EMP</span>
                            </a>
                            <br />
                            <a [routerLink]="['/servers', server.id, 'hardware']" (click)="closeModal()">
                                <span class="fa fa-caretright"></span>
                                <span i18n="@@bma_common_hardwarescanresults">Hardware Scan Results</span>
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th i18n="@@bma_common_status">Status</th>
                        <td colspan="2">
                            <dl class="row mb-0">
                                <dt i18n="@@bma_common_customer" class="col mb-0">Customer</dt>
                                <dd class="col mb-0">
                                    {{ server.contract ? 'ASSIGNED' : 'UNASSIGNED' }}
                                </dd>

                                <dt i18n="@@bma_common_delivery" class="col mb-0">Delivery</dt>
                                <dd class="col mb-0">
                                    <app-contract-delivery-status [deliveryStatus]="server.contract?.deliveryStatus|default:'UNASSIGNED'"> </app-contract-delivery-status>
                                </dd>

                                <dt i18n="@@bma_common_contract" class="col mb-0">Contract</dt>
                                <dd class="col mb-0">
                                    <app-contract-status [status]="server.contract?.status|default:'UNASSIGNED'"></app-contract-status>
                                </dd>
                            </dl>
                        </td>
                    </tr>

                    <tr *ngIf="server.contract">
                        <th i18n="@@bma_common_cusstomerid">Customer ID</th>
                        <td>
                            <span class="selectable">{{ server.contract.customerId }}</span>
                        </td>
                        <td>
                            <a [routerLink]="['/servers']" [queryParams]="{customerId: server.contract.customerId, salesOrgId: server.contract.salesOrgId}" (click)="closeModal()">
                                <span class="fa fa-caretright"></span>
                                <span i18n="@@bma_common_emp">EMP</span>
                            </a>
                        </td>
                    </tr>

                    <tr *ngIf="server.contract">
                        <th i18n="@@bma_common_reference">Reference</th>
                        <td>
                            <span *ngIf="server.contract.reference; else notAvailable">
                                {{ server.contract.reference }}
                            </span>
                        </td>
                        <td>
                            <a [routerLink]="['/servers', server.id]" (click)="closeModal()">
                                <span class="fa fa-caretright"></span>
                                <span i18n="@@bma_common_emp">EMP</span>
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th i18n="@@bma_common_ipmiipaddress">IPMI IP Address</th>
                        <td>
                            <span *ngIf="server.networkInterfaces.remoteManagement; else notAvailable">
                                <a href="http://{{ server.networkInterfaces.remoteManagement.ip|cidrToIp }}">
                                    {{ server.networkInterfaces.remoteManagement.ip }}
                                </a>
                            </span>
                        </td>
                        <td>
                            <a [routerLink]="['/servers', server.id]" (click)="closeModal()">
                                <span class="fa fa-caretright"></span>
                                <span i18n="@@bma_serverinfopopupmodal_rm">Remote Management</span>
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th i18n="@@bma_common_primaryipaddress">Primary IP Address</th>
                        <td>
                            <span class="selectable">{{  server.networkInterfaces.public.ip ? server.networkInterfaces.public.ip : '-' }}</span>
                            <span i18n="@@bma_common_nullrouted" *ngIf="server.networkInterfaces.public.nullRouted" class="badge badge-danger">Null Routed</span>
                        </td>
                        <td>
                            <span *ngIf="server.networkInterfaces.public.ip">
                                <a [routerLink]="['/dhcp-reservations']" [queryParams]="{filter: server.networkInterfaces.public.ip|cidrToIp, site: server.location.site}" (click)="closeModal()">
                                    <span class="fa fa-caretright"></span>
                                    <span i18n="@@bma_common_viewdhcpreservation">View DHCP Reservations</span>
                                </a>
                            </span>
                            &nbsp;<a href="/ipam/ips/List?SearchConfig.SearchTerm={{ server.id }}&SearchConfig.SearchBy=NodeId">
                                <span class="fa fa-caretright"></span>
                                <span i18n="@@bma_common_ipam">IPAM</span>
                            </a>
                        </td>
                    </tr>
                    <ng-container *ngFor="let networkInterface of server.networkInterfaces | keyvalue">
                        <tr *ngIf="networkInterface.value">
                            <th>
                                <span i18n="@@bma_common_publicswitchport" *ngIf="networkInterface.key=='public'">Public Switch Port </span>
                                <span i18n="@@bma_common_internalswitchport" *ngIf="networkInterface.key=='internal'">Internal Switch Port </span>
                                <span i18n="@@bma_common_rmswitchport" *ngIf="networkInterface.key=='remoteManagement'">RM Switch Port </span>
                            </th>
                            <td>
                                {{ networkInterface.value.mac ? networkInterface.value.mac : '' }}
                            </td>
                            <td>
                                <span *ngIf="networkInterface.value?.ports else noSwitchPort">
                                    <a [routerLink]="['/networkDevices', networkInterface.value.ports[0]?.name]" (click)="closeModal()">
                                        {{ networkInterface.value.ports[0]?.name }}
                                        port
                                        {{ networkInterface.value.ports[0]?.port }}
                                    </a>
                                </span>
                                <ng-template #noSwitchPort> No Switch Port </ng-template>
                            </td>
                        </tr>
                    </ng-container>
                    <tr>
                        <th i18n="@@bma_common_powerautomation">Power Automation</th>
                        <td *ngIf="!server.featureAvailability.powerCycle">
                            <span i18n="@@bma_common_warning" class="badge badge-warning"> WARNING </span>
                        </td>
                        <td i18n="@@bma_serverinfopopupmodal_nopowercycle" *ngIf="!server.featureAvailability.powerCycle">No PDU and IPMI information. Unable to power cycle this dedicated server.</td>
                        <td i18n="@@bma_serverinfopopupmodal_noipmireboot1" *ngIf="server.featureAvailability.ipmiReboot">Via IPMI</td>
                        <td i18n="@@bma_serverinfopopupmodal_noipmireboot2" *ngIf="server.featureAvailability.ipmiReboot">Dedicated server has no PDU information, using IPMI for power automation.</td>
                        <td i18n="@@bma_serverinfopopupmodal_nopdu" *ngIf="server.featureAvailability.powerCycle && !server.featureAvailability.ipmiReboot">Via PDU</td>
                        <td *ngIf="server.featureAvailability.powerCycle && !server.featureAvailability.ipmiReboot">
                            <span *ngIf="server.powerPorts">
                                <a [routerLink]="['/powerbars', server.powerPorts[0].name]" (click)="closeModal()"> PDU {{ server.powerPorts[0].name }}, outlet {{ server.powerPorts[0].port }} </a>
                            </span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div *ngIf="!server && error">
            <p i18n="@@bma_serverinfopopupmodal_description1">Dedicated server {{ macAddress || serverId }} is not known.</p>
        </div>
    </div>
    <ng-template #notAvailable>
        <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
    </ng-template>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n="@@bma_common_close" pButton type="button" icon="pi pi-times" iconPos="left" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
        </div>
    </ng-template>
</p-dialog>
