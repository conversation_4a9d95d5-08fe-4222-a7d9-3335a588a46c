import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';

@Component({
  selector: 'app-server-custom-installation-modal',
  templateUrl: './server-custom-installation-modal.component.html',
  styleUrls: ['./server-custom-installation-modal.component.css'],
})
export class ServerCustomInstallationModalComponent implements OnInit {
  serverId: string;
  presets: any;
  bootFile: any;
  form: UntypedFormGroup;
  isLoading = false;
  isSubmitting = false;
  isDeleting = false;
  showDialog = true;

  constructor(
    private alertService: AlertService,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private formBuildder: UntypedFormBuilder,
    private currentUserService: CurrentUserService,
    private httpClient: HttpClient
  ) {}

  ngOnInit(): void {
    this.serverId = this.config.data.serverId;

    this.presets = [
      { label: 'netboot.xyz menu', value: 'http://boot.netboot.xyz/menu.ipxe' },
      { label: 'Acronis Backup', value: 'http://boot.leaseweb.com/acronis/current/boot.ipxe' },
    ];

    if (this.currentUserService.isEmployee()) {
      this.presets.push({ label: 'lswasp live image', value: 'http://boot.leaseweb.com/lswasp-rockylinux/boot.ipxe' });
    }

    this.getCurrentBootFile();

    this.form = this.formBuildder.group({
      powerCycle: [false],
      bootFile: [null, [Validators.required, Validators.pattern('((http?://)|(https?://)).*')]],
    });
  }

  getCurrentBootFile(): void {
    this.isLoading = true;

    this.httpClient.get<any>(`/_/internal/bmpapi/v2/servers/${this.serverId}/leases`).subscribe({
      next: (data: any) => {
        this.isLoading = false;
        if (data.leases?.length > 0) {
          this.bootFile = data.leases[0].bootfile;
        }
      },
      error: (error: any) => {
        this.isLoading = false;
      },
    });
  }

  closeModal(): void {
    this.dynamicDialogRef.close();
  }

  submitCustomInstall(): void {
    if (this.form.invalid) {
      return;
    }

    this.isSubmitting = true;

    const body = { bootFileName: this.form.get('bootFile').value };

    this.httpClient.post<any>(`/_/internal/bmpapi/v2/servers/${this.serverId}/leases`, body).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;
        this.closeModal();
        this.alertService.alert({
          type: 'success',
          description: $localize`:@@bma_servercustominstallationmodal_success:Successfully created custom installation for dedicated server ${this.serverId}.`,
        });

        if (this.form.get('powerCycle').value === true) {
          this.powerCycle();
        }
      },
      error: (error: any) => {
        this.isSubmitting = false;
        this.form.setErrors({ api: error.error });
      },
    });
  }

  powerCycle(): void {
    this.isSubmitting = true;
    this.httpClient.post<any>(`/_/internal/bmpapi/v2/servers/${this.serverId}/powerCycle`, null).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;
        this.alertService.alert(
          {
            type: 'success',
            description: $localize`:@@bma_servercustominstallationmodal_powercyclesuccess:Server powerCycle operation performed, please wait 10 minutes for it to complete.`,
          },
          true
        );
      },
      error: (error: any) => {
        this.isSubmitting = false;
        this.alertService.alert(
          {
            type: 'error',
            description: $localize`:@@bma_servercustominstallationmodal_powercyclefailure:Server powerCycle operation cannot be performed. Please contact customer support.`,
          },
          true
        );
      },
    });
  }

  removeBootFile(): void {
    this.isDeleting = true;

    this.httpClient.delete<any>(`/_/internal/bmpapi/v2/servers/${this.serverId}/leases`).subscribe({
      next: (data: any) => {
        this.isDeleting = false;
        this.closeModal();
        this.alertService.alert({
          type: 'success',
          description: $localize`:@@bma_servercustominstallationmodal_removebootfilesuccess:Successfully removed custom installation for dedicated server ${this.serverId}.`,
        });
      },
      error: (error: any) => {
        this.isDeleting = false;
        this.form.setErrors({ api: error.error });
      },
    });
  }
}
