<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '800px' }">
    <ng-template pTemplate="header">
        <h3 class="modal-title">
            <span i18n="@@bma_common_custominstallation" class="mr-1">Custom Installation</span>
            <sup>
                <a href="https://kb.leaseweb.com/products/dedicated-server/installing-servers-using-your-own-pxe-boot-environment" target="_blank">
                    <span title="More Information" class="fa fa-knowledge fl-lg" aria-hidden="true"></span>
                </a>
            </sup>
        </h3>
    </ng-template>
    <div class="modal-body">
        <app-loader *ngIf="isLoading"></app-loader>

        <div *ngIf="!isLoading">
            <div class="current-install">
                <div>
                    <strong i18n="@@bma_servercustominstallationmodal_description1">Current custom installation:</strong><br />
                    {{ bootFile ? bootFile : "None" }}
                </div>
                <button i18n-label="@@bma_common_remove" pButton *ngIf="bootFile" (click)="removeBootFile()" [loading]="isDeleting" class="pull-right" class="p-button-danger" label="Remove"></button>
            </div>

            <hr />

            <form [formGroup]="form">
                <div class="modal-body" id="action-customInstall-body">
                    <label i18n="@@bma_common_bootfile" for="bootFile" class="control-label">Bootfile</label>
                    <p-dropdown [options]="presets" formControlName="bootFile" editable="true" placeholder="http://" autofocus></p-dropdown>

                    <small *ngIf="form.get('bootFile').dirty && form.get('bootFile').errors" class="text-danger">
                        <span i18n="@@bma_servercustominstallationmodal_bootfiledescription1" *ngIf="form.get('bootFile').errors.required">Boot file is required</span>
                        <span i18n="@@bma_servercustominstallationmodal_bootfileerror" *ngIf="form.get('bootFile').errors.pattern">Please specify a URL with a http:// protocol</span>
                    </small>

                    <div>
                        <button pButton type="button" label="What is netboot.xyz?" class="p-button-link" (click)="netbootXYZPanel.toggle($event)"></button>
                        <p-overlayPanel #netbootXYZPanel [dismissable]="true" [style]="{'width':'500px'}" [baseZIndex]="10000" [appendTo]="'body'">
                            <ng-template pTemplate>
                                <h3 i18n="@@bma_servercustominstallationmodal_netboot">Netboot.xyz</h3>
                                <p i18n="@@bma_servercustominstallationmodal_netbootdescription1">A custom install using http://boot.netboot.xyz/menu.ipxe allows you to pxe boot into any operating system supported by the <a href="https://netboot.xyz">https://netboot.xyz</a> project.</p>
                                <p i18n="@@bma_servercustominstallationmodal_netbootdescription2">This is ideal in case you would like to do a manual installation using Leaseweb Remote Management, without having to mount a large ISO file via the server's Remote Management interface.</p>
                                <p i18n="@@bma_servercustominstallationmodal_netbootdescription3">To learn what Operating Systems and other live images are supported, checkout the FAQ <a href="https://netboot.xyz/faq/">https://netboot.xyz/faq/</a></p>
                            </ng-template>
                        </p-overlayPanel>
                    </div>

                    <div class="power-cycle">
                        <strong i18n="@@bma_common_rebootinstallation">Please note that you need to reboot the dedicated server for the installation to start.</strong>
                        <div class="form-check">
                            <input class="form-check-input" formControlName="powerCycle" type="checkbox" id="powerCycle" />
                            <label i18n="@@bma_servercustominstallationmodal_powercycle" class="form-check-label" for="powerCycle">Power cycle after the creation of the reservation</label>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div *ngIf="form.errors?.api?.errorMessage" class="alert alert-danger" role="alert">
        <span>{{ form.errors.api.errorMessage }}</span>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_cancel" pButton type="button" icon="pi pi-times" iconPos="left" label="Cancel" class="p-button-secondary" (click)="closeModal()"></button>
            <button i18n-label="@@bma_common_submit" pButton (click)="submitCustomInstall()" icon="pi pi-check" iconPos="left" [disabled]="form.invalid" [loading]="isSubmitting" label="Submit"></button>
        </div>
    </ng-template>
</p-dialog>
