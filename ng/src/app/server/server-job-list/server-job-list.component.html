<div class="job-list">
    <h3 class="mb-3">
        <i class="fa fa-tasks mr-1"></i>
        <span i18n="@@bma_common_serverjobs">Server Jobs</span><br />
    </h3>
    <app-loader *ngIf="isLoading"></app-loader>
    <div *ngIf="!isLoading && jobs" class="row mb-5 w-100">
        <div *ngIf="jobs.length === 0">
            <td i18n="@@bma_common_nojobs" class="text-center p-4" colspan="8">No jobs found.</td>
        </div>
        <div class="col-12 mt-3" *ngFor="let job of jobs">
            <div class="table-responsive">
                <h3 *ngIf="job.day|default:''">
                    {{ job.day|lswDate:"" }}
                </h3>
                <table class="table table-striped job-list">
                    <thead>
                        <tr>
                            <th i18n="@@bma_common_created" scope="col" class="text-center table-col-size-2">Created</th>
                            <th i18n="@@bma_common_status" scope="col" class="text-center table-col-size-2">Status</th>
                            <th i18n="@@bma_common_type" scope="col" class="table-col-size-3">Type</th>
                            <th i18n="@@bma_common_progress" scope="col" class="table-col-size-3">Progress</th>
                        </tr>
                    </thead>

                    <tbody>
                        <tr *ngFor="let job of job.jobs|default:[]" data-id="{{ job.uuid }}" class="bmp-job-status-{{ job.status|lowercase }}">
                            <td class="text-center">
                                {{ job.createdAt|timeAgo }}
                                <br />
                                <small class="text-muted">{{ job.createdAt|lswDateTime }}</small>
                            </td>

                            <td class="text-center">
                                <span class="job-status btn btn-sm py-0 disabled" [ngClass]="getJobStatusClass(job.status)">
                                    {{ job.status }}
                                </span>
                            </td>

                            <td class="text-truncate">
                                <a *ngIf="isEmployee; else jobTypeTextOnly" [routerLink]="[job.uuid]"> {{ job.type }} </a>
                                <ng-template #jobTypeTextOnly>
                                    <strong>{{ job.type }}</strong>
                                </ng-template>
                                <br />
                                <small *ngIf="job.type == 'install'" class="text-monospace">
                                    <span class="selectable">{{ job.payload.operatingSystemId }}</span>
                                </small>
                                <small *ngIf="job.type == 'rescueMode'" class="text-monospace">
                                    <span class="selectable">{{ job.payload.rescueImageId }}</span>
                                </small>
                                <br />
                                <small class="text-monospace">
                                    <span class="selectable">{{ job.node }}</span>
                                </small>
                            </td>

                            <td>
                                <p-progressBar *ngIf="job.status=='ACTIVE' && job.currentTask|default:''" [value]="job.progress.percentage"></p-progressBar>
                                <small *ngIf="isEmployee && job.status!='FINISHED' && job.currentTask" class="text-monospace d-inline-block text-truncate task-type-label"> {{ job.currentTask.flow}}::{{job.currentTask.type }} </small>
                            </td>

                            <td class="text-right">
                                <button i18n-label="@@bma_common_cancel" pButton type="button" *ngIf="(!isEmployee || currentUserService.hasPermission('job_expire_and_cancel')) && job.status == 'ACTIVE'" (click)="openJobCancelModal(job.uuid)" label="Cancel" icon="pi pi-trash" class="p-button-sm p-button-danger"></button>
                                <button i18n-label="@@bma_common_retry" pButton type="button" *ngIf="!job.isRunning && allowJobRetry(job)" (click)="openJobRetryModal(job)" label="Retry" icon="pi pi-refresh" class="p-button-sm p-button-success"></button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<div *ngIf="!isLoading && jobs">
    <app-pagination [totalCount]="totalCount" [limit]="limit" [offset]="offset" (pageChange)="onPageChange($event)"></app-pagination>
</div>
