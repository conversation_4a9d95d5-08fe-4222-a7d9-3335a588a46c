#job-filter FIELDSET LEGEND {
  display: none;
}
#job-filter UL LI {
  margin-bottom: 0.5em;
}

tr.bmp-job-status-active {
  background-color: #efefef;
}
tr.bmp-job-status-active > td.bmp-job-status {
  font-weight: bold;
}
.server-details-table .detail-not-available {
  color: #00000025 !important;
}

.job-list {
  border-bottom: none;
  border-collapse: separate;
}
.job-list .no-jobs {
  padding: 4em;
  border: none;
  text-align: center;
  background-color: #f2f2f2;
}
.job-list TR:not(:first-child) H3 {
  margin-top: 3em;
}
.job-list .job-status {
  width: 7em;
  line-height: 2.1em;
  height: 2em;
  display: inline-block;
}
.job-list TR TD,
.job-list TR TH {
  line-height: 1.5em !important;
  vertical-align: middle !important;
}
.task-type-label {
  width: 240px;
}
.table-col-size-1 {
  width: calc(100% / 12 * 1);
}
.table-col-size-2 {
  width: calc(100% / 12 * 2);
}
.table-col-size-3 {
  width: calc(100% / 12 * 3);
}

button:disabled {
  background-color: gray;
  border-color: gray;
}
