import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { JobCancelModalComponent } from 'src/app/job/job-cancel-modal/job-cancel-modal.component';
import { JobRetryModalComponent } from 'src/app/job/job-retry-modal/job-retry-modal.component';
import { LswNotificationHubService } from '@lsw/notification-hub';
import { MercureService } from 'src/app/services/mercure.service';
import { first } from 'rxjs';
import { ServerEvent } from 'src/app/models/server-event.model';

@Component({
  selector: 'app-server-job-list',
  templateUrl: './server-job-list.component.html',
  styleUrls: ['./server-job-list.component.css'],
})
export class ServerJobListComponent implements OnInit {
  @Input() server: any;
  jobs: Array<any> = [];
  limit = 20;
  offset = 0;
  totalCount = 0;
  isLoading = false;
  isEmployee = false;
  dynamicDialogRef: DynamicDialogRef;

  constructor(
    private modalService: ModalService,
    private httpClient: HttpClient,
    private alertService: AlertService,
    private currentUserService: CurrentUserService,
    private notificationHubService: LswNotificationHubService,
    private mercureService: MercureService,
    private ref: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.getJobs();
  }

  getJobs(): void {
    this.alertService.clear();
    let params = new HttpParams();
    params = params.set('limit', this.limit.toString());
    params = params.set('offset', this.offset.toString());

    this.isLoading = true;
    this.httpClient.get<any>(`/_/internal/bmpapi/v2/servers/${this.server.id}/jobs`, { params }).subscribe(
      (data: any) => {
        this.isLoading = false;
        this.totalCount = data._metadata.totalCount;
        this.limit = data._metadata.limit;
        this.offset = data._metadata.offset;
        if (data.jobs) {
          this.jobs = [];

          data.jobs.forEach((job) => {
            const day = new Date(job.createdAt ?? null);

            if (!this.jobs.some((period) => period.day === day.toISOString().slice(0, 10))) {
              this.jobs.push({ day: day.toISOString().slice(0, 10), jobs: [] });
            }

            const periodIndex = this.jobs.findIndex((period) => period.day === day.toISOString().slice(0, 10));
            this.jobs[periodIndex].jobs.push(job);

            if (job.progress.percentage < 100) {
              this.subscribeForUpdate(job.uuid);
            }
          });
        } else {
          this.jobs = [];
        }
      },
      (error: any) => {
        this.isLoading = false;
        this.jobs = [];
        this.alertService.alert(
          {
            type: 'error',
            description: $localize`:@@bma_common_getjobsfailure:Unable to get jobs.`,
          },
          true
        );
      }
    );
  }

  getJobStatusClass(status) {
    switch (status) {
      case 'ACTIVE':
        return 'btn-outline-primary';
      case 'FINISHED':
        return 'btn-outline-success';
      case 'CANCELED':
      case 'EXPIRED':
        return 'btn-outline-warning';
      case 'FAILED':
        return 'btn-outline-danger';
      default:
        return '';
    }
  }

  openJobCancelModal(jobUuid) {
    this.dynamicDialogRef = this.modalService.show(JobCancelModalComponent, {
      serverId: this.server.id,
      uuid: jobUuid,
    });

    this.dynamicDialogRef.onClose.subscribe((event) => {
      if (event?.jobCancelled === true) {
        this.getJobs();
      }
    });
  }

  openJobRetryModal(job) {
    this.dynamicDialogRef = this.modalService.show(JobRetryModalComponent, {
      serverId: this.server.id,
      uuid: job.uuid,
      oldPowerCycle: job.payload.powerCycle,
    });

    this.dynamicDialogRef.onClose.subscribe((event) => {
      if (event?.jobRetried === true) {
        this.getJobs();
      }
    });
  }

  allowJobRetry(job) {
    const createdAtDateDifference = (new Date().getTime() - new Date(job.createdAt).getTime()) / (1000 * 60 * 60 * 24);
    return createdAtDateDifference <= 7;
  }

  onPageChange(event): void {
    this.limit = event.limit;
    this.offset = event.offset;
    this.getJobs();
  }

  subscribeForUpdate(jobUuid) {
    this.notificationHubService.setTokenParams({ serverId: this.server.id });
    this.notificationHubService
      .getNotificationHubToken(this.mercureService.getTokenURL('bmpapi'))
      .pipe(first())
      .subscribe({
        next: () => {
          this.notificationHubService.sync<ServerEvent>(`${this.server.id}/jobs`).subscribe({
            next: (event) => {
              const jobIndex = this.findJobIndexByJobUuid(jobUuid);
              const jobDayIndex = this.findJobDayIndexByJobUuid(jobUuid);
              if (this.jobs[jobDayIndex].jobs[jobIndex].progress.percentage < 100) {
                this.jobs[jobDayIndex].jobs[jobIndex] = event.data;
                this.ref.detectChanges();
              }
            },
            error: (error) => console.log({ error }),
          });
        },
      });
  }

  findJobIndexByJobUuid(jobUuid: string): number {
    const index = this.findJobDayIndexByJobUuid(jobUuid);
    return this.jobs[index].jobs.findIndex((job) => job.uuid === jobUuid);
  }

  findJobDayIndexByJobUuid(jobUuid: string): number {
    return this.jobs.findIndex((day) => day.jobs.find((job) => job.uuid === jobUuid) !== null);
  }
}
