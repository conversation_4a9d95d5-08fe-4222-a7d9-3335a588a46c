import { Component, OnInit } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-server-prepare-for-sale-modal',
  templateUrl: './server-prepare-for-sale-modal.component.html',
  styleUrls: ['./server-prepare-for-sale-modal.component.css'],
})
export class ServerPrepareForSaleModalComponent implements OnInit {
  serverId: any;
  form: UntypedFormGroup;
  isSubmitting = false;
  isEmployee = false;
  showDialog = true;
  showMemoryTestTimeOptions = false;

  constructor(
    private alertService: AlertService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.serverId = this.config.data.serverId;
    this.form = this.formBuilder.group({
      firmwareUpgrade: [true, Validators.required],
      doWipeDisks: [true, Validators.required],
      powerCycle: [true, Validators.required],
      memoryTest: [false, Validators.required],
      stressTest: ['30m'],
      batchId: [null],
    });
  }

  doPrepareForSale(): void {
    if (this.form.invalid) {
      return;
    }

    this.isSubmitting = true;

    const body = {
      powerCycle: this.form.get('powerCycle').value,
      firmwareUpgrade: this.form.get('firmwareUpgrade').value,
      doWipeDisks: this.form.get('doWipeDisks').value,
      stressTest: !this.form.get('memoryTest').value ? null : this.form.get('stressTest').value,
      batchId: this.form.get('batchId').value,
    };

    this.httpClient.post<any>(`/_/internal/bmpapi/v2/servers/${this.serverId}/prepareForSale`, body).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;
        if (this.isEmployee) {
          this.router.navigate([`/servers/${this.serverId}/jobs/${data.uuid}`], { skipLocationChange: false });
        }
        this.alertService.alert({
          type: 'success',
          description: $localize`:@@bma_serverprepareforsalemodal_success:Prepare for sale scheduled for dedicated server ${this.serverId}.`,
        });
        this.closeModal();
      },
      error: (error: any) => {
        this.isSubmitting = false;
        this.form.setErrors({ api: error.error });
      },
    });
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }

  openMemoryTestTimeOptions() {
    this.showMemoryTestTimeOptions = !this.showMemoryTestTimeOptions;
  }
}
