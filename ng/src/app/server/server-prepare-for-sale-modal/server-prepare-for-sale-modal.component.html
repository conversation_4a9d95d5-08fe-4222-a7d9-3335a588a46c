<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '800px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_common_pfs" class="modal-title">Prepare for Sale</h3>
    </ng-template>

    <form [formGroup]="form" (ngSubmit)="doPrepareForSale()" class="form-horizontal">
        <div class="modal-body">
            <p i18n="@@bma_serverprepareforsalemodal_confirmation">
                <span class="fa fa-alert icon-gray fa-lg"></span> Are you sure you want to prepare dedicated server
                <span class="text-monospace">{{ serverId }}</span>
                for sale?
            </p>
            <p i18n="@@bma_serverprepareforsalemodal_description1">This process will:</p>
            <ul class="list-group mb-2">
                <li i18n="@@bma_serverprepareforsalemodal_description2" class="list-group-item">Reset IPMI credentials (if present).</li>
                <li i18n="@@bma_serverprepareforsalemodal_description3" class="list-group-item">If RAID controller is present within the dedicated server, set it to single drive array configuration.</li>
                <li class="list-group-item text-danger">
                    <span class="fa fa-alert mr-1"></span>
                    <strong i18n="@@bma_serverprepareforsalemodal_description4">Wipe the data from all disks. </strong>
                </li>
            </ul>
            <p>
                <strong i18n="@@bma_common_rebootprocess"> Be aware that the dedicated server needs to be rebooted for this process to complete. </strong>
            </p>
            <div class="form-group">
                <div class="checkbox">
                    <input type="checkbox" id="powerCycle" formControlName="powerCycle" value="true" checked="checked" autofocus />
                    <label i18n="@@bma_common_powercycleserver" for="powerCycle" class="ml-1">Power cycle the dedicated server</label>
                </div>
            </div>

            <div class="form-group">
                <div class="checkbox">
                    <input type="checkbox" id="doWipeDisks" formControlName="doWipeDisks" value="true" checked="checked" />
                    <label i18n="@@bma_serverprepareforsalemodal_wipedata" for="doWipeDisks" class="ml-1">Wipe the data from all disks</label>
                </div>
            </div>

            <div class="form-group">
                <div class="checkbox">
                    <input type="checkbox" id="firmwareUpgrade" formControlName="firmwareUpgrade" value="true" checked="checked" />
                    <label i18n="@@bma_serverprepareforsalemodal_updatefirmware" for="firmwareUpgrade" class="ml-1">Update Firmware</label>
                </div>
            </div>

            <div class="form-group">
                <div class="checkbox">
                    <input type="checkbox" id="memoryTest" formControlName="memoryTest" value="false" checked="unchecked" (click)="openMemoryTestTimeOptions()" />
                    <label i18n="@@bma_serverprepareforsalemodal_memoryTest" for="memoryTest" class="ml-1"> Memory Test (experimental)</label>
                    <select id="stressTest" formControlName="stressTest" autofocus="" class="form-control ng-pristine ng-valid ng-touched" ng-reflect-name="stressTest" *ngIf="showMemoryTestTimeOptions">
                        <option value="30m" ng-reflect-value="30m" class="ng-star-inserted">30 minutes</option>
                        <option value="12h" ng-reflect-value="12h" class="ng-star-inserted">12 hours</option>
                    </select>
                </div>
            </div>

            <div class="input-group">
                <div class="input-group-prepend">
                    <span i18n="@@bma_serverprepareforsalemodal_batchid" class="input-group-text" for="batchId">Batch job ID</span>
                </div>
                <input type="text" class="form-control" id="batchId" formControlName="batchId" i18n-placeholder="@@bma_serverprepareforsalemodal_batchidplaceholder" placeholder="Optional" />
            </div>

            <div class="alert alert-info mt-4">
                <div class="alert-icon"><i class="fa fa-information fa-lg" aria-hidden="true"></i></div>
                <h4 i18n="@@bma_common_information" class="alert-heading">Information</h4>
                <p i18n="@@bma_common_leasewebreboot" class="alert-body small">Leaseweb can perform a power cycle for you, or you can choose to reboot the dedicated server yourself. If you choose to reboot the dedicated server yourself, you need to do so within the next 4 hours.</p>
            </div>
        </div>
    </form>

    <div *ngIf="form.errors?.api?.errorMessage" class="alert alert-danger" role="alert">
        <span>{{ form.errors.api.errorMessage }}</span>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_cancel" pButton label="Cancel" icon="pi pi-times" (click)="closeModal()" type="button" class="p-button-secondary"></button>
            <button i18n-label="@@bma_common_launch" pButton (click)="doPrepareForSale()" label="Launch" icon="pi pi-check" [loading]="isSubmitting" [disabled]="form.invalid"></button>
        </div>
    </ng-template>
</p-dialog>
