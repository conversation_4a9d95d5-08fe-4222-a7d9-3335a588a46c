import { Component, OnInit, Input } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Clipboard } from '@angular/cdk/clipboard';
import { ModalService } from 'src/app/services/modal.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ReferenceEditModalComponent } from 'src/app/reference-edit-modal/reference-edit-modal.component';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { CommerceService } from 'src/app/services/commerce.service';
import { CidrToIpPipe } from 'src/app/pipes/cidr-to-ip.pipe';
import { PowerbarOutletOperationModalComponent } from 'src/app/powerbar/powerbar-outlet-operation-modal/powerbar-outlet-operation-modal.component';
import { ServerRemoteManagementModalComponent } from '../server-remote-management-modal/server-remote-management-modal.component';

@Component({
  selector: 'app-server-details',
  templateUrl: './server-details.component.html',
  styleUrls: ['./server-details.component.css'],
})
export class ServerDetailsComponent implements OnInit {
  @Input() server: any;
  @Input() equipmentId: any;
  @Input() isEmployee: boolean;
  @Input() equipmentType: any;
  isLoadingOsDetails = false;
  dynamicDialogRef: DynamicDialogRef;
  job: any;
  networkPorts = [];
  monitoring: any = {};

  constructor(
    private alertService: AlertService,
    private httpClient: HttpClient,
    private modalService: ModalService,
    private currentUserService: CurrentUserService,
    private cidrToIpPipe: CidrToIpPipe,
    private commerceService: CommerceService,
    private clipboard: Clipboard
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    if (this.server.contract) {
      this.getMonitoringStatus();
    }
    this.getInstalledOs();
    if (this.server.networkInterfaces) {
      const networkInterfaceOrder = ['public', 'internal', 'remoteManagement'];
      networkInterfaceOrder.forEach((networkInterfacerKey) => {
        if (
          this.server.networkInterfaces[networkInterfacerKey] &&
          this.server.networkInterfaces[networkInterfacerKey].ports.length > 0
        ) {
          const networkPorts = this.server.networkInterfaces[networkInterfacerKey].ports;
          networkPorts.forEach((networkPort) => {
            networkPort.networkType = networkInterfacerKey;
            this.networkPorts.push(networkPort);
          });
        }
      });
    }
  }

  getInstalledOs(): void {
    if (!this.server.contract?.startsAt) {
      return;
    }
    this.isLoadingOsDetails = true;
    const contractStartDate = new Date(this.server.contract.startsAt);
    contractStartDate.setDate(contractStartDate.getDate() - 7);
    const obj = {
      $and: [
        { createdAt: { $gte: contractStartDate.toISOString().slice(0, 10) } },
        { status: 'FINISHED' },
        { type: 'install' },
      ],
    };
    const params = new HttpParams().set('filter', JSON.stringify(obj)).set('limit', '1').set('offset', '0');
    this.httpClient.get<any>(`/_/internal/bmpapi/v2/servers/${this.server.id}/jobs`, { params }).subscribe({
      next: (data: any) => {
        if (data.jobs.length > 0) {
          this.job = data.jobs[0];
        }
        this.isLoadingOsDetails = false;
      },
      error: (error: any) => {
        this.isLoadingOsDetails = false;
      },
    });
  }

  openRemoteManagementModal(serverId: string) {
    this.modalService.show(ServerRemoteManagementModalComponent, { serverId });
  }

  getControlPanelURL(): string {
    const panel = this.job?.payload.controlPanelId;

    if (!panel) {
      return '';
    }

    const publicIp = this.cidrToIpPipe.transform(this.server.networkInterfaces.public?.ip);

    if (!publicIp) {
      return '';
    }

    let port;
    if (panel.startsWith('CPANEL')) {
      port = 2087;
    } else if (panel.startsWith('PLESK')) {
      port = 8443;
    } else {
      return '';
    }

    return 'https://' + publicIp + ':' + port;
  }

  openReferenceEditModal() {
    this.dynamicDialogRef = this.modalService.show(ReferenceEditModalComponent, {
      equipment: this.server,
      equipmentType: 'servers',
    });

    this.dynamicDialogRef.onClose.subscribe((reason: any) => {
      if (typeof reason === 'string') {
        if (this.server.contract) {
          this.server.contract.reference = reason;
        }
      }
    });
  }

  openPowerbarOutletOperationModal(name: string, outlet: string) {
    this.modalService.show(PowerbarOutletOperationModalComponent, {
      name,
      outlet,
    });
  }

  getMonitoringStatus() {
    this.monitoring.isLoading = true;

    /*eslint-disable */
    const customerHeaders = {
      'APIGW-Impersonated-Customer-ID': this.server.contract.customerId,
      'APIGW-Impersonated-Customer-Sales-Org-ID': this.server.contract.salesOrgId,
    };
    /*eslint-enable */

    this.httpClient
      .get<any>(`/_/internal/monitoring/hosts/status?equipments[]=${this.server.id}`, {
        headers: customerHeaders,
      })
      .subscribe({
        next: (data: any) => {
          this.monitoring.isLoading = false;
          this.monitoring.isActive = data.monitorings.filter((monitoring) => monitoring.id !== null).length > 0; // at least one monitoring is valid
          this.monitoring.isHostUp = data.monitorings.filter((monitoring) => monitoring.status !== 'UP').length === 0; // no monitoring reports something else than UP
        },
        error: (error: any) => {
          this.monitoring.isLoading = false;
          this.monitoring.error = 'Error fetching monitoring status';
        },
      });
  }

  startMonitoring() {
    this.monitoring.isLoading = true;

    /*eslint-disable */
    const customerHeaders = {
      'APIGW-Impersonated-Customer-ID': this.server.contract.customerId,
      'APIGW-Impersonated-Customer-Sales-Org-ID': this.server.contract.salesOrgId,
    };
    /*eslint-enable */

    this.httpClient
      .post<any>(
        '/_/internal/monitoring/hosts',
        {
          address: this.cidrToIpPipe.transform(this.server.networkInterfaces.public.ip),
          customerId: this.server.contract.customerId,
          salesOrgId: this.server.contract.salesOrgId,
          productType: 'DEDICATED',
          equipmentId: this.server.id,
          zone: this.server.location.site,
          emails: [],
          phones: [],
          services: [],
        },
        {
          headers: customerHeaders,
        }
      )
      .subscribe({
        next: (data: any) => {
          this.monitoring.isLoading = false;
          this.monitoring.isActive = true;
          this.monitoring.isHostUp = null;

          this.alertService.alert({
            type: 'success',
            description: 'The monitoring will be started shortly',
          });
        },
        error: (error: any) => {
          this.monitoring.isLoading = false;
          this.monitoring.error = 'Error starting monitoring';
        },
      });
  }

  getMonitoringLink() {
    // eslint-disable-next-line
    return window['MONITORING_URL'];
  }

  getMonitoringClass() {
    if (this.monitoring.isHostUp === true) {
      return 'text-success';
    }

    if (this.monitoring.isHostUp === false) {
      return 'text-danger';
    }

    return 'text-secondary';
  }

  getCommerceConfigureProductUrl(type) {
    return this.commerceService.getCommerceConfigureProductUrl(this.server.contract.id, type);
  }

  doCopy(publicIp: string): void {
    const pending = this.clipboard.beginCopy(this.cidrToIpPipe.transform(publicIp));
    let remainingAttempts = 3;
    const attempt = () => {
      const result = pending.copy();
      if (remainingAttempts === 0) {
        this.alertService.alert({
          type: 'error',
          description: $localize`:@@bma_common_errorcopyingpublicip:Sorry, there was an error copying public IP.`,
        });
      } else if (!result && --remainingAttempts) {
        setTimeout(attempt);
      } else {
        pending.destroy();
      }
    };
    attempt();

    this.alertService.alert({
      type: 'success',
      description: $localize`:@@bma_common_publicipcopiedtoclipboard:Public IP copied to clipboard`,
    });
  }
}
