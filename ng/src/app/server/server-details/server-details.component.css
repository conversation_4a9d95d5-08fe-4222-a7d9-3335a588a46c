.table-col-25 {
  width: 25%;
}
.table-col-75 {
  width: 75%;
}
.disabled {
  color: #aaa !important;
  pointer-events: none;
  cursor: not-allowed;
}
.disabled * {
  color: #aaa !important;
  pointer-events: none;
  cursor: not-allowed;
}

.windows-cancel-spla-hover-wrapper {
  position: relative;
  float: right;
  margin-left: 2px;
}

.windows-cancel-spla-hover-wrapper .hover-content {
  display: none;
  position: absolute;
  z-index: 1;
  width: 250px;
  padding: 15px;
  background-color: #f9f9f9;
  border: 1px solid #ccc;
  padding: 15px !important;
  padding-bottom: 0px !important;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  left: 50%;
  transform: translateX(-50%);
  overflow: auto;
  max-height: 90vh;
}

.windows-cancel-spla-hover-wrapper:hover .hover-content {
  display: block;
}
