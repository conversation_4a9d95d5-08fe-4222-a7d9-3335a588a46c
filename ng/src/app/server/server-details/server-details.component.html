<div class="row">
    <div class="col-md-6">
        <h3>
            <span i18n="@@bma_common_technicaldetails" class="mr-1">Technical Details</span>
            <sup>
                <a target="_blank" href="https://kb.leaseweb.com/kb/dedicated-server/dedicated-server-managing-your-dedicated-server-details/#ManagingyourDedicatedServerdetails-Technicaldetails">
                    <i i18n-title="@@bma_common_kb" title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i>
                </a>
            </sup>
        </h3>
        <table class="table table-sm">
            <colgroup>
                <col class="table-col-25" />
                <col class="table-col-75" />
            </colgroup>

            <tbody>
                <tr>
                    <th i18n="@@bma_common_brand">Brand</th>
                    <td>{{ server.specs.brand }}</td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_model">Model</th>
                    <td>{{ server.specs.chassis|default:'-' }}</td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_serial">Serial</th>
                    <td>
                        <ng-container *ngIf="server.serialNumber|default; else notAvailable">
                            {{ server.serialNumber }}
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_processor">Processor</th>
                    <td>
                        <ng-container *ngIf="server.specs.cpu.quantity; else notAvailable"> {{ server.specs.cpu.quantity }}x {{ server.specs.cpu.type }} </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_ram">RAM</th>
                    <td>
                        <ng-container *ngIf="!isEmployee">
                            <a [class.disabled]="server.isSharedEol" i18n-title="@@bma_common_hardwareupgrade" *ngIf="(server.contract?.status|default:'') == 'ACTIVE'; else pendingContractModification" target="_blank" [href]="getCommerceConfigureProductUrl('DEDSER02_MOD_RAM')" class="float-right" title="Hardware Upgrade">
                                <i class="fa fa-upgrade"></i>
                            </a>
                        </ng-container>
                        <ng-container *ngIf="server.specs.ram.size; else notAvailable"> {{ server.specs.ram.size }} {{ server.specs.ram.unit }} </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_disks">Disks</th>
                    <td>
                        <ng-container *ngIf="!isEmployee">
                            <a [class.disabled]="server.isSharedEol" i18n-title="@@bma_common_hardwareupgrade" *ngIf="(server.contract?.status|default:'') == 'ACTIVE'; else pendingContractModification" target="_blank" [href]="getCommerceConfigureProductUrl('DEDSER02_MOD_DISK')" class="float-right" title="Hardware Upgrade">
                                <i class="fa fa-upgrade"></i>
                            </a>
                        </ng-container>
                        <ng-container *ngIf="server.specs.hdd?.length > 0; else notAvailable">
                            <ng-container *ngFor="let hdd of server.specs.hdd">
                                {{ hdd.amount }}x{{ hdd.size }}{{ hdd.unit }} {{ hdd.type }}
                                {{ hdd.performanceType|default|formatDiskPerformanceType }}
                                <br />
                            </ng-container>
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_pcicards">PCI Cards</th>
                    <td>
                        <ng-container *ngIf="(server.specs.pciCards|default:[]).length > 0; else notAvailable">
                            <ng-container *ngFor="let pciCard of server.specs.pciCards|default:[]"> {{ pciCard.description }}<br /></ng-container>
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_publicip">Public IP</th>
                    <td>
                        <ng-container *ngIf="server.networkInterfaces.public?.ip|default; noPublicIp">
                            <span class="selectable">{{ server.networkInterfaces.public?.ip|default|cidrToIp }}</span>
                            <span i18n="@@bma_common_nullrouted" *ngIf="server.networkInterfaces.public?.nullRouted|default" class="badge badge-danger">Null Routed</span>
                            <a *ngIf="server.networkInterfaces.public?.ip" href="javascript:void(0);" (click)="doCopy(server.networkInterfaces.public?.ip)"> &nbsp;<i i18n-title="@@bma_common_copypublicip" title="Copy" class="fa fa-copy" aria-hidden="true"></i> </a>
                        </ng-container>

                        <ng-container *ngIf="!isEmployee">
                            <a [class.disabled]="((server.contract|default:'') && server.contract?.status != 'ACTIVE' || ('DEDICATED' == (server.rack.type|default:''|uppercase) && server.rack.id|default:'')) || server.isSharedEol" i18n-title="@@bma_common_requestmoreips" *ngIf="server.contract?.status|default:'' == 'ACTIVE'; else pendingContractModification" class="float-right" title="Request more IPs" target="_blank" [href]="getCommerceConfigureProductUrl('DEDSER02_MOD_IPV4')" data-client-validation="true" data-force-another="2">
                                <i class="fa fa-ipmanagement"></i>
                            </a>
                        </ng-container>
                    </td>
                </tr>
                <tr *ngIf="server.contract|default">
                    <th i18n="@@bma_common_monitoringstatus">Monitoring Status</th>
                    <td>
                        <ng-container *ngIf="!monitoring.error; else monitoringError">
                            <span i18n="@@bma_common_loading" *ngIf="monitoring.isLoading">Loading...</span>
                            <a *ngIf="!monitoring.isLoading && monitoring.isActive === true" [href]="getMonitoringLink()"><i [ngClass]="getMonitoringClass()" class="fa fa-monitoring fa-lg" aria-hidden="true"></i> <span i18n="@@bma_common_mamagemonitoring" class="ml-1">Manage monitoring</span></a>
                            <span *ngIf="!monitoring.isLoading && monitoring.isActive === false" (click)="startMonitoring()">
                                <a i18n="@@bma_common_startmonitoring" href="javascript:void(0);">Start monitoring</a>
                            </span>
                        </ng-container>
                        <ng-template #monitoringError>
                            <span class="text-danger">{{ monitoring.error }}</span>
                        </ng-template>
                    </td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_remotemanagement">Remote Management</th>
                    <td>
                        <a href="javascript:void(0);" (click)="openRemoteManagementModal(server.id)" class="float-right">
                            <i i18n-title="@@bma_common_remotemanagement" title="Remote Management" class="fa fa-remotemanagement" aria-hidden="true"></i>
                        </a>

                        <ng-container *ngIf="server.featureAvailability.remoteManagement; else notAvailable">
                            <span class="selectable">{{ server.networkInterfaces.remoteManagement.ip|default:'No IP details'|cidrToIp }}</span>
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_hardwareraid">Hardware RAID</th>
                    <td>
                        <ng-container *ngIf="server.specs.hardwareRaidCapable|default; else notAvailable"><span i18n="@@bma_common_available">Available</span></ng-container>
                    </td>
                </tr>
            </tbody>
        </table>

        <h3 class="mt-5 mb-2" i18n="@@bma_common_pduinformation">PDU Information</h3>

        <table class="table table-sm">
            <thead>
                <tr>
                    <th i18n="@@bma_common_pdu" scope="col">PDU</th>
                    <th i18n="@@bma_common_outlet" scope="col">Outlet</th>
                    <th scope="col"></th>
                </tr>
            </thead>

            <tbody>
                <tr *ngFor="let powerPort of server.powerPorts">
                    <td *ngIf="isEmployee" class="align-middle">
                        <a [routerLink]="['/powerbars', powerPort.name]">
                            {{ powerPort.name | uppercase }}
                        </a>
                    </td>
                    <td *ngIf="!isEmployee">{{ powerPort.name | uppercase }}</td>

                    <td class="align-middle">{{ powerPort.port | uppercase }}</td>

                    <td *ngIf="isEmployee" class="text-right">
                        <a href="javascript:void(0);" (click)="openPowerbarOutletOperationModal(powerPort.name, powerPort.port)">
                            <span class="fa fa-administration"></span>
                        </a>
                    </td>
                </tr>
                <tr *ngIf="server.powerPorts?.length == 0">
                    <td i18n="@@bma_common_nopduoutlets" colspan="3" class="text-center p-4">There are no PDU outlets available for this server</td>
                </tr>
            </tbody>
        </table>

        <h3 class="mt-5 mb-2">
            <span i18n="@@bma_common_networkinformation">Network Information</span>
        </h3>

        <table class="table table-sm">
            <thead>
                <tr>
                    <th i18n="@@bma_common_switch" scope="col">Switch</th>
                    <th i18n="@@bma_common_network" scope="col">Network</th>
                    <th i18n="@@bma_common_port" scope="col" class="text-right">Port</th>
                </tr>
            </thead>
            <ng-container *ngIf="networkPorts">
                <tbody>
                    <tr *ngFor="let networkPort of networkPorts">
                        <td>
                            <a *ngIf="isEmployee" [routerLink]="['/networkDevices', networkPort.name]">
                                {{ networkPort.name }}
                            </a>
                            <span *ngIf="!isEmployee">{{ networkPort.name }}</span>
                        </td>
                        <td>{{ networkPort.networkType }}</td>
                        <td class="text-right">{{ networkPort.port }}</td>
                    </tr>
                </tbody>
            </ng-container>
            <tr *ngIf="networkPorts?.length == 0">
                <td i18n="@@bma_common_nonetworkports" colspan="3" class="text-center p-4">There are no network ports available for this equipment</td>
            </tr>
        </table>
    </div>

    <div class="col-md-6">
        <h3>
            <span i18n="@@bma_common_administrativedetails" class="mr-1">Administrative Details</span>
            <sup>
                <a target="_blank" href="https://kb.leaseweb.com/kb/dedicated-server/dedicated-server-managing-your-dedicated-server-details/#ManagingyourDedicatedServerdetails-Administrativedetails">
                    <i i18n-title="@@bma_common_kb" title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i>
                </a>
            </sup>
        </h3>
        <table class="table table-sm">
            <tbody>
                <tr *ngIf="'DEDICATED' == (server.rack.type|default|uppercase) && server.rack.id|default">
                    <th i18n="@@bma_common_dedicatedrack">Dedicated Rack</th>
                    <td>
                        <a [routerLink]="['/racks', server.rack.id]">
                            <span class="text-monospace">{{ server.rack.id }}</span>
                        </a>
                    </td>
                </tr>

                <tr *ngIf="server.contract?.aggregationPackId|default">
                    <th i18n="@@bma_common_aggregationpack">Aggregation Pack</th>
                    <td>
                        <a [routerLink]="['/aggregationPacks', server.contract?.aggregationPackId]">
                            <span class="text-monospace">{{ server.contract?.aggregationPackId }}</span>
                        </a>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_deliverystatus">Delivery Status</th>
                    <td>
                        <app-contract-delivery-status [deliveryStatus]="server.contract?.deliveryStatus|default:'UNASSIGNED'"> </app-contract-delivery-status>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_contractstatus">Contract Status</th>
                    <td>
                        <app-contract-status [status]="server.contract?.status|default:'UNASSIGNED'"></app-contract-status>

                        <small i18n="@@bma_common_contractstatusupgradealert" *ngIf="['IN_MODIFICATION', 'MODIFICATION_FAILED'].indexOf((server.contract?.status|default|uppercase)) > -1"> It is not possible to request upgrades, enable private network, or request additional IP address while the contract status is </small>
                        <small i18n="@@bma_contractstatus_modification" *ngIf="(server.contract?.status|default|uppercase) == 'IN_MODIFICATION'"> IN MODIFICATION </small>
                        <small i18n="@@bma_contractstatus_modification_failed" *ngIf="(server.contract?.status|default|uppercase) == 'MODIFICATION_FAILED'"> MODIFICATION FAILED </small>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_id">ID</th>
                    <td>
                        <span class="selectable">{{ server.id }}</span>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_product">Product</th>
                    <td>
                        <span>{{ server.contract?.managedServices?.packName ? 'Managed Server' : 'Dedicated Server' }}</span>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_location">Location</th>
                    <td>
                        <app-equipment-location [location]="server.location"></app-equipment-location>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_serviceid">Service ID</th>
                    <td>
                        <span *ngIf="server.contract; else notAvailable" class="selectable">{{ server.contract.id|default }}</span>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_reference">Reference</th>
                    <td>
                        <ng-container *ngIf="server.contract?.reference|default; else notAvailable">
                            {{ server.contract?.reference|default }}
                        </ng-container>

                        <a i18n-title="@@bma_common_editreference" *ngIf="server.contract" class="pull-right" href="#" (click)="openReferenceEditModal(); false" title="Edit Reference">
                            <span class="fa fa-change" aria-hidden="true"></span>
                        </a>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_billingfrequency">Billing Frequency</th>
                    <td>
                        <ng-container *ngIf="server.contract; else notAvailable">
                            {{ server.contract.billingCycle|default }}
                            {{ server.contract.billingFrequency|default | lowercase }}(s)
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_contractterm">Contract Term</th>
                    <td>
                        <ng-container *ngIf="server.contract?.contractTerm|default; else notAvailable">
                            {{ server.contract.contractTerm }} month(s)
                            <ng-container *ngIf="server.contract.contractType != 'NORMAL'">
                                &nbsp;
                                <span i18n="@@bma_common_contracttype" class="badge badge-info"> {{ server.contract.contractType|uppercase }} Contract </span>
                            </ng-container>
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_startdate">Start Date</th>
                    <td>
                        <ng-container *ngIf="server.contract?.startsAt|default; else notAvailable">
                            {{ server.contract.startsAt|lswDate }}
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_enddate">End Date</th>
                    <td>
                        <ng-container *ngIf="server.contract?.endsAt|default; else notAvailable">
                            {{ server.contract.endsAt|lswDate }}
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_connectivity">Connectivity</th>
                    <td>
                        <app-data-pack-display [equipment]="server" [equipmentType]="equipmentType" [isEmployee]="isEmployee"></app-data-pack-display>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_dataused">Data Used</th>
                    <td class="data_used">
                        <app-data-usage [equipment]="server" [equipmentType]="equipmentType"></app-data-usage>
                    </td>
                </tr>

                <tr>
                    <th>
                        <span i18n="@@bma_common_sla" class="mr-1">SLA</span>
                        <sup>
                            <a href="https://kb.leaseweb.com/support/service-level-agreement-sla" target="_blank">
                                <i i18n-title="@@bma_common_kb" title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i>
                            </a>
                        </sup>
                    </th>
                    <td>
                        <ng-container *ngIf="server.contract; else notAvailable">
                            {{ server.contract.sla }}
                            <ng-container *ngIf="!isEmployee && !server.contract?.managedServices?.packName">
                                <ng-container *ngIf="(server.contract?.status|default:'') == 'ACTIVE'; else pendingContractModification">
                                    <a [class.disabled]="server.isSharedEol" i18n-title="@@bma_common_upgradesla" title="Upgrade SLA" class="float-right" target="_blank" [href]="getCommerceConfigureProductUrl('DEDSER02_MOD_SLA')">
                                        <i class="fa fa-upgrade" aria-hidden="true"></i>
                                    </a>
                                </ng-container>
                            </ng-container>
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th>
                        <span i18n="@@bma_common_softwarelicenses" class="mr-1">Software Licenses</span>
                        <sup>
                            <a target="_blank" href="https://kb.leaseweb.com/kb/software/">
                                <i i18n-title="@@bma_common_kb" title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i>
                            </a>
                        </sup>
                    </th>
                    <td>
                        <ng-container *ngIf="server.contract; else notAvailable">
                            <span class="d-inline-block">
                                <ng-container *ngFor="let sw of server.contract.softwareLicenses|default:[]">
                                    {{ sw.name }}
                                    <ng-container *ngIf="sw.name.toLowerCase().includes('windows')">
                                        <div class="windows-cancel-spla-hover-wrapper">
                                            <small>
                                                <i class="fa fa-knowledge" aria-hidden="true"></i>
                                            </small>
                                            <div class="hover-content">
                                                <p i18n="@@bma_common_cancel_spla_for_windows" class="alert-body">It is crucial to remember to cancel the SPLA separately when it is no longer needed. This can be done by opening a support ticket. It is important because if this step is not taken, you will continue to be billed for the SPLA, even if you are no longer using it.</p>
                                            </div>
                                        </div>
                                    </ng-container>
                                    <br />
                                </ng-container>
                                <ng-container *ngIf="(server.contract.softwareLicenses|default:[]).length == 0">
                                    <span i18n="@@bma_serverdetails_nosoftware" class="text-muted">No Software found </span>
                                </ng-container>
                            </span>
                            <ng-container *ngIf="!isEmployee && !server.contract?.managedServices?.packName">
                                <a *ngIf="server.contract?.status == 'ACTIVE' && !server.isSharedEol" class="float-right" target="_blank" [href]="getCommerceConfigureProductUrl('DEDSER02_MOD_CPS')" data-client-validation="true" data-force-another="2">
                                    <span class="fa fa-cart mr-1" aria-hidden="true"></span>
                                    <span i18n="@@bma_serverdetails_ordercontrolpanel">Order Control Panel</span>
                                </a>
                                <br />
                                <a *ngIf="server.contract?.status == 'ACTIVE' && !server.isSharedEol" class="float-right" target="_blank" [href]="getCommerceConfigureProductUrl('DEDSER02_MOD_OS')" data-client-validation="true" data-force-another="2">
                                    <span class="fa fa-cart mr-1" aria-hidden="true"></span>
                                    <span i18n="@@bma_serverdetails_orderwindows_license">Order Windows License</span>
                                </a>
                            </ng-container>
                        </ng-container>
                    </td>
                </tr>
            </tbody>
        </table>
        <app-private-network-information-section [equipment]="server" [equipmentType]="equipmentType"></app-private-network-information-section>
        <div *ngIf="server.contract?.managedServices?.packName" class="mt-5">
            <h3 i18n="@@bma_serverdetails_managedhosting">Managed Hosting</h3>
            <table class="table table-sm">
                <tbody>
                    <tr>
                        <th i18n="@@bma_common_package">Package</th>
                        <td>
                            <ng-container *ngIf="server.contract.managedServices.packName; else notAvailable">{{ server.contract.managedServices.packName }}</ng-container>
                        </td>
                    </tr>
                    <tr>
                        <th i18n="@@bma_serverdetails_bundle">Bundle</th>
                        <td>
                            <ng-container *ngIf="server.contract.managedServices.bundleName; else notAvailable">{{ server.contract.managedServices.bundleName }}</ng-container>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-5">
            <h3>
                <span i18n="@@bma_serverdetails_automatedinstallinformation" class="mr-1">Automated Install Information</span>
                <p-overlayPanel #op [dismissable]="true" [style]="{'width':'400px'}" [appendTo]="'body'">
                    <ng-template pTemplate>
                        <h3 i18n="@@bma_serverdetails_installinformation">Install information</h3>
                        <span i18n="@@bma_serverdetails_installinformationdescription">Please note - information displayed here is taken from the last successful automated installation job, therefore it might not accurately represent the current state of the dedicated server.</span>
                    </ng-template>
                </p-overlayPanel>

                <sup>
                    <a (click)="op.toggle($event)" href="javascript:void(0);">
                        <i class="fa fa-knowledge install-information" aria-hidden="true"></i>
                    </a>
                </sup>
            </h3>
            <app-loader *ngIf="isLoadingOsDetails"></app-loader>
            <table *ngIf="!isLoadingOsDetails && job" class="table table-sm">
                <tbody>
                    <tr>
                        <th i18n="@@bma_serverdetails_os">OS</th>
                        <td>{{job.payload.os.name|default:'NA'}}</td>
                    </tr>
                    <tr>
                        <th i18n="@@bma_common_installationdate">Installation Date</th>
                        <td>{{job.updatedAt|default:'NA'}}</td>
                    </tr>
                    <tr>
                        <th>
                            <span i18n="@@bma_common_cp">Control Panel</span>
                            <sup>
                                <a (click)="controlPanelOp.toggle($event)" href="javascript:void(0);">
                                    <i class="fa fa-knowledge install-information ml-1" aria-hidden="true"></i>
                                </a>
                            </sup>
                            <p-overlayPanel #controlPanelOp [dismissable]="true" [style]="{'width':'400px'}" [appendTo]="'body'">
                                <ng-template pTemplate>
                                    <h3 i18n="@@bma_serverdetails_securitywarningbypass">Security warning bypass</h3>
                                    <span i18n="@@bma_serverdetails_securitywarningdescription">You can safely ignore the security warning displayed by the browser when opening the control panel</span>
                                </ng-template>
                            </p-overlayPanel>
                        </th>
                        <td>
                            <a *ngIf="job.payload.controlPanelId; else notAvailable" [href]="getControlPanelURL()" target="_blank">{{job.payload.controlPanelId}}</a>
                        </td>
                    </tr>
                    <tr>
                        <th i18n="@@bma_common_hostname">Hostname</th>
                        <td>{{ job.payload.hostname|default:'NA' }}</td>
                    </tr>
                    <tr *ngIf="job.raid">
                        <th i18n="@@bma_common_raid">RAID</th>
                        <td>
                            <ul class="list-unstyled mb-0">
                                <li i18n="@@bma_common_raidtypeinstallinfo" class="text-muted">Type: {{ job.raid.type|default:'NA' }}</li>
                                <li i18n="@@bma_common_raidlevelinstallinfo" class="text-muted">Level: {{ job.raid.level|default:'NA' }}</li>
                                <li i18n="@@bma_common_raiddisksinstallinfo" class="text-muted">No. of Disks: {{ job.raid.numberOfDisks|default:'All disks' }}</li>
                            </ul>
                        </td>
                    </tr>
                    <tr *ngIf="job.featuresUtilized?.length > 0">
                        <th i18n="@@bma_common_utilizedfeatures">Utilized Features</th>
                        <td>
                            <ul class="list-unstyled mb-0">
                                <li *ngFor="let feature of job.featuresUtilized">{{ feature }}</li>
                            </ul>
                        </td>
                    </tr>
                    <tr *ngIf="isEmployee">
                        <th i18n="@@bma_common_installationdetails">Installation Details</th>
                        <td>
                            <a [routerLink]="['jobs', job.uuid]">{{ job.uuid }}</a>
                        </td>
                    </tr>
                </tbody>
            </table>
            <div *ngIf="!isLoadingOsDetails && !job">
                <p i18n="@@bma_serverdetails_noinstalllation">No known installation details available</p>
            </div>
        </div>
    </div>
</div>

<ng-template #notAvailable>
    <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
</ng-template>

<ng-template #pendingContractModification>
    <span title="Pending contract modification" class="float-right text-muted">
        <i class="fa fa-refresh"></i>
        <span i18n-title="@@bma_common_pendingcontractmodification" class="sr-only">Pending contract modification</span>
    </span>
</ng-template>
