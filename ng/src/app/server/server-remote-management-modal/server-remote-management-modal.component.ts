import { AlertService } from 'src/app/services/alert.service';
import { Component, OnInit } from '@angular/core';
import { Clipboard } from '@angular/cdk/clipboard';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { CurrentUserService } from 'src/app/services/current-user.service';

@Component({
  selector: 'app-server-remote-management-modal',
  templateUrl: './server-remote-management-modal.component.html',
  styleUrls: ['./server-remote-management-modal.component.css'],
})
export class ServerRemoteManagementModalComponent implements OnInit {
  serverId: string;
  server: any;
  credentials: any;
  activeJob: any;
  isEmployee = false;
  powerCycle = true;
  isPrepareIpmiReset = false;
  canlaunchIpmiReset = false;
  ipmiJobError = false;
  error: boolean;
  isSubmitting = false;
  isLoadingServer = false;
  isLoadingCredentials = false;
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private alertService: AlertService,
    private clipboard: Clipboard
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.serverId = this.config.data.serverId;
    if (this.server) {
      this.error = false;
      this.serverId = this.server.id;
    }

    if (!this.serverId) {
      this.error = true;
      return;
    }

    if (!this.server) {
      this.isLoadingServer = true;
      this.httpClient.get<any>(`/_/bareMetals/v2/servers/${this.serverId}`).subscribe(
        (server: any) => {
          this.isLoadingServer = false;
          this.server = server;
          this.server.isSharedEol = this.isServerSharedEol(this.server);
          this.error = false;
        },
        (error) => {
          this.isLoadingServer = false;
          this.error = true;
        }
      );
    }

    this.isLoadingCredentials = true;
    this.httpClient.get<any>(`/_/bareMetals/v2/servers/${this.serverId}/credentials/REMOTE_MANAGEMENT`).subscribe(
      (credentials: any) => {
        this.isLoadingCredentials = false;
        this.credentials = credentials;
        this.error = false;
      },
      (error) => {
        this.isLoadingCredentials = false;
        this.credentials = [];
      }
    );
  }

  prepareIpmiReset() {
    this.isPrepareIpmiReset = true;
    this.canlaunchIpmiReset = false;
    if (this.server?.networkInterfaces?.public?.nullRouted) {
      return;
    }

    this.httpClient
      .get<any>(`/_/bareMetals/v2/servers/${this.serverId}/jobs?limit=1&status=ACTIVE`)
      .subscribe((jobs: any) => {
        if (jobs._metadata.totalCount > 0) {
          this.activeJob = jobs.jobs[0];
          this.canlaunchIpmiReset = false;
        } else {
          this.canlaunchIpmiReset = true;
        }
      });
  }

  launchIpmiReset(): boolean {
    this.isSubmitting = true;
    this.alertService.clear();

    const body = {
      powerCycle: this.powerCycle,
    };
    this.httpClient.post<any>(`/_/bareMetals/v2/servers/${this.serverId}/ipmiReset`, body).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;
        this.ipmiJobError = false;
        this.alertService.alert({
          type: 'success',
          description: $localize`:@@bma_serverremotemanagementmodal_success:IPMI Reset is launched`,
        });
        this.closeModal();
      },
      error: (error: any) => {
        this.isSubmitting = false;
        this.ipmiJobError = error.error;
        this.canlaunchIpmiReset = false;
      },
    });

    return false;
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }

  isServerSharedEol(server: any): boolean {
    if (!server.contract) {
      return false;
    }

    return server.contract.salesOrgId === '1700' && server.rack.type === 'SHARED_EOL';
  }
}
