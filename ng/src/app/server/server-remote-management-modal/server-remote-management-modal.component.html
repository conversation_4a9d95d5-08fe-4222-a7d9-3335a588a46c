<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '800px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_serverremotemanagementmodal_header" class="pull-left">
            Remote Management
            <sup>
                <a href="https://kb.leaseweb.com/kb/remote-management-of-your-dedicated-server/remote-management-of-your-dedicated-server-overview/#RemoteManagementofyourDedicatedServer-RemoteManagementDetails" target="_blank">
                    <i title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i>
                </a>
            </sup>
        </h3>
    </ng-template>

    <div class="modal-body">
        <app-loader *ngIf="isLoadingServer || isLoadingCredentials"></app-loader>

        <div *ngIf="!isLoadingServer && !isLoadingCredentials && server && credentials.credentials">
            <div *ngIf="server.featureAvailability.remoteManagement && credentials.credentials.length === 0" class="alert alert-warning alert-dismissible" role="alert">
                <p i18n="@@bma_serverremotemanagementmodal_description1">Your dedicated server is capable of remote management, but it has either never been setup, or the credentials have been removed.</p>
                <p i18n="@@bma_serverremotemanagementmodal_description2">Please perform an IPMI reset to setup Remote Management.</p>
            </div>

            <div *ngIf="!server.featureAvailability.remoteManagement">
                <h4 i18n="@@bma_serverremotemanagementmodal_subheader">Remote Management not available</h4>
                <p i18n="@@bma_serverremotemanagementmodal_description3">Unfortunately remote management is not available for this dedicated server.</p>
                <p i18n="@@bma_serverremotemanagementmodal_description4">Your dedicated server, or the rack which it is located in is not capable of remote management.</p>
                <p i18n="@@bma_serverremotemanagementmodal_description5">Contact a sales representative to discuss the available options.</p>
            </div>

            <div *ngIf="server.featureAvailability.remoteManagement">
                <div>
                    <h4 i18n="@@bma_common_ipmiipaddress">IPMI IP Address</h4>
                    <p>
                        <span>{{ server.networkInterfaces.remoteManagement.ip|cidrToIp }}</span> -
                        <a target="_blank" href="http://{{ server.networkInterfaces.remoteManagement.ip|cidrToIp }}" [class.disabled]="server.isSharedEol"><span class="fa fa-login"></span> <span i18n="@@bma_serverremotemanagementmodal_description6"> Login to IPMI Interface </span></a>
                    </p>
                    <div *ngIf="!isEmployee">
                        <p>
                            <a href="/bare-metals/remoteManagement"> <span class="fa fa-download"></span> <span i18n="@@bma_serverremotemanagementmodal_description7"> Download OpenVPN profile </span> </a>
                        </p>
                        <p i18n="@@bma_serverremotemanagementmodal_description8" class="help-block">
                            You are only able to access the IPMI interface of your dedicated server if you are connected to the Leaseweb remote management network using OpenVPN. For more information on setting up an OpenVPN connection go to the
                            <a href="/bare-metals/remoteManagement">Remote Management overview page</a>.
                        </p>
                    </div>
                </div>

                <div *ngIf="credentials.credentials.length > 0">
                    <h4 i18n="@@bma_common_ipmicredentials">IPMI Credentials</h4>
                    <p i18n="@@bma_serverremotemanagementmodal_description9">If these credentials do not work you should perform an IPMI Reset.</p>
                    <div class="table">
                        <app-credential-list-table [equipmentId]="serverId" credentialsType="REMOTE_MANAGEMENT" [addCredentials]="false" [loadedCredentials]="credentials"></app-credential-list-table>
                    </div>
                </div>

                <h4 class="mt-3" i18n="@@bma_common_ipmireset">IPMI Reset</h4>

                <div *ngIf="!isPrepareIpmiReset">
                    <p i18n="@@bma_serverremotemanagementmodal_description10">
                        If you experience issues with your IPMI interface you can
                        <a href (click)="prepareIpmiReset(); false" [class.disabled]="server.isSharedEol">launch an IPMI Reset</a>.
                    </p>
                </div>

                <div *ngIf="isPrepareIpmiReset">
                    <p i18n="@@bma_serverremotemanagementmodal_description11">If the dedicated server does not have IPMI credentials, or if the IPMI credentials are not working, you can perform an IPMI reset.</p>
                    <p>
                        <strong i18n="@@bma_serverremotemanagementmodal_description12"> Be aware that the dedicated server needs to be rebooted for this process to complete and that the interface is always set to DHCP during an IPMI reset. </strong>
                    </p>
                    <div class="form-group">
                        <div class="checkbox">
                            <input type="checkbox" id="powerCycle" [(ngModel)]="powerCycle" autofocus />
                            <label i18n="@@bma_common_powercycleserver" for="powerCycle">Power cycle the dedicated server</label>
                            <p i18n="@@bma_common_leasewebreboot" class="help-block">Leaseweb can perform a power cycle for you, or you can choose to reboot the dedicated server yourself. If you choose to reboot the dedicated server yourself, you need to do so within the next 4 hours.</p>
                        </div>
                    </div>
                    <div id="ipmi-reset-warnings" class="alert alert-warning alert-dismissible" role="alert">
                        <p i18n="@@bma_serverremotemanagementmodal_description13" *ngIf="server.networkInterfaces.public.nullRouted">The Main IP Address {{ server.networkInterfaces.public.ip|cidrToIp }} is null routed.</p>
                        <p i18n="@@bma_serverremotemanagementmodal_description14" *ngIf="activeJob">There is already an active {{ activeJob.type }} job for this server.</p>
                        <p *ngIf="ipmiJobError" class="text-danger">{{ ipmiJobError.errorMessage }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_close" pButton type="button" icon="pi pi-times" iconPos="left" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button i18n-label="@@bma_common_launchipmireset" pButton *ngIf="isPrepareIpmiReset" icon="pi pi-check" iconPos="left" (click)="launchIpmiReset()" [disabled]="!canlaunchIpmiReset" [loading]="isSubmitting" label="Launch IPMI Reset"></button>
        </div>
    </ng-template>
</p-dialog>

<ng-template #redacted>
    <span i18n="@@bma_common_redacted" class="text-danger" *ngIf="!userCanSeePassword()">[redacted]</span>
</ng-template>
