<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '900px'}">
    <ng-template pTemplate="header">
        <h3 class="modal-title">
            <span i18n="@@bma_common_rescuemode" class="mr-1">Rescue Mode</span>
            <sup>
                <a target="_blank" href="https://kb.leaseweb.com/kb/dedicated-server/dedicated-server-starting-your-dedicated-server-in-rescue-mode/">
                    <i title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i>
                </a>
            </sup>
        </h3>
    </ng-template>
    <div class="modal-body">
        <div class="alert alert-info">
            <div class="alert-icon"><i class="fa fa-information fa-lg" aria-hidden="true"></i></div>
            <h4 i18n="@@bma_common_information" class="alert-heading">Information</h4>
            <p i18n="@@bma_serverrescuemodemodal_description1" class="alert-body small">Performing rescue mode will boot your dedicated server in this environment, recovering (if possible) from any problems.</p>

            <div *ngIf="server.networkInterfaces?.public?.nullRouted" class="alert alert-danger">
                <span class="fa fa-alert icon-gray"></span>
                <p i18n="@@bma_serverrescuemodemodal_alert" class="alert-body small">This Server\'s IP address is currently null routed. Rescue mode may be launched, but will not start until this is resolved. Leaseweb recommends that you first remote the null route from the IP address before proceeding, or the Rescue mode may not succeed.</p>
            </div>

            <div *ngIf="publicNetworkInterface?.status == 'CLOSED'" class="alert alert-danger">
                <span class="fa fa-alert icon-gray"></span>
                <p i18n="@@bma_serverrescuemodemodal_alert1" class="alert-body small">This Server's public network interface is currently closed. Rescue mode may be launched, but will not start until this is resolved. Leaseweb recommends that you first open the interface before proceeding, or the Rescue mode may not succeed.</p>
            </div>
        </div>
        <form [formGroup]="form" (ngSubmit)="rescueMode()">
            <div class="form-group">
                <label i18n="@@bma_serverrescuemodemodal_rescueimage" class="control-label required" for="rescueModerescue_image_id">Rescue Image</label>
                <select id="rescueModerescue_image_id" formControlName="rescueImageId" class="form-control" autofocus>
                    <option *ngFor="let rescueImage of rescueImages.rescueImages" value="{{ rescueImage.id }}">
                        {{ rescueImage.name }}
                    </option>
                </select>
            </div>

            <div class="form-group">
                <label i18n="@@bma_common_callbackurl" class="control-label" for="rescueModeCallbackUrl">Callback URL</label>
                <input type="url" id="rescueModeCallbackUrl" formControlName="callbackUrl" class="form-control" placeholder="http://...." />
            </div>

            <app-local-storage-editor i18n-label="@@bma_common_sshkeys" key="sshKeys" [(content)]="sshKeys" (contentChange)="form.setErrors(null)" label="SSH keys" rows="3" [clearButton]="true" [uploadButton]="true" [dragAndDrop]="true" [uploadAllowedTypes]="['.pub']"></app-local-storage-editor>

            <div class="form-group">
                <label i18n="@@bma_common_customscript" class="control-label" for="rescueModePostInstallScript">Custom startup script</label>
                <textarea id="rescueModePostInstallScript" formControlName="postInstallScript" class="form-control" placeholder="#!/usr/bin/env bash"></textarea>
            </div>

            <div class="form-group">
                <div class="checkbox">
                    <input type="checkbox" id="rescueModePowerCycle" formControlName="powerCycle" value="true" checked="checked" />
                    <label i18n="@@bma_common_powercycleserver" for="rescueModePowerCycle" class="ml-1">Power cycle the dedicated server</label>
                </div>
            </div>

            <div class="form-group">
                <div class="checkbox">
                    <input type="checkbox" id="rescueModeDoEmailNotification" formControlName="doEmailNotification" value="true" checked="checked" />
                    <label i18n="@@bma_serverrwescuemodemodal_doemailnotification" for="rescueModeDoEmailNotification" class="ml-1">Send email notification</label>
                </div>
            </div>
        </form>
    </div>

    <div *ngIf="form.errors?.api?.errorMessage" class="alert alert-danger" role="alert">
        <span>{{ form.errors.api.errorMessage }}</span>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_cancel" pButton (click)="closeModal()" type="button" icon="pi pi-times" iconPos="left" label="Cancel" class="p-button-secondary"></button>
            <button i18n-label="@@bma_common_launch" pButton (click)="rescueMode()" label="Launch" icon="pi pi-check" [loading]="isSubmitting" [disabled]="form.invalid"></button>
        </div>
    </ng-template>
</p-dialog>
