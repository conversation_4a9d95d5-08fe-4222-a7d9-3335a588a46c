import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { Router } from '@angular/router';

@Component({
  selector: 'app-server-rescue-mode-modal',
  templateUrl: './server-rescue-mode-modal.component.html',
  styleUrls: ['./server-rescue-mode-modal.component.css'],
})
export class ServerRescueModeModalComponent implements OnInit {
  server: any;
  publicNetworkInterface: any;
  rescueImages: any;
  isSubmitting = false;
  isEmployee = false;
  form: UntypedFormGroup;
  sshKeys = '';
  showDialog = true;

  constructor(
    private alertService: AlertService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.server = this.config.data.server;

    this.httpClient.get<any>('/_/internal/bmpapi/v2/rescueImages', {}).subscribe({
      next: (data: any) => {
        this.rescueImages = data;
      },
      error: (error: any) => {
        this.rescueImages = [];
      },
    });

    if (this.server.networkInterfaces?.public?.ports?.length > 0) {
      this.httpClient
        .get<any>(`/_/internal/nseapi/v2/servers/${this.server.id}/networkInterfaces/public`, {})
        .subscribe({
          next: (data: any) => {
            this.publicNetworkInterface = data;
          },
          error: (error: any) => {
            this.publicNetworkInterface = null;
          },
        });
    }

    this.form = this.formBuilder.group({
      rescueImageId: ['GRML', Validators.required],
      postInstallScript: [null],
      powerCycle: [true],
      doEmailNotification: [true],
      callbackUrl: [null],
    });
  }

  rescueMode(): void {
    if (this.form.invalid) {
      return;
    }

    this.isSubmitting = true;
    const body: any = {
      rescueImageId: this.form.get('rescueImageId').value,
      powerCycle: this.form.get('powerCycle').value,
      doEmailNotification: this.form.get('doEmailNotification').value,
      callbackUrl: this.form.get('callbackUrl').value,
    };

    if (this.sshKeys.length > 0) {
      body.sshKeys = this.sshKeys;
    }

    if (this.form.get('postInstallScript').value !== null) {
      body.postInstallScript = btoa(this.form.get('postInstallScript').value);
    }

    this.httpClient.post<any>(`/_/bareMetals/v2/servers/${this.server.id}/rescueMode`, body).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;
        if (this.isEmployee) {
          this.router.navigate([`/servers/${this.server.id}/jobs/${data.uuid}`], { skipLocationChange: false });
        }
        this.alertService.alert({
          type: 'success',
          description: $localize`:@@bma_serverrescuemodalmodal_success:Rescue mode successfully launched.`,
        });
        this.closeModal();
      },
      error: (error: any) => {
        this.isSubmitting = false;
        this.form.setErrors({ api: error.error });
      },
    });
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }
}
