import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivateFn, Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { Observable } from 'rxjs';

export const IsFiberringGuard: CanActivateFn = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot
): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree => {
  const currentUserService = inject(CurrentUserService);
  const router = inject(Router);

  if (currentUserService.isEmployee() || currentUserService.getSalesOrgId() === '2200') {
    return true;
  }
  return router.navigate(['not-found']);
};
