import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { IsEmployeeGuard } from './guards/is-employee.guard';
import { IsCustomerGuard } from './guards/is-customer.guard';

import { AggregationPackListComponent } from './aggregation-pack/aggregation-pack-list/aggregation-pack-list.component';
import { AggregationPackTabsComponent } from './aggregation-pack/aggregation-pack-tabs/aggregation-pack-tabs.component';
import { AuditLogsComponent } from './audit-logs/audit-logs.component';
import { ColocationTabsComponent } from './colocation/colocation-tabs/colocation-tabs.component';
import { ColocationNetworkDetailsComponent } from './colocation/colocation-network-details/colocation-network-details.component';
import { CustomerListComponent } from './customer/customer-list/customer-list.component';
import { DdosIpProtectionListComponent } from './ddos-ip-protection/ddos-ip-protection-list/ddos-ip-protection-list.component';
import { DdosIpProtectionAdvancedListComponent } from './ddos-ip-protection/ddos-ip-protection-advanced-list/ddos-ip-protection-advanced-list.component';
import { FirmwareVersionsListComponent } from './firmware-versions/firmware-versions-list/firmware-versions-list.component';
import { JobWizardComponent } from './job/job-wizard/job-wizard.component';
import { FloatingIpRangeListComponent } from './floating-ip/floating-ip-range-list/floating-ip-range-list.component';
import { FloatingIpRangeDetailsComponent } from './floating-ip/floating-ip-range-details/floating-ip-range-details.component';
import { DhcpReservationListComponent } from './dhcp-reservation/dhcp-reservation-list/dhcp-reservation-list.component';
import { MetricsReportComponent } from './metrics-report/metrics-report.component';
import { LookupComponent } from './lookup/lookup.component';
import { NetworkDeviceDetailsComponent } from './network-device/network-device-details/network-device-details.component';
import { NetworkDeviceGenerateConfigComponent } from './network-device/network-device-generate-config/network-device-generate-config.component';
import { NetworkDeviceListComponent } from './network-device/network-device-list/network-device-list.component';
import { NetworkDeviceLldpComponent } from './network-device/network-device-lldp/network-device-lldp.component';
import { NetworkDeviceQueryInterfacesComponent } from './network-device/network-device-query-interfaces/network-device-query-interfaces.component';
import { NetworkDeviceTabsComponent } from './network-device/network-device-tabs/network-device-tabs.component';
import { NetworkDeviceBackupConfigComponent } from './network-device/network-device-backup-config/network-device-backup-config.component';
import { NetworkDeviceDownloadConfigComponent } from './network-device/network-device-download-config/network-device-download-config.component';
import { NetworkDeviceRestoreConfigComponent } from './network-device/network-device-restore-config/network-device-restore-config.component';
import { NetworkDeviceDownloadGeneratedConfigComponent } from './network-device/network-device-download-generated-config/network-device-download-generated-config.component';
import { IpPerformanceMeasurementListComponent } from './ip-performance/ip-performance-measurement-list/ip-performance-measurement-list.component';
import { IpPerformanceMeasurementTabComponent } from './ip-performance/ip-performance-measurement-tabs/ip-performance-measurement-tabs.component';
import { IpPerformanceMeasurementTracemonComponent } from './ip-performance/ip-performance-measurement-tracemon/ip-performance-measurement-tracemon.component';
import { IpPerformanceMeasurementLatencymonComponent } from './ip-performance/ip-performance-measurement-latencymon/ip-performance-measurement-latencymon.component';
import { PageNotFoundComponent } from './page-not-found/page-not-found.component';
import { PowerbarDetailsComponent } from './powerbar/powerbar-details/powerbar-details.component';
import { PowerbarListComponent } from './powerbar/powerbar-list/powerbar-list.component';
import { PowerbarTabsComponent } from './powerbar/powerbar-tabs/powerbar-tabs.component';
import { PowerbarGenerateConfigComponent } from './powerbar/powerbar-generate-config/powerbar-generate-config.component';
import { SiteConfigurationComponent } from './site-configuration/site-configuration.component';
import { VlanListComponent } from './vlan/vlan-list/vlan-list.component';
import { PrivateNetworkColocationListComponent } from './private-network/private-network-colocation-list/private-network-colocation-list.component';
import { PrivateNetworkPrivateRackListComponent } from './private-network/private-network-private-rack-list/private-network-private-rack-list.component';
import { PrivateNetworkServerListComponent } from './private-network/private-network-server-list/private-network-server-list.component';
import { PrivateNetworkNetworkEquipmentListComponent } from './private-network/private-network-network-equipment-list/private-network-network-equipment-list.component';
import { PrivateNetworkDetailsComponent } from './private-network/private-network-details/private-network-details.component';
import { PrivateNetworkDedicatedStorageListComponent } from './private-network/private-network-dedicated-storage-list/private-network-dedicated-storage-list.component';
import { PrivateNetworkListComponent } from './private-network/private-network-list/private-network-list.component';
import { NotificationListComponent } from './notification/notification-list/notification-list.component';
import { CredentialListComponent } from './credential/credential-list/credential-list.component';
import { ColocationDetailsComponent } from './colocation/colocation-details/colocation-details.component';
import { NullRouteIpHistoryListComponent } from './null-route-ip/null-route-ip-history-list/null-route-ip-history-list.component';
import { PrivateRackTabsComponent } from './private-rack/private-rack-tabs/private-rack-tabs.component';
import { IpTransitTabsComponent } from './ip-transit/ip-transit-tabs/ip-transit-tabs.component';
import { IpTransitDetailsComponent } from './ip-transit/ip-transit-details/ip-transit-details.component';
import { DirectInternetAccessDetailsComponent } from './direct-internet-access/direct-internet-access-details/direct-internet-access-details.component';
import { DirectInternetAccessTabsComponent } from './direct-internet-access/direct-internet-access-tabs/direct-internet-access-tabs.component';
import { PrivateRackNetworkDetailsComponent } from './private-rack/private-rack-network-details/private-rack-network-details.component';
import { PrivateRackDetailsComponent } from './private-rack/private-rack-details/private-rack-details.component';
import { ServerTabsComponent } from './server/server-tabs/server-tabs.component';
import { IpListComponent } from './ip/ip-list/ip-list.component';
import { ServerDetailsComponent } from './server/server-details/server-details.component';
import { ServerListComponent } from './v3/features/equipments/server/server-list/server-list.component';
import { ServerHardwareDetailsComponent } from './server/server-hardware-details/server-hardware-details.component';
import { ServerNetworkDetailsComponent } from './server/server-network-details/server-network-details.component';
import { TrafficGraphsComponent } from './graph/traffic-graphs/traffic-graphs.component';
import { IpPerformanceMeasurementGraphsComponent } from './graph/ip-performance-measurement-graphs/ip-performance-measurement-graphs.component';
import { ServerInstallWizardComponent } from './server/server-install-wizard/server-install-wizard.component';
import { JobListComponent } from './job/job-list/job-list.component';
import { ServerJobDetailsComponent } from './server/server-job-details/server-job-details.component';
import { ServerJobListComponent } from './server/server-job-list/server-job-list.component';
import { NetworkEquipmentListComponent } from './network-equipment/network-equipment-list/network-equipment-list.component';
import { NetworkEquipmentTabsComponent } from './network-equipment/network-equipment-tabs/network-equipment-tabs.component';
import { NetworkEquipmentDetailsComponent } from './network-equipment/network-equipment-details/network-equipment-details.component';
import { DedicatedStorageDetailsComponent } from './dedicated-storage/dedicated-storage-details/dedicated-storage-details.component';
import { DedicatedStorageNetworkDetailsComponent } from './dedicated-storage/dedicated-storage-network-details/dedicated-storage-network-details.component';
import { DedicatedStorageTabsComponent } from './dedicated-storage/dedicated-storage-tabs/dedicated-storage-tabs.component';
import { NetworkEquipmentNetworkDetailsComponent } from './network-equipment/network-equipment-network-details/network-equipment-network-details.component';
import { RemoteManagementComponent } from './remote-management/remote-management.component';
import { ActiveAnomaliesComponent } from './active-anomalies/active-anomalies.component';
import { ArpCheckerListComponent } from './arp-checker/arp-checker-list/arp-checker-list.component';
import { DiskDetailsComponent } from './disk/disk-details/disk-details.component';
import { RemoteHandsListComponent } from './remote-hands-list/remote-hands-list.component';
import { DirectInternetAccessListComponent } from './direct-internet-access/direct-internet-access-list/direct-internet-access-list.component';
import { IpTransitListComponent } from './ip-transit/ip-transit-list/ip-transit-list.component';
import { CloudConnectListComponent } from './cloud-connect/cloud-connect-list/cloud-connect-list.component';
import { CloudConnectDetailsComponent } from './cloud-connect/cloud-connect-details/cloud-connect-details.component';
import { JobPayloadGeneratorComponent } from './job/job-payload-generator/job-payload-generator.component';
import { PrivateRackListComponent } from './v3/features/equipments/private-rack/private-rack-list/private-rack-list.component';
import { DedicatedStorageListComponent } from './v3/features/equipments/dedicated-storage/dedicated-storage-list/dedicated-storage-list.component';
import { ColocationListComponent } from './v3/features/equipments/colocation/colocation-list/colocation-list.component';

const routes: Routes = [
  { path: '', canActivate: [IsEmployeeGuard], component: LookupComponent },
  { path: 'jobs', canActivate: [IsEmployeeGuard], component: JobListComponent },
  { path: 'servers', component: ServerListComponent },
  { path: 'audit-logs', canActivate: [IsEmployeeGuard], component: AuditLogsComponent },
  { path: 'aggregationPacks', component: AggregationPackListComponent },
  {
    path: 'aggregationPacks/:aggregationPackId',
    component: AggregationPackTabsComponent,
    children: [
      { path: '', component: TrafficGraphsComponent },
      { path: 'notifications', component: NotificationListComponent },
    ],
  },
  { path: 'arp-checker/entries', component: ArpCheckerListComponent },
  { path: 'colocations', component: ColocationListComponent },
  { path: 'remoteHands', component: RemoteHandsListComponent },
  { path: 'networkEquipments', component: NetworkEquipmentListComponent },
  {
    path: 'networkEquipments/:networkEquipmentId',
    component: NetworkEquipmentTabsComponent,
    children: [
      { path: '', component: NetworkEquipmentDetailsComponent },
      { path: 'credentials', component: CredentialListComponent },
      { path: 'network', component: NetworkEquipmentNetworkDetailsComponent },
      { path: 'ips', component: IpListComponent },
      { path: 'nullRouteHistory', component: NullRouteIpHistoryListComponent },
      { path: 'graphs', component: TrafficGraphsComponent },
    ],
  },
  { path: 'dedicatedStorages', component: DedicatedStorageListComponent },
  {
    path: 'dedicatedStorages/:dedicatedStorageId',
    component: DedicatedStorageTabsComponent,
    children: [
      { path: '', component: DedicatedStorageDetailsComponent },
      { path: 'network', component: DedicatedStorageNetworkDetailsComponent },
      { path: 'credentials', component: CredentialListComponent },
      { path: 'ips', component: IpListComponent },
    ],
  },
  {
    path: 'colocations/:colocationId',
    component: ColocationTabsComponent,
    children: [
      { path: '', component: ColocationDetailsComponent },
      { path: 'credentials', component: CredentialListComponent },
      { path: 'notifications', component: NotificationListComponent },
      { path: 'ips', component: IpListComponent },
      { path: 'network', component: ColocationNetworkDetailsComponent },
      { path: 'graphs', component: TrafficGraphsComponent },
      { path: 'graphs/privateNetwork', component: TrafficGraphsComponent },
      { path: 'nullRouteHistory', component: NullRouteIpHistoryListComponent },
    ],
  },
  { path: 'racks', component: PrivateRackListComponent },
  {
    path: 'racks/:rackId',
    component: PrivateRackTabsComponent,
    children: [
      { path: '', component: PrivateRackDetailsComponent },
      { path: 'ips', component: IpListComponent },
      { path: 'network', component: PrivateRackNetworkDetailsComponent },
      { path: 'notifications', component: NotificationListComponent },
      { path: 'credentials', component: CredentialListComponent },
      { path: 'graphs', component: TrafficGraphsComponent },
      { path: 'nullRouteHistory', component: NullRouteIpHistoryListComponent },
    ],
  },
  { path: 'ipTransits', component: IpTransitListComponent },
  {
    path: 'ipTransits/:ipTransitId',
    component: IpTransitTabsComponent,
    children: [
      { path: '', component: IpTransitDetailsComponent },
      { path: 'graphs', component: TrafficGraphsComponent },
    ],
  },
  { path: 'directInternetAccess', component: DirectInternetAccessListComponent },
  {
    path: 'directInternetAccess/:directInternetAccessId',
    component: DirectInternetAccessTabsComponent,
    children: [
      { path: '', component: DirectInternetAccessDetailsComponent },
      { path: 'graphs', component: TrafficGraphsComponent },
    ],
  },
  { path: 'customers', canActivate: [IsEmployeeGuard], component: CustomerListComponent },
  { path: 'dhcp-reservations', canActivate: [IsEmployeeGuard], component: DhcpReservationListComponent },
  { path: 'firmware-versions', canActivate: [IsEmployeeGuard], component: FirmwareVersionsListComponent },
  { path: 'job-wizard', canActivate: [IsEmployeeGuard], component: JobWizardComponent },
  { path: 'job-payload-generator', canActivate: [IsEmployeeGuard], component: JobPayloadGeneratorComponent },
  { path: 'floating-ips', component: FloatingIpRangeListComponent },
  { path: 'floating-ips/:rangeId', component: FloatingIpRangeDetailsComponent },
  { path: 'remoteManagement', canActivate: [IsCustomerGuard], component: RemoteManagementComponent },
  { path: 'metrics/overview', component: MetricsReportComponent },
  { path: 'networkDevices', canActivate: [IsEmployeeGuard], component: NetworkDeviceListComponent },
  {
    path: 'servers/:serverId',
    component: ServerTabsComponent,
    children: [
      { path: '', component: ServerDetailsComponent },
      { path: 'credentials', component: CredentialListComponent },
      { path: 'notifications', component: NotificationListComponent },
      { path: 'network', component: ServerNetworkDetailsComponent },
      { path: 'install', component: ServerInstallWizardComponent },
      { path: 'ips', component: IpListComponent },
      { path: 'nullRouteHistory', component: NullRouteIpHistoryListComponent },
      { path: 'hardware', component: ServerHardwareDetailsComponent },
      { path: 'graphs', component: TrafficGraphsComponent },
      { path: 'graphs/privateNetwork', component: TrafficGraphsComponent },
      { path: 'jobs', component: ServerJobListComponent },
      { path: 'jobs/:uuid', canActivate: [IsEmployeeGuard], component: ServerJobDetailsComponent },
    ],
  },
  {
    path: 'networkDevices/downloadConfiguration',
    canActivate: [IsEmployeeGuard],
    component: NetworkDeviceDownloadGeneratedConfigComponent,
  },
  {
    path: 'networkDevices/generate-config',
    canActivate: [IsEmployeeGuard],
    component: NetworkDeviceGenerateConfigComponent,
  },
  {
    path: 'networkDevices/:name',
    component: NetworkDeviceTabsComponent,
    canActivate: [IsEmployeeGuard],
    children: [
      { path: '', component: NetworkDeviceDetailsComponent },
      { path: 'lldp', component: NetworkDeviceLldpComponent },
      { path: 'interfaces', component: NetworkDeviceQueryInterfacesComponent },
      { path: 'backupConfiguration', component: NetworkDeviceBackupConfigComponent },
      { path: 'downloadConfiguration', component: NetworkDeviceDownloadConfigComponent },
      { path: 'restoreConfiguration', component: NetworkDeviceRestoreConfigComponent },
      { path: 'graphs', component: TrafficGraphsComponent },
    ],
  },
  {
    path: 'ip-performance/measurements',
    component: IpPerformanceMeasurementListComponent,
  },
  {
    path: 'ip-performance/measurements/:measurementId',
    component: IpPerformanceMeasurementTabComponent,
    children: [
      { path: 'graphs', component: IpPerformanceMeasurementGraphsComponent },
      { path: 'tracemon', canActivate: [IsEmployeeGuard], component: IpPerformanceMeasurementTracemonComponent },
      { path: 'latencymon', component: IpPerformanceMeasurementLatencymonComponent },
    ],
  },
  { path: 'nse/sites-configuration', canActivate: [IsEmployeeGuard], component: SiteConfigurationComponent },
  { path: 'vlans', canActivate: [IsEmployeeGuard], component: VlanListComponent },
  { path: 'ddos/profiles', canActivate: [IsEmployeeGuard], component: DdosIpProtectionListComponent },
  { path: 'powerbars', canActivate: [IsEmployeeGuard], component: PowerbarListComponent },
  { path: 'ddos/anomalies', canActivate: [IsEmployeeGuard], component: ActiveAnomaliesComponent },
  { path: 'ddos-ip-protection', component: DdosIpProtectionAdvancedListComponent },
  {
    path: 'powerbars/:name',
    component: PowerbarTabsComponent,
    canActivate: [IsEmployeeGuard],
    children: [
      { path: '', component: PowerbarDetailsComponent },
      { path: 'generate-config', component: PowerbarGenerateConfigComponent },
    ],
  },
  { path: 'privateNetworks', component: PrivateNetworkListComponent },
  {
    path: 'privateNetworks/:id',
    component: PrivateNetworkDetailsComponent,
    children: [
      { path: '', component: PrivateNetworkServerListComponent },
      { path: 'colocations', component: PrivateNetworkColocationListComponent },
      { path: 'private-racks', component: PrivateNetworkPrivateRackListComponent },
      { path: 'dedicated-network-equipments', component: PrivateNetworkNetworkEquipmentListComponent },
      { path: 'dedicated-storages', component: PrivateNetworkDedicatedStorageListComponent },
    ],
  },
  {
    path: 'disk/:serial',
    component: DiskDetailsComponent,
    canActivate: [IsEmployeeGuard],
  },
  { path: 'cloud-connects', component: CloudConnectListComponent },
  { path: 'cloud-connects/:cloudConnectId', component: CloudConnectDetailsComponent },

  { path: '**', component: PageNotFoundComponent },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      scrollPositionRestoration: 'enabled',
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
