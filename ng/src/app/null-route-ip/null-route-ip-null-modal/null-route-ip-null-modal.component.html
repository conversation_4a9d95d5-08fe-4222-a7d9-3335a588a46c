<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '800px' }">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_nullrouteipnullmodal_header" class="modal-title">Null Route IP Address</h3>
    </ng-template>
    <form [formGroup]="form" (ngSubmit)="onNullIpAddress()">
        <div class="modal-body">
            <div id="action-nullroute-body-text">
                <div class="row mt-1 mb-2">
                    <div class="md-form input-group">
                        <div i18n="@@bma_common_comment" class="col-12 mb-1">Comment</div>
                        <div class="col-12 mb-1">
                            <textarea id="comment" formControlName="comment" required="required" class="form-control"></textarea>
                            <span *ngIf="form.get('comment').errors && form.get('comment').errors.server">{{ form.get('comment').errors.server }}<br /></span>
                        </div>
                    </div>
                </div>

                <div class="row mt-1 mb-2">
                    <div class="md-form input-group">
                        <div i18n="@@bma_common_ticket" class="col-12 mb-1">Ticket</div>
                        <div class="col-12 mb-1">
                            <input type="text" id="ticket" formControlName="ticket" class="form-control" autofocus />
                            <span *ngIf="form.get('ticket').errors && form.get('ticket').errors.server">{{ form.get('ticket').errors.server }}<br /></span>
                        </div>
                    </div>
                </div>

                <div class="row mt-1 mb-2">
                    <div class="md-form input-group">
                        <div i18n="@@bma_nullrouteipnullmodal_duration" class="mb-1 col-12">Null-routing duration (hours)</div>
                        <div class="col-12 mb-1">
                            <input i18n-placeholder="@@bma_nullrouteipnullmodal_numberofhours" type="number" id="temporaryNullRoute" formControlName="temporaryNullRoute" class="form-control" placeholder="Number of hours to null-route IP. Leave empty for unlimited" />
                            <span *ngIf="form.get('temporaryNullRoute').errors && form.get('temporaryNullRoute').errors.server">{{ form.get('temporaryNullRoute').errors.server }}<br /></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_close" pButton type="button" class="p-button-secondary" label="Close" (click)="closeModal()"></button>
            <button i18n-label="@@bma_common_confirm" pButton (click)="onNullIpAddress()" [loading]="isSubmitting" icon="pi pi-check" iconPos="left" label="Confirm"></button>
        </div>
    </ng-template>
</p-dialog>
