import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { CidrToIpPipe } from 'src/app/pipes/cidr-to-ip.pipe';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-null-route-ip-null-modal',
  templateUrl: './null-route-ip-null-modal.component.html',
  styleUrls: ['./null-route-ip-null-modal.component.css'],
})
export class NullRouteIpNullModalComponent implements OnInit {
  equipment: any;
  equipmentType: string;

  form: UntypedFormGroup;
  ipAddress: '';
  isSubmitting = false;
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    private readonly formBuilder: UntypedFormBuilder,
    private alertService: AlertService,
    private cidrToIpPipe: CidrToIpPipe,
    private dynamicDialogRef: DynamicDialogRef,
    public dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.equipment = this.dynamicDialogConfig.data.equipment;
    this.equipmentType = this.dynamicDialogConfig.data.equipmentType;
    this.ipAddress = this.dynamicDialogConfig.data.ipAddress;

    this.form = this.formBuilder.group({
      comment: [null, Validators.required],
      ticket: [],
      temporaryNullRoute: [],
    });
  }

  onNullIpAddress() {
    this.alertService.clear();
    if (this.form.valid) {
      this.isSubmitting = true;

      const params: any = {};

      params.comment = this.form.get('comment').value;
      params.ticket = this.form.get('ticket').value;
      const temporaryNullRoute = parseInt(this.form.get('temporaryNullRoute').value, 10);
      if (temporaryNullRoute) {
        const time = new Date();
        time.setHours(time.getHours() + temporaryNullRoute);
        params.automatedUnnullingAt = time.toISOString();
      }

      this.httpClient
        .post(
          `/_/internal/dedicatedserverapi/v2/${this.equipmentType}/${this.equipment.id}/ips/${this.ipAddress}/null`,
          params
        )
        .subscribe({
          next: (data: any) => {
            this.isSubmitting = false;
            this.alertService.alert(
              {
                type: 'success',
                description: $localize`:@@bma_nullroteipnullmodal_success:IP address ${this.ipAddress} was successfully null routed.`,
              },
              true
            );
            this.closeModal({ nulled: true });
          },
          error: (error: any) => {
            this.isSubmitting = false;
            if (error.error.errorDetails) {
              Object.entries(error.error.errorDetails).forEach(([key, value]) => {
                this.form.get(key).setErrors({ server: value[0] });
              });
            }
            this.alertService.alertApiError(error.error, true);
            this.closeModal({ nulled: false });
          },
        });
    } else {
      this.alertService.alert(
        {
          type: 'error',
          description: $localize`:@@bma_common_invalidentry:Invalid entries entered. Please check it.`,
        },
        true
      );
    }
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
