import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert.service';
import { CidrToIpPipe } from 'src/app/pipes/cidr-to-ip.pipe';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-null-route-ip-unnull-modal',
  templateUrl: './null-route-ip-unnull-modal.component.html',
  styleUrls: ['./null-route-ip-unnull-modal.component.css'],
})
export class NullRouteIpUnnullModalComponent implements OnInit {
  equipment: any;
  equipmentType: string;
  ipAddress = '';

  isSubmitting = false;
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    private alertService: AlertService,
    private cidrToIpPipe: CidrToIpPipe,
    private dynamicDialogConfig: DynamicDialogConfig,
    private dynamicDialogRef: DynamicDialogRef
  ) {}

  ngOnInit(): void {
    this.equipment = this.dynamicDialogConfig.data.equipment;
    this.equipmentType = this.dynamicDialogConfig.data.equipmentType;
    this.ipAddress = this.dynamicDialogConfig.data.ipAddress;
  }

  onUnNullIpAddress() {
    this.isSubmitting = true;
    this.alertService.clear();
    this.httpClient
      .post(
        `/_/internal/dedicatedserverapi/v2/${this.equipmentType}/${this.equipment.id}/ips/${this.ipAddress}/unnull`,
        []
      )
      .subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.alertService.alert(
            {
              type: 'success',
              description: $localize`:@@bma_nullroteipunnullmodal_success:Successfully removed null route from IP address ${this.ipAddress}, please wait 5-10 minutes for the changes to become active.`,
            },
            true
          );
          this.closeModal({ unnulled: true });
        },
        error: (error: any) => {
          this.isSubmitting = false;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ unnulled: false });
        },
      });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
