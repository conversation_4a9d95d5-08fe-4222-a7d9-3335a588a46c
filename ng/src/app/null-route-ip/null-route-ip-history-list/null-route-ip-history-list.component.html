<div class="row mb-4">
    <div class="col">
        <h3>
            <span class="fa fa-transfer"></span>
            <span i18n="@@bma_common_nullroutehistory" class="mx-1">Null Route History</span>
            <sup>
                <a href="https://kb.leaseweb.com/kb/dedicated-server/dedicated-server-viewing-null-route-history" target="_blank">
                    <i i18n="@@bma_common_kb" title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i>
                </a>
            </sup>
        </h3>
    </div>
</div>

<app-loader *ngIf="isLoading"></app-loader>

<div class="table-responsive" *ngIf="!isLoading && nullRoutes">
    <table class="table table-striped">
        <thead>
            <tr>
                <th i18n="@@bma_common_ipaddress" scope="col">IP Address</th>
                <th i18n="@@bma_common_requestdate" scope="col">Request Date</th>
                <th i18n="@@bma_common_period" scope="col">Period</th>
                <th i18n="@@bma_common_comment" scope="col">Comment</th>
                <th i18n="@@bma_common_user" scope="col" *ngIf="isEmployee">User</th>
            </tr>
        </thead>

        <tbody>
            <tr *ngIf="nullRoutes.nullRoutes.length === 0">
                <td i18n="@@bma_nullrouteiphistorylist_nohistory" colspan="99" class="text-center p-4">No null routes found for this equipment..</td>
            </tr>
            <tr *ngFor="let nullRoute of nullRoutes.nullRoutes">
                <td>
                    <span class="text-monospace">
                        {{ nullRoute.ip }}
                    </span>
                </td>
                <td>
                    <span *ngIf="nullRoute.updatedAt;else notAvailable">
                        {{ nullRoute.updatedAt|lswDateTime:"short" }}
                    </span>
                    <ng-template #notAvailable>
                        <span i18n="@@bma_common_unknown" class="text-muted">UNKNOWN</span>
                    </ng-template>
                </td>
                <td>
                    <ng-container *ngIf="nullRoute.nulledAt && nullRoute.unnulledAt;else elseIfDateBlock">
                        from
                        {{ nullRoute.nulledAt|lswDateTime:"short" }}<br />
                        to
                        {{ nullRoute.unnulledAt|lswDateTime:"short" }}
                    </ng-container>
                    <ng-template #elseIfDateBlock>
                        <ng-container *ngIf="nullRoute.nulledAt && nullRoute.automatedUnnullingAt;else elseDateBlock">
                            from
                            {{ nullRoute.nulledAt|lswDateTime:"short" }}<br />
                            to
                            {{ nullRoute.automatedUnnullingAt|lswDateTime:"short" }}
                        </ng-container>
                    </ng-template>
                    <ng-template #elseDateBlock>
                        <span i18n="@@bma_common_unlimited" class="text-muted">Unlimited</span>
                    </ng-template>
                </td>
                <td>
                    <ng-container *ngIf="nullRoute.ticketNumber">
                        <strong>Ticket:</strong>
                        <span class="text-monospace"> #{{ nullRoute.ticketNumber }} </span><br />
                    </ng-container>

                    {{ nullRoute.comment|default }}
                </td>
                <td *ngIf="isEmployee">
                    {{ nullRoute.updatedBy|default }}
                </td>
            </tr>
        </tbody>
    </table>
</div>

<div *ngIf="!isLoading && nullRoutes">
    <app-pagination [totalCount]="nullRoutes._metadata.totalCount" [limit]="nullRoutes._metadata.limit" [offset]="nullRoutes._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
</div>
