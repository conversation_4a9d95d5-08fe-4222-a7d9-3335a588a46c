import { Component, OnInit, Input } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';

@Component({
  selector: 'app-null-route-ip-history-list',
  templateUrl: './null-route-ip-history-list.component.html',
  styleUrls: ['./null-route-ip-history-list.component.css'],
})
export class NullRouteIpHistoryListComponent implements OnInit {
  @Input() equipment: any;
  @Input() equipmentType: string;

  isLoading = false;
  nullRoutes: any;
  isEmployee = false;
  filters: UntypedFormGroup;

  constructor(
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private currentUserService: CurrentUserService
  ) {}

  ngOnInit(): void {
    this.router.routeReuseStrategy.shouldReuseRoute = () => false;
    this.isEmployee = this.currentUserService.isEmployee();

    this.filters = this.formBuilder.group({
      offset: [50],
      limit: [0],
      filter: [null],
    });

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    this.isLoading = true;
    this.filters.reset(queryParams);
    const params = this.sanitise(this.filters.getRawValue());

    this.httpClient
      .get(`/_/internal/dedicatedserverapi/v2/${this.equipmentType}/${this.equipment.id}/nullRouteHistory`, {
        params,
      })
      .subscribe({
        next: (data: any) => {
          this.isLoading = false;
          this.nullRoutes = data;
        },
        error: (error: any) => {
          this.isLoading = false;
          this.nullRoutes = null;
        },
      });
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
