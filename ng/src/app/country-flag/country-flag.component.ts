import { Component, OnInit, Input } from '@angular/core';
import { SiteService } from 'src/app/services/site.service';

@Component({
  selector: 'app-country-flag',
  templateUrl: './country-flag.component.html',
  styleUrls: ['./country-flag.component.css'],
})
export class CountryFlagComponent implements OnInit {
  @Input() countryCode: string;
  @Input() salesOrgId: string;

  constructor(private siteService: SiteService) {}

  ngOnInit(): void {
    if (!this.countryCode) {
      this.countryCode = this.siteService.getCountry(this.salesOrgId);
    }
    this.countryCode = this.countryCode.toLowerCase();
  }
}
