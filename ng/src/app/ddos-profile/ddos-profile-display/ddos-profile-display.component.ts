import { Component, OnInit, Input } from '@angular/core';
import { CommerceService } from 'src/app/services/commerce.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { ModalService } from 'src/app/services/modal.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { DdosProfileChangeModalComponent } from 'src/app/ddos-profile/ddos-profile-change-modal/ddos-profile-change-modal.component';
import { CidrToIpPipe } from 'src/app/pipes/cidr-to-ip.pipe';

@Component({
  selector: 'app-ddos-profile-display',
  templateUrl: './ddos-profile-display.component.html',
  styleUrls: ['./ddos-profile-display.component.css'],
})
export class DdosProfileDisplayComponent implements OnInit {
  @Input() ipAddress: any;
  @Input() equipment: any;
  @Input() equipmentType: any;
  isEmployee = false;
  dynamicDialogRef: DynamicDialogRef;

  constructor(
    private currentUserService: CurrentUserService,
    private modalService: ModalService,
    private commerceService: CommerceService,
    private cidrToIpPipe: CidrToIpPipe
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
  }

  openDdosProfileChangeModal() {
    this.dynamicDialogRef = this.modalService.show(DdosProfileChangeModalComponent, {
      equipment: this.equipment,
      equipmentType: this.equipmentType,
      ipDetails: this.ipAddress,
    });

    this.dynamicDialogRef.onClose.subscribe((data) => {
      if (data?.ddosProfileChanged === true) {
        this.ipAddress = data.ipDetails;
      }
    });
  }

  ddosIpProtectionTypeUpgradeUrl() {
    return this.commerceService.getCommerceModifyContractUrl(this.equipment.contract.id, 'ip/upgrade', {
      ipaddresses: this.cidrToIpPipe.transform(this.ipAddress.ip),
    });
  }
}
