<ng-container *ngIf="!ipAddress.floatingIp">
    <ng-container *ngIf="ipAddress.ddos; else elseIfIpBlock;">
        <div class="p-grid">
            <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_ddosprofiledisplay_type">Type</strong></div>
            <div class="p-col-8 grid-content grid-content-border">
                <span i18n="@@bma_ddosprofiledisplay_standard" *ngIf="ipAddress.ddos.protectionType == 'STANDARD'">STANDARD</span>
                <span i18n="@@bma_ddosprofiledisplay_advanced" *ngIf="ipAddress.ddos.protectionType == 'ADVANCED'">ADVANCED</span>
                <ng-container *ngIf="!isEmployee && equipment.contract.status | default: '' == 'ACTIVE'">
                    <ng-container *ngIf="'STANDARD' == ipAddress.ddos.protectionType;">
                        <a i18n-title="@@bma_ddosprofiledisplay_upgradetoadvance" title="Upgrade to DDoS IP Protection Advanced" [href]="ddosIpProtectionTypeUpgradeUrl()">
                            <i class="fa fa-upgrade" aria-hidden="true"></i>
                        </a>
                    </ng-container>
                </ng-container>
            </div>
        </div>
        <div class="p-grid">
            <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_ddosprofiledisplay_profile">Profile</strong></div>
            <div class="p-col-8 grid-content grid-content-border">
                <ng-container [ngSwitch]="ipAddress.ddos.detectionProfile">
                    <span *ngSwitchCase="'ADVANCED_DEFAULT'" i18n="@@bma_common_default">Default</span>
                    <span *ngSwitchCase="'ADVANCED_LOW_UDP'" i18n="@@bma_common_lowudp">Low UDP</span>
                    <span *ngSwitchCase="'ADVANCED_MED_UDP'" i18n="@@bma_common_mediumudp">Medium UDP</span>
                    <span *ngSwitchCase="'STANDARD_DEFAULT'" i18n="@@bma_common_standarddefault">Standard Default</span>
                    <span *ngSwitchDefault i18n="@@bma_common_notavailable">Not Available</span>
                </ng-container>
                <ng-container *ngIf="'ADVANCED' == ipAddress.ddos.protectionType;else elseDdosProfileBlock">
                    <a i18n-title="@@bma_ddosprofiledisplay_updateprofile" (click)="openDdosProfileChangeModal()" title="Update DDoS profile" [class.disabled]="equipment.isSharedEol">
                        <span i18n-title="@@bma_ddosprofiledisplay_updateprofile" title="Update DDoS profile" class="fa fa-change ml-1" aria-hidden="true"></span>
                    </a>
                </ng-container>
                <ng-template #elseDdosProfileBlock>
                    <span i18n-title="@@bma_ddosprofiledisplay_updateprofile" class="text-muted" title="Update DDoS profile">
                        <span i18n-title="@@bma_ddosprofiledisplay_updateprofile" title="Update DDoS profile" class="fa fa-change ml-1" aria-hidden="true"></span>
                    </span>
                </ng-template>
            </div>
        </div>
        <div class="p-grid">
            <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_ddosprofiledisplay_mitigationreport">Mitigation Report</strong></div>
            <div class="p-col-8 grid-content grid-content-border">
                <app-ddos-mitigation-report [ipAddress]="ipAddress"></app-ddos-mitigation-report>
            </div>
        </div>
    </ng-container>
    <ng-template #elseIfIpBlock>
        <div class="p-grid">
            <div class="p-col-12 grid-content grid-content-border">
                <span class="text-muted" i18n="@@bma_common_notavailable">Not Available</span>
            </div>
        </div>
    </ng-template>
</ng-container>
