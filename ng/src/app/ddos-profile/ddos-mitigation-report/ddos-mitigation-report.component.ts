import { Component, OnInit, Input } from '@angular/core';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { CidrToIpPipe } from 'src/app/pipes/cidr-to-ip.pipe';
import { HttpClient } from '@angular/common/http';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

@Component({
  selector: 'app-ddos-mitigation-report',
  templateUrl: './ddos-mitigation-report.component.html',
  styleUrls: ['./ddos-mitigation-report.component.css'],
})
export class DdosMitigationReportComponent implements OnInit {
  @Input() ipAddress: any;
  @Input() isDdosIpProtectionAdvancedListPage = false;
  ipDetails: any;
  isEmployee = false;
  public report: SafeResourceUrl;
  error = null;
  advancedProfile = false;
  blobReport = null;

  constructor(
    private currentUserService: CurrentUserService,
    private cidrToIpPipe: CidrToIpPipe,
    private httpClient: HttpClient,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();

    if (this.isDdosIpProtectionAdvancedListPage) {
      this.advancedProfile = true;
      this.ipAddress = this.cidrToIpPipe.transform(this.ipAddress);
    } else {
      this.ipDetails = this.ipAddress;
      this.advancedProfile = this.ipDetails.ddos && this.ipDetails.ddos.protectionType === 'ADVANCED';
      this.ipAddress = this.cidrToIpPipe.transform(this.ipDetails.ip);
    }
    this.report = this.sanitizer.bypassSecurityTrustUrl('javascript:;');
  }

  openReport(): void {
    if (this.advancedProfile) {
      if (!this.blobReport) {
        this.loadReport();
      } else {
        window.open(this.blobReport, '_blank', '');
      }
    }
  }

  loadReport(): void {
    this.httpClient
      .get(`/_/internal/nseapi/v2/ddosProfiles/${this.ipAddress}/report`, {
        responseType: 'arraybuffer',
      })
      .subscribe({
        next: (data: any) => {
          const blob = new Blob([data], { type: 'application/pdf' });
          this.blobReport = URL.createObjectURL(blob);
          this.error = null;

          this.openReport();
        },
        error: (error: any) => {
          if (error.ok === false) {
            this.error = `Error: ${error.statusText} - ${error.status}`;
          }

          this.blobReport = null;
        },
      });
  }
}
