<ng-container *ngIf="isDdosIpProtectionAdvancedListPage; else elseOtherPage">
    <a pbutton *ngIf="!error" i18n="@@bma_ddosmitigationreport_downloadlatest" i18n-title="@@bma_ddosmitigationreport_openreport" href="javascript:;" (click)="openReport()" class="p-element p-button-link p-button p-component ng-star-inserted action-download-report" title="Click to open the report.">Download latest report</a>
</ng-container>
<ng-template #elseOtherPage>
    <ng-container *ngIf="advancedProfile; else elseProtectionTypeAdvanced">
        <ng-container *ngIf="!error; else elseReportError">
            <a i18n-title="@@bma_ddosmitigationreport_openreport" href="javascript:;" (click)="openReport()" class="action-download-report" title="Click to open the report.">
                <span><i class="pi pi-file-pdf" aria-hidden="true"></i></span><span i18n="@@bma_ddosmitigationreport_download">Download latest report </span>
            </a>
        </ng-container>
        <ng-template #elseReportError>
            <span [title]="error" class="action-download-report text-warning">
                <i class="pi pi-file-pdf" aria-hidden="true"></i>
            </span>
        </ng-template>
    </ng-container>
    <ng-template #elseProtectionTypeAdvanced>
        <span><i class="pi pi-file-pdf" aria-hidden="true"></i></span><span i18n="@@bma_ddosmitigationreport_availableforadvance" i18n-title="@@bma_ddosmitigationreport_availabelforadvance" class="text-muted" title="Only available for Advanced profiles."> Only available for Advanced profiles </span>
    </ng-template>
</ng-template>
