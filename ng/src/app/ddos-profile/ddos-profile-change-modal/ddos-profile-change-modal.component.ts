import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { CidrToIpPipe } from 'src/app/pipes/cidr-to-ip.pipe';

@Component({
  selector: 'app-ddos-profile-change-modal',
  templateUrl: './ddos-profile-change-modal.component.html',
  styleUrls: ['./ddos-profile-change-modal.component.css'],
})
export class DdosProfileChangeModalComponent implements OnInit {
  equipment: any;
  equipmentType: string;
  ipDetails: any;
  ipAddress: any;
  form: UntypedFormGroup;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    private readonly formBuilder: UntypedFormBuilder,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private alertService: AlertService,
    private cidrToIpPipe: CidrToIpPipe
  ) {}

  ngOnInit(): void {
    this.equipment = this.config.data.equipment;
    this.equipmentType = this.config.data.equipmentType;
    this.ipDetails = this.config.data.ipDetails;
    this.ipAddress = this.cidrToIpPipe.transform(this.ipDetails.ip);
    this.form = this.formBuilder.group({
      detectionProfile: [this.ipDetails.ddos.detectionProfile, Validators.required],
    });
  }

  onUpdateDdosProfile() {
    this.alertService.clear();
    const errorMsg = this.validate();
    if (this.form.valid && errorMsg === '') {
      this.isSubmitting = true;

      const params: any = {};

      params.detectionProfile = this.form.get('detectionProfile').value;

      this.httpClient
        .put(
          `/_/internal/dedicatedserverapi/v2/${this.equipmentType}/${this.equipment.id}/ips/${this.ipAddress}`,
          params
        )
        .subscribe({
          next: (data: any) => {
            this.isSubmitting = false;
            this.ipDetails.ddos.detectionProfile = params.detectionProfile;
            this.alertService.alert(
              {
                type: 'success',
                description:
                  $localize`:@@bma_ddosprofilechangemodal_updatemessage:Advance DDoS profile changed for IP address` +
                  this.ipAddress +
                  `.`,
              },
              true
            );
            this.closeModal({ ddosProfileChanged: true, ipDetails: this.ipDetails });
          },
          error: (error: any) => {
            this.isSubmitting = false;
            this.alertService.alertApiError(error.error, true);
            this.closeModal({ ddosProfileChanged: false });
          },
        });
    } else {
      this.alertService.alert(
        {
          type: 'error',
          description: errorMsg,
        },
        true
      );
      this.closeModal();
    }
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }

  private validate(): string {
    if (this.ipDetails.floatingIp) {
      return (
        $localize`:@@bma_ddosprofilechangemodal_floatingipmessage:Can't change advance DDoS protection type for floating IP` +
        `'${this.ipAddress}', ` +
        $localize`:@@bma_ddosprofilechangemodal_updateanchorip:update its anchor IP` +
        `.`
      );
    }
    return '';
  }
}
