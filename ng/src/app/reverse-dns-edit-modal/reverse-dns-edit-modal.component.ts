import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { CidrToIpPipe } from 'src/app/pipes/cidr-to-ip.pipe';

@Component({
  selector: 'app-reverse-dns-edit-modal',
  templateUrl: './reverse-dns-edit-modal.component.html',
  styleUrls: ['./reverse-dns-edit-modal.component.css'],
})
export class ReverseDnsEditModalComponent implements OnInit {
  equipment: any;
  equipmentType: string;
  ipAddress: any;
  reverseLookup: any;

  form: UntypedFormGroup;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    private alertService: AlertService,
    private cidrToIpPipe: CidrToIpPipe,
    private readonly formBuilder: UntypedFormBuilder,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.equipment = this.dynamicDialogConfig.data.equipment;
    this.equipmentType = this.dynamicDialogConfig.data.equipmentType;
    this.ipAddress = this.dynamicDialogConfig.data.ipAddress;
    this.reverseLookup = this.dynamicDialogConfig.data.reverseLookup;

    this.ipAddress = this.cidrToIpPipe.transform(this.ipAddress);
    this.form = this.formBuilder.group({
      reverseLookup: [this.reverseLookup],
    });
  }

  onUpdateReverseDns() {
    this.alertService.clear();
    const errorMsg = this.validate();
    if (this.form.valid && (errorMsg === '' || errorMsg === null)) {
      this.isSubmitting = true;
      const params: any = {};

      params.reverseLookup = this.form.get('reverseLookup').value;

      this.httpClient
        .put(
          `/_/internal/dedicatedserverapi/v2/${this.equipmentType}/${this.equipment.id}/ips/${this.ipAddress}`,
          params
        )
        .subscribe({
          next: (data: any) => {
            this.isSubmitting = false;
            this.alertService.alert(
              {
                type: 'success',
                description: $localize`:@@bma_reversednseditmodal_success:Reverse DNS for IP address ${this.ipAddress} updated to ${params.reverseLookup}.`,
              },
              true
            );
            this.closeModal({ reverseDnsUpdated: true, updatedReverseDns: params.reverseLookup });
          },
          error: (error: any) => {
            this.isSubmitting = false;
            this.alertService.alertApiError(error.error, true);
            this.closeModal({ reverseDnsUpdated: false });
          },
        });
    } else {
      this.closeModal({ reverseDnsUpdated: false });
      this.alertService.alert(
        {
          type: 'error',
          description: errorMsg,
        },
        true
      );
    }
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }

  private validate(): string {
    const reverseLookup = this.form.get('reverseLookup').value;
    if (reverseLookup) {
      if (!/^[a-zA-Z0-9._-]+$/.test(reverseLookup)) {
        return $localize`:@@bma_reversednseditmodal_invaliddomainname:Domain name ${reverseLookup} for IP address ${this.ipAddress} is not valid. Please enter a valid domain name.`;
      }
    }
    return '';
  }
}
