<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '500px' }">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_reversedns_title" class="modal-title">Update Reverse DNS</h3>
    </ng-template>

    <form [formGroup]="form" (ngSubmit)="onUpdateReverseDns()">
        <div class="modal-body">
            <div id="action-update-reverse-dns-body-text">
                <div class="row mt-1 mb-2">
                    <div class="md-form input-group">
                        <div i18n="@@bma_reversedns_validname" class="col-12 mb-1">Valid domain name</div>
                        <div class="col-12 mb-1">
                            <input i18n-placeholder="@@bma_reversedns_hostedbyleaseweb" type="text" class="form-control" placeholder="hosted-by.leaseweb.com" id="reverseLookup" formControlName="reverseLookup" autofocus />
                            <span *ngIf="form.get('reverseLookup').errors && form.get('reverseLookup').errors.server">{{ form.get('reverseLookup').errors.server }}<br /></span>
                            <small class="text-muted font-italic"></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_cancel" pButton type="button" icon="pi pi-times" iconPos="left" label="Cancel" class="p-button-secondary" (click)="closeModal()"></button>
            <button i18n-label="@@bma_common_confirm" pButton (click)="onUpdateReverseDns()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" label="Confirm"></button>
        </div>
    </ng-template>
</p-dialog>
