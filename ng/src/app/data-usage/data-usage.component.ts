import { Component, Input, OnInit } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { formatDate } from '@angular/common';
import { FormatSizeUnitPipe } from 'src/app/pipes/format-size-unit.pipe';

@Component({
  selector: 'app-data-usage',
  templateUrl: './data-usage.component.html',
  styleUrls: ['./data-usage.component.css'],
})
export class DataUsageComponent implements OnInit {
  @Input() equipment: any;
  @Input() equipmentType = '';
  usage = $localize`:@@bma_common_loading:Loading...`;

  constructor(
    private httpClient: HttpClient,
    private formatSizeUnitPipe: FormatSizeUnitPipe
  ) {}

  ngOnInit(): void {
    this.getDataUsage();
  }

  getDataUsage() {
    const date = new Date();
    const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
    const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    const from = formatDate(firstDay, 'yyyy-MM-dd', 'en-US');
    const to = formatDate(lastDay, 'yyyy-MM-dd', 'en-US');

    let params = new HttpParams();

    params = params.set('from', from);
    params = params.set('to', to);

    if (
      this.equipment.contract &&
      this.equipment.contract.networkTraffic &&
      this.equipment.contract.networkTraffic.type
    ) {
      switch (this.equipment.contract.networkTraffic.type) {
        case '95TH':
        case 'FLATFEE':
        case 'BANDWIDTH_UNMETERED':
          params = params.set('aggregation', '95TH');
          this.httpClient
            .get(`/_/bareMetals/v2/${this.equipmentType}/${this.equipment.id}/metrics/bandwidth`, {
              params,
            })
            .subscribe(
              (usage: any) => {
                if (usage) {
                  this.usage =
                    this.formatSizeUnitPipe.transform(usage.metrics.PUBLIC.values[0].value, false, 'bytes/s') + ' 95th';
                }
              },
              (error) => {
                this.usage = $localize`:@@bma_common_error:error`;
              }
            );
          break;
        case 'DATATRAFFIC':
          params = params.set('aggregation', 'SUM');
          this.httpClient
            .get(`/_/bareMetals/v2/${this.equipmentType}/${this.equipment.id}/metrics/datatraffic`, {
              params,
            })
            .subscribe(
              (usage: any) => {
                if (usage) {
                  this.usage = this.formatSizeUnitPipe.transform(
                    usage.metrics.DOWN_PUBLIC.values[0].value + usage.metrics.UP_PUBLIC.values[0].value,
                    false,
                    'bytes'
                  );
                }
              },
              (error) => {
                this.usage = $localize`:@@bma_common_error:error`;
              }
            );
          break;

        default:
          this.usage = $localize`:@@bma_common_notavailable:Not Available`;
      }
    } else {
      this.usage = $localize`:@@bma_common_notavailable:Not Available`;
    }
  }
}
