import { Component, OnInit } from '@angular/core';
import { AlertService } from 'src/app/services/alert.service';
import { HttpClient } from '@angular/common/http';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-job-cancel-modal',
  templateUrl: './job-cancel-modal.component.html',
  styleUrls: ['./job-cancel-modal.component.css'],
})
export class JobCancelModalComponent implements OnInit {
  uuid: any;
  serverId: any;
  isSubmitting = false;
  isEmployee = false;
  showDialog = true;

  constructor(
    private alertService: AlertService,
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.uuid = this.config.data.uuid;
    this.serverId = this.config.data.serverId;
    this.isEmployee = this.currentUserService.isEmployee();
  }

  cancelJob(): void {
    this.isSubmitting = true;
    this.alertService.clear();

    let url;
    if (this.isEmployee) {
      url = `/_/internal/bmpapi/v2/jobs/${this.uuid}/cancel`;
    } else {
      url = `/_/internal/bmpapi/v2/servers/${this.serverId}/cancelActiveJob`;
    }

    this.httpClient.post<any>(url, {}).subscribe({
      next: (data: any) => {
        this.alertService.alert(
          {
            type: 'success',
            description: $localize`:@@bma_jobcancelmodal_success:BMP-API job with UUID ${this.uuid} canceled.`,
          },
          true
        );
        this.isSubmitting = false;
        this.closeModal({ jobCancelled: true });
      },
      error: (error: any) => {
        this.isSubmitting = false;
        this.alertService.alert(
          {
            type: 'error',
            description: $localize`:@@bma_jobcancelmodal_failure:Could not cancel job with UUID ${this.uuid}.`,
          },
          true
        );
        this.closeModal({ jobCancelled: false });
      },
    });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
