<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '600px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_jobexpiremodal_header" class="modal-title">Expire active job</h3>
    </ng-template>

    <div class="modal-body">
        <p i18n="@@bma_jobexpiremodal_confirmation">Are you sure you want to expire this job?</p>
        <p i18n="@@bma_jobexpiremodal_confirmation_extra">Be aware that expiring a job does _not_ trigger the <strong>"onFail"</strong> and <strong>"finally"</strong> workflows of this job.</p>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_no" pButton label="No" (click)="closeModal()" type="button" class="p-button-secondary"></button>
            <button i18n-label="@@bma_common_yes" pButton (click)="expireJob()" [loading]="isSubmitting" label="Yes"></button>
        </div>
    </ng-template>
</p-dialog>
