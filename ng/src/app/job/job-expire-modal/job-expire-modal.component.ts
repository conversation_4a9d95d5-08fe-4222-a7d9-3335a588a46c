import { Component, OnInit } from '@angular/core';
import { AlertService } from 'src/app/services/alert.service';
import { HttpClient } from '@angular/common/http';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-job-expire-modal',
  templateUrl: './job-expire-modal.component.html',
  styleUrls: ['./job-expire-modal.component.css'],
})
export class JobExpireModalComponent implements OnInit {
  uuid: any;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private alertService: AlertService,
    private httpClient: HttpClient,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.uuid = this.config.data.uuid;
  }

  expireJob(): void {
    this.isSubmitting = true;
    this.alertService.clear();

    this.httpClient.post<any>(`/_/internal/bmpapi/v2/jobs/${this.uuid}/expire`, {}).subscribe({
      next: (data: any) => {
        this.alertService.alert(
          {
            type: 'success',
            description: $localize`:@@bma_jobexpiremodal_success:BMP-API job with UUID ${this.uuid} expired.`,
          },
          true
        );
        this.isSubmitting = false;
        this.closeModal({ jobExpired: false });
      },
      error: (error: any) => {
        this.alertService.alert(
          {
            type: 'error',
            description: $localize`:@@bma_jobexpiremodal_failure:Could not expire job with UUID ${this.uuid}.`,
          },
          true
        );
        this.isSubmitting = false;
        this.closeModal({ jobExpired: false });
      },
    });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
