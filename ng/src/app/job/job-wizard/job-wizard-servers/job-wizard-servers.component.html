<div>
    <h4 i18n="@@bma_jobwizardservers_adddedicatedservers">Add dedicated servers</h4>
    <div class="alert alert-warning" role="alert" *ngIf="serversAlreadyAdded.length > 0 || serversNotFound.length > 0 || serversWithPrimaryIpNullRouted.length > 0">
        <span class="alert-icon fa fa-information fa-lg" aria-hidden="true"></span>

        <p class="alert-body" *ngIf="serversAlreadyAdded.length > 0">
            <span i18n="@@bma_jobwizardservers_description1">Dedicated servers with the following IDs have been already added to the list:</span>
            <strong *ngFor="let serverId of serversAlreadyAdded">
                {{ serverId }}
            </strong>
        </p>

        <p class="alert-body" *ngIf="serversNotFound.length > 0">
            <span i18n="@@bma_jobwizardservers_description2">Dedicated servers with the following IDs or MAC addresses not found:</span>
            <strong *ngFor="let serverId of serversNotFound">
                {{ serverId }}
            </strong>
        </p>

        <p class="alert-body" *ngIf="serversWithPrimaryIpNullRouted.length > 0">
            <span i18n="@@bma_jobwizardservers_description3">Dedicated servers with the following IDs or MAC addresses have null routed primary IP:</span>
            <strong *ngFor="let serverId of serversWithPrimaryIpNullRouted">
                {{ serverId }}
            </strong>
        </p>
    </div>
</div>

<form [formGroup]="form" (ngSubmit)="addServersByServerIdsOrMacAddresses()">
    <textarea i18n-placeholder="@@bma_jobwizardservers_serveridandmac" class="col-12 form-control" placeholder="Server IDs and/or MAC addresses" autofocus rows="10" formControlName="serverIdsOrMacAddresses"></textarea>
    <button i18n-label="@@bma_common_add" pButton [loading]="isFetchingServers" [disabled]="form.invalid" label="Add" class="p-button-primary my-3"></button>
</form>

<div class="alert alert-danger" role="alert" *ngIf="errorMessages.length > 0">
    <span class="alert-icon fa fa-alert fa-lg" aria-hidden="true"></span>
    <p class="alert-body" *ngFor="let message of errorMessages">{{ message }}</p>
</div>

<div *ngIf="servers.length > 0" class="table-responsive">
    <hr />
    <table class="table table-striped">
        <thead>
            <tr>
                <th>#</th>
                <th i18n="@@bma_common_id">ID</th>
                <th i18n="@@bma_common_customerid">Customer ID</th>
                <th i18n="@@bma_common_reference">Reference</th>
                <th i18n="@@bma_common_ipaddress">IP Address</th>
                <th i18n="@@bma_common_netmask">Netmask</th>
                <th i18n="@@bma_common_gateway">Gateway</th>
                <th i18n="@@bma_common_macaddress">Mac Address</th>
                <th i18n="@@bma_common_site">Site</th>
                <th>&nbsp;</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let server of servers;let i = index">
                <td>{{ i + 1 }}</td>
                <td>{{ server.serverId }}</td>
                <td>{{ server.customerId }}</td>
                <td>{{ server.reference }}</td>
                <td>
                    <span class="selectable">{{ server.ipAddress }}</span>
                </td>
                <td>{{ server.netmask }}</td>
                <td>{{ server.gateway }}</td>
                <td>
                    <span class="selectable">{{ server.macAddress }}</span>
                </td>
                <td>{{ server.site }}</td>
                <td>
                    <a title="Remove Server" role="button" class="pull-right" (click)="removeServer(i)">
                        <i class="fa fa-lg fa-cancel mr-1 text-pink"></i>
                    </a>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<div class="mt-3 pager-next">
    <button i18n-label="@@bma_common_nextstep" pButton type="button" (click)="next()" icon="pi pi-chevron-right" iconPos="right" label="Next step" class="p-button-primary" [disabled]="!isServersListValid() || isFetchingServers"></button>
</div>
