import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { CustomValidator } from 'src/app/validators/custom.validator';

@Component({
  selector: 'app-job-wizard-servers',
  templateUrl: './job-wizard-servers.component.html',
  styleUrls: ['./job-wizard-servers.component.css', '../job-wizard-shared.css'],
})
export class JobWizardServersComponent implements OnInit {
  @Output() submitted = new EventEmitter<any>();

  form: UntypedFormGroup;
  servers = [];
  serversNotFound = [];
  serversAlreadyAdded = [];
  serversWithPrimaryIpNullRouted = [];
  errorMessages = [];
  isFetchingServers = false;

  constructor(
    private httpClient: HttpClient,
    private formBuilder: UntypedFormBuilder,
    private customValidator: CustomValidator
  ) {}

  ngOnInit(): void {
    this.form = this.formBuilder.group({
      serverIdsOrMacAddresses: [null, Validators.required],
    });
  }

  reset() {
    this.form.reset();
    this.servers = [];
    this.serversNotFound = [];
    this.serversAlreadyAdded = [];
    this.serversWithPrimaryIpNullRouted = [];
    this.errorMessages = [];
    this.isFetchingServers = false;
  }

  next() {
    this.submitted.emit({ servers: this.servers });
  }

  checkServersList() {
    this.errorMessages = [];

    if (!this.hasServersListSameCustomer()) {
      this.errorMessages.push(
        'Only servers that belong to one customer or unassigned/free can be added. Please remove redundant servers.'
      );
    }
  }

  isServersListValid() {
    return this.servers.length > 0 && this.errorMessages.length === 0;
  }

  hasServersListSameCustomer() {
    if (this.servers.length === 0) {
      return true;
    }

    const customerId = this.servers[0].customerId;
    const customerServers = this.servers.filter((item) => item.customerId === customerId);
    return customerServers.length === this.servers.length;
  }

  addServersByServerIdsOrMacAddresses() {
    const serverIdsOrMacAddresses = this.form
      .get('serverIdsOrMacAddresses')
      .value.split(/[\s;,\r\n|\n]+/)
      .map((value) => value.trim())
      .filter((value) => value.length > 0);

    this.getServers(serverIdsOrMacAddresses);
  }

  async getServers(serverIdsOrMacAddresses) {
    this.isFetchingServers = true;
    this.serversNotFound = [];
    this.serversWithPrimaryIpNullRouted = [];
    this.serversAlreadyAdded = [];

    for (const serverIdOrMacAddress of serverIdsOrMacAddresses) {
      const params: any = {};
      if (this.customValidator.isValidMacAddress(serverIdOrMacAddress)) {
        params.macAddress = serverIdOrMacAddress;
      } else if (Number.isInteger(Number(serverIdOrMacAddress))) {
        params.serverId = serverIdOrMacAddress;
      } else {
        this.serversNotFound.push(serverIdOrMacAddress);
        continue;
      }

      await this.httpClient
        .get('/_/internal/dedicatedserverapi/v2/servers', { params })
        .toPromise()
        .then(
          (data: any) => {
            if (data.servers.length === 0) {
              this.serversNotFound.push(serverIdOrMacAddress);
              return;
            }

            const serverFull = data.servers[0];

            if (serverFull.networkInterfaces.public?.nullRouted) {
              this.serversWithPrimaryIpNullRouted.push(serverIdOrMacAddress);
              return;
            }

            const server = this.extractData(serverFull);

            for (const s of this.servers) {
              if (s.serverId === server.serverId || s.macAddress === server.macAddress) {
                this.serversAlreadyAdded.push(serverIdOrMacAddress);
                return;
              }
            }

            this.servers.push(server);
          },
          (error) => {
            this.serversNotFound.push(serverIdOrMacAddress);
          }
        );
    }

    this.isFetchingServers = false;
    this.checkServersList();
  }

  extractData(serverFull) {
    const server = {
      serverId: serverFull.id,
      customerId: serverFull.contract ? (serverFull.contract.customerId ?? '-') : 'Unassigned',
      reference: serverFull.contract?.reference ?? '-',
      site: serverFull.location?.site ?? '-',
      ipAddress: '-',
      macAddress: '-',
      gateway: '-',
      netmask: '-',
    };

    const publicInterface = serverFull.networkInterfaces?.public;

    if (publicInterface) {
      server.macAddress = publicInterface.mac ?? '-';
      server.gateway = publicInterface.gateway ?? '-';

      if (publicInterface.ip) {
        const ipParts = publicInterface.ip.split('/');

        server.ipAddress = ipParts[0];

        if (ipParts.length > 1) {
          const netmask = [];
          for (let i = 0, ipNetmask = ipParts[1]; i < 4; i++) {
            const n = Math.min(ipNetmask, 8);
            netmask.push(256 - Math.pow(2, 8 - n));
            ipNetmask -= n;
          }
          server.netmask = netmask.join('.');
        }
      }
    }

    return server;
  }

  removeServer(index): void {
    this.servers.splice(index, 1);
    this.checkServersList();
  }
}
