<a href="https://wiki.ocom.com/display/KC/Use+the+Job+Wizard+to+launch+bulk+jobs+in+EMP" class="pull-right" target="_blank">Job Wizard documentation</a>

<h1 i18n="@@bma_jobwizard_header">BMP-API Job Wizard</h1>

<p-tabView [(activeIndex)]="tabIndex">
    <p-tabPanel header="Servers" [disabled]="batchRunStatus">
        <app-job-wizard-servers #serversComponent (submitted)="onServersSubmitted($event)"></app-job-wizard-servers>
    </p-tabPanel>

    <p-tabPanel header="Job" [disabled]="batchRunStatus || tabIndex < 1">
        <app-job-wizard-job #jobComponent (canceled)="onJobCanceled($event)" (submitted)="onJobSubmitted($event)"></app-job-wizard-job>
    </p-tabPanel>

    <p-tabPanel header="Confirmation" [disabled]="batchRunStatus || tabIndex < 2">
        <app-job-wizard-confirmation #confirmationComponent [servers]="servers" [payload]="payload" [job]="job" (canceled)="onConfirmationCanceled($event)" (submitted)="onConfirmationSubmitted($event)"></app-job-wizard-confirmation>
    </p-tabPanel>

    <p-tabPanel header="Execution" [disabled]="batchRunStatus || tabIndex < 3">
        <app-job-wizard-execution [job]="job" [batchRunStatus]="batchRunStatus" [successfulJobServers]="successfulJobServers" [failedJobServers]="failedJobServers" [batchId]="batchId"></app-job-wizard-execution>
    </p-tabPanel>
</p-tabView>

<button i18n-label="@@bma_jobwizard_requestanotherjob" pButton *ngIf="batchRunStatus === 'completed'" type="button" icon="fa fa-reinstall" iconPos="left" label="Request another job" class="p-button-primary mt-3" (click)="serversComponent.reset(); jobComponent.reset(); confirmationComponent.reset(); resetState()"></button>
