<div>
    <span *ngIf="batchId && successfulJobServers" class="pull-right">
        <b>Batch:&nbsp;</b>
        <a [routerLink]="['/jobs']" [queryParams]="{batchId: batchId}">
            {{ batchId }}
        </a>
    </span>

    <h4 i18n="@@bma_jobwizardexecution_jobrequest">You have requested the {{ job }} job.</h4>
    <span> {{ batchRunStatus }}</span>

    <div class="alert alert-success" *ngIf="successfulJobServers.length > 0" role="alert">
        <p i18n="@@bma_jobwizardexecution_description1">The job has been successfully requested for the following dedicated servers:</p>
        <table class="table table-striped">
            <thead>
                <tr>
                    <th i18n="@@bma_common_id">ID</th>
                    <th i18n="@@bma_common_ipaddress">IP Address</th>
                    <th i18n="@@bma_common_netmask">Netmask</th>
                    <th i18n="@@bma_common_gateway">Gateway</th>
                    <th i18n="@@bma_common_macaddress">Mac Address</th>
                    <th i18n="@@bma_common_site">Site</th>
                    <th>&nbsp;</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let server of successfulJobServers">
                    <td>{{ server.serverId }}</td>
                    <td>{{ server.ipAddress }}</td>
                    <td>{{ server.netmask }}</td>
                    <td>{{ server.gateway }}</td>
                    <td>{{ server.macAddress }}</td>
                    <td>{{ server.site }}</td>
                    <td><a i18n="@@bma_common_details" [routerLink]="['/servers', server.serverId, 'jobs', server.jobUuid]">Details</a></td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="alert alert-danger" *ngIf="failedJobServers.length > 0" role="alert">
        <p i18n="@@bma_jobwizardexecution_description2">An error has occurred while requesting the job for the following dedicated servers:</p>
        <table class="table table-striped">
            <thead>
                <tr>
                    <th i18n="@@bma_common_id">ID</th>
                    <th i18n="@@bma_common_ipaddress">IP Address</th>
                    <th i18n="@@bma_common_netmask">Netmask</th>
                    <th i18n="@@bma_common_gateway">Gateway</th>
                    <th i18n="@@bma_common_macaddress">Mac Address</th>
                    <th i18n="@@bma_common_site">Site</th>
                    <th i18n="@@bma_common_Error">Error</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let server of failedJobServers">
                    <td>{{ server.serverId }}</td>
                    <td>{{ server.ipAddress }}</td>
                    <td>{{ server.netmask }}</td>
                    <td>{{ server.gateway }}</td>
                    <td>{{ server.macAddress }}</td>
                    <td>{{ server.site }}</td>
                    <td>
                        <p class="text-danger">
                            <strong>{{ server.jobError.errorMessage }}</strong>
                        </p>
                        <strong>Correlation ID: {{ server.jobError.correlationId }}</strong
                        ><br />
                    </td>
                </tr>
            </tbody>
        </table>
        <p i18n="@@bma_jobwizardexecution_description3">
            Please use the correlation ID of the dedicated servers with failed requests while
            <a href="https://one.leaseweb.net/serviceportal#reportIncident" target="_blank">contacting support</a>.
        </p>
    </div>
</div>
