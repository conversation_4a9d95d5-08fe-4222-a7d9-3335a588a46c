import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-job-wizard-execution',
  templateUrl: './job-wizard-execution.component.html',
  styleUrls: ['./job-wizard-execution.component.css', '../job-wizard-shared.css'],
})
export class JobWizardExecutionComponent {
  @Input() batchId: string;
  @Input() job: string;
  @Input() batchRunStatus: string;
  @Input() successfulJobServers: any = [];
  @Input() failedJobServers: any = [];
}
