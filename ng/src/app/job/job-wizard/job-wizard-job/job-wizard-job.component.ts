import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-job-wizard-job',
  templateUrl: './job-wizard-job.component.html',
  styleUrls: ['./job-wizard-job.component.css', '../job-wizard-shared.css'],
})
export class JobWizardJobComponent implements OnInit {
  @Output() submitted = new EventEmitter<any>();
  @Output() canceled = new EventEmitter();

  jobs = [];
  job = null;
  payload = '';

  raidLevel = 'single';
  numberOfDisks = '';
  powerCycle = true;

  constructor(private httpClient: HttpClient) {}

  ngOnInit(): void {
    this.getAvailableJobs();
  }

  reset() {
    this.job = null;
    this.payload = '';
    this.raidLevel = 'single';
    this.numberOfDisks = '';
    this.powerCycle = true;
  }

  prev() {
    this.canceled.emit();
  }

  next() {
    this.submitted.emit({
      job: this.job,
      payload: this.payload.trim() ? JSON.parse(this.payload) : null,
    });
  }

  getAvailableJobs() {
    this.httpClient.get<any>(`/_/internal/bmpapi/v2/jobDefinitions`).subscribe((data: any) => {
      this.jobs = data.definitions;
    });
  }

  isPayloadValid() {
    const payload = this.payload.trim();

    if (payload.length === 0) {
      return true;
    }

    try {
      JSON.parse(payload);
    } catch (e) {
      return false;
    }

    return true;
  }

  onJobChanged() {
    this.payload = this.getDefaultPayload();
  }

  onConfigureHardwareRaidChanged() {
    this.setConfigureHardwareRaidJobPayload();
  }

  setConfigureHardwareRaidJobPayload() {
    const payload: any = {
      powerCycle: this.powerCycle,
      type: this.raidLevel === 'single' ? 'NONE' : 'HW',
    };

    if (this.raidLevel !== 'single') {
      payload.level = parseInt(this.raidLevel, 10);
      const numberOfDisks = parseInt(this.numberOfDisks, 10);
      if (numberOfDisks) {
        payload.numberOfDisks = numberOfDisks;
      }
    }

    this.payload = JSON.stringify(payload);
  }

  getDefaultPayload(): any {
    let defaultPayload = null;
    switch (this.job) {
      case 'prepareForSale':
        defaultPayload = {
          powerCycle: true,
          doWipeDisks: true,
          firmwareUpgrade: true,
        };
    }
    return defaultPayload ? JSON.stringify(defaultPayload, null, 2) : '';
  }
}
