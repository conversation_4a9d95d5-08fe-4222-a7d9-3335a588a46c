<div>
    <h4 i18n="@@bma_common_job">Job</h4>
    <div class="row">
        <div class="col-sm-3 form-group">
            <select name="job" [(ngModel)]="job" (change)="onJobChanged()" label="job" class="form-control">
                <option i18n="@@bma_common_jobselect" value="null" disabled>Select a job</option>
                <option *ngFor="let job of jobs" [value]="job">{{ job }}</option>
            </select>
        </div>
    </div>

    <form *ngIf="job === 'configureHardwareRaid'" class="col-4 pl-0">
        <div class="form-group">
            <label i18n="@@bma_common_raidlevel" class="col-form-label" for="raidLevel">Raid Level:</label>
            <select class="form-control" id="raidLevel" name="raidLevel" [(ngModel)]="raidLevel" (change)="onConfigureHardwareRaidChanged()">
                <option i18n="@@bma_common_singledrivearray" value="single" selected>Single Drive Array</option>
                <option i18n="@@bma_common_raid0" value="0">RAID 0</option>
                <option i18n="@@bma_common_raid1" value="1">RAID 1</option>
                <option i18n="@@bma_common_raid5" value="5">RAID 5</option>
                <option i18n="@@bma_common_raid10" value="10">RAID 10</option>
            </select>
        </div>

        <div class="form-group" *ngIf="raidLevel !== 'single'">
            <label i18n="@@bma_common_diskstouse" class="col-form-label" for="numberOfDisks">Number of disks to use:</label>
            <input class="form-control" type="number" id="numberOfDisks" name="numberOfDisks" [(ngModel)]="numberOfDisks" (change)="onConfigureHardwareRaidChanged()" />
        </div>

        <div class="form-check">
            <input class="form-check-input" type="checkbox" id="powerCycle" name="powerCycle" [(ngModel)]="powerCycle" (change)="onConfigureHardwareRaidChanged()" />
            <label i18n="@@bma_common_powercycle" class="form-check-label" for="powerCycle">Power Cycle</label>
        </div>
    </form>

    <app-local-storage-editor *ngIf="job !== null && job !== 'configureHardwareRaid'" [key]="'jobPayload.' + job" [(content)]="payload" label="Payload (JSON)"></app-local-storage-editor>
</div>

<div class="mt-3 pager-prev-next">
    <button i18n="@@bma_common_previousstep" pButton type="button" icon="pi pi-chevron-left" iconPos="left" label="Previous step" (click)="prev()" class="p-button-primary"></button>
    <button i18n="@@bma_common_nextstep" pButton type="button" icon="pi pi-chevron-right" iconPos="right" label="Next step" (click)="next()" *ngIf="job !== null && isPayloadValid()" class="p-button-primary"></button>
</div>
