<h3 i18n="@@bma_jobwizardconfirmation_header">Dedicated Servers</h3>

<div class="row">
    <div i18n="@@bma_jobwizardconfirmation_selecteddedicatedservers" class="col-sm-2 text-right text-primary">Selected dedicated servers</div>
    <div class="col-sm-10">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>#</th>
                        <th i18n="@@bma_common_id">ID</th>
                        <th i18n="@@bma_common_customerid">Customer ID</th>
                        <th i18n="@@bma_common_reference">Reference</th>
                        <th i18n="@@bma_common_ipaddress">IP Address</th>
                        <th i18n="@@bma_common_netmask">Netmask</th>
                        <th i18n="@@bma_common_gateway">Gateway</th>
                        <th i18n="@@bma_common_macaddress">Mac Address</th>
                        <th i18n="@@bma_common_site">Site</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let server of servers; let i = index;">
                        <td>{{ i + 1 }}</td>
                        <td>{{ server.serverId }}</td>
                        <td>{{ server.customerId }}</td>
                        <td>{{ server.reference }}</td>
                        <td>
                            <span class="selectable">{{ server.ipAddress }}</span>
                        </td>
                        <td>{{ server.netmask }}</td>
                        <td>{{ server.gateway }}</td>
                        <td>
                            <span class="selectable">{{ server.macAddress }}</span>
                        </td>
                        <td>{{ server.site }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<h3>BMP-API Job</h3>

<div class="row">
    <div i18n="@@bma_jobwizardconfirmation_selectedjob" class="col-sm-2 text-right text-primary">Selected BMP-API job</div>
    <div class="col-sm-10">
        {{ job }}
    </div>
</div>

<div *ngIf="payload">
    <div class="row">
        <div i18n="@@bma_common_payload" class="col-sm-2 text-right text-primary">Payload</div>
        <div class="col-sm-9">
            <pre><span class="text-monospace">{{ payload | json }}</span></pre>
        </div>
    </div>
</div>

<div class="row mt-3">
    <h3>&nbsp;</h3>
    <div class="col-sm-2 text-primary">
        <h3 i18n="@@bma_common_batchid">Batch ID</h3>
    </div>
    <form [formGroup]="form" class="col-sm-5">
        <input type="text" class="form-control" formControlName="batchId" />
    </form>
</div>

<div class="mt-3 pager-prev-next">
    <button i18n="@@bma_common_previousstep" pButton type="button" icon="pi pi-chevron-left" iconPos="left" label="Previous step" (click)="prev()" class="p-button-primary"></button>
    <button i18n="@@bma_common_execute" pButton type="button" icon="pi pi-check" iconPos="right" label="Execute" (click)="next()" class="p-button-primary" [disabled]="form.invalid"></button>
</div>
