import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';

@Component({
  selector: 'app-job-wizard-confirmation',
  templateUrl: './job-wizard-confirmation.component.html',
  styleUrls: ['./job-wizard-confirmation.component.css', '../job-wizard-shared.css'],
})
export class JobWizardConfirmationComponent implements OnInit {
  @Input() servers: Array<any>;
  @Input() payload: any;
  @Input() job: string;
  @Output() submitted = new EventEmitter<any>();
  @Output() canceled = new EventEmitter();

  form: UntypedFormGroup;

  constructor(
    private httpClient: HttpClient,
    private readonly formBuilder: UntypedFormBuilder
  ) {}

  ngOnInit(): void {
    this.form = this.formBuilder.group({
      batchId: [null, Validators.pattern('[a-zA-Z0-9_-]*')],
    });
    this.generateBatchId();
  }

  reset() {
    this.generateBatchId();
  }

  prev() {
    this.canceled.emit();
  }

  next() {
    this.submitted.emit({ batchId: this.form.get('batchId').value });
  }

  generateBatchId() {
    this.httpClient.get<any>('/randomwords.json').subscribe((randomwords: string[]) => {
      const words = [];
      do {
        words.push(randomwords[Math.floor(Math.random() * randomwords.length)]);
      } while (words.length < 3);
      this.form.get('batchId').setValue(words.join('-'));
    });
  }
}
