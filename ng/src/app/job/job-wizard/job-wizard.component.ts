import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-job-wizard',
  templateUrl: './job-wizard.component.html',
  styleUrls: ['./job-wizard.component.css'],
})
export class JobWizardComponent implements OnInit {
  job = null;
  servers = [];
  batchId: string;
  payload: any;
  batchRunStatus = '';
  successfulJobServers: any = [];
  failedJobServers: any = [];
  tabIndex = 0;

  constructor(
    private httpClient: HttpClient,
    private titleService: Title
  ) {}

  ngOnInit(): void {
    this.titleService.setTitle($localize`:@@bma_jobwizard_title:Job Wizard` + ' | Leaseweb');
  }

  openNextTab() {
    this.tabIndex += 1;
  }

  openPrevTab() {
    this.tabIndex -= 1;
  }

  onServersSubmitted(event) {
    this.servers = event.servers;
    this.openNextTab();
  }

  onJobCanceled(event) {
    this.openPrevTab();
  }

  onJobSubmitted(event) {
    this.job = event.job;
    this.payload = event.payload;
    this.openNextTab();
  }

  onConfirmationCanceled(event) {
    this.openPrevTab();
  }

  onConfirmationSubmitted(event) {
    this.batchId = event.batchId;
    this.openNextTab();
    this.runBatch();
  }

  async runBatch() {
    this.batchRunStatus = 'pending';

    const params: any = {
      name: this.job,
    };

    if (this.payload) {
      params.payload = this.payload;
    }

    if (this.batchId.length > 0) {
      params.batchId = this.batchId;
    }

    for (const server of this.servers) {
      params.serverId = server.serverId;

      await this.httpClient
        .post('/_/internal/bmpapi/v2/jobs', params)
        .toPromise()
        .then(
          (data: any) => {
            server.jobUuid = data.uuid;
            this.successfulJobServers.push(server);
          },
          (error) => {
            server.jobError = error.error;
            this.failedJobServers.push(server);
          }
        );
    }

    this.batchRunStatus = 'completed';
  }

  resetState() {
    this.job = null;
    this.payload = '';
    this.servers = [];
    this.successfulJobServers = [];
    this.failedJobServers = [];
    this.batchRunStatus = '';
    this.tabIndex = 0;
  }
}
