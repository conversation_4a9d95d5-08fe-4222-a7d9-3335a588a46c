<div class="row">
    <h1 i18n="@@bma_common_bmpapijobs" class="col-md-12">BMP-API Jobs</h1>
</div>

<form [formGroup]="filters" (ngSubmit)="submitFilters()" role="form">
    <div class="row mb-3">
        <div class="col">
            <div class="input-group">
                <input i18n-placeholder="@@bma_common_filter" type="text" formControlName="filter" placeholder="Filter..." value="{{ filters.get('filter').value|default }}" class="form-control validate-json mr-2" autofocus />

                <div class="input-group-append">
                    <a href="https://wiki.ocom.com/display/PROG/How+to+filter+BMA+Jobs+using+JSON+Queries" target="_blank" class="btn btn-outline-primary" role="button">
                        <span class="fa fa-information mr-2" aria-hidden="true"></span>
                        <span i18n="@@bma_common_jsonsearch">Searching with JSO<PERSON></span>
                    </a>
                </div>

                <div i18n-placeholder="@@bma_common_invalidjson" class="invalid-feedback">Invalid JSON string.</div>
            </div>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col">
            <select formControlName="site" class="form-control">
                <option i18n="@@bma_common_site" value="null">Site</option>

                <option *ngFor="let option of sites|default:[]" value="{{ option }}">
                    {{ option }}
                </option>
            </select>
        </div>

        <div class="col">
            <select formControlName="status" class="form-control">
                <option i18n="@@bma_common_status" value="null">Status</option>

                <option *ngFor="let jobStatus of statuses" value="{{ jobStatus }}">
                    {{ jobStatus }}
                </option>
            </select>
        </div>

        <div class="col">
            <select formControlName="type" class="form-control">
                <option i18n="@@bma_common_type" value="null">Type</option>

                <option *ngFor="let jobType of jobTypes" value="{{ jobType }}">
                    {{ jobType }}
                </option>
            </select>
        </div>

        <div class="col">
            <input i18n-placeholder="@@bma_common_batchid" type="text" formControlName="batchId" value="{{ filters.get('batchId').value }}" placeholder="Batch ID" class="form-control" />
        </div>
    </div>
    <div class="row mb-3">
        <div class="col">
            <input i18n-placeholder="@@bma_common_customerid" type="text" formControlName="customerId" value="{{ filters.get('customerId').value }}" placeholder="Customer ID" class="form-control" />
        </div>

        <div class="col">
            <div class="input-group justify-content-between">
                <div class="input-group-prepend">
                    <span i18n="@@bma_joblist_from" class="input-group-text">From</span>
                </div>
                <p-calendar dataType="string" (onSelect)="onFromDateSelect($event)" styleClass="d-flex flex-grow-1 calendar" formControlName="from" dateFormat="yy-mm-dd" [maxDate]="maxDate" [readonlyInput]="true" [showIcon]="true"></p-calendar>
            </div>
        </div>

        <div class="col">
            <div class="input-group justify-content-between">
                <div class="input-group-prepend">
                    <span i18n="@@bma_common_to" class="input-group-text">To</span>
                </div>
                <p-calendar dataType="string" styleClass="d-flex flex-grow-1 calendar" dateFormat="yy-mm-dd" formControlName="to" [readonlyInput]="true" [minDate]="toMinDate" [maxDate]="maxDate" [showIcon]="true"></p-calendar>
            </div>
        </div>

        <div class="col">
            <select formControlName="operatingSystemId" class="form-control">
                <option i18n="@@bma_common_os" value="null">Operating System</option>

                <option *ngFor="let os of operatingSystems|default:[]" value="{{ os.id }}">
                    {{ os.name }}
                </option>
            </select>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col text-right">
            <button i18n-label="@@bma_common_reset" pButton (click)="clearFilters()" type="button" icon="pi pi-refresh" class="p-button-secondary mr-2" label="Reset"></button>
            <button i18n-label="@@bma_common_search" pButton [disabled]="isLoading" label="Search" icon="pi pi-search"></button>
        </div>
    </div>
</form>

<div *ngIf="(jobs|default:'') && batch" class="row mb-3">
    <div class="col-sm-3">
        <div class="form-check form-check-inline">
            <input class="form-check-input" [(ngModel)]="selectAllJobs" [checked]="{'checked': jobs.length == selectedJobs && selectedJobs.length > 0}" (change)="toggleAllJobsSelection()" name="selectAllJobs" type="checkbox" id="select-all-jobs" />
            <label i18n="@@bma_joblist_selectactivejobs" class="form-check-label" for="select-all-jobs">Select all active jobs</label>
        </div>
    </div>
    <div class="col-sm-2 text-right">
        <button i18n-label="@@bma_common_cancel" pButton (click)="cancelJobs()" [loading]="isCancelingJobs" class="p-button-danger p-button-block" [ngClass]="{'invisible': selectedJobs.length == 0}" label="Cancel"></button>
    </div>
</div>

<div class="d-flex justify-content-center align-items-center">
    <app-loader *ngIf="isLoading"></app-loader>
</div>

<div *ngIf="!isLoading && jobs">
    <div class="row mb-3">
        <div class="col">
            <p i18n="@@bma_common_nojobs" class="text-center p-4" *ngIf="jobs.length === 0">No jobs found.</p>
            <ng-container *ngFor="let job of jobs">
                <div>
                    <h3 *ngIf="job.day|default:''">
                        {{ job.day|date:'longDate' }}
                    </h3>
                    <table class="table table-striped job-list">
                        <thead>
                            <tr>
                                <th *ngIf="batchId|default:''" scope="col" class="col-batch-id"></th>
                                <th i18n="@@bma_common_created" scope="col" class="text-center table-col-size-2">Created</th>
                                <th i18n="@@bma_common_status" scope="col" class="text-center table-col-size-1">Status</th>
                                <th i18n="@@bma_common_id" scope="col" class="table-col-size-3">ID</th>
                                <th i18n="@@bma_common_type" scope="col" class="table-col-size-3">Type</th>
                                <th i18n="@@bma_common_progress" scope="col" class="table-col-size-3">Progress</th>
                            </tr>
                        </thead>

                        <tbody>
                            <tr *ngFor="let job of job.jobs|default:[]" data-id="{{ job.uuid }}" class="bmp-job-status-{{ job.status|lowercase }}">
                                <td *ngIf="batchId|default:''" class="text-center">
                                    <input *ngIf="job.status == 'ACTIVE'" [checked]="isJobSelected(job.uuid)" type="checkbox" (change)="toggleJobSelection($event)" name="jobs" data-url="#" value="{{ job.uuid }}" class="form-control active-jobs" />
                                </td>
                                <td class="text-center">
                                    {{ job.createdAt|timeAgo }}
                                    <br />
                                    <small class="text-muted">{{ job.createdAt|lswDateTime }}</small>
                                </td>

                                <td class="text-center">
                                    <span class="job-status btn btn-sm py-0 disabled" [ngClass]="getJobStatusClass(job.status)">
                                        {{ job.status }}
                                    </span>
                                </td>

                                <td>
                                    <a href="#" (click)="openServerInfoPopupModal(job.serverId); false"> {{ job.serverId }} </a><br />
                                    <small class="text-monospace">
                                        <span class="selectable">{{ job.node }}</span>
                                    </small>
                                </td>

                                <td class="text-truncate">
                                    <a [routerLink]="['/servers', job.serverId, 'jobs', job.uuid]"> {{ job.type }} </a><br />
                                    <small *ngIf="job.type == 'install'" class="text-monospace">
                                        <span class="selectable">{{ job.payload.operatingSystemId }}</span>
                                    </small>
                                    <small *ngIf="job.type == 'rescueMode'" class="text-monospace">
                                        <span class="selectable">{{ job.payload.rescueImageId }}</span>
                                    </small>
                                </td>
                                <td>
                                    <p-progressBar *ngIf="job.status == 'ACTIVE' && job.currentTask" [value]="job.progress.percentage"></p-progressBar>
                                    <small *ngIf="job.currentTask" class="text-monospace d-inline-block text-truncate"> {{ job.currentTask.flow}}::{{job.currentTask.type }} </small>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </ng-container>
        </div>
    </div>
</div>

<div *ngIf="!isLoading && jobs">
    <app-pagination [totalCount]="jobs._metadata.totalCount" [limit]="jobs._metadata.limit" [offset]="jobs._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
</div>
