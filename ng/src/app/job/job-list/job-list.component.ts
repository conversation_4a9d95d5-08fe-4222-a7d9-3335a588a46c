import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { AlertService } from 'src/app/services/alert.service';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { SiteService } from 'src/app/services/site.service';
import { ModalService } from 'src/app/services/modal.service';
import { ServerInfoPopupModalComponent } from 'src/app/server/server-info-popup-modal/server-info-popup-modal.component';
import { LswNotificationHubService } from '@lsw/notification-hub';
import { MercureService } from 'src/app/services/mercure.service';
import { ServerEvent } from 'src/app/models/server-event.model';
import { first } from 'rxjs';

@Component({
  selector: 'app-job-list',
  templateUrl: './job-list.component.html',
  styleUrls: ['./job-list.component.css'],
})
export class JobListComponent implements OnInit {
  jobs: any;
  filters: UntypedFormGroup;
  sites: string[];
  jobTypes: any;
  isLoading = false;
  isCancelingJobs = false;
  operatingSystems: any;
  jsonFilter: any;
  statuses = ['ACTIVE', 'FINISHED', 'FAILED', 'EXPIRED', 'CANCELED'];
  text: string;
  selectAllJobs = false;
  selectedJobs = [];
  batch: any;
  maxDate: Date;
  toMinDate: Date;

  constructor(
    private activatedRoute: ActivatedRoute,
    private modalService: ModalService,
    private httpClient: HttpClient,
    private router: Router,
    private siteService: SiteService,
    private titleService: Title,
    private readonly formBuilder: UntypedFormBuilder,
    private alertService: AlertService,
    private notificationHubService: LswNotificationHubService,
    private mercureService: MercureService,
    private ref: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.maxDate = new Date();
    this.titleService.setTitle('BMP-API Jobs | Leaseweb');
    this.sites = this.siteService.sites();
    this.filters = this.formBuilder.group({
      limit: [10],
      offset: [0],
      filter: [null],
      site: [null],
      status: [null],
      type: [null],
      batchId: [null],
      customerId: [null],
      from: [null],
      to: [null],
      operatingSystemId: [null],
    });
    this.getJobTypes();
    this.getOperatingSystems();
    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    this.filters.reset(queryParams);
    this.getJobs();
  }

  onFromDateSelect($event): void {
    this.toMinDate = new Date($event);
  }

  getJobTypes(): void {
    this.httpClient.get<any>(`/_/internal/bmpapi/v2/jobDefinitions`, {}).subscribe(
      (data: any) => {
        if (data.definitions) {
          this.jobTypes = data.definitions;
        }
      },
      (error: any) => {
        this.jobTypes = [];
      }
    );
  }

  getOperatingSystems(): void {
    this.httpClient.get<any>(`/_/bareMetals/v2/operatingSystems`, {}).subscribe(
      (data: any) => {
        if (data.operatingSystems) {
          this.operatingSystems = data.operatingSystems;
        }
      },
      (error: any) => {
        this.operatingSystems = [];
      }
    );
  }

  getJobs(): void {
    const customisedFilter = this.buildJsonFilter();
    let params = new HttpParams();
    params = params.set('filter', customisedFilter);
    params = params.set('limit', this.filters.get('limit').value ?? 10);
    params = params.set('offset', this.filters.get('offset').value ?? 0);
    this.alertService.clear();
    this.isLoading = true;

    this.httpClient.get<any>('/_/internal/bmpapi/v2/jobs', { params }).subscribe(
      (data: any) => {
        if (data.jobs) {
          this.jobs = [];

          data.jobs.forEach((job) => {
            const day = new Date(job.createdAt ?? null);

            if (!this.jobs.some((period) => period.day === day.toISOString().slice(0, 10))) {
              this.jobs.push({ day: day.toISOString().slice(0, 10), jobs: [] });
            }

            const periodIndex = this.jobs.findIndex((period) => period.day === day.toISOString().slice(0, 10));
            this.jobs[periodIndex].jobs.push(job);
            this.batch = this.filters.get('batchId').value ?? null;

            if (job.progress.percentage < 100) {
              this.subscribeForUpdate(job.serverId);
            }
          });
        } else {
          this.jobs = [];
        }
        this.jobs._metadata = data._metadata;
        this.isLoading = false;
        this.text = 'Loaded';
      },
      (error: any) => {
        this.isLoading = false;
        this.jobs = [];
        this.alertService.alert(
          {
            type: 'error',
            description: $localize`:@@bma_common_getjobsfailure:Unable to get jobs.`,
          },
          true
        );
      }
    );
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  clearFilters(): void {
    this.filters.reset();
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
    });
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }

  isJSON(str): boolean {
    let isJson = false;
    try {
      isJson = typeof str === 'string' && typeof JSON.parse(str) === 'object';
    } catch (e) {}

    return isJson;
  }

  buildJsonFilter(): string {
    const jsonFilter = [];
    const filter = this.filters.get('filter').value;
    if (filter) {
      if (this.isJSON(filter)) {
        return filter;
      }
      const splitFilter = filter.split(/[\s,]+/);
      if (splitFilter.length <= 1 && isNaN(splitFilter[0])) {
        return splitFilter[0];
      }

      jsonFilter.push({ serverId: { $in: splitFilter } });
    }

    if (this.filters.get('batchId').value) {
      jsonFilter.push({ batchId: this.filters.get('batchId').value });
    }

    if (this.filters.get('customerId').value) {
      jsonFilter.push({ customerId: this.filters.get('customerId').value });
    }

    let createdFrom = this.filters.get('from').value ?? null;
    createdFrom = createdFrom ? new Date(createdFrom) : null;

    let createdTo = this.filters.get('to').value ?? null;
    createdTo = createdTo ? new Date(createdTo) : null;

    if (createdFrom && createdTo) {
      const from = new Date(Math.min(createdFrom, createdTo));
      const to = new Date(Math.max(createdFrom, createdTo));
      to.setDate(to.getDate() + 1);
      jsonFilter.push({
        $and: [
          { createdAt: { $gte: from.toISOString().slice(0, 10) } },
          { createdAt: { $lt: to.toISOString().slice(0, 10) } },
        ],
      });
    } else if (createdFrom) {
      jsonFilter.push({ createdAt: { $gte: createdFrom.toISOString().slice(0, 10) } });
    } else if (createdTo) {
      jsonFilter.push({ createdAt: { $lte: createdFrom.toISOString().slice(0, 10) } });
    }

    if (this.filters.get('operatingSystemId').value) {
      jsonFilter.push({ operatingSystemId: this.filters.get('operatingSystemId').value });
    }

    if (this.filters.get('site').value) {
      jsonFilter.push({ site: this.filters.get('site').value });
    }
    if (this.filters.get('status').value) {
      jsonFilter.push({ status: this.filters.get('status').value });
    }
    if (this.filters.get('type').value) {
      jsonFilter.push({ type: this.filters.get('type').value });
    }

    return jsonFilter.length > 0 ? JSON.stringify({ $and: jsonFilter }) : '';
  }

  openServerInfoPopupModal(serverId: string) {
    this.modalService.show(ServerInfoPopupModalComponent, { serverId });
  }

  toggleAllJobsSelection(): void {
    if (!this.selectAllJobs) {
      this.selectedJobs = [];

      return;
    }

    for (const jobs of this.jobs) {
      for (const job of jobs.jobs) {
        this.selectedJobs.push(job.uuid);
      }
    }
  }

  toggleJobSelection(event): void {
    if (event.target.checked === false) {
      const index = this.selectedJobs.indexOf(event.target.value);
      this.selectedJobs.splice(index, 1);
      return;
    }

    this.selectedJobs.push(event.target.value);
  }

  isJobSelected(jobUuid): boolean {
    if (this.selectedJobs.indexOf(jobUuid) > -1) {
      return true;
    }

    return false;
  }

  getJobStatusClass(status) {
    switch (status) {
      case 'ACTIVE':
        return 'btn-outline-primary';
      case 'FINISHED':
        return 'btn-outline-success';
      case 'CANCELED':
      case 'EXPIRED':
        return 'btn-outline-warning';
      case 'FAILED':
        return 'btn-outline-danger';
      default:
        return '';
    }
  }

  cancelJobs(): void {
    let remainingJobs = this.selectedJobs.length;
    for (const jobUuid of this.selectedJobs) {
      this.isCancelingJobs = true;
      this.httpClient.post<any>(`/_/internal/bmpapi/v2/jobs/${jobUuid}/cancel`, {}).subscribe({
        next: (data: any) => {
          this.alertService.alert(
            {
              type: 'success',
              description: $localize`:@@bma_joblist_canceljobsuccess:BMP-API job with UUID ${jobUuid} canceled.`,
            },
            true
          );
          remainingJobs -= 1;
          this.isCancelingJobs = false;
          if (remainingJobs === 0) {
            this.selectedJobs = [];
            this.router.navigate([], {
              queryParamsHandling: 'merge',
              relativeTo: this.activatedRoute,
              queryParams: this.sanitise(this.filters.getRawValue()),
            });
          }
        },
        error: (error: any) => {
          remainingJobs -= 1;
          this.isCancelingJobs = false;
          this.alertService.alert(
            {
              type: 'error',
              description: $localize`:@@bma_joblist_canceljobfailure:Could not cancel job with UUID ${jobUuid}.`,
            },
            true
          );
          if (remainingJobs === 0) {
            this.selectedJobs = [];
            this.router.navigate([], {
              queryParamsHandling: 'merge',
              relativeTo: this.activatedRoute,
              queryParams: this.sanitise(this.filters.getRawValue()),
            });
          }
        },
      });
    }
  }

  subscribeForUpdate(serverId) {
    this.notificationHubService.setTokenParams({ serverId });
    this.notificationHubService
      .getNotificationHubToken(this.mercureService.getTokenURL('bmpapi'))
      .pipe(first())
      .subscribe({
        next: () => {
          this.notificationHubService.sync<ServerEvent>(`${serverId}/jobs`).subscribe({
            next: (event) => {
              const jobIndex = this.findJobIndexByServerId(serverId);
              const jobDayIndex = this.findJobDayIndexByJobServerId(serverId);
              if (this.jobs[jobDayIndex].jobs[jobIndex].progress.percentage < 100) {
                this.jobs[jobDayIndex].jobs[jobIndex] = event.data;
                this.ref.detectChanges();
              }
            },
            error: (error) => console.log({ error }),
          });
        },
      });
  }

  findJobIndexByServerId(serverId: string): number {
    const index = this.findJobDayIndexByJobServerId(serverId);
    return this.jobs[index].jobs.findIndex((job) => job.serverId === serverId);
  }

  findJobDayIndexByJobServerId(serverId: string): number {
    return this.jobs.findIndex((day) => day.jobs.find((job) => job.uuid === serverId) !== null);
  }
}
