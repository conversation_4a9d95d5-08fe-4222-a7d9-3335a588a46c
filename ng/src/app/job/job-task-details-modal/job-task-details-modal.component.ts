import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Clipboard } from '@angular/cdk/clipboard';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { AlertService } from 'src/app/services/alert.service';

@Component({
  selector: 'app-job-task-details-modal',
  templateUrl: './job-task-details-modal.component.html',
  styleUrls: ['./job-task-details-modal.component.css'],
})
export class JobTaskDetailsModalComponent implements OnInit {
  uuid: any;
  serverId: any;
  task: any;
  isLoading = false;
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private clipboard: Clipboard,
    private alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.uuid = this.config.data.uuid;
    this.serverId = this.config.data.serverId;
    this.getTask();
  }

  getTask(): void {
    this.isLoading = true;
    this.httpClient.get<any>(`/_/internal/bmpapi/v2/tasks/${this.uuid}`).subscribe({
      next: (data: any) => {
        this.task = data;
        this.renderLinkToFirmwareUpdatesOnLogstor();
        this.isLoading = false;
      },
      error: (error: any) => {
        this.task = {};
        this.isLoading = false;
      },
    });
  }

  renderLinkToFirmwareUpdatesOnLogstor(): string {
    if (!this.task?.updatedAt) {
      return '';
    }
    const folder = this.task.updatedAt.slice(0, 7).replace(/-/g, '/');
    const filename = this.task.updatedAt.slice(0, 10).replace(/-/g, '.');
    const today = '0'.concat(new Date().getUTCDate().toString(10)).slice(-2);
    let url = `https://logstor.ams1.nl.leaseweb.net/lswasp/s${this.serverId}/${folder}/${filename}.log`;
    if (today !== this.task.updatedAt.slice(8, 10)) {
      url += '.bz2';
    }
    return url;
  }

  selectText(event): void {
    window.getSelection().selectAllChildren(event.target);
  }

  closeModal(): void {
    this.dynamicDialogRef.close();
  }

  doCopy(taskUuid: string): void {
    const pending = this.clipboard.beginCopy(taskUuid);
    let remainingAttempts = 3;
    const attempt = () => {
      const result = pending.copy();
      if (remainingAttempts === 0) {
        this.alertService.alert({
          type: 'error',
          description: $localize`:@@bma_common_errorcopyingtaskuuid:Sorry, there was an error copying task uuid.`,
        });
      } else if (!result && --remainingAttempts) {
        setTimeout(attempt);
      } else {
        pending.destroy();
      }
    };
    attempt();

    this.alertService.alert({
      type: 'success',
      description: $localize`:@@bma_common_taskuuidcopiedtoclipboard:Task uuid copied to clipboard`,
    });
  }
}
