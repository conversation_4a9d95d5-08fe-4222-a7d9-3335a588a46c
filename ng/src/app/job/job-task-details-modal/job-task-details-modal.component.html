<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '600px'}">
    <ng-template pTemplate="header">
        <h4 class="modal-title" id="task-modal-{{ uuid }}-label">
            <span i18n="@@bma_jobtaskdetailsmodal_header">View script</span>
            <span class="selectable ml-1" (dblclick)="selectText($event)">{{ uuid }}</span>
            <button pButton type="button" icon="pi pi-copy" class="p-button-sm ml-1" (click)="doCopy(uuid)"></button>
        </h4>
    </ng-template>
    <app-loader *ngIf="isLoading"></app-loader>
    <div class="modal-body" *ngIf="!isLoading && task">
        <p><a i18n="@@bma_common_tasklogs" [href]="renderLinkToFirmwareUpdatesOnLogstor(task)" target="_blank">View logs of this task at logstor.</a></p>
        <hr />
        <pre><span class="text-monospace"><code>{{ task?.script|default:'No script found' }}</code></span></pre>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_close" pButton type="button" icon="pi pi-times" iconPos="left" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
        </div>
    </ng-template>
</p-dialog>
