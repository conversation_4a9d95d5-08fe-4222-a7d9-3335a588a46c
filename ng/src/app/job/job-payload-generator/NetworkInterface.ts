interface NetworkInterface {
  mac: string;
  ip?: string;
  nullRouted?: boolean;
  gateway?: string;
  ports: { name: string; port: string }[];
  locationId?: string;
}

interface NetworkData {
  public: {
    ip: string;
    mac: string;
    cidr: string;
    prefix: number;
    netmask: string;
    netmask_hex: string;
    gateway: string;
    network: string;
    broadcast: string;
    nameservers: string[];
    isCustom: boolean;
  };
  internal: {
    mac: string;
  };
}

export class NetworkInterfaceClass {
  cidrToNetmask(cidr: number): string {
    // eslint-disable-next-line no-bitwise
    const mask = (0xffffffff << (32 - cidr)) >>> 0;
    // eslint-disable-next-line no-bitwise
    return [(mask >>> 24) & 0xff, (mask >>> 16) & 0xff, (mask >>> 8) & 0xff, mask & 0xff].join('.');
  }

  netmaskToHex(netmask: string): string {
    return (
      '0x' +
      netmask
        .split('.')
        .map((octet) => parseInt(octet, 10).toString(16).padStart(2, '0'))
        .join('')
    );
  }

  calculateNetworkAddress(ip: string, netmask: string): string {
    const ipParts = ip.split('.').map(Number);
    const maskParts = netmask.split('.').map(Number);
    // eslint-disable-next-line no-bitwise
    const networkParts = ipParts.map((part, index) => part & maskParts[index]);
    return networkParts.join('.');
  }

  calculateBroadcastAddress(ip: string, netmask: string): string {
    const ipParts = ip.split('.').map(Number);
    const maskParts = netmask.split('.').map(Number);
    // eslint-disable-next-line no-bitwise
    const broadcastParts = ipParts.map((part, index) => part | (~maskParts[index] & 0xff));
    return broadcastParts.join('.');
  }

  ipToCidr(ip: string, prefix: number): string {
    return `${ip}/${prefix}`;
  }

  extractPrefixFromCidr(cidrIp: string): number {
    const parts = cidrIp.split('/');
    return parseInt(parts[1], 10);
  }

  removeSubnet(ip: string): string {
    return ip.split('/')[0];
  }

  convertNetworkInterfacesToNetworkData(interfaces: {
    public: NetworkInterface;
    internal: NetworkInterface;
    remoteManagement: NetworkInterface;
  }): NetworkData {
    const publicInterface = interfaces.public;
    const ip = this.removeSubnet(publicInterface?.ip);
    const prefix = this.extractPrefixFromCidr(publicInterface?.ip);
    const netmask = this.cidrToNetmask(prefix);
    const network = this.calculateNetworkAddress(ip, netmask);
    const broadcast = this.calculateBroadcastAddress(ip, netmask);

    return {
      public: {
        ip,
        mac: publicInterface.mac,
        cidr: this.ipToCidr(ip, prefix),
        prefix,
        netmask,
        netmask_hex: this.netmaskToHex(netmask),
        gateway: publicInterface?.gateway,
        network,
        broadcast,
        nameservers: ['***********'],
        isCustom: false,
      },
      internal: {
        mac: interfaces.internal.mac,
      },
    };
  }
}
