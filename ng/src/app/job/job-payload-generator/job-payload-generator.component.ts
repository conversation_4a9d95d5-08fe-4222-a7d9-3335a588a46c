import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Title } from '@angular/platform-browser';
import { Clipboard } from '@angular/cdk/clipboard';
import { AlertService } from 'src/app/services/alert.service';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { NetworkInterfaceClass } from './NetworkInterface';

@Component({
  selector: 'app-job-payload-generator',
  templateUrl: './job-payload-generator.component.html',
  styleUrls: ['./job-payload-generator.component.css'],
})
export class JobPayloadGeneratorComponent implements OnInit {
  operatingSystems = [];
  payload = {};
  jsonPayload = '';
  isLoadingOperatingSystems = false;
  selectedOperatingSystem = null;
  localInstallChecked = false;
  testJobChecked = false;
  sshKeys = [];
  serverId = null;
  server = null;
  operatingSystemDefaults = null;
  isLoadingServerDetails = false;
  hostname = null;
  raidForm: FormGroup;

  constructor(
    private httpClient: HttpClient,
    private titleService: Title,
    private clipboard: Clipboard,
    private alertService: AlertService,
    private formBuilder: FormBuilder
  ) {}

  ngOnInit(): void {
    this.titleService.setTitle($localize`:@@bma_jobpayloadgenerator_title:Job Payload Generator` + ' | Leaseweb');
    this.getOperatingSystems();
    this.raidForm = new FormGroup({
      use: new FormControl(),
      diskSet: new FormControl(),
      raidLevel: new FormControl(),
      numberOfDisks: new FormControl(),
    });
  }

  onOsChange(event: { value: any }) {
    const selectedOs = event.value.id;
    this.getOperatingSystemDefaults(selectedOs);
  }

  onServerIdChange(): void {
    if (this.serverId === null || this.serverId.toString().length < 2) {
      return;
    }
    this.isLoadingServerDetails = true;
    this.httpClient.get(`/_/internal/dedicatedserverapi/v2/servers/${this.serverId}`, {}).subscribe({
      next: (data: any) => {
        this.isLoadingServerDetails = false;
        this.server = data;
        const networkData = new NetworkInterfaceClass().convertNetworkInterfacesToNetworkData(data.networkInterfaces);
        const serverInfo = {
          network: networkData,
          serverBrand: data.specs.brand,
          serverChassis: data.specs.chassis,
          serverHardwareRaid: data.specs.hardwareRaidCapable,
        };
        if (!('hostname' in this.payload)) {
          this.hostname = `s${this.serverId}.dedi.leaseweb.net`;
          this.onHostnameChange();
        }
        this.payload = Object.assign({}, this.payload, serverInfo);
        this.updateJsonPayload();
      },
      error: (error: any) => {
        this.isLoadingServerDetails = false;
        this.alertService.alert({
          type: 'error',
          description: $localize`:@@bma_common_servernotfound:Server not found.`,
        });
        this.clearServerDetails();
        console.error(error);
      },
    });
  }

  onPartitioningDataUpdated(event: any): void {
    if (event.payload.partitions) {
      this.payload = Object.assign({}, this.payload, {
        partitions: event.payload.partitions,
      });
    }
    if (event.payload.device) {
      this.payload = Object.assign({}, this.payload, {
        device: event.payload.device,
      });
    }
    if (event.payload.raid) {
      this.payload = Object.assign({}, this.payload, {
        raid: event.payload.raid,
      });
    } else if (!event.payload.raid && 'raid' in this.payload) {
      delete this.payload.raid;
    }
    this.updateJsonPayload();
  }

  onPartitioningSubmitted(event: any): void {}

  onHostnameChange() {
    if (this.hostname.length < 1 && 'hostname' in this.payload) {
      delete this.payload.hostname;
      this.updateJsonPayload();
      return;
    }
    this.payload = Object.assign({}, this.payload, {
      hostname: this.hostname,
    });
    this.updateJsonPayload();
  }

  onCheckboxTestJobChange(event: { checked: any }) {
    const checked = event.checked[0];
    if (checked === false) {
      this.payload = Object.assign({}, this.payload, {
        testJob: true,
      });
    } else {
      if ('testJob' in this.payload) {
        delete this.payload.testJob;
      }
    }
    this.updateJsonPayload();
  }

  onSshKeysChange(value) {
    if (value.trim().length < 1 && 'sshKeys' in this.payload) {
      delete this.payload.sshKeys;
      this.updateJsonPayload();
      return;
    }
    this.payload = Object.assign({}, this.payload, {
      sshKeys: value.trim(),
    });
    this.updateJsonPayload();
  }

  getOperatingSystems(): void {
    this.isLoadingOperatingSystems = true;
    this.httpClient.get(`/_/bareMetals/v2/operatingSystems`, {}).subscribe({
      next: (data: any) => {
        this.isLoadingOperatingSystems = false;
        if (data.operatingSystems) {
          this.operatingSystems = data.operatingSystems;
        }
      },
      error: (error: any) => {
        this.isLoadingOperatingSystems = false;
        this.operatingSystems = [];
      },
    });
  }

  getOperatingSystemDefaults(operatingSystemId: string): any {
    this.httpClient.get<any>(`/_/bareMetals/v2/operatingSystems/${operatingSystemId}`, {}).subscribe({
      next: (data: any) => {
        this.payload = Object.assign({}, this.payload, {
          os: {
            type: data.type,
            name: data.name,
            family: data.family,
            version: data.version,
            architecture: data.architecture,
          },
          device: data.defaults.device,
          partitions: data.defaults.partitions,
          operatingSystemId,
        });
        this.updateJsonPayload();
        this.operatingSystemDefaults = data;
        if (data.defaults) {
          this.operatingSystemDefaults.timezone = 'UTC';
          this.operatingSystemDefaults.powerCycle = true;
          this.operatingSystemDefaults.doEmailNotification = true;
          for (const osDefault in data.defaults) {
            if (data.defaults[osDefault]) {
              this.operatingSystemDefaults[osDefault] = data.defaults[osDefault];
            }
          }
          this.operatingSystemDefaults.filesystems = this.operatingSystemDefaults.supportedFileSystems;
        }
      },
      error: (error: any) => {
        console.error(error);
      },
    });
  }

  clearServerDetails(): void {
    this.serverId = null;
    if ('network' in this.payload) {
      delete this.payload.network;
    }
    if ('serverBrand' in this.payload) {
      delete this.payload.serverBrand;
    }
    if ('serverChassis' in this.payload) {
      delete this.payload.serverChassis;
    }
    if ('serverHardwareRaid' in this.payload) {
      delete this.payload.serverHardwareRaid;
    }
    this.updateJsonPayload();
  }

  clearHostname(): void {
    this.hostname = null;
    if ('hostname' in this.payload) {
      delete this.payload.hostname;
    }
    this.updateJsonPayload();
  }

  updateJsonPayload() {
    this.jsonPayload = JSON.stringify(this.payload, null, 2);
  }

  doCopy(): void {
    const pending = this.clipboard.beginCopy(this.jsonPayload);
    let remainingAttempts = 3;
    const attempt = () => {
      const result = pending.copy();
      if (remainingAttempts === 0) {
        this.alertService.alert({
          type: 'error',
          description: $localize`:@@bma_common_errorcopyingpayload:Sorry, there was an error copying payload.`,
        });
      } else if (!result && --remainingAttempts) {
        setTimeout(attempt);
      } else {
        pending.destroy();
      }
    };
    attempt();

    this.alertService.alert({
      type: 'success',
      description: $localize`:@@bma_common_payloadcopiedtoclipboard:Payload copied to clipboard`,
    });
  }
}
