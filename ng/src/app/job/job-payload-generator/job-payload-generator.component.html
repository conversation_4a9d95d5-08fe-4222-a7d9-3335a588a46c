<h1 i18n="@@bma_jobpayloadgenerator_header">BMP-API Job Payload Generator</h1>
<div class="row">
    <div class="col-6 card pt-3">
        <h3 class="mb-3">
            <i class="fa fa-administration mr-1"></i>
            <span i18n="@@bma_common_configuration">Configuration</span>
        </h3>
        <label i18n="@@bma_common_operatingSystem" for="operatingSystems" class="m-0">Operating system</label>
        <p-dropdown [options]="operatingSystems" [loading]="isLoadingOperatingSystems" [filter]="true" filterBy="name" inputId="operatingSystems" optionLabel="name" placeholder="Select a operating system" (onChange)="onOsChange($event)" class="mb-3" />
        <div class="flex flex-column gap-2 mb-3">
            <label i18n="@@bma_common_serverId" for="serverId" class="m-0">Server ID</label>
            <p-inputGroup>
                <p-inputNumber inputId="serverId" [useGrouping]="false" [(ngModel)]="serverId" (onBlur)="onServerIdChange()" [disabled]="isLoadingServerDetails" placeholder="123" />
                <button type="button" pButton icon="pi pi-times" [disabled]="isLoadingServerDetails" class="p-button-danger" (click)="clearServerDetails()"></button>
            </p-inputGroup>
        </div>
        <div class="flex flex-column gap-2 mb-3" *ngIf="server && operatingSystemDefaults">
            <app-server-install-wizard-partitioning [server]="server" [operatingSystemDefaults]="operatingSystemDefaults" (dataUpdated)="onPartitioningDataUpdated($event)" (submitted)="onPartitioningSubmitted($event)" [nextButtonEnabled]="false"></app-server-install-wizard-partitioning>
        </div>
        <div class="flex flex-column gap-2 mb-3">
            <label i18n="@@bma_common_hostname" for="hostname" class="m-0">Hostname</label>
            <p-inputGroup>
                <input type="text" pInputText placeholder="server.leaseweb.net" [(ngModel)]="hostname" (ngModelChange)="onHostnameChange()" [disabled]="isLoadingServerDetails" />
                <button type="button" pButton icon="pi pi-times" [disabled]="isLoadingServerDetails" class="p-button-danger" (click)="clearHostname()"></button>
            </p-inputGroup>
        </div>
        <div class="flex flex-column gap-2 mb-3">
            <p-checkbox name="testJob" [value]="testJobChecked" (onChange)="onCheckboxTestJobChange($event)" inputId="testJob" />
            <label i18n="@@bma_common_testjob" for="testJob" class="m-0">Test job</label>
        </div>
        <app-ssh-keys-selector [(content)]="sshKeys" (contentChange)="onSshKeysChange($event)"></app-ssh-keys-selector>
    </div>
    <div class="col-6 card pt-3">
        <div id="jobPayload">
            <h3 class="mb-3">
                <i class="fa fa-details mr-1"></i>
                <span i18n="@@bma_common_payload">Payload</span>
            </h3>

            <div class="card mb-5 mt-2">
                <div class="card-body">
                    <button pButton type="button" icon="pi pi-copy" class="p-button-sm float-right" (click)="doCopy()"></button>
                    <pre id="payload-json" [innerHtml]="jsonPayload"></pre>
                </div>
            </div>
        </div>
    </div>
</div>
