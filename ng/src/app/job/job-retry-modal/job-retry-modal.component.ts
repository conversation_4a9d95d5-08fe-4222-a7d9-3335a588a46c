import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { AlertService } from 'src/app/services/alert.service';
import { HttpClient } from '@angular/common/http';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-job-retry-modal',
  templateUrl: './job-retry-modal.component.html',
  styleUrls: ['./job-retry-modal.component.css'],
})
export class JobRetryModalComponent implements OnInit {
  @Input() uuid: any;
  @Input() serverId: any;
  @Input() oldPowerCycle: any;
  @Output() retryJobUuidEventEmitter = new EventEmitter<string>();
  form: UntypedFormGroup;
  isSubmitting = false;
  isEmployee = false;
  showDialog = true;

  constructor(
    private alertService: AlertService,
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private readonly formBuilder: UntypedFormBuilder
  ) {}

  ngOnInit(): void {
    this.uuid = this.config.data.uuid;
    this.oldPowerCycle = this.config.data.oldPowerCycle;
    this.serverId = this.config.data.serverId;
    this.isEmployee = this.currentUserService.isEmployee();
    this.form = this.formBuilder.group({
      powerCycle: [this.oldPowerCycle, Validators.required],
    });
  }

  retryJob(): void {
    this.isSubmitting = true;
    this.alertService.clear();

    let url;
    if (this.isEmployee) {
      url = `/_/internal/bmpapi/v2/jobs/${this.uuid}/retry`;
    } else {
      url = `/_/internal/bmpapi/v2/servers/${this.serverId}/jobs/${this.uuid}/retry`;
    }

    this.httpClient
      .post<any>(url, {
        powerCycle: this.form.get('powerCycle').value,
      })
      .subscribe({
        next: (data: any) => {
          this.alertService.alert(
            {
              type: 'success',
              description: $localize`:@@bma_jobretrymodal_success:Scheduled a copy of BMP-API job with UUID ${this.uuid}.`,
            },
            true
          );
          this.isSubmitting = false;
          this.closeModal({ jobRetried: true, newJobUuid: data.uuid });
        },
        error: (error: any) => {
          this.alertService.alert(
            {
              type: 'error',
              description: $localize`:@@bma_jobretrymodal_failure:Could not schedule a copy of BMP-API job with UUID ${this.uuid}.`,
            },
            true
          );
          this.isSubmitting = false;
          this.closeModal({ jobRetried: false });
        },
      });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
