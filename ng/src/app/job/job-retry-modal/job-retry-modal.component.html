<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '600px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_jobretrymodal_header" class="modal-title">Retry job</h3>
    </ng-template>

    <form [formGroup]="form" (ngSubmit)="retryJob()" class="form-horizontal">
        <div class="modal-body">
            <div id="action-retry-job-body-text">
                <p i18n="@@bma_jobretrymodal_confirmation">Are you sure you want to retry this job?</p>
                <p i18n="@@bma_jobretrymodal_confirmation_extra">This will create a new job with the same type and payload, and add it to the job queue.</p>
            </div>

            <div class="form-group">
                <div class="checkbox">
                    <input formControlName="powerCycle" type="checkbox" id="retryJobPowerCycle" autofocus />
                    <label i18n="@@bma_common_powercycleserver">Power cycle the dedicated server</label>
                </div>
            </div>
        </div>
    </form>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_no" pButton (click)="closeModal()" type="button" icon="pi pi-times" iconPos="left" label="No" class="p-button-secondary"></button>
            <button i18n-label="@@bma_common_yes" pButton (click)="retryJob()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" label="Yes"></button>
        </div>
    </ng-template>
</p-dialog>
