<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '800px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_powerbaroutletoperationmodal_header" class="modal-title">Power Outlet Operations</h3>
    </ng-template>

    <form [formGroup]="form" (ngSubmit)="submitOperation()">
        <div class="modal-body">
            <div class="row form-group mt-1 mb-2">
                <ng-container *ngIf="outlets.length > 1; else singleOutlet">
                    <label i18n="@@bma_common_outlet" class="col-2 col-form-label text-right" for="outlet">Outlet</label>
                    <select class="col-6 form-control" id="outlet" formControlName="outlet" autofocus>
                        <option i18n="@@bma_common_defaultoutlet" value="null" disabled>--- Select an outlet ---</option>
                        <option *ngFor="let outlet of outlets" value="{{ outlet }}">{{ outlet }}</option>
                    </select>
                </ng-container>
                <ng-template #singleOutlet>
                    <label i18n="@@bma_common_outlet" class="col-2 text-right" for="outlet">Outlet</label>
                    <span class="col-6">{{outlets[0]}}</span>
                    <input type="hidden" id="outlet" formControlName="outlet" value="{{outlets[0]}}" />
                </ng-template>
            </div>

            <div class="row form-group mt-1 mb-2">
                <ng-container *ngIf="operations.length > 1; else singleOperation">
                    <label i18n="@@bma_common_operation" class="col-2 col-form-label text-right" for="operation">Operation</label>
                    <select class="col-6 form-control" id="operation" formControlName="operation" autofocus>
                        <option i18n="@@bma_common_selectoperation" value="null" disabled>--- Select an operation ---</option>
                        <option *ngFor="let operation of operations" value="{{ operation.value }}">{{ operation.label }}</option>
                    </select>
                </ng-container>
                <ng-template #singleOperation>
                    <label i18n="@@bma_common_operation" class="col-2 text-right" for="operation">Operation</label>
                    <span class="col-6">{{operations[0].label}}</span>
                    <input type="hidden" id="operation" formControlName="operation" value="{{operations[0].value}}" />
                </ng-template>
            </div>
        </div>
    </form>

    <div class="alert alert-info">
        <span i18n="@@bma_powerbaroutletoperationmodal_description1">This action will affect any equipment connected to this power outlet.</span><br />
        <span i18n="@@bma_powerbaroutletoperationmodal_description2">Please make sure that you have double checked that the correct equipment is connected to this power outlet.</span>
    </div>

    <div *ngIf="form.errors?.api?.errorMessage" class="alert alert-danger" role="alert">
        <span>{{ form.errors.api.errorMessage }}</span>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_cancel" pButton (click)="closeModal()" label="Cancel" icon="pi pi-times" type="button" class="p-button-secondary"></button>
            <button i18n-label="@@bma_common_submit" pButton (click)="submitOperation()" label="Submit" icon="pi pi-check" [loading]="isSubmitting" [disabled]="form.invalid"></button>
        </div>
    </ng-template>
</p-dialog>
