import { Component, OnInit } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert.service';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-powerbar-outlet-operation-modal',
  templateUrl: './powerbar-outlet-operation-modal.component.html',
  styleUrls: ['./powerbar-outlet-operation-modal.component.css'],
})
export class PowerbarOutletOperationModalComponent implements OnInit {
  name: any;
  outlets = Array.from(Array(36), (e, i) => i + 1); // 1 to 36
  operations = [
    { value: 'poweron', label: 'Power On' },
    { value: 'poweroff', label: 'Power Off' },
    { value: 'powercycle', label: 'Power Cycle' },
  ];
  isSubmitting = false;
  form: UntypedFormGroup;
  showDialog = true;

  constructor(
    private alertService: AlertService,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private httpClient: HttpClient,
    private readonly formBuilder: UntypedFormBuilder
  ) {}

  ngOnInit(): void {
    const data = this.config.data;

    this.name = data.name;

    let defaultOutlet = null;
    let defaultOperation = null;

    if (data.hasOwnProperty('outlet')) {
      this.outlets = [data.outlet];
      defaultOutlet = data.outlet;
    }

    if (data.hasOwnProperty('operation')) {
      this.operations = [this.operations.find((op) => op.value === data.operation)];
      defaultOperation = data.operation;
    }

    this.form = this.formBuilder.group({
      outlet: [defaultOutlet, Validators.required],
      operation: [defaultOperation, Validators.required],
    });
  }

  submitOperation() {
    if (this.form.invalid) {
      return;
    }

    this.isSubmitting = true;

    const outlet = this.form.get('outlet').value;
    const operation = this.form.get('operation').value;

    this.httpClient.post(`/_/internal/bmpapi/v1/powerbars/${this.name}/outlets/${outlet}/${operation}`, {}).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;
        this.closeModal();
        this.alertService.alert(
          {
            type: 'success',
            description:
              $localize`:@@bma_powerbaroutletoperationmodal_added:Your ${operation} action on outlet ${outlet} for PDU ${this.name} was successfully added to the queue. It will be executed as soon as possible` +
              '.',
          },
          true
        );
      },
      error: (error) => {
        this.isSubmitting = false;
        this.form.setErrors({ api: error.error });
      },
    });
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }
}
