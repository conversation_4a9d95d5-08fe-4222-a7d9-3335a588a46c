<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '800px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_powerbarexporttocsvmodal_header" class="modal-title">Fields to Export Powerbars to CSV</h3>
    </ng-template>
    <form [formGroup]="form" (ngSubmit)="openCsv()">
        <div class="modal-body row">
            <div class="form-group col-6">
                <div class="checkbox">
                    <input type="checkbox" id="name" formControlName="name" value="true" checked="checked" />
                    <label i18n="@@bma_common_name" for="name" class="ml-1">Name</label>
                </div>
                <div class="checkbox">
                    <input type="checkbox" id="ip" formControlName="ip" value="true" checked="checked" />
                    <label i18n="@@bma_common_ip" for="ip" class="ml-1">IP</label>
                </div>
                <div class="checkbox">
                    <input type="checkbox" id="site" formControlName="site" value="true" checked="checked" />
                    <label i18n="@@bma_common_site" for="site" class="ml-1">Site</label>
                </div>
                <div class="checkbox">
                    <input type="checkbox" id="type" formControlName="type" value="true" checked="checked" />
                    <label i18n="@@bma_common_type" for="type" class="ml-1">Type</label>
                </div>
                <div class="checkbox">
                    <input type="checkbox" id="brand" formControlName="brand" value="true" checked="checked" />
                    <label i18n="@@bma_common_brand" for="brand" class="ml-1">Brand</label>
                </div>
            </div>
            <div class="form-group col-6">
                <div class="checkbox">
                    <input type="checkbox" id="model" formControlName="model" value="true" checked="checked" />
                    <label i18n="@@bma_common_model" for="model" class="ml-1">Model</label>
                </div>
                <div class="checkbox">
                    <input type="checkbox" id="af1" formControlName="af1" value="true" checked="checked" />
                    <label i18n="@@bma_common_af1version" for="af1" class="ml-1">AF1 version</label>
                </div>
                <div class="checkbox">
                    <input type="checkbox" id="pf" formControlName="pf" value="true" checked="checked" />
                    <label i18n="@@bma_common_pfversion" for="pf" class="ml-1">PF version</label>
                </div>
                <div class="checkbox">
                    <input type="checkbox" id="mb" formControlName="mb" value="true" checked="checked" />
                    <label i18n="@@bma_common_mbversion" for="mb" class="ml-1">MB version</label>
                </div>
                <div class="checkbox">
                    <input type="checkbox" id="serial" formControlName="serial" value="true" checked="checked" />
                    <label i18n="@@bma_common_serial" for="serial" class="ml-1">Serial</label>
                </div>
            </div>
        </div>
    </form>

    <div *ngIf="form.errors?.api?.errorMessage" class="alert alert-danger" role="alert">
        <span>{{ form.errors.api.errorMessage }}</span>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_cancel" pButton (click)="closeModal()" label="Cancel" icon="pi pi-times" type="button" class="p-button-secondary"></button>
            <button i18n-label="@@bma_common_export" pButton (click)="openCsv()" icon="pi pi-check" [loading]="isSubmitting" [disabled]="form.invalid" label="Export"></button>
        </div>
    </ng-template>
</p-dialog>
