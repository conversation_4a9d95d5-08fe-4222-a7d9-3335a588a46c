import { Component, OnInit } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { AlertService } from 'src/app/services/alert.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-powerbar-export-to-csv-modal',
  templateUrl: './powerbar-export-to-csv-modal.component.html',
  styleUrls: ['./powerbar-export-to-csv-modal.component.css'],
})
export class PowerbarExportToCsvModalComponent implements OnInit {
  filters: any;
  sites: any;
  form: UntypedFormGroup;
  isSubmitting = false;
  isEquipmentLoading = false;
  isEquipmentInSap: boolean;
  showDialog = true;
  blobCsv = null;

  constructor(
    private activatedRoute: ActivatedRoute,
    private alertService: AlertService,
    private httpClient: HttpClient,
    private dynamicDialogRef: DynamicDialogRef,
    private readonly formBuilder: UntypedFormBuilder
  ) {}

  ngOnInit(): void {
    this.form = this.formBuilder.group({
      name: true,
      ip: true,
      site: true,
      type: true,
      brand: true,
      model: true,
      af1: true,
      pf: true,
      mb: true,
      serial: true,
    });
    this.activatedRoute.queryParams.subscribe((parameters) => (this.filters = parameters));
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }

  openCsv() {
    if (!this.blobCsv) {
      this.loadCsv();
    } else {
      const fileLink = document.createElement('a');
      fileLink.href = this.blobCsv;
      fileLink.download = 'powerbars.csv';
      fileLink.click();
    }
  }

  loadCsv(): void {
    this.isSubmitting = true;
    let params = new HttpParams();
    params = params.set('fields', this.optionsToString(this.form.getRawValue()));

    if (this.filters) {
      const keys = Object.keys(this.filters);
      keys.forEach((key) => {
        if (key === 'limit' || key === 'offset') {
          return;
        }
        params = params.set(key, this.filters[key as keyof typeof this.filters]);
      });
    }

    this.httpClient
      .get(`/_/internal/bmpapi/v1/powerbars/exportToCsv`, {
        responseType: 'arraybuffer',
        params,
      })
      .subscribe({
        next: (data: any) => {
          const blob = new Blob([data], { type: 'application/csv' });
          this.blobCsv = URL.createObjectURL(blob);
          this.openCsv();
          this.alertService.alert(
            {
              type: 'success',
              description: $localize`:@@bma_common_csvdownloadstarted:CSV download started`,
            },
            true
          );
          this.closeModal();
          this.isSubmitting = false;
        },
        error: (error: any) => {
          this.blobCsv = null;
          this.alertService.alert(
            {
              type: 'error',
              description: $localize`:@@bma_common_errordownloadingcsv:Error downloading CSV`,
            },
            true
          );
          this.closeModal();
          this.isSubmitting = false;
        },
      });
  }

  optionsToString(obj: { [key: string]: boolean }): string {
    return Object.keys(obj)
      .filter((key) => obj[key])
      .join(',');
  }
}
