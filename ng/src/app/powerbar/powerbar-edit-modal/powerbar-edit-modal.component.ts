import { Component, OnInit } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { SiteService } from 'src/app/services/site.service';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-powerbar-edit-modal',
  templateUrl: './powerbar-edit-modal.component.html',
  styleUrls: ['./powerbar-edit-modal.component.css'],
})
export class PowerbarEditModalComponent implements OnInit {
  powerbar: any;
  sites: any;
  form: UntypedFormGroup;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private siteService: SiteService,
    private readonly formBuilder: UntypedFormBuilder,
    private router: Router
  ) {}

  ngOnInit(): void {
    const data = this.config.data;
    this.powerbar = data.powerbar;
    this.sites = this.siteService.sites();

    this.form = this.formBuilder.group({
      name: [this.powerbar.name, Validators.required],
      ip: [this.powerbar.ip, Validators.required],
      type: [this.powerbar.type, Validators.required],
      brand: [this.powerbar.brand, Validators.required],
      community: [this.powerbar.community, Validators.required],
      equipmentId: [this.powerbar.equipmentId, Validators.required],
      site: [this.powerbar.site, Validators.required],
    });
  }

  onPowerbarSubmit() {
    if (this.form.invalid) {
      return;
    }

    this.isSubmitting = true;
    const body = this.form.getRawValue();

    this.httpClient.put(`/_/internal/bmpapi/v1/powerbars/${this.powerbar.name}`, body).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;
        if (data.name) {
          this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
            this.router.navigate([`powerbars/${data.name}`]);
          });
        }
        this.closeModal();
      },
      error: (error) => {
        this.isSubmitting = false;
        this.form.setErrors({ api: error.error });
        Object.entries(error.error.errorDetails || {}).forEach(([key, value]) => {
          this.form.get(key).setErrors({ api: value[0] });
        });
      },
    });
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }
}
