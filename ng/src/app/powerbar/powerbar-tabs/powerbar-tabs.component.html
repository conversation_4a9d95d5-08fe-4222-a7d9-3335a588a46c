<app-customer-aware-header i18n="@@bma_powerbartabs_header" *ngIf="powerbar" caption="Powerbar {{powerbar.name}}" isEmployee="true"></app-customer-aware-header>

<app-loader *ngIf="isLoading"></app-loader>

<ul class="nav nav-pills mb-4" *ngIf="!isLoading && powerbar">
    <li class="nav-item">
        <a [routerLink]="['/powerbars', powerbar?.name]" class="nav-link">
            <span class="text-muted fa fa-details mr-2"></span>
            <span i18n="@@bma_common_tabdetails">Details</span>
        </a>
    </li>
    <li class="nav-item dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-details mr-2"></span>
            <span i18n="@@bma_common_activities">Activities</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a *ngIf="powerbar" [href]="powerbar._links.logstorUrl" class="dropdown-item">
                <span class="text-muted fa fa-search mr-2"></span>
                <span i18n="@@bma_common_syslog">Syslog</span>
            </a>
            <a *ngIf="powerbar" [href]="powerbar._links.icingaUrl" class="dropdown-item">
                <span class="text-muted fa fa-search mr-2"></span>
                <span i18n="@@bma_common_icingamonitoring">Icinga Monitoring</span>
            </a>
            <a [routerLink]="['/audit-logs']" [queryParams]="{filter: powerbar?.name}" class="dropdown-item">
                <span class="text-muted fa fa-search mr-2"></span>
                <span i18n="@@bma_common_tabauditlogs">Audit Logs</span>
            </a>
        </div>
    </li>
    <li class="nav-item navbar-right dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-actions mr-2"></span>
            <span i18n="@@bma_common_actions">Actions</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a href="#" class="dropdown-item" (click)="openPowerbarEditModal(); false">
                <span class="text-muted fa fa-change mr-2"></span>
                <span i18n="@@bma_powerbartabs_editpowerbar">Edit Powerbar</span>
            </a>
            <a [routerLink]="['generate-config']" class="dropdown-item">
                <span class="text-muted fa fa-administration mr-2"></span>
                <span i18n="@@bma_common_generateconfiguration">Generate Configuration</span>
            </a>
            <a href="#" class="dropdown-item" (click)="openPowerbarOutletOperationModal(); false">
                <span class="text-muted fa fa-powercycle mr-2"></span>
                <span i18n="@@bma_common_performoutletoperation">Perform Power Outlet Operation</span>
            </a>
            <a href="#" class="dropdown-item" (click)="openPowerbarRemoveModal(); false">
                <span class="text-muted fa fa-delete mr-2"></span>
                <span i18n="@@bma_powerbartabs_removepowerbar">Remove Powerbar</span>
            </a>
        </div>
    </li>
    <li class="nav-item navbar-right dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-knowledge mr-2"></span>
            <span i18n="@@bma_common_help">Help</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a href="https://wiki.ocom.com/display/KC/Add+a+PDU+or+ATS+to+automation" class="dropdown-item">
                <span class="text-muted fa fa-knowledge mr-2"></span>
                <span i18n="@@bma_powerbartabs_addpowerbarautomation">How to add a Powerbar to automation</span>
            </a>
            <a href="https://wiki.ocom.com/display/KB/Adding+a+PDU+or+Switch+to+CAS+and+EMP" class="dropdown-item">
                <span class="text-muted fa fa-knowledge mr-2"></span>
                <span i18n="@@bma_common_pduswitchaddition">How to add a Powerbar or Switch to SAP and EMP</span>
            </a>
            <a href="https://wiki.ocom.com/display/PROV/Global+name+structure" class="dropdown-item">
                <span class="text-muted fa fa-knowledge mr-2"></span>
                <span i18n="@@bma_common_globalnamestructure">Global name structure</span>
            </a>
            <a href="https://grafana.leaseweb.net/d/scphHGjWz/power-pdu-overview" class="dropdown-item">
                <span class="text-muted fa fa-knowledge mr-2"></span>
                <span i18n="@@bma_common_powermonitoring">Power Monitoring</span>
            </a>
        </div>
    </li>
</ul>

<ng-container *ngIf="powerbar">
    <router-outlet (activate)="onPowerbarLoaded($event)"></router-outlet>
</ng-container>
