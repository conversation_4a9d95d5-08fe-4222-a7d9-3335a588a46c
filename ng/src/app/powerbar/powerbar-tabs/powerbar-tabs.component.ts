import { ActivatedRoute, Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Title } from '@angular/platform-browser';
import { ModalService } from 'src/app/services/modal.service';
import { PowerbarEditModalComponent } from '../powerbar-edit-modal/powerbar-edit-modal.component';
import { PowerbarRemoveModalComponent } from '../powerbar-remove-modal/powerbar-remove-modal.component';
import { PowerbarOutletOperationModalComponent } from '../powerbar-outlet-operation-modal/powerbar-outlet-operation-modal.component';

@Component({
  selector: 'app-powerbar-tabs',
  templateUrl: './powerbar-tabs.component.html',
  styleUrls: ['./powerbar-tabs.component.css'],
})
export class PowerbarTabsComponent implements OnInit {
  isLoading = false;
  powerbar: any;

  constructor(
    private httpClient: HttpClient,
    private activatedRoute: ActivatedRoute,
    private modalService: ModalService,
    private titleService: Title,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.activatedRoute.url.subscribe((parameters) => this.onUrlParamsChanged(parameters));
  }

  onUrlParamsChanged(urlParams): void {
    this.isLoading = true;
    this.powerbar = null;
    this.httpClient.get<any>(`/_/internal/bmpapi/v1/powerbars/${urlParams[1].path}`).subscribe(
      (powerbar) => {
        this.isLoading = false;
        this.powerbar = powerbar;
        this.titleService.setTitle($localize`:@@bma_powerbartabs_title:Powerbar ${this.powerbar.name}` + ' | Leaseweb');
      },
      (error) => {
        this.isLoading = false;
        this.router.navigate(['**']);
      }
    );
  }

  openPowerbarRemoveModal() {
    const dynamicDialogRef = this.modalService.show(PowerbarRemoveModalComponent, {
      powerbar: this.powerbar,
    });

    dynamicDialogRef.onClose.subscribe((status) => {
      if (status?.powerbarRemoved === true) {
        this.router.navigate(['powerbars']);
      }
    });
  }

  openPowerbarEditModal() {
    this.modalService.show(PowerbarEditModalComponent, {
      powerbar: this.powerbar,
    });
  }

  openPowerbarOutletOperationModal() {
    this.modalService.show(PowerbarOutletOperationModalComponent, {
      name: this.powerbar.name,
    });
  }

  onPowerbarLoaded(component) {
    if (this.powerbar) {
      component.powerbar = this.powerbar;
    }
  }
}
