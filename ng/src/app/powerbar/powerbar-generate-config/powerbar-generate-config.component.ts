import { Component, OnInit, Input } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-powerbar-generate-config',
  templateUrl: './powerbar-generate-config.component.html',
  styleUrls: ['./powerbar-generate-config.component.css'],
})
export class PowerbarGenerateConfigComponent implements OnInit {
  @Input() powerbar: any;
  isGenerating = false;
  form: UntypedFormGroup;
  generatedConfig = '';

  constructor(
    private httpClient: HttpClient,
    private readonly formBuilder: UntypedFormBuilder
  ) {}

  ngOnInit(): void {
    this.form = this.formBuilder.group({
      model: [
        this.powerbar.type === 'ATS' ? (this.powerbar.brand === 'APC' ? 'AP44XX' : 'other') : 'AP89XX',
        Validators.required,
      ],
    });
  }

  onGenerateConfig() {
    if (!this.form.valid) {
      return;
    }

    this.generatedConfig = $localize`:@@bma_common_loading:Loading...`;
    this.isGenerating = true;

    this.httpClient
      .post(
        `/_/internal/bmpapi/v1/powerbars/${this.powerbar.name}/generateConfig`,
        { model: this.form.get('model').value },
        { responseType: 'text' }
      )
      .subscribe(
        (data) => {
          this.generatedConfig = data;
          this.isGenerating = false;
        },
        (error) => {
          this.generatedConfig = error.errorMessage;
          this.isGenerating = false;
        }
      );
  }
}
