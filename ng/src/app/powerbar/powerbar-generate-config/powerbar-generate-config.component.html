<div class="row">
    <div class="col">
        <h3 i18n="@@bma_powerbargenerateconfig_header" class="mb-3">Generate Powerbar Configuration</h3>
    </div>
</div>

<div class="row">
    <div class="col">
        <p i18n="@@bma_powerbargenerateconfig_description">This will not apply the configuration on the powerbar.</p>
    </div>
</div>

<div class="row">
    <div class="col">
        <form [formGroup]="form" (ngSubmit)="onGenerateConfig()">
            <div class="form-group">
                <select i18n-label="@@bma_powerbargenerateconfig_atsmodel" *ngIf="powerbar.type === 'ATS'" class="form-control" formControlName="model" label="ATS Model" autofocus>
                    <ng-container *ngIf="powerbar.brand === 'APC'">
                        <option i18n="@@bma_powerbargenerateconfig_apcap44xxseries" value="AP44XX">APC AP44XX series</option>
                        <option i18n="@@bma_powerbargenerateconfig_apcap77xxseries" value="AP77XX">APC AP77XX series</option>
                    </ng-container>
                    <option i18n="@@bma_powerbargenerateconfig_tripplitegeneric" *ngIf="powerbar.brand === 'TRIPPLITE'" selected="selected" value="other">Tripplite generic</option>
                </select>
                <select i18n-label="@@bma_powerbargenerateconfig_pdumodel" *ngIf="powerbar.type === 'PDU'" class="form-control" formControlName="model" label="PDU Model">
                    <option i18n="@@bma_powerbargenerateconfig_apcap78xxseries" value="AP78XX">APC AP78XX series</option>
                    <option i18n="@@bma_powerbargenerateconfig_apcap89xxseries" value="AP89XX">APC AP89XX series</option>
                    <option i18n="@@bma_powerbargenerateconfig_apcap99xxseries" value="AP99XX">APC AP99XX series</option>
                </select>
            </div>
            <div class="form-group mb-3">
                <button i18n-label="@@bma_common_generate" pButton [loading]="isGenerating" label="Generate"></button>
            </div>
        </form>
    </div>
</div>

<div *ngIf="generatedConfig" class="row">
    <div class="col-12">
        <hr />
        <textarea [(ngModel)]="generatedConfig" [rows]="generatedConfig.split('\n').length + 5" readonly></textarea>
    </div>
</div>
