import { AlertService } from 'src/app/services/alert.service';
import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-powerbar-remove-modal',
  templateUrl: './powerbar-remove-modal.component.html',
  styleUrls: ['./powerbar-remove-modal.component.css'],
})
export class PowerbarRemoveModalComponent implements OnInit {
  powerbar: any;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    private dynamicDialogRef: DynamicDialogRef,
    public config: DynamicDialogConfig,
    private alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.powerbar = this.config.data.powerbar;
  }

  onPowerbarRemove() {
    this.isSubmitting = true;
    this.alertService.clear();
    this.httpClient.delete(`/_/internal/bmpapi/v1/powerbars/${this.powerbar.name}`, {}).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;
        this.closeModal({ powerbarRemoved: true });
        this.alertService.alert(
          {
            type: 'success',
            description: `Successfully removed PDU ${this.powerbar.name}`,
          },
          true
        );
      },
      error: (error: any) => {
        this.isSubmitting = false;
        this.alertService.alertApiError(error.error, true);
        this.closeModal({ powerbarRemoved: false });
      },
    });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
