.pdu-table .pdu-attribute:before {
  display: inline-block;
  font-size: 0.8em;
  vertical-align: middle;
  color: #aaa;
}

.pdu-attribute-model:before {
  content: 'model:';
}

.pdu-attribute-af1:before {
  content: 'af1:';
}

.pdu-attribute-pf:before {
  content: 'pf:';
}

.pdu-attribute-mb:before {
  content: 'mb:';
}

:host ::ng-deep .p-accordion .p-accordion-content {
  padding: 0.5rem;
}

.grid-content {
  margin-bottom: 5px;
  padding-bottom: 4px;
  padding-left: 2px;
  font-size: 0.8rem !important;
}

.grid-content-border {
  border-bottom: 1px solid #ddd;
}

.grid-row {
  padding-right: 10px;
}

.show-data {
  cursor: pointer;
}

.quick-actions a,
.quick-actions button {
  padding-top: 4px;
  padding-bottom: 4px;
  font-size: 0.9rem;
  text-align: left;
}

.filter-field {
  height: auto !important;
  line-height: 1.975 !important;
  border: 1px solid #ced4da !important;
  border-radius: 3px !important;
}

.align-middle {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
