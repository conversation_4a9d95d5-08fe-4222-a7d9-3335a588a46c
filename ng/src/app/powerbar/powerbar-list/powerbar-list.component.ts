import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { SiteService } from 'src/app/services/site.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { ModalService } from 'src/app/services/modal.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { PowerbarAddModalComponent } from '../powerbar-add-modal/powerbar-add-modal.component';
import { PowerbarExportToCsvModalComponent } from '../powerbar-export-to-csv-modal/powerbar-export-to-csv-modal.component';

@Component({
  selector: 'app-powerbar-list',
  templateUrl: './powerbar-list.component.html',
  styleUrls: ['./powerbar-list.component.css'],
})
export class PowerbarListComponent implements OnInit {
  isLoading = false;
  error = false;
  powerbars: any;
  metadataAf1Versions: any;
  metadataMbVersions: any;
  metadataModels: any;
  metadataPfVersions: any;
  selectedSites = [];
  selectedMetadataAf1Versions = [];
  selectedMetadataMbVersions = [];
  selectedMetadataModels = [];
  selectedMetadataPfVersions = [];
  sites: any[] = [];
  type: string[];
  brand: string[];
  isEmployee = false;

  filters: UntypedFormGroup;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private modalService: ModalService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private siteService: SiteService,
    private titleService: Title,
    private currentUserService: CurrentUserService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.filters = this.formBuilder.group({
      offset: [0],
      limit: [10],
      filter: [null],
      site: [null],
      type: [null],
      brand: [null],
      networkType: [null],
      metadataModel: [null],
      metadataAf1Version: [null],
      metadataPfVersion: [null],
      metadataMbVersion: [null],
    });

    this.titleService.setTitle($localize`:@@bma_powerbarlist_title:Powerbars` + ' | Leaseweb');
    this.siteService.sites().forEach((item) => {
      this.sites.push({ site: item });
    });
    this.type = ['PDU', 'ATS'];
    this.brand = ['APC', 'TRIPPLITE'];
    this.httpClient.get<any>('/_/internal/bmpapi/v1/powerbarModels?limit=2000').subscribe((data: any) => {
      this.metadataModels = data.powerbarModels;
    });
    this.httpClient.get<any>('/_/internal/bmpapi/v1/powerbarAf1Versions?limit=2000').subscribe((data: any) => {
      this.metadataAf1Versions = data.powerbarAf1Versions;
    });
    this.httpClient.get<any>('/_/internal/bmpapi/v1/powerbarPfVersions?limit=2000').subscribe((data: any) => {
      this.metadataPfVersions = data.powerbarPfVersions;
    });
    this.httpClient.get<any>('/_/internal/bmpapi/v1/powerbarMbVersions?limit=2000').subscribe((data: any) => {
      this.metadataMbVersions = data.powerbarMbVersions;
    });

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    this.isLoading = true;
    this.filters.reset(queryParams);
    const params = this.sanitise(this.filters.getRawValue());
    if (params.site) {
      this.selectedSites = params.site.split(',');
    }
    if (params.metadataModel) {
      this.selectedMetadataModels = params.metadataModel.split(',');
    }
    if (params.metadataAf1Version) {
      this.selectedMetadataAf1Versions = params.metadataAf1Version.split(',');
    }
    if (params.metadataPfVersion) {
      this.selectedMetadataPfVersions = params.metadataPfVersion.split(',');
    }
    if (params.metadataMbVersion) {
      this.selectedMetadataMbVersions = params.metadataMbVersion.split(',');
    }
    this.httpClient.get<any>('/_/internal/bmpapi/v1/powerbars', { params }).subscribe(
      (data: any) => {
        this.isLoading = false;
        this.error = false;
        this.powerbars = data;
      },
      (error) => {
        this.isLoading = false;
        this.error = true;
        this.powerbars = { _metadata: { totalCount: 0 }, powerbars: [] };
      }
    );
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.serializeFilters(),
    });
  }

  serializeFilters(): any {
    const queryParam = this.sanitise(this.filters.getRawValue());
    if (queryParam.site) {
      queryParam.site = queryParam.site.join(',');
    }
    if (queryParam.metadataModel) {
      queryParam.metadataModel = queryParam.metadataModel.join(',');
    }
    if (queryParam.metadataAf1Version) {
      queryParam.metadataAf1Version = queryParam.metadataAf1Version.join(',');
    }
    if (queryParam.metadataPfVersion) {
      queryParam.metadataPfVersion = queryParam.metadataPfVersion.join(',');
    }
    if (queryParam.metadataMbVersion) {
      queryParam.metadataMbVersion = queryParam.metadataMbVersion.join(',');
    }
    return queryParam;
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  clearFilters(): void {
    this.filters.reset();
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
    });
  }

  getMetadataByKey(powerbarAttributes: any[], key: string): string {
    if (!powerbarAttributes || powerbarAttributes.length === 0) {
      return '-';
    }

    const metadata = powerbarAttributes.find((value) => value.key === key);

    return metadata ? metadata.value : '-';
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (!val || val === 'null') {
        return acc;
      }
      if (Array.isArray(val) && val.length === 0) {
        return acc;
      }
      acc[key] = val;
      return acc;
    }, {});
  }

  openPowerbarAddModal() {
    this.modalService.show(PowerbarAddModalComponent, {});
  }

  openPowebarExportToCsvModal() {
    this.modalService.show(PowerbarExportToCsvModalComponent, {});
  }
}
