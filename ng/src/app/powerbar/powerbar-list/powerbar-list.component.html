<div class="row mb-4">
    <div class="col">
        <div class="float-right">
            <button *ngIf="isEmployee" pButton (click)="openPowebarExportToCsvModal(); false" class="p-button p-button-link" i18n="@@bma_commom_exportresultsascsv">Export results as CSV</button>
            <button pButton (click)="openPowerbarAddModal()" type="button" label="Add Powerbar" icon="pi pi-plus" class="p-button-success"></button>
        </div>
        <h1 i18n="@@bma_powerbarlist_header">Powerbars</h1>
    </div>
</div>

<form role="form" [formGroup]="filters" (ngSubmit)="submitFilters()">
    <fieldset>
        <div class="row mb-3">
            <div class="col">
                <input i18n-placeholder="@@bma_common_filter" type="text" id="filter" formControlName="filter" class="form-control filter-field" value="" placeholder="Filter..." autofocus />
            </div>
            <div class="col">
                <p-multiSelect i18n-label="@@bma_common_site" formControlName="site" [(ngModel)]="selectedSites" [options]="sites" optionLabel="site" optionValue="site" [maxSelectedLabels]="1" defaultLabel="Site" [selectedItemsLabel]="'{0} sites'"></p-multiSelect>
            </div>

            <div class="col">
                <p-dropdown i18n-placeholder="@@bma_common_type" id="type" formControlName="type" [options]="type" [showClear]="true" placeholder="Type"></p-dropdown>
            </div>

            <div class="col">
                <p-dropdown i18n-placeholder="@@bma_common_brand" id="brand" formControlName="brand" [options]="brand" [showClear]="true" placeholder="Brand"></p-dropdown>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col">
                <p-multiSelect i18n-label="@@bma_common_model" formControlName="metadataModel" [(ngModel)]="selectedMetadataModels" [options]="metadataModels" optionLabel="name" optionValue="name" [maxSelectedLabels]="1" defaultLabel="Model" [selectedItemsLabel]="'{0} models'"></p-multiSelect>
            </div>

            <div class="col">
                <p-multiSelect i18n-label="@@bma_common_af1version" formControlName="metadataAf1Version" [(ngModel)]="selectedMetadataAf1Versions" [options]="metadataAf1Versions" optionLabel="name" optionValue="name" [maxSelectedLabels]="1" defaultLabel="Af1 Version" [selectedItemsLabel]="'{0} Af1 Versions'"></p-multiSelect>
            </div>

            <div class="col">
                <p-multiSelect i18n-label="@@bma_common_pfversion" formControlName="metadataPfVersion" [(ngModel)]="selectedMetadataPfVersions" [options]="metadataPfVersions" optionLabel="name" optionValue="name" [maxSelectedLabels]="1" defaultLabel="Pf Version" [selectedItemsLabel]="'{0} Pf Versions'"></p-multiSelect>
            </div>

            <div class="col">
                <p-multiSelect i18n-label="@@bma_common_mbversion" formControlName="metadataMbVersion" [(ngModel)]="selectedMetadataMbVersions" [options]="metadataMbVersions" optionLabel="name" optionValue="name" [maxSelectedLabels]="1" defaultLabel="Mb Version" [selectedItemsLabel]="'{0} Mb Versions'"></p-multiSelect>
            </div>
        </div>

        <div class="row mb-3 mt-3">
            <div class="col"></div>
            <div class="col text-right">
                <button i18n-label="@@bma_common_reset" pButton type="button" (click)="clearFilters()" label="Reset" icon="pi pi-refresh" class="p-button-secondary mr-2"></button>
                <button i18n-label="@@bma_common_search" pButton [disabled]="isLoading" label="Search" icon="pi pi-search"></button>
            </div>
        </div>
    </fieldset>
</form>

<app-loader *ngIf="isLoading"></app-loader>

<div *ngIf="!isLoading && powerbars">
    <p-accordion [multiple]="true" *ngIf="powerbars.powerbars.length > 0 && !error; else noData">
        <p-accordionTab *ngFor="let powerbar of powerbars.powerbars">
            <ng-template pTemplate="header">
                <div class="row col-sm-12 align-items-center">
                    <div class="col-sm-2">
                        <h3 class="mt-2">{{ powerbar.site }}</h3>
                    </div>
                    <div class="col-sm-4">
                        <span class="h5">
                            {{ powerbar.name }}
                        </span>
                    </div>
                    <div class="col-sm-3">
                        <span class="h5">
                            {{ powerbar.ip }}
                        </span>
                    </div>
                    <div class="col-sm-3">
                        <span class="h5">
                            {{ getMetadataByKey(powerbar.attributes, 'MODEL') }}
                        </span>
                    </div>
                </div>
            </ng-template>
            <ng-template pTemplate="content" class="p-p-0">
                <div class="p-grid border-bottom">
                    <div class="p-col-12 p-md-10 border-right">
                        <h5 class="p-ml-6 pt-2">Powerbar Details</h5>
                        <div class="p-col-12 p-d-flex p-flex-col p-pb-0">
                            <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-2">
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_name">Name</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ powerbar.name }}</div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_equipmentid">Equipment ID</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <span class="selectable">{{ powerbar.equipmentId|default:'-' }}</span>
                                    </div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_ipaddress">IP Address</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <span class="selectable">{{ powerbar.ip|default:'-' }}</span>
                                    </div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_devicetype">Device Type</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ powerbar.brand }} {{ powerbar.type }}</div>
                                </div>
                            </div>
                            <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-5">
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_site">Site</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ powerbar.site }}</div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_serial">Serial</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ getMetadataByKey(powerbar.attributes, 'SERIAL') }}</div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_community">Community</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <app-community-string [communityString]="powerbar.community"></app-community-string>
                                    </div>
                                </div>

                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_versions">Versions</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <div class="row m-0 p-0">
                                            <div class="col-7 p-0">
                                                <span class="pdu-attribute pdu-attribute-model">
                                                    {{ getMetadataByKey(powerbar.attributes, 'MODEL') }}
                                                </span>
                                            </div>
                                            <div class="col-5 p-0">
                                                <span class="pdu-attribute pdu-attribute-af1">
                                                    {{ getMetadataByKey(powerbar.attributes, 'AF1') }}
                                                </span>
                                            </div>
                                            <div class="col-7 p-0">
                                                <span class="pdu-attribute pdu-attribute-pf">
                                                    {{ getMetadataByKey(powerbar.attributes, 'PF') }}
                                                </span>
                                            </div>
                                            <div class="col-5 p-0">
                                                <span class="pdu-attribute pdu-attribute-mb">
                                                    {{ getMetadataByKey(powerbar.attributes, 'MB') }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-col-12 p-md-2 quick-actions">
                        <h5 i18n="@@bma_common_quickactions" class="p-2">Quick Actions</h5>
                        <div class="p-grid p-flex-column align-items-start">
                            <a i18n-label="@@bma_common_powerusagegraph" pButton href="https://grafana.leaseweb.net/d/rtn21JiGk/power-pdu-details?refresh=5m&var-site={{ powerbar.site }}&var-powerbarName={{ powerbar.name }}" *ngIf="'PDU' === powerbar.type" target="_blank" label="Power usage graph" class="p-button-link" title="Power usage graph"> </a>
                            <a i18n-label="@@bma_common_powerusagegraph" pButton href="https://grafana.leaseweb.net/d/rtn21JiGkj/power-ats-details?refresh=5m&var-site={{ powerbar.site }}&var-powerbarName={{ powerbar.name }}" *ngIf="'ATS' === powerbar.type" target="_blank" label="Power usage graph" class="p-button-link" title="Power usage graph"> </a>
                            <!-- <a pButton [href]="powerbar._links.logstorUrl" target="_blank" label="Syslog" class="p-button-link" title="Syslog"></a>
                            <a pButton [href]="powerbar._links.icingaUrl" target="_blank" label="Icinga Monitoring" class="p-button-link" title="Icinga Monitoring"></a> -->
                            <a i18n-label="@@bma_common_auditlogs" pButton target="_blank" [routerLink]="['/audit-logs']" [queryParams]="{filter: powerbar?.name}" label="Audit Logs" class="p-button-link"></a>
                        </div>
                    </div>
                </div>
                <div class="p-grid p-pr-5 p-pt-3">
                    <a i18n-label="@@bma_common_manage" pButton [routerLink]="['/powerbars', powerbar.name]" class="p-button-primary ml-auto" label="Manage"></a>
                </div>
            </ng-template>
        </p-accordionTab>
    </p-accordion>
</div>
<div *ngIf="!isLoading && powerbars">
    <app-pagination [totalCount]="powerbars._metadata.totalCount" [limit]="powerbars._metadata.limit" [offset]="powerbars._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
</div>
<ng-template #noData>
    <div class="align-middle bg-white p-mb-2 p-py-4">
        <span i18n="@@bma_common_nopowerbarsfound" *ngIf="!error">No Powerbars found</span>
        <span i18n="@@bma_common_errorfetchingdata" class="text-danger" *ngIf="error">Error fetching the data</span>
    </div>
</ng-template>
