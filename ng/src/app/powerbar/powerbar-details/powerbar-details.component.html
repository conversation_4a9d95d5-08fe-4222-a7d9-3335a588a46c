<div class="row mb-4">
    <div class="col">
        <!-- Details -->
        <h3 i18n="@@bma_common_details" class="mb-3">Details</h3>

        <app-loader *ngIf="!powerbar"></app-loader>

        <table class="table table-sm" *ngIf="powerbar">
            <colgroup>
                <col class="table-col-30" />
                <col class="table-col-70" />
            </colgroup>

            <tbody>
                <tr>
                    <td i18n="@@bma_common_site">Site</td>
                    <td>
                        {{ powerbar.site }}
                    </td>
                </tr>

                <tr>
                    <td i18n="@@bma_common_equipmentid">Equipment ID</td>
                    <td>
                        <span class="selectable">
                            {{ powerbar.equipmentId }}
                        </span>
                    </td>
                </tr>

                <tr>
                    <td i18n="@@bma_common_name">Name</td>
                    <td>
                        <span class="selectable">
                            {{ powerbar.name }}
                        </span>
                    </td>
                </tr>

                <tr>
                    <td i18n="@@bma_common_devicetype">Device Type</td>
                    <td>
                        {{ powerbar.type|uppercase }}
                    </td>
                </tr>

                <tr>
                    <td i18n="@@bma_common_brand">Brand</td>
                    <td>
                        {{ powerbar.brand|uppercase }}
                    </td>
                </tr>

                <tr>
                    <td i18n="@@bma_common_ipaddress">IP Address</td>
                    <td>
                        <span class="text-monospace selectable">
                            {{ powerbar.ip }}
                        </span>
                    </td>
                </tr>

                <tr>
                    <td i18n="@@bma_common_communitystring" class="py-2">Community String</td>
                    <td class="py-2">
                        <app-community-string [communityString]="powerbar.community"></app-community-string>
                    </td>
                </tr>

                <tr>
                    <td i18n="@@bma_common_created" class="py-2">Created</td>
                    <td class="py-2">
                        {{ powerbar.createdAt|timeAgo }} ago

                        <small class="text-muted pull-right">
                            {{ powerbar.createdAt|lswDateTime }}
                        </small>
                    </td>
                </tr>

                <tr>
                    <td i18n="@@bma_common_updated">Updated</td>
                    <td>
                        {{ powerbar.updatedAt|timeAgo }} ago

                        <small class="text-muted pull-right">
                            {{ powerbar.updatedAt|lswDateTime }}
                        </small>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Attributes -->
    <div class="col">
        <h3 i18n="@@bma_common_attributes" class="mb-3">Attributes</h3>

        <app-loader *ngIf="!powerbar"></app-loader>

        <table class="table table-sm" *ngIf="powerbar">
            <thead>
                <tr>
                    <th i18n="@@bma_common_attribute" class="table-col-25">Attribute</th>
                    <th i18n="@@bma_common_value" class="table-col-50">Value</th>
                    <th i18n="@@bma_common_updated" class="text-center table-col-20">Updated</th>
                    <th class="text-center table-col-5">&nbsp;</th>
                </tr>
            </thead>

            <tbody>
                <tr *ngFor="let item of powerbar['attributes']">
                    <td>
                        {{ item.key }}
                    </td>
                    <td>
                        {{ item.value }}
                    </td>
                    <td class="text-center" title="{{ item.updatedAt|timeAgo }} ago">
                        <small title="{{ item.updatedAt|timeAgo }} ago" data-toggle="tooltip" data-placement="bottom"> {{ item.updatedAt|timeAgo }} ago </small>
                    </td>
                </tr>
            </tbody>
        </table>

        <br />

        <!-- System Information -->
        <h3 i18n="@@bma_common_systeminfo" class="mb-3">System information</h3>

        <app-loader *ngIf="isLoadingSysInfo"></app-loader>
        <div *ngIf="!errorSysInfo; else errorFetchingData">
            <table id="powerbar-system-info" class="table table-sm" *ngIf="!isLoadingSysInfo && powerbarSystemInformation && powerbar">
                <colgroup>
                    <col class="table-col-20" />
                    <col class="table-col-80" />
                </colgroup>
                <tbody>
                    <tr>
                        <td i18n="@@bma_common_hostname">Hostname</td>
                        <td id="name">{{ powerbarSystemInformation.name | default: 'Not Available' }}</td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_description">Description</td>
                        <td id="description">
                            {{ powerbarSystemInformation.description | default: 'Not Available' }}
                        </td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_model">Model</td>
                        <td id="model">{{ powerbarSystemInformation.details.model | default: 'Not Available' }}</td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_firmwareversion">Firmware Version</td>
                        <td id="af1">{{ powerbarSystemInformation.details.af1 | default: 'Not Available' }}</td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_bootfilename">Bootfile Name</td>
                        <td id="an1">{{ powerbarSystemInformation.details.an1 | default: 'Not Available' }}</td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_uptime">Uptime</td>
                        <td id="uptime">{{ powerbarSystemInformation.uptime | timeAgo }}</td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_contact">Contact</td>
                        <td id="contact">{{ powerbarSystemInformation.contact | default: 'Not Available' }}</td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_serialno">Serial Number</td>
                        <td id="serial">{{ powerbarSystemInformation.details.serial | default: 'Not Available' }}</td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_outlets">Outlets</td>
                        <td id="outlets">
                            {{ powerbarSystemInformation.numberOfPorts ? powerbarSystemInformation.numberOfPorts : '-' }}
                        </td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_watt">Watt</td>
                        <td>
                            <ng-container *ngIf="powerbarSystemInformation.wattInformation !== null; else errorFetchingData">
                                <span id="watt">{{ (powerbarSystemInformation.wattInformation/1000).toFixed(1) }} kW</span>
                            </ng-container>
                            <span class="float-right">
                                <a href="https://grafana.leaseweb.net/d/rtn21JiGk/power-pdu-details?refresh=5m&var-site={{ powerbar.site }}&var-powerbarName={{ powerbar.name }}&var-rackFunctionalLocation={{ powerbar.functionalLocation }}" *ngIf="'PDU' === powerbar.type" target="_blank" title="Power usage graph"> <i class="fa fa-usage"></i> <span i18n="@@bma_powerbardetails_powerusagegraph">Power usage graph</span></a>
                                <a href="https://grafana.leaseweb.net/d/rtn21JiGkj/power-ats-details?refresh=5m&var-site={{ powerbar.site }}&var-powerbarName={{ powerbar.name }}&var-rackFunctionalLocation={{ powerbar.functionalLocation }}" *ngIf="'ATS' === powerbar.type" target="_blank" title="Power usage graph"> <i class="fa fa-usage"></i> <span i18n="@@bma_powerbardetails_powerusagegraph">Power usage graph</span></a>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_amperage">Amperage</td>
                        <td id="amperage">
                            <ng-container *ngIf="powerbarSystemInformation.amperageInformation !== null; else errorFetchingData">
                                <ng-container *ngIf="powerbar.type === 'ATS'">
                                    <span i18n="@@bma_common_combined">Combined: {{ (powerbarSystemInformation.amperageInformation.total/1000).toFixed(1) }} A</span><br />
                                    <span i18n="@@bma_common_feeda">Feed A: {{ (powerbarSystemInformation.amperageInformation.phaseA/1000).toFixed(1) }} A</span><br />
                                    <span i18n="@@bma_common_feedb">Feed B: {{ (powerbarSystemInformation.amperageInformation.phaseB/1000).toFixed(1) }} A</span>
                                </ng-container>
                                <ng-container *ngIf="powerbar.type === 'PDU'"> {{ (powerbarSystemInformation.amperageInformation/1000).toFixed(1) }} A </ng-container>
                            </ng-container>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- End System Information -->
    </div>
</div>
<div class="row mb-4" *ngIf="powerbar['type'] === 'PDU'">
    <div class="col">
        <h3 i18n="@@bma_common_outlets" class="mb-3">Outlets</h3>

        <app-loader *ngIf="isLoadingOutlets"></app-loader>

        <div *ngIf="!errorOutlets; else errorFetchingData">
            <table id="powerbar-outlets-status" class="table table-striped" *ngIf="!isLoadingOutlets && outletStatistics && outletStatistics['outlets']">
                <thead id="outlets-status-header">
                    <tr>
                        <th i18n="@@bma_common_outlet" scope="col" class="text-center table-col-20">Outlet</th>
                        <th i18n="@@bma_common_status" scope="col" class="text-center table-col-40">Status</th>
                        <th i18n="@@bma_common_manage" scope="col" class="text-center table-col-40">Manage</th>
                    </tr>
                </thead>
                <tbody id="outlets-status-body">
                    <tr *ngFor="let outlet of outletStatistics.outlets ">
                        <td class="text-center">
                            {{ outlet.outlet }}
                        </td>

                        <td class="text-center">
                            <span *ngIf="outlet.status === 'on'" type="button" class="btn btn-cap-network-interface btn-sm btn-outline-success">
                                {{ outlet.status }}
                            </span>
                            <span *ngIf="outlet.status === 'off'" type="button" class="btn btn-cap-network-interface btn-sm btn-outline-danger">
                                {{ outlet.status}}
                            </span>
                            <span *ngIf="outlet.status !== 'on' && outlet.status !== 'off'" type="button" class="btn btn-cap-network-interface btn-sm btn-outline-primary">
                                {{ outlet.status}}
                            </span>
                        </td>
                        <td class="text-center">
                            <button i18n-label="@@bma_common_poweroff" pButton label="Power Off" class="p-button-outlined p-button-danger mr-2" *ngIf="outlet.status === 'on'" type="button" (click)="openPowerbarOutletOperationModal(outlet.outlet, 'poweroff')"></button>
                            <button i18n-label="@@bma_common_poweron" pButton label="Power On" class="p-button-outlined p-button-success mr-2" *ngIf="outlet.status === 'off'" type="button" (click)="openPowerbarOutletOperationModal(outlet.outlet, 'poweron')"></button>
                            <ng-container *ngIf="outlet.status !== 'on' && outlet.status !== 'off'">
                                <button i18n-label="@@bma_common_poweron" pButton label="Power On" class="p-button-outlined p-button-success mr-2" type="button"></button>
                                <button i18n-label="@@bma_common_poweroff" pButton label="Power Off" class="p-button-outlined p-button-danger mr-2" type="button"></button>
                            </ng-container>
                            <button i18n-label="@@bma_common_powercycle" pButton label="Power Cycle" class="p-button-outlined p-button-primary mr-2" type="button" (click)="openPowerbarOutletOperationModal(outlet.outlet, 'powercycle')"></button>
                        </td>
                    </tr>
                    <tr *ngIf="outletStatistics.outlets.length === 0">
                        <td i18n-label="@@bma_common_nooutlets" colspan="9" class="text-center p-5 h5">No outlet status information found.</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
<ng-template #errorFetchingData>
    <span i18n="@@bma_common_errorfetchingdata" class="text-danger">Error fetching the data</span>
</ng-template>
