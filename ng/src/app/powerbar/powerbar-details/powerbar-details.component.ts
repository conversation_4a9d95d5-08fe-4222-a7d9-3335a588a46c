import { Component, OnInit, Input } from '@angular/core';
import { ModalService } from 'src/app/services/modal.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { PowerbarOutletOperationModalComponent } from '../powerbar-outlet-operation-modal/powerbar-outlet-operation-modal.component';

@Component({
  selector: 'app-powerbar-details',
  templateUrl: './powerbar-details.component.html',
  styleUrls: ['./powerbar-details.component.css'],
})
export class PowerbarDetailsComponent implements OnInit {
  @Input() powerbar: any;
  @Input() powerbarSystemInformation: any;
  isLoadingOutlets = false;
  errorOutlets = false;
  isLoadingSysInfo = false;
  errorSysInfo = false;
  outletStatistics: any;
  dynamicDialogRef: DynamicDialogRef;

  constructor(
    private httpClient: HttpClient,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    if (this.powerbar) {
      this.isLoadingSysInfo = true;
      this.httpClient.post(`/_/internal/bmpapi/v1/powerbars/${this.powerbar.name}/querySystemInfo`, null).subscribe(
        (powerbarSystemInfo) => {
          this.isLoadingSysInfo = false;
          if (powerbarSystemInfo) {
            this.powerbarSystemInformation = powerbarSystemInfo;
          }
        },
        (error) => {
          this.isLoadingSysInfo = false;
          this.errorSysInfo = true;
        }
      );

      if (this.powerbar.type === 'PDU') {
        this.isLoadingOutlets = true;
        this.httpClient.get(`/_/internal/bmpapi/v1/powerbars/${this.powerbar.name}/outlets/queryStatistics`).subscribe(
          (outletStatistics) => {
            this.isLoadingOutlets = false;
            if (outletStatistics) {
              this.outletStatistics = outletStatistics;
            }
          },
          (error) => {
            this.isLoadingOutlets = false;
            this.errorOutlets = true;
          }
        );
      }
    }
  }

  openPowerbarOutletOperationModal(outlet: string, operation: string) {
    this.modalService.show(PowerbarOutletOperationModalComponent, {
      name: this.powerbar.name,
      outlet,
      operation,
    });
  }
}
