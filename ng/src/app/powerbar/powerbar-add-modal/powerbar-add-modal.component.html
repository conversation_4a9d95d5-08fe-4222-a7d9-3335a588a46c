<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '800px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_powerbaraddmodal_header" class="modal-title">Add Powerbar</h3>
    </ng-template>

    <form [formGroup]="form" (ngSubmit)="onPowerbarSubmit()">
        <div class="modal-body">
            <div class="row form-group mt-1 mb-2">
                <label i18n="@@bma_common_equipmentid" class="col-2 col-form-label" for="equipmentId">Equipment ID</label>
                <div class="col-9">
                    <div class="input-group">
                        <input #equipmentId type="text" id="equipmentId" class="form-control" (keyup.enter)="getEquipmentDetail(equipmentId.value)" formControlName="equipmentId" autofocus />
                        <div class="input-group-append">
                            <button i18n-label="@@bma_common_search" pButton type="button" (click)="getEquipmentDetail(equipmentId.value)" [loading]="isEquipmentLoading" label="Search" icon="pi pi-search" class="p-button-primary p-button-outlined pt-0 pb-0"></button>
                        </div>
                    </div>

                    <span i18n="@@bma_common_equipmentnotfound" *ngIf="isEquipmentInSap === false" class="text-warning">Equipment not found in SAP or not supported yet. You can continue if you know what you are doing.<br /></span>
                    <small i18n="@@bma_common_equipmentfiledsap" class="text-muted font-italic">This field is the id of the equipment from SAP</small>
                    <small *ngIf="form.get('equipmentId').errors?.api" class="text-danger"><br />{{ form.get('equipmentId').errors.api }}</small>
                </div>
            </div>
            <div *ngIf="isEquipmentInSap !== undefined">
                <div class="row mt-1 mb-2">
                    <div class="md-form input-group">
                        <div i18n="@@bma_common_name" class="input-group-prepend col-2 text-right">Name</div>
                        <div class="col-9">
                            <input i18n-placeholder="@@bma_common_namingconvention" type="text" id="name" formControlName="name" required="required" class="form-control" placeholder="Please comply with the naming convention" />
                            <small i18n="@@bma_common_pdunameautomation" class="text-muted font-italic">For automation to work, it is important that the name of the PDU here matches the name in SAP.</small>
                            <small *ngIf="form.get('name').errors?.api" class="text-danger"><br />{{ form.get('name').errors.api }}</small>
                        </div>
                    </div>
                </div>

                <div class="row mt-1 mb-2">
                    <div class="md-form input-group">
                        <div i18n="@@bma_common_ipaddress" class="input-group-prepend col-2">IP Address</div>
                        <div class="col-9">
                            <input i18n-placeholder="@@bma_common_powerbarip" type="text" id="ip" formControlName="ip" class="form-control" placeholder="The ipv4 address of the powerbar" />
                            <small *ngIf="form.get('ip').errors?.api" class="text-danger">{{ form.get('ip').errors.api }}</small>
                        </div>
                    </div>
                </div>

                <div class="row mt-1 mb-2">
                    <div class="md-form input-group">
                        <div i18n="@@bma_common_type" class="input-group-prepend col-2">Type</div>
                        <div class="col-9">
                            <select class="form-control" id="type" formControlName="type">
                                <option i18n="@@bma_common_choosetype" value="null" disabled>Choose a type</option>
                                <option i18n="@@bma_common_pdu" value="PDU">PDU</option>
                                <option i18n="@@bma_common_ats" value="ATS">ATS</option>
                            </select>
                            <small *ngIf="form.get('type').errors?.api" class="text-danger">{{ form.get('type').errors.api }}</small>
                        </div>
                    </div>
                </div>

                <div class="row mt-1 mb-2">
                    <div class="md-form input-group">
                        <div i18n="@@bma_common_brand" class="input-group-prepend col-2">Brand</div>
                        <div class="col-9">
                            <select class="form-control" id="brand" formControlName="brand">
                                <option i18n="@@bma_common_choosebrand" value="null" disabled>Choose a brand</option>
                                <option i18n="@@bma_common_apc" value="APC">APC</option>
                                <option i18n="@@bma_common_tripplite" value="TRIPPLITE">TRIPPLITE</option>
                            </select>
                            <small *ngIf="form.get('brand').errors?.api" class="text-danger">{{ form.get('brand').errors.api }}</small>
                        </div>
                    </div>
                </div>

                <div class="row mt-1 mb-2">
                    <div class="md-form input-group">
                        <div i18n="@@bma_common_community" class="input-group-prepend col-2">Community</div>
                        <div class="col-9">
                            <input type="text" id="community" formControlName="community" class="form-control" />
                            <small i18n="@@bma_common_powerbarcommunity" class="text-muted font-italic">The read-write community string configured on APC PDUs. Required for automation to perform power operations on APC power outlets.</small>
                            <small *ngIf="form.get('community').errors?.api" class="text-danger"><br />{{ form.get('community').errors.api }}</small>
                        </div>
                    </div>
                </div>

                <div class="row mt-1 mb-2">
                    <div class="md-form input-group">
                        <div i18n="@@bma_common_site" class="input-group-prepend col-2">Site</div>
                        <div class="col-9">
                            <select class="form-control" id="site" formControlName="site">
                                <option i18n="@@bma_common_choosesite" value="null" disabled>Choose a site for automation</option>
                                <option value="{{ site }}" *ngFor="let site of sites">{{ site }}</option>
                            </select>
                            <small i18n="@@bma_common_powerbarsite" class="text-muted font-italic">Site this powerbar belongs to.</small>
                            <small *ngIf="form.get('site').errors?.api" class="text-danger"><br />{{ form.get('site').errors.api }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <div *ngIf="form.errors?.api?.errorMessage" class="alert alert-danger" role="alert">
        <span>{{ form.errors.api.errorMessage }}</span>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_cancel" pButton (click)="closeModal()" label="Cancel" icon="pi pi-times" type="button" class="p-button-secondary"></button>
            <button i18n-label="@@bma_common_add" pButton (click)="onPowerbarSubmit()" icon="pi pi-check" [loading]="isSubmitting" [disabled]="form.invalid" label="Add"></button>
        </div>
    </ng-template>
</p-dialog>
