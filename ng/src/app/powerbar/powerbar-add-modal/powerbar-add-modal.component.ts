import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { SiteService } from 'src/app/services/site.service';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { CidrToIpPipe } from 'src/app/pipes/cidr-to-ip.pipe';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { AlertService } from 'src/app/services/alert.service';

@Component({
  selector: 'app-powerbar-add-modal',
  templateUrl: './powerbar-add-modal.component.html',
  styleUrls: ['./powerbar-add-modal.component.css'],
})
export class PowerbarAddModalComponent implements OnInit {
  sites: any;
  form: UntypedFormGroup;
  isSubmitting = false;
  isEquipmentLoading = false;
  isEquipmentInSap: boolean;
  showDialog = true;

  constructor(
    private siteService: SiteService,
    private alertService: AlertService,
    private httpClient: HttpClient,
    private dynamicDialogRef: DynamicDialogRef,
    private readonly formBuilder: UntypedFormBuilder,
    private router: Router,
    private cidrToIpPipe: CidrToIpPipe
  ) {}

  ngOnInit(): void {
    this.sites = this.siteService.sites();

    this.form = this.formBuilder.group({
      name: [null, Validators.required],
      ip: [null, Validators.required],
      type: [null, Validators.required],
      brand: [null, Validators.required],
      community: [null, Validators.required],
      equipmentId: [null, Validators.required],
      site: [null, Validators.required],
    });
  }

  onPowerbarSubmit() {
    // Hack to make Enter press work as expected for the first equipment search
    // Following presses will trigger the whole form submit
    if (this.isEquipmentInSap === undefined) {
      this.getEquipmentDetail(this.form.get('equipmentId').value);
      return;
    }

    if (this.form.invalid) {
      return;
    }

    this.isSubmitting = true;
    const body = this.form.getRawValue();

    this.httpClient.post('/_/internal/bmpapi/v1/powerbars', body).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;
        this.alertService.alert(
          {
            type: 'success',
            description: $localize`:@@bma_powerbaraddmodal_added:Added new Powerbar ${data?.name}` + '.',
          },
          true
        );
        this.closeModal();
        if (data.name) {
          this.router.navigate([`powerbars/${data.name}`]);
        } else {
          this.router.navigate(['powerbars']);
        }
      },
      error: (error) => {
        this.isSubmitting = false;
        this.form.setErrors({ api: error.error });
        Object.entries(error.error.errorDetails || {}).forEach(([key, value]) => {
          this.form.get(key).setErrors({ api: value[0] });
        });
      },
    });
  }

  generateCommunityString(length: number = 11) {
    if (!length) {
      length = 11;
    }
    const charSet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let randomCommunityString = '';
    for (let i = 0; i < length; i++) {
      const randomPosition = Math.floor(Math.random() * charSet.length);
      randomCommunityString += charSet.substring(randomPosition, randomPosition + 1);
    }

    return randomCommunityString;
  }

  getEquipmentDetail(equipmentId): void {
    this.isEquipmentLoading = true;
    this.httpClient.get<any>(`/_legacy/powerbars/${equipmentId}`).subscribe({
      next: (powerbar: any) => {
        this.isEquipmentLoading = false;
        this.isEquipmentInSap = true;

        this.prePopulateForm(powerbar);
      },
      error: (error: any) => {
        this.isEquipmentLoading = false;
        this.isEquipmentInSap = false;

        this.resetForm(equipmentId);
      },
    });
  }

  resetForm(equipmentId): void {
    this.form.reset({
      community: this.generateCommunityString(11),
      equipmentId,
    });
  }

  prePopulateForm(powerbar): void {
    this.form.setValue({
      name: powerbar.description,
      ip: this.cidrToIpPipe.transform(powerbar.ip),
      community: this.generateCommunityString(11),
      type: powerbar.type ?? null,
      site: null,
      brand: powerbar.brand ?? null,
      equipmentId: powerbar.id,
    });
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }
}
