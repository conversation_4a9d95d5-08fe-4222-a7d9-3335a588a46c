import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-equipment-location',
  templateUrl: './equipment-location.component.html',
  styleUrls: ['./equipment-location.component.css'],
})
export class EquipmentLocationComponent implements OnInit {
  @Input() location: any;
  @Input() forceFont: boolean;

  ngOnInit(): void {
    if (typeof this.forceFont === 'undefined') {
      this.forceFont = true;
    }
  }
}
