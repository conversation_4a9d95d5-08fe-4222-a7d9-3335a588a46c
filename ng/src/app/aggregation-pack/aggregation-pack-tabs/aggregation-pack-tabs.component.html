<app-customer-aware-header *ngIf="aggregationPack" caption="Aggregation Pack {{aggregationPack.id}}" [reference]="aggregationPack.dataTrafficCommit ? (aggregationPack.dataTrafficCommit + ' ' + aggregationPack.dataTrafficCommitUnit + ' ' + aggregationPack.networkPerformanceType) : 'Not Available'" [customerId]="aggregationPack.customerId" [salesOrgId]="aggregationPack.salesOrgId" [isEmployee]="isEmployee"></app-customer-aware-header>

<app-loader *ngIf="isLoading"></app-loader>

<ul class="nav nav-pills mb-4" *ngIf="aggregationPack">
    <li class="nav-item dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-usage mr-2"></span>
            <span i18n="@@bma_common_usage">Usage</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a [routerLink]="['notifications']" class="dropdown-item">
                <span class="text-muted fa fa-notification mr-2"></span>
                <span i18n="@@bma_common_notifications">Notifications</span>
            </a>
            <a [routerLink]="['.']" class="dropdown-item">
                <span class="text-muted fa fa-usage mr-2"></span>
                <span i18n="@@bma_common_graphs">Graphs</span>
            </a>
        </div>
    </li>
</ul>

<ng-container *ngIf="aggregationPack">
    <router-outlet (activate)="onAggregationPackLoaded($event)"></router-outlet>
</ng-container>
