import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { SiteService } from 'src/app/services/site.service';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-aggregation-pack-tabs',
  templateUrl: './aggregation-pack-tabs.component.html',
  styleUrls: ['./aggregation-pack-tabs.component.css'],
})
export class AggregationPackTabsComponent implements OnInit {
  isLoading = false;
  aggregationPack: any;
  isEmployee = false;
  headerTitle = 'Aggregation Pack';

  constructor(
    private activatedRoute: ActivatedRoute,
    private titleService: Title,
    private currentUserService: CurrentUserService,
    private httpClient: HttpClient,
    private router: Router,
    private siteService: SiteService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.activatedRoute.url.subscribe((parameters) => this.onUrlParamsChanged(parameters));
  }

  onUrlParamsChanged(urlParams): void {
    this.isLoading = true;

    this.httpClient.get<any>(`/_/internal/nseapi/v2/aggregationPacks/${urlParams[1].path}`).subscribe({
      next: (data: any) => {
        this.isLoading = false;
        this.aggregationPack = data;
        this.headerTitle = `Aggregation Pack ${this.aggregationPack.id}`;

        if (this.isEmployee) {
          /*eslint-disable */
          const event = new Event('update_product_navbar');
          event['customerId'] = this.aggregationPack.customerId;
          event['salesOrgId'] = this.aggregationPack.salesOrgId;
          event['country'] = this.siteService.getCountry(this.aggregationPack.salesOrgId);
          window.dispatchEvent(event);
          /*eslint-enable */
          this.headerTitle = `${this.headerTitle} | Customer ${this.aggregationPack.customerId}`;
        }

        this.titleService.setTitle(`${this.headerTitle} | Leaseweb`);
      },
      error: (error: any) => {
        this.isLoading = false;
        this.router.navigate(['**']);
      },
    });
  }

  onAggregationPackLoaded(component) {
    if (this.aggregationPack) {
      component.equipment = this.aggregationPack;
      component.server = this.aggregationPack;
      component.equipmentId = this.aggregationPack.id;
      component.equipmentType = 'aggregationPacks';
    }
  }
}
