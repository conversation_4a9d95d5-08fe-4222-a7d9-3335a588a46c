<app-customer-aware-header caption="Aggregation Packs" [customerId]="customerId" [salesOrgId]="salesOrgId" [isEmployee]="isEmployee"></app-customer-aware-header>

<app-loader *ngIf="isLoading"></app-loader>

<div class="table-responsive" *ngIf="!isLoading && aggregationPacks">
    <table class="table table-striped">
        <thead>
            <tr>
                <th i18n="@@bma_common_id" class="text-left">ID</th>
                <th i18n="@@bma_aggregationpack_connectivity" class="text-center">Connectivity Type</th>
                <th i18n="@@bma_aggregationpack_commitment" class="text-center">Commitment</th>
                <th i18n="@@bma_aggregationpack_performancetype" class="text-center">Network Performance Type</th>
                <th i18n="@@bma_common_startdate" class="text-center">Start Date</th>
                <th>&nbsp;</th>
            </tr>
        </thead>

        <tbody>
            <tr *ngIf="aggregationPacks.connectContractItems.length === 0">
                <td *ngIf="isEmployee; else noAggregationPacks" i18n="@@bma_aggregationpack_noresultsemployee" class="text-center p-4" colspan="999">No aggregation packs found</td>
                <ng-template #noAggregationPacks>
                    <td i18n="@@bma_aggregationpack_noresultscustomer" class="text-center p-4" colspan="999">You don't have aggregation packs.</td>
                </ng-template>
            </tr>
            <tr *ngFor="let aggregationPack of aggregationPacks.connectContractItems">
                <td class="text-left">
                    <a [routerLink]="[aggregationPack.id]">
                        <span class="text-monospace">{{ aggregationPack.id }}</span>
                    </a>
                </td>
                <td class="text-center">
                    <span *ngIf="aggregationPack.connectivityType; else notAvailable">{{ aggregationPack.connectivityType }}</span>
                </td>
                <td class="text-center">
                    <span *ngIf="aggregationPack.dataTrafficCommit; else notAvailable">{{ aggregationPack.dataTrafficCommit + ' ' + aggregationPack.dataTrafficCommitUnit }}</span>
                    <ng-container *ngIf="!isEmployee && aggregationPack.networkPerformanceType|default">
                        <ng-container *ngIf="aggregationPack.contract?.status|default:'' != 'ACTIVE'; else pendingContractModification">
                            <a title="Upgrade Connectivity" id="bandwidth-upgrades-btn" class="float-right" target="_blank" [href]="getCommerceUpgradeConnectivityUrl(aggregationPack)">
                                <i class="fa fa-upgrade" aria-hidden="true"></i>
                            </a>
                        </ng-container>
                    </ng-container>
                </td>
                <td *ngIf="aggregationPack.networkPerformanceType; else notAvailable" class="text-center">
                    <span>{{ aggregationPack.networkPerformanceType }}</span>
                </td>
                <td class="text-center">
                    <span>{{ aggregationPack.contractStartDate|lswDate }}</span>
                </td>

                <td class="text-right text-nowrap">
                    <a [routerLink]="[aggregationPack.id]" title="Datagraph" class="fa fa-usage fa-lg"></a>
                    <a *ngIf="!isFiberring" [routerLink]="[aggregationPack.id, 'notifications']" title="Notifications" class="fa fa-notification fa-lg"></a>
                </td>
            </tr>
        </tbody>
    </table>
</div>
<ng-template #notAvailable>
    <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
</ng-template>
<ng-template #pendingContractModification>
    <span title="Pending contract modification" class="float-right text-muted">
        <i class="fa fa-refresh"></i>
        <span i18n-title="@@bma_common_pendingcontractmodification" class="sr-only">Pending contract modification</span>
    </span>
</ng-template>
