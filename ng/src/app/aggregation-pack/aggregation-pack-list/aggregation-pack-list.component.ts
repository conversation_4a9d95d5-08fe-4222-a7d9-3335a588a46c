import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { CommerceService } from 'src/app/services/commerce.service';
import { SiteService } from 'src/app/services/site.service';

@Component({
  selector: 'app-aggregation-pack-list',
  templateUrl: './aggregation-pack-list.component.html',
  styleUrls: ['./aggregation-pack-list.component.css'],
})
export class AggregationPackListComponent implements OnInit {
  aggregationPacks: any;
  customerId = '';
  headerTitle = '';
  icon = 'server';
  isEmployee = false;
  isFiberring = false;
  isLoading = false;
  salesOrgId = '';
  dynamicDialogRef: DynamicDialogRef;

  constructor(
    private activatedRoute: ActivatedRoute,
    private httpClient: HttpClient,
    private titleService: Title,
    private currentUserService: CurrentUserService,
    private siteService: SiteService,
    private commerceService: CommerceService
  ) {}

  ngOnInit(): void {
    this.headerTitle = 'Aggregation Packs';
    this.isEmployee = this.currentUserService.isEmployee();
    this.isFiberring = this.currentUserService.isFiberring();

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    if (!this.isEmployee) {
      this.customerId = this.currentUserService.getCustomerId();
    } else if (queryParams.customerId) {
      this.customerId = queryParams.customerId;
    }

    if (!this.isEmployee) {
      this.salesOrgId = this.currentUserService.getSalesOrgId();
    } else if (queryParams.salesOrgId) {
      this.salesOrgId = queryParams.salesOrgId;
    }

    if (this.isEmployee) {
      /*eslint-disable */
      const event = new Event('update_product_navbar');
      event['customerId'] = this.customerId;
      event['salesOrgId'] = this.salesOrgId;
      event['country'] = this.siteService.getCountry(this.salesOrgId);
      window.dispatchEvent(event);
      /*eslint-enable */

      this.headerTitle = `${this.headerTitle} | Customer ${this.customerId} | Leaseweb`;

      this.titleService.setTitle(this.headerTitle);
    }

    let params = new HttpParams();
    if (this.isEmployee) {
      params = params.set('customerId', this.customerId);
      params = params.set('salesOrgId', this.salesOrgId);
    }
    this.isLoading = true;
    this.httpClient.get<any>(`/_/internal/nseapi/v2/aggregationPacks`, { params }).subscribe(
      (data: any) => {
        this.isLoading = false;
        this.aggregationPacks = data;
      },
      (error: any) => {
        this.isLoading = false;
        this.aggregationPacks = [];
      }
    );
  }

  getCommerceUpgradeConnectivityUrl(aggregationPack: any): string {
    return this.commerceService.getCommerceConfigureProductUrl(aggregationPack.id, 'CONNEC02_MOD_DATAPACK');
  }
}
