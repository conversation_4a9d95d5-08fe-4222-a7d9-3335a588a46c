import { Component, Input, OnInit, Output, OnChanges, EventEmitter, SimpleChanges } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { AlertService } from '../services/alert.service';

@Component({
  selector: 'app-ssh-keys-selector',
  templateUrl: './ssh-keys-selector.component.html',
  styleUrls: ['./ssh-keys-selector.component.css'],
})
export class SshKeysSelectorComponent implements OnInit, OnChanges {
  @Input() content: string;
  @Output() contentChange = new EventEmitter<string>();

  key = 'sshKeys';
  savedItems: any = {};
  savedItemsKeys: any = {};
  downloadKeys: any = {};
  secretManagedSshKeys: any = {};
  sshKeysIsLoading = true;
  sshKeyIsLoading = false;
  isEmployee = false;

  constructor(
    private alertService: AlertService,
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService
  ) {}

  updatedSavedItems(updatedSavedItems: object) {
    this.savedItems = updatedSavedItems;
    this.updatedSavedItemsKeys();
  }

  updatedSavedItemsKeys() {
    this.savedItemsKeys = Object.keys(this.savedItems);
  }

  ngOnInit() {
    this.retrieveSavedItems();
    this.updatedSavedItemsKeys();

    this.isEmployee = this.currentUserService.isEmployee();
    if (!this.isEmployee) {
      this.getSecretManagedKeys();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.hasOwnProperty('key')) {
      this.retrieveSavedItems();
    }
  }

  onContentChange(event) {
    this.contentChange.emit(event);
  }

  updateContent(sshKey) {
    if (this.content.includes(sshKey)) {
      this.content = this.content.replace(sshKey, '');
    } else {
      this.content += '\n' + sshKey;
    }
    // Cleaning empty line
    this.content = this.content.replace(/(^[ \t]*\n)/gm, '');
    this.contentChange.emit(this.content);
  }

  useLocalSshKeyItem(keyId) {
    this.updateContent(this.savedItems[keyId]);
  }

  removeSavedItem(key) {
    delete this.savedItems[key];
    this.syncSavedItems();
  }

  syncSavedItems() {
    localStorage.setItem(this.key, JSON.stringify(this.savedItems));
    this.updatedSavedItemsKeys();
  }

  retrieveSavedItems() {
    this.savedItems = {};

    if (typeof Storage == 'undefined') {
      return;
    }

    const localStorageItem = localStorage.getItem(this.key);

    this.savedItems = this.sanitizeItem(localStorageItem);
  }

  sanitizeItem(item) {
    let parsedItem;

    try {
      parsedItem = JSON.parse(item);
    } catch (e) {
      return {};
    }

    if (parsedItem === null || typeof parsedItem !== 'object') {
      return {};
    }

    for (const key in parsedItem) {
      if (typeof parsedItem[key] !== 'string') {
        return {};
      }
    }

    return parsedItem;
  }

  getSecretManagedKeys() {
    // https://api.staging.devleaseweb.com/secrets/v1/secrets?type=SSH_PUBLIC_KEY
    this.sshKeysIsLoading = true;
    this.httpClient.get<any>(`/_/secrets/v1/secrets?type=SSH_PUBLIC_KEY`).subscribe({
      next: (data: any) => {
        this.sshKeysIsLoading = false;
        this.secretManagedSshKeys = data.secrets;
      },
      error: (error: any) => {
        this.sshKeysIsLoading = false;
        this.alertService.alert(
          {
            type: 'error',
            description: $localize`:@@bma_common_errorretrievingsshkeys:There was an error retrieving ssh keys`,
          },
          true
        );
      },
    });
  }

  getSecretManagedKey(keyId) {
    if (keyId in this.downloadKeys) {
      return this.updateContent(this.downloadKeys[keyId]);
    }
    this.sshKeyIsLoading = true;
    this.httpClient.get<any>(`/_/secrets/v1/secrets/${keyId}/data`).subscribe({
      next: (data: any) => {
        this.sshKeyIsLoading = false;
        this.downloadKeys[keyId] = data.key;
        return this.updateContent(data.key);
      },
      error: (error: any) => {
        this.sshKeyIsLoading = false;
        this.alertService.alert(
          {
            type: 'error',
            description: $localize`:@@bma_common_errorretrievingsshkey:There was an error retrieving the ssh key`,
          },
          true
        );
      },
    });
  }
}
