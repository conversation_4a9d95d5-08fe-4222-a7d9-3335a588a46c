<div class="form-group">
    <div class="row mb-3">
        <div [ngClass]="{'col-12': isEmployee, 'col-6 border-right': !isEmployee}">
            <h5 i18n="@@bma_common_localsavedsshkeys">Local saved ssh keys</h5>
            <div *ngIf="savedItemsKeys.length == 0" i18n="@@bma_common_nosshkeysfound">No ssh keys found</div>
            <div *ngFor="let key of savedItemsKeys">
                <div class="">
                    <p-checkbox [name]="key" [inputId]="key" [value]="key" (onChange)="useLocalSshKeyItem(key)" [disabled]="sshKeyIsLoading" [inputId]="key" />
                    <label [for]="key" class="m-0">{{ key }}</label>
                    <i class="pi pi-trash text-danger font-size-s cursor-pointer ml-1" (click)="removeSavedItem(key)"></i>
                </div>
            </div>
        </div>
        <div *ngIf="!isEmployee" class="col-6 border-left">
            <h5 i18n="@@bma_common_secretmanagementsshkeys">Secret management ssh keys</h5>
            <div *ngIf="!sshKeysIsLoading">
                <div *ngIf="secretManagedSshKeys.length == 0" i18n="@@bma_common_nosshkeysfound">No ssh keys found</div>
                <div *ngFor="let key of secretManagedSshKeys">
                    <div class="form-check">
                        <p-checkbox [name]="key.name" [inputId]="key.id" [value]="key.id" (onChange)="getSecretManagedKey(key.id)" [disabled]="sshKeyIsLoading" [inputId]="key.id" />
                        <label [for]="key.id" class="m-0">{{ key.name }}</label>
                        <i *ngIf="sshKeyIsLoading" class="pi pi-spin pi-spinner font-size-s ml-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <app-local-storage-editor key="sshKeys" [(content)]="content" (contentChange)="onContentChange($event)" label="SSH keys" rows="5" [clearButton]="true" [uploadButton]="true" [dragAndDrop]="true" [uploadAllowedTypes]="['.pub']" [showSavedItems]="false" (updatedSavedItemsEmitter)="updatedSavedItems($event)"></app-local-storage-editor>
        </div>
    </div>
</div>
