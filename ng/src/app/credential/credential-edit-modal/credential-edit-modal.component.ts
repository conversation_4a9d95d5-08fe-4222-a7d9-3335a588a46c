import { Component, OnInit } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { HttpClient } from '@angular/common/http';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-credential-edit-modal',
  templateUrl: './credential-edit-modal.component.html',
  styleUrls: ['./credential-edit-modal.component.css'],
})
export class CredentialEditModalComponent implements OnInit {
  equipmentId: any;
  equipmentType: any;
  type: any;
  username: any;
  form: UntypedFormGroup;
  credentialTypes = [
    'OPERATING_SYSTEM',
    'RESCUE_MODE',
    'REMOTE_MANAGEMENT',
    'CONTROL_PANEL',
    'SWITCH',
    'PDU',
    'FIREWALL',
    'LOAD_BALANCER',
    'COMBINATION_LOCK',
  ];
  isSubmitting = false;
  showDialog = true;

  constructor(
    private alertService: AlertService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.equipmentId = this.dynamicDialogConfig.data.equipmentId;
    this.equipmentType = this.dynamicDialogConfig.data.equipmentType;
    this.type = this.dynamicDialogConfig.data.type;
    this.username = this.dynamicDialogConfig.data.username;
    this.form = this.formBuilder.group({
      type: [{ value: this.type, disabled: true }, Validators.required],
      username: [{ value: this.username, disabled: true }, Validators.required],
      password: [null, Validators.required],
    });
  }

  updateCredentials(): void {
    this.isSubmitting = true;
    this.alertService.clear();
    const params = {
      type: this.form.get('type').value,
      username: this.form.get('username').value,
      password: this.form.get('password').value,
    };
    this.httpClient
      .put<any>(
        `/_/internal/bmpapi/v2/${this.equipmentType}/${this.equipmentId}/credentials/${this.type}/${this.username}`,
        params
      )
      .subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.alertService.alert({
            type: 'success',
            description: $localize`:@@bma_credentialeditmodal_updated:Credentials successfully updated` + '.',
          });
          this.closeModal({ credentialUpdated: true });
        },
        error: (error: any) => {
          this.isSubmitting = false;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ credentialUpdated: false });
        },
      });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
