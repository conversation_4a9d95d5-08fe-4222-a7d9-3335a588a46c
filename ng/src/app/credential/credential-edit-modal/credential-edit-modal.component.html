<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{width: '100%', 'max-height': '500px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_credentialeditmodal_header" class="modal-title">Edit Credentials</h3>
    </ng-template>

    <form [formGroup]="form" (ngSubmit)="updateCredentials()" class="form-horizontal">
        <div class="modal-body">
            <div class="form-group">
                <label i18n="@@bma_common_type" for="type" class="col-form-label">Type</label>
                <select class="form-control" formControlName="type" id="type" autofocus>
                    <option *ngFor="let credentialType of credentialTypes" value="{{ credentialType|uppercase }}">
                        {{ credentialType|uppercase }}
                    </option>
                </select>
            </div>

            <div class="form-group">
                <label i18n="@@bma_common_username" for="username" class="col-form-label">Username</label>
                <input type="text" class="form-control" formControlName="username" id="username" />
            </div>

            <div class="form-group">
                <label i18n="@@bma_common_password" for="password" class="col-form-label">Password</label>
                <input type="password" class="form-control" formControlName="password" id="password" data-hj-suppress />

                <small i18n="@@bma_common_helpblock" id="helpBlock" class="form-text"> Please note: The password will NOT be updated on the dedicated server. The ability to set credentials on this page is for convenience only. </small>
            </div>
        </div>
    </form>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_cancel" pButton (click)="closeModal()" type="button" icon="pi pi-times" iconPos="left" label="Cancel" class="p-button-secondary"></button>
            <button i18n-label="@@bma_common_save" pButton (click)="updateCredentials()" label="Save" icon="pi pi-check" iconPos="left" [loading]="isSubmitting"></button>
        </div>
    </ng-template>
</p-dialog>
