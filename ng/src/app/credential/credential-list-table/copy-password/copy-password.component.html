<div class="row" *ngIf="userCanSeePassword(); else redacted">
    <div class="col-5 align">
        <span *ngIf="!credential.password?.isLoading && !credential.password?.error && credential.password?.show" (dblclick)="doCopy()">
            {{ credential.password.value }}
        </span>
        <span *ngIf="!credential.password?.show && !credential.password?.error">********</span>
        <span i18n="@@bma_common_loading" *ngIf="credential.password?.isLoading && true == credential.password?.show">Loading...</span>
        <span i18n="@@bma_common_passworderror" *ngIf="credential.password?.error" class="text-danger">Error fetching password</span>
    </div>
    <div class="col-5">
        <button pButton type="button" class="p-button-sm ml-1" (click)="showHidePassword(i)" [icon]="credential.password?.show && true == credential.password?.show ? 'pi pi-eye-slash' : 'pi pi-eye'" [disabled]="credential.password?.isLoading"></button>
        <button pButton type="button" icon="pi pi-copy" class="p-button-sm ml-1" [disabled]="credential.password?.isLoading" (click)="doCopy(i)"></button>
    </div>
</div>

<ng-template #redacted>
    <span i18n="@@bma_common_redacted" class="text-danger">[redacted]</span>
</ng-template>
