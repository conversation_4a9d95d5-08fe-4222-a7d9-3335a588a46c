import { Component, Input, OnInit } from '@angular/core';
import { Clipboard } from '@angular/cdk/clipboard';
import { HttpClient } from '@angular/common/http';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { AlertService } from 'src/app/services/alert.service';

@Component({
  selector: 'app-copy-password',
  templateUrl: './copy-password.component.html',
  styleUrls: ['./copy-password.component.css'],
})
export class CopyPasswordComponent implements OnInit {
  @Input() credential: any;
  @Input() equipmentId: any;
  @Input() equipmentType: string;

  isEmployee = false;

  constructor(
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    private clipboard: Clipboard,
    private alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
  }

  userCanSeePassword(): boolean {
    return (
      (this.currentUserService.isEmployee() && this.currentUserService.hasPermission('credentials_view')) ||
      this.currentUserService.isCustomer()
    );
  }

  showHidePassword(index: number): void {
    if (!this.credential.password?.value) {
      this.getPassword(index);
    }
    this.credential.password.show = !this.credential.password.show;
  }

  getPassword(index: number, copy = false): void {
    this.credential.password = { isLoading: true, show: false };

    const type = this.credential.type;
    const username = this.credential.username;

    let uri = `/_/internal/bmpapi/v2/${this.equipmentType}/${this.equipmentId}/credentials/${type}/${username}`;

    if (!this.equipmentType) {
      uri = `/_/bareMetals/v2/servers/${this.equipmentId}/credentials/${type}/${username}`;
    }

    this.httpClient.get<any>(uri).subscribe({
      next: (data: any) => {
        this.credential.password.value = data.password;
        this.credential.password.isLoading = false;
        if (copy) {
          this.doCopy(index);
        }
      },
      error: (error: any) => {
        this.credential.password.isLoading = false;
        this.credential.password.error = true;
      },
    });
  }

  doCopy(index: number): void {
    if (!this.credential.password?.value) {
      this.getPassword(index, true);
      return;
    }

    const pending = this.clipboard.beginCopy(this.credential.password.value);
    let remainingAttempts = 3;
    const attempt = () => {
      const result = pending.copy();
      if (remainingAttempts === 0) {
        this.alertService.alert({
          type: 'error',
          description: $localize`:@@bma_common_errorcopyingpassword:Sorry, there was an error copying password.`,
        });
      } else if (!result && --remainingAttempts) {
        setTimeout(attempt);
      } else {
        pending.destroy();
      }
    };
    attempt();

    this.alertService.alert({
      type: 'success',
      description: $localize`:@@bma_common_passwordcopiedtoclipboard:Password copied to clipboard`,
    });
  }
}
