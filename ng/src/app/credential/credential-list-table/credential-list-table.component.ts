import { Component, Input, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { CredentialRemoveModalComponent } from '../credential-remove-modal/credential-remove-modal.component';
import { CredentialAddModalComponent } from '../credential-add-modal/credential-add-modal.component';
import { CredentialEditModalComponent } from '../credential-edit-modal/credential-edit-modal.component';

@Component({
  selector: 'app-credential-list-table',
  templateUrl: './credential-list-table.component.html',
  styleUrls: ['./credential-list-table.component.css'],
})
export class CredentialListTableComponent implements OnInit {
  @Input() loadedCredentials: any;
  @Input() equipmentId: any;
  @Input() equipmentType: string;
  @Input() credentialsType: string;
  @Input() addCredentials: boolean;

  credentials: any;
  isLoading = false;
  isSubmitting = false;
  isEmployee = false;
  dynamicDialogRef: DynamicDialogRef;
  filters: UntypedFormGroup;

  constructor(
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    private modalService: ModalService,
    private readonly formBuilder: UntypedFormBuilder,
    private activatedRoute: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.credentials = this.loadedCredentials ?? [];
    this.filters = this.formBuilder.group({
      offset: [0],
      limit: [20],
      filter: [null],
    });

    if (!this.loadedCredentials) {
      this.getCredentials();
    }
    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  onQueryParamsChanged(queryParams): void {
    this.filters.reset(queryParams);
    this.getCredentials();
  }

  getCredentials(): void {
    this.isLoading = true;
    const params = this.sanitise(this.filters.getRawValue());
    let uri = `/_/internal/bmpapi/v2/${this.equipmentType}/${this.equipmentId}/credentials`;

    if (!this.equipmentType) {
      uri = `/_/bareMetals/v2/servers/${this.equipmentId}/credentials`;
    }

    if (this.credentialsType) {
      uri = uri + `/${this.credentialsType}`;
    }

    this.httpClient.get<any>(uri, { params }).subscribe({
      next: (data: any) => {
        this.isLoading = false;
        this.credentials = data;
      },
      error: (error: any) => {
        this.isLoading = false;
        this.credentials = [];
      },
    });
  }

  openCredentialRemoveModal(credential: any) {
    this.dynamicDialogRef = this.modalService.show(CredentialRemoveModalComponent, {
      equipmentType: this.equipmentType,
      equipmentId: this.equipmentId,
      type: credential.type,
      username: credential.username,
    });

    this.dynamicDialogRef.onClose.subscribe((event: any) => {
      if (event?.credentialRemoved === true) {
        this.refreshCredentials();
      }
    });
  }

  openCredentialAddModal() {
    this.dynamicDialogRef = this.modalService.show(CredentialAddModalComponent, {
      equipmentType: this.equipmentType,
      equipmentId: this.equipmentId,
    });

    this.dynamicDialogRef.onClose.subscribe((event: any) => {
      if (event?.credentialAdded === true) {
        this.refreshCredentials();
      }
    });
  }

  openCredentialEditModal(credential: any) {
    this.dynamicDialogRef = this.modalService.show(CredentialEditModalComponent, {
      equipmentType: this.equipmentType,
      equipmentId: this.equipmentId,
      type: credential.type,
      username: credential.username,
    });

    this.dynamicDialogRef.onClose.subscribe((event: any) => {
      if (event?.credentialUpdated === true) {
        this.refreshCredentials();
      }
    });
  }

  refreshCredentials(): void {
    this.getCredentials();
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
