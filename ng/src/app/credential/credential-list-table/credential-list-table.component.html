<app-loader *ngIf="isLoading"></app-loader>
<table class="table table-striped" *ngIf="!isLoading">
    <thead>
        <tr>
            <th i18n="@@bma_common_type" *ngIf="addCredentials">Type</th>
            <th i18n="@@bma_common_username">Username</th>
            <th i18n="@@bma_common_password" class="w-35">Password</th>
            <th class="text-right" *ngIf="addCredentials"></th>
        </tr>
    </thead>

    <tbody>
        <tr *ngIf="credentials.credentials?.length === 0">
            <td i18n="@@bma_credentiallist_none" class="text-center p-4" colspan="4">No credentials found.</td>
        </tr>
        <tr *ngFor="let credential of credentials.credentials; index as i">
            <td *ngIf="addCredentials">
                {{ credential.type }}
            </td>
            <td>
                {{ credential.username }}
            </td>
            <td data-hj-suppress class="selectable">
                <app-copy-password [equipmentId]="equipmentId" [equipmentType]="equipmentType" [credential]="credential"></app-copy-password>
            </td>
            <td class="text-right" *ngIf="addCredentials">
                <a i18n-title="@@bma_common_viewalllog" *ngIf="isEmployee" [routerLink]="['/audit-logs']" [queryParams]="{filter: 'EQUIPMENT_CREDENTIALS_STORED AND ' + equipmentId + ' AND ' + credential.username}" class="text-info" title="View all related log messages...">
                    <i class="fa fa-log mr-1"></i>
                    <span i18n="@@bma_common_logs">Logs</span>
                </a>
                <button i18n-label="@@bma_common_update" pButton label="Update" icon="fa fa-change" class="p-button-link p-button-sm p-py-0 p-px-2" (click)="openCredentialEditModal(credential)"></button>
                <button i18n-label="@@bma_common_remove" pButton label="Remove" icon="fa fa-delete" class="p-button-link p-button-sm text-danger p-py-0 p-px-2" (click)="openCredentialRemoveModal(credential)"></button>
            </td>
        </tr>
    </tbody>
</table>

<div *ngIf="!isLoading && credentials">
    <app-pagination [totalCount]="credentials._metadata.totalCount" [limit]="credentials._metadata.limit" [offset]="credentials._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
    <div class="text-right p-0 pt-2" *ngIf="addCredentials">
        <button i18n-label="@@bma_credentiallist_add" pButton label="Add credentials" class="p-button-sm" (click)="openCredentialAddModal()"></button>
    </div>
</div>
