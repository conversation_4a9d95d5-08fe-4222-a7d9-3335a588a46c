import { Component, OnInit } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert.service';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-credential-add-modal',
  templateUrl: './credential-add-modal.component.html',
  styleUrls: ['./credential-add-modal.component.css'],
})
export class CredentialAddModalComponent implements OnInit {
  equipmentId: any;
  equipmentType: any;
  form: UntypedFormGroup;
  credentialTypes = [
    'OPERATING_SYSTEM',
    'RESCUE_MODE',
    'REMOTE_MANAGEMENT',
    'CONTROL_PANEL',
    'SWITCH',
    'PDU',
    'FIREWALL',
    'LOAD_BALANCER',
    'COMBINATION_LOCK',
  ];
  isSubmitting = false;
  showDialog = true;

  constructor(
    private alertService: AlertService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.equipmentId = this.dynamicDialogConfig.data.equipmentId;
    this.equipmentType = this.dynamicDialogConfig.data.equipmentType;
    this.form = this.formBuilder.group({
      type: ['OPERATING_SYSTEM', Validators.required],
      username: [null, Validators.required],
      password: [null, Validators.required],
    });
  }

  createCredentials(): void {
    this.isSubmitting = true;
    this.alertService.clear();
    const params = {
      type: this.form.get('type').value,
      username: this.form.get('username').value,
      password: this.form.get('password').value,
    };
    this.httpClient
      .post<any>(`/_/internal/bmpapi/v2/${this.equipmentType}/${this.equipmentId}/credentials`, params)
      .subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.form.reset();
          this.alertService.alert({
            type: 'success',
            description: $localize`:@@bma_credentialaddmodal_created:Credentials created successfully` + '.',
          });
          this.closeModal({ credentialAdded: true });
        },
        error: (error: any) => {
          this.isSubmitting = false;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ credentialAdded: false });
        },
      });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
