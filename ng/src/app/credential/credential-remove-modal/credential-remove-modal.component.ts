import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert.service';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-credential-remove-modal',
  templateUrl: './credential-remove-modal.component.html',
  styleUrls: ['./credential-remove-modal.component.css'],
})
export class CredentialRemoveModalComponent implements OnInit {
  equipmentId: any;
  equipmentType: any;
  equipment: any;
  notification: any;
  type: any;
  username: any;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private alertService: AlertService,
    private httpClient: HttpClient,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.equipmentId = this.dynamicDialogConfig.data.equipmentId;
    this.equipmentType = this.dynamicDialogConfig.data.equipmentType;
    this.equipment = this.dynamicDialogConfig.data.equipment;
    this.notification = this.dynamicDialogConfig.data.notification;
    this.type = this.dynamicDialogConfig.data.type;
    this.username = this.dynamicDialogConfig.data.username;
  }

  removeCredential(): void {
    this.isSubmitting = true;
    this.alertService.clear();
    this.httpClient
      .delete<any>(
        `/_/internal/bmpapi/v2/${this.equipmentType}/${this.equipmentId}/credentials/${this.type}/${this.username}`
      )
      .subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.alertService.alert({
            type: 'success',
            description: $localize`:@@bma_credentialremovemodal_removed:Credentials successfully removed` + '.',
          });
          this.closeModal({ credentialRemoved: true });
        },
        error: (error: any) => {
          this.isSubmitting = false;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ credentialRemoved: false });
        },
      });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
