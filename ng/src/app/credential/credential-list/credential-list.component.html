<div class="row">
    <div class="col-12">
        <h3 class="mb-3">
            <i class="fa fa-lock mr-1"></i>
            <span i18n="@@bma_credentiallist_header">Credentials</span>
        </h3>

        <ng-container *ngIf="equipmentType == 'servers'; else nonServerEquipment">
            <p i18n="@@bma_credentiallist_description1">Our automation system will automatically update credentials after a server has successfully been reinstalled or launched in rescue mode.</p>
            <p i18n="@@bma_credentiallist_description2">The ability to update credentials on this page is for convenience only, changes will not synchronize to the server.</p>
        </ng-container>

        <ng-template #nonServerEquipment>
            <p i18n="@@bma_credentiallist_description3">The ability to update credentials on this page is for convenience only, changes will not be synchronized to the equipment.</p>
        </ng-template>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="table-responsive">
            <app-credential-list-table [equipmentId]="equipmentId" [equipmentType]="equipmentType" [addCredentials]="true"></app-credential-list-table>
        </div>
    </div>
</div>

<ng-template #redacted>
    <span i18n="@@bma_common_redacted" class="text-danger" *ngIf="!userCanSeePassword()">[redacted]</span>
</ng-template>
