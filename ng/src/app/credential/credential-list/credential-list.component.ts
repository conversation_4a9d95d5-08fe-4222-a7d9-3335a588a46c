import { Component, Input, OnInit } from '@angular/core';
import { CurrentUserService } from 'src/app/services/current-user.service';

@Component({
  selector: 'app-credential-list',
  templateUrl: './credential-list.component.html',
  styleUrls: ['./credential-list.component.css'],
})
export class CredentialListComponent implements OnInit {
  @Input() equipmentId: any;
  @Input() equipmentType: string;

  isEmployee = false;

  constructor(private currentUserService: CurrentUserService) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
  }
}
