import { Component, OnInit, Input } from '@angular/core';
import { ModalService } from 'src/app/services/modal.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ReferenceEditModalComponent } from 'src/app/reference-edit-modal/reference-edit-modal.component';
import { CurrentUserService } from 'src/app/services/current-user.service';

@Component({
  selector: 'app-ip-direct-internet-access-details',
  templateUrl: './direct-internet-access-details.component.html',
  styleUrls: ['./direct-internet-access-details.component.css'],
})
export class DirectInternetAccessDetailsComponent implements OnInit {
  @Input() directInternetAccess: any;

  isEmployee: boolean;
  dynamicDialogRef: DynamicDialogRef;
  networkPorts = [];

  constructor(
    private modalService: ModalService,
    private currentUserService: CurrentUserService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();

    if (this.directInternetAccess.networkInterfaces) {
      const networkInterfaceOrder = ['public'];
      networkInterfaceOrder.forEach((networkInterfacerKey) => {
        if (
          this.directInternetAccess.networkInterfaces[networkInterfacerKey] &&
          this.directInternetAccess.networkInterfaces[networkInterfacerKey].ports.length > 0
        ) {
          const networkPorts = this.directInternetAccess.networkInterfaces[networkInterfacerKey].ports;
          networkPorts.forEach((networkPort) => {
            networkPort.networkType = networkInterfacerKey;
            this.networkPorts.push(networkPort);
          });
        }
      });
    }
  }
  openReferenceEditModal() {
    this.dynamicDialogRef = this.modalService.show(ReferenceEditModalComponent, {
      equipment: this.directInternetAccess,
      equipmentType: 'directInternetAccess',
    });

    this.dynamicDialogRef.onClose.subscribe((reason: any) => {
      if (typeof reason === 'string') {
        if (this.directInternetAccess.contract) {
          this.directInternetAccess.contract.reference = reason;
        }
      }
    });
  }
}
