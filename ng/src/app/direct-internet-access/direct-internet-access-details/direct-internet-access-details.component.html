<div class="row">
    <div class="col-md-6">
        <h3 class="mb-2">
            <span class="ssc-icon icon-powerswitch"></span>
            <span i18n="@@bma_common_networkinformation">Network Information</span>
        </h3>

        <table class="table table-sm">
            <thead>
                <tr>
                    <th i18n="@@bma_common_switch" scope="col">Switch</th>
                    <th i18n="@@bma_common_network" scope="col">Network</th>
                    <th i18n="@@bma_common_port" scope="col" class="text-right">Port</th>
                </tr>
            </thead>
            <ng-container *ngIf="networkPorts">
                <tbody>
                    <tr *ngFor="let networkPort of networkPorts">
                        <td>
                            <a *ngIf="isEmployee" [routerLink]="['/networkDevices', networkPort.name]">
                                {{ networkPort.name }}
                            </a>
                            <span *ngIf="!isEmployee">{{ networkPort.name }}</span>
                        </td>
                        <td>{{ networkPort.networkType }}</td>
                        <td class="text-right">{{ networkPort.port }}</td>
                    </tr>
                </tbody>
            </ng-container>
            <tr *ngIf="networkPorts?.length == 0">
                <td i18n="@@bma_common_nonetworkports" colspan="3" class="text-center p-4">There are no network ports available for this equipment</td>
            </tr>
        </table>
    </div>

    <div class="col-md-6 table-responsive information">
        <h3>
            <span i18n="@@bma_common_administrativedetails">Administrative Details</span>
        </h3>
        <table class="table table-sm">
            <tbody>
                <tr>
                    <th i18n="@@bma_common_deliverystatus">Delivery Status</th>
                    <td>
                        <app-contract-delivery-status [deliveryStatus]="directInternetAccess.contract?.deliveryStatus|default:'UNASSIGNED'"> </app-contract-delivery-status>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_contractstatus">Contract Status</th>
                    <td>
                        <app-contract-status [status]="directInternetAccess.contract?.status|default:'UNASSIGNED'"></app-contract-status>

                        <small i18n="@@bma_common_contractstatusupgradealert" *ngIf="['IN_MODIFICATION', 'MODIFICATION_FAILED'].indexOf((directInternetAccess.contract?.status|default|uppercase)) > -1"> It is not possible to request upgrades, enable private network, or request additional IP address while the contract status is </small>
                        <small i18n="@@bma_contractstatus_modification" *ngIf="(directInternetAccess.contract?.status|default|uppercase) == 'IN_MODIFICATION'"> IN MODIFICATION </small>
                        <small i18n="@@bma_contractstatus_modification_failed" *ngIf="(directInternetAccess.contract?.status|default|uppercase) == 'MODIFICATION_FAILED'"> MODIFICATION FAILED </small>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_directInternetAccessid">Direct Internet Access ID</th>
                    <td>
                        <span class="selectable">{{ directInternetAccess.id }}</span>
                    </td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_serviceid">Service ID</th>
                    <td>
                        <span *ngIf="directInternetAccess.contract; else notAvailable" class="selectable">{{ directInternetAccess.contract.id|default }}</span>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_reference">Reference</th>
                    <td>
                        <ng-container *ngIf="directInternetAccess.contract?.reference|default; else notAvailable">
                            {{ directInternetAccess.contract?.reference|default }}
                        </ng-container>

                        <a i18n-title="@@bma_common_editreference" *ngIf="directInternetAccess.contract" class="pull-right" href="#" (click)="openReferenceEditModal(); false" title="Edit Reference">
                            <span class="fa fa-change" aria-hidden="true"></span>
                        </a>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_billingfrequency">Billing Frequency</th>
                    <td>
                        <ng-container *ngIf="directInternetAccess.contract; else notAvailable">
                            {{ directInternetAccess.contract.billingCycle|default }}
                            {{ directInternetAccess.contract.billingFrequency|default | lowercase }}(s)
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_contractterm">Contract Term</th>
                    <td>
                        <ng-container *ngIf="directInternetAccess.contract?.contractTerm|default; else notAvailable">
                            {{ directInternetAccess.contract.contractTerm }} month(s)
                            <ng-container *ngIf="directInternetAccess.contract.contractType != 'NORMAL'">
                                &nbsp;
                                <span i18n="@@bma_common_contracttype" class="badge badge-info"> {{ directInternetAccess.contract.contractType|uppercase }} Contract </span>
                            </ng-container>
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_startdate">Start Date</th>
                    <td>
                        <ng-container *ngIf="directInternetAccess.contract?.startsAt|default; else notAvailable">
                            {{ directInternetAccess.contract.startsAt|date:'longDate':'UTC' }}
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_enddate">End Date</th>
                    <td>
                        <ng-container *ngIf="directInternetAccess.contract?.endsAt|default; else notAvailable">
                            {{ directInternetAccess.contract.endsAt|date:'longDate':'UTC' }}
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_connectivity">Connectivity</th>
                    <td>
                        <ng-container i18n="@@bma_common_connectivityseeotherpacks" *ngIf="(directInternetAccess.contract?.networkTraffic.type|default:'') == 'CONNECTIVITY'; else flatfee"> Connectivity - see other packs </ng-container>

                        <ng-template #flatfee>
                            <ng-container *ngIf="(directInternetAccess.contract?.networkTraffic.type|default:'') == 'FLATFEE'; else other">
                                Bandwidth (Flat) -
                                {{ directInternetAccess.contract.networkTraffic.datatrafficLimit }}
                                {{ directInternetAccess.contract.networkTraffic.datatrafficUnit }}
                                ({{ directInternetAccess.contract.networkTraffic.trafficType }})
                            </ng-container>
                        </ng-template>

                        <ng-template #other>
                            <ng-container *ngIf="directInternetAccess.contract?.networkTraffic.type|default; else notAvailable">
                                {{ directInternetAccess.contract.networkTraffic.type }}
                                {{ directInternetAccess.contract.networkTraffic.datatrafficLimit }}
                                {{ directInternetAccess.contract.networkTraffic.datatrafficUnit }}
                                ({{ directInternetAccess.contract.networkTraffic.trafficType }})
                            </ng-container>
                        </ng-template>
                    </td>
                </tr>
                <tr>
                    <th>
                        <span i18n="@@bma_common_sla">SLA</span>
                        <sup>
                            <a href="https://kb.leaseweb.com/support/service-level-agreement-sla" target="_blank">
                                <i i18n-title="@@bma_common_knowledgebase" title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i>
                            </a>
                        </sup>
                    </th>
                    <td>
                        <ng-container *ngIf="directInternetAccess.contract; else notAvailable">
                            {{ directInternetAccess.contract.sla }}
                        </ng-container>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
<ng-template #notAvailable>
    <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
</ng-template>
