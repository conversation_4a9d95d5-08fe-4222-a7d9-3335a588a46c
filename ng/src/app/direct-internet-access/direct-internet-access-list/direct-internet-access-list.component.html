<app-customer-aware-header i18n-caption="@@bma_directinternetaccesslist_caption" caption="Direct Internet Access" [customerId]="customerId" [salesOrgId]="salesOrgId" [isEmployee]="isEmployee"></app-customer-aware-header>

<app-loader *ngIf="isLoading"></app-loader>

<div *ngIf="!isLoading && directInternetAccess">
    <div class="p-grid p-col-12 bg-white p-mb-2 p-py-4 justify-content-center" *ngIf="directInternetAccess.directInternetAccess.length === 0">
        <span i18n="@@bma_directinternetaccesslist_nodirectinternetaccess" *ngIf="isEmployee; else notAvailableForCustomer">No direct internet access found.</span>
        <ng-template #notAvailableForCustomer>
            <span i18n="@@bma_directinternetaccesslist_nodirectinternetaccesscustomer">You don't have direct internet access.</span>
        </ng-template>
    </div>
    <p-accordion [multiple]="true">
        <p-accordionTab *ngFor="let directInternetAccess of directInternetAccess.directInternetAccess">
            <ng-template pTemplate="header">
                <div class="row col-sm-12 align-items-center">
                    <div class="col-sm-3">
                        <h3 class="mt-2">{{ directInternetAccess.id }}</h3>
                    </div>
                    <div class="col-sm-4">
                        <h3 class="mt-2">{{ directInternetAccess?.contract?.id ? directInternetAccess?.contract?.id : '-' }}</h3>
                    </div>
                    <div class="col-sm-3">
                        <h3 class="mt-2">{{ directInternetAccess?.contract?.deliveryStatus ? directInternetAccess?.contract?.deliveryStatus : '-' }}</h3>
                    </div>
                    <div class="col-sm-2">
                        <span class="h5">
                            {{ directInternetAccess?.contract?.reference ? directInternetAccess?.contract?.reference : '-' }}
                        </span>
                    </div>
                </div>
            </ng-template>
            <ng-template pTemplate="content" class="p-p-0">
                <div class="p-grid border-bottom">
                    <div class="p-col-12 p-md-10 border-right">
                        <h5 i18n="@@bma_common_directinternetaccessdetails" class="p-ml-6 pt-2">Direct Internet Access Details</h5>
                        <div class="p-col-12 p-d-flex p-flex-col p-pb-0">
                            <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-2">
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_directinternetaccessid">Direct Internet Access ID</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ directInternetAccess.id }}</div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_ipaddress">IP Address</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <ng-container *ngIf="(directInternetAccess?.networkInterfaces?.public && directInternetAccess?.networkInterfaces?.public?.ip); else notAvailable">
                                            <span class="selectable">{{ directInternetAccess.networkInterfaces.public.ip|default:'-'|cidrToIp }}</span>
                                        </ng-container>
                                    </div>
                                </div>
                            </div>
                            <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-2">
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_contractid">Contract ID</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ directInternetAccess?.contract?.id ? directInternetAccess?.contract?.id : '-' }}</div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_deliverystatus">Delivery Status</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ directInternetAccess?.contract?.deliveryStatus ? directInternetAccess?.contract?.deliveryStatus : '-' }}</div>
                                </div>
                            </div>
                        </div>
                        <ng-template #notAvailable>
                            <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
                        </ng-template>
                    </div>
                    <div class="p-col-12 p-md-2 quick-actions">
                        <h5 i18n="@@bma_common_quickactions" class="p-2">Quick Actions</h5>
                        <div class="p-grid p-flex-column align-items-start">
                            <a pButton i18n-label="@@bma_common_datagraphs" *ngIf="(directInternetAccess?.networkInterfaces?.public|default:'') && (directInternetAccess?.networkInterfaces?.public?.ports|default:''); else notDataGraph" [routerLink]="['/directInternetAccess', directInternetAccess.id, 'graphs']" label="Datagraphs" class="p-button-link"></a>
                            <ng-template #notDataGraph>
                                <button pButton i18n-label="@@bma_common_datagraphs" label="Datagraphs" disabled class="p-button-link"></button>
                            </ng-template>
                        </div>
                    </div>
                </div>
                <div class="p-grid p-pr-5 p-pt-3">
                    <a i18n-label="@@bma_common_manage" pButton [routerLink]="['/directInternetAccess', directInternetAccess.id]" class="p-button-primary ml-auto" label="Manage"></a>
                </div>
            </ng-template>
        </p-accordionTab>
    </p-accordion>
</div>

<div *ngIf="!isLoading && directInternetAccess">
    <app-pagination [totalCount]="directInternetAccess._metadata.totalCount" [limit]="directInternetAccess._metadata.limit" [offset]="directInternetAccess._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
</div>
