<app-customer-aware-header *ngIf="directInternetAccess" caption="Direct Internet Access {{directInternetAccess.id}}" [reference]="directInternetAccess.contract.reference" [customerId]="directInternetAccess.contract.customerId" [salesOrgId]="directInternetAccess.contract.salesOrgId" [isEmployee]="isEmployee"></app-customer-aware-header>

<app-loader *ngIf="isLoading"></app-loader>

<ul class="nav nav-pills mb-4">
    <li class="nav-item dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-usage mr-2"></span>
            <span i18n="@@bma_common_usage">Usage</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a [routerLink]="['graphs']" class="dropdown-item">
                <span class="text-muted fa fa-usage mr-2"></span>
                <span i18n="@@bma_common_graphs">Graphs</span>
            </a>
        </div>
    </li>
</ul>

<ng-container *ngIf="directInternetAccess">
    <router-outlet (activate)="onDirectInternetAccessLoaded($event)"></router-outlet>
</ng-container>
