import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { SiteService } from 'src/app/services/site.service';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-direct-internet-access-tabs',
  templateUrl: './direct-internet-access-tabs.component.html',
  styleUrls: ['./direct-internet-access-tabs.component.css'],
})
export class DirectInternetAccessTabsComponent implements OnInit {
  isLoading = false;
  directInternetAccess: any;
  directInternetAccessId: string;
  isEmployee = false;

  constructor(
    private activatedRoute: ActivatedRoute,
    private titleService: Title,
    private currentUserService: CurrentUserService,
    private httpClient: HttpClient,
    private router: Router,
    private siteService: SiteService
  ) {}

  ngOnInit(): void {
    this.directInternetAccessId = this.activatedRoute.snapshot.url[1].path;
    this.isEmployee = this.currentUserService.isEmployee();

    this.isLoading = true;

    this.httpClient
      .get<any>(`/_/internal/dedicatedserverapi/v2/directInternetAccess/${this.directInternetAccessId}`)
      .subscribe({
        next: (data: any) => {
          this.isLoading = false;
          this.directInternetAccess = data;
          this.titleService.setTitle(
            `${this.directInternetAccess.id} - Direct Internet Access ${
              this.directInternetAccess.contract.reference ? this.directInternetAccess.contract.reference : ''
            }| Leaseweb`
          );

          /*eslint-disable */
          const event = new Event('update_product_navbar');
          event['customerId'] = this.directInternetAccess.contract.customerId;
          event['salesOrgId'] = this.directInternetAccess.contract.salesOrgId;
          event['country'] = this.siteService.getCountry(this.directInternetAccess.contract.salesOrgId);
          window.dispatchEvent(event);
          /*eslint-enable */
        },
        error: (error: any) => {
          this.isLoading = false;
          this.router.navigate(['**']);
        },
      });
  }

  onDirectInternetAccessLoaded(component): void {
    if (this.directInternetAccess) {
      component.equipment = this.directInternetAccess;
      component.directInternetAccess = this.directInternetAccess;
      component.equipmentId = this.directInternetAccess.id;
      component.equipmentType = 'directInternetAccess';
    }
  }
}
