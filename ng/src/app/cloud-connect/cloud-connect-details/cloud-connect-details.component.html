<h1 i18n="@@bma_cloudconnectdetails_header">Cloud Connect Details</h1>
<div class="row">
    <div class="col-md-6">
        <h3 class="mb-2">
            <span class="ssc-icon icon-powerswitch"></span>
            <span i18n="@@bma_common_administrativedetails">Administrative Details</span>
        </h3>

        <app-loader *ngIf="isLoading"></app-loader>
        <table *ngIf="!isLoading && cloudConnect" class="table table-sm">
            <colgroup>
                <col class="table-col-25" />
                <col class="table-col-75" />
            </colgroup>
            <tbody>
                <tr>
                    <th i18n="@@bma_common_contractid">Contract ID</th>
                    <td>{{ cloudConnect.id }}</td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_mastercontractid">Master Contract ID</th>
                    <td>{{ cloudConnect.masterContractId }}</td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_contractstatus">Contract Status</th>
                    <td><app-contract-status [status]="cloudConnect.contractStatus|default:'UNASSIGNED'"></app-contract-status></td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_contractstartdate">Contract Start Date</th>
                    <td>{{ cloudConnect.contractStartDate|lswDate }}</td>
                </tr>
                <tr>
                    <th><span i18n="@@bma_common_contractterm">Contract Term</span> (<span i18n="@@bma_pipes_month:months">months</span>)</th>
                    <td>{{ cloudConnect.contractTerm }}</td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_billingcycle">Billing Cycle</th>
                    <td>{{ cloudConnect.billingCycle|default }}</td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_billingfrequency">Billing Frequency</th>
                    <td>{{ cloudConnect.billingFrequency|default | lowercase }}(s)</td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_billingdate">Billing Date</th>
                    <td>{{ cloudConnect.billingDate}}</td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_nextbillingdate">Next Billing Date</th>
                    <td>{{ cloudConnect.nextBillingDate|lswDate|default: '-' }}</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="col-md-6">
        <h3 class="mb-2">
            <span i18n="@@bma_common_technicaldetails">Technical Details</span>
        </h3>

        <table *ngIf="!isLoading && cloudConnect" class="table table-sm">
            <colgroup>
                <col class="table-col-25" />
                <col class="table-col-75" />
            </colgroup>
            <tbody>
                <tr>
                    <th i18n="@@bma_common_provider">Provider</th>
                    <td>{{ cloudConnect.provider }}</td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_providermodel">Provider Model</th>
                    <td>{{ cloudConnect.providerModel }}</td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_metro">Metro</th>
                    <td>{{ cloudConnect.metro }}</td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_metrozone">Metro Zone</th>
                    <td>{{ cloudConnect.metroZone }}</td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_connectionspeed">Connection Speed</th>
                    <td>{{ cloudConnect.connectionSpeed }}</td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_connectionspeednum">Connection Speed (Mbps)</th>
                    <td>{{ cloudConnect.connectionSpeedNum }}</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<ng-template #notAvailable>
    <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
</ng-template>
