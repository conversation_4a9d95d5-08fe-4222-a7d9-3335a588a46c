import { Component, OnInit, Input } from '@angular/core';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { ActivatedRoute } from '@angular/router';
import { UntypedFormGroup } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { SiteService } from '../../services/site.service';

@Component({
  selector: 'app-cloud-connect-details',
  templateUrl: './cloud-connect-details.component.html',
  styleUrls: ['./cloud-connect-details.component.css'],
})
export class CloudConnectDetailsComponent implements OnInit {
  @Input() ipTransit: any;
  @Input() cloudConnect: any;

  filters: UntypedFormGroup;

  isEmployee: boolean;
  isLoading: boolean;

  dynamicDialogRef: DynamicDialogRef;
  networkPorts = [];

  constructor(
    private currentUserService: CurrentUserService,
    private httpClient: HttpClient,
    private siteService: SiteService,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    const cloudConnectId = this.route.snapshot.paramMap.get('cloudConnectId');

    if (this.isEmployee) {
      /*eslint-disable */
      const event = new Event('update_product_navbar');
      event['customerId'] = this.filters.get('customerId').value;
      event['salesOrgId'] = this.filters.get('salesOrgId').value;
      event['country'] = this.siteService.getCountry(this.filters.get('salesOrgId').value);
      window.dispatchEvent(event);
      /*eslint-enable */
    }

    this.isLoading = true;

    this.httpClient.get<any>(`/_/internal/dedicatedserverapi/v2/cloudConnects/${cloudConnectId}`).subscribe(
      (data: any) => {
        this.cloudConnect = data;
        this.isLoading = false;
      },
      () => {
        this.isLoading = false;
      }
    );
  }
}
