<div class="row">
    <div class="col-8">
        <app-customer-aware-header caption="Cloud Connect" [customerId]="filters.get('customerId').value" [salesOrgId]="filters.get('salesOrgId').value" [isEmployee]="isEmployee" knowledgeBaseLink="https://kb.leaseweb.com/kb/cloud-connect/cloud-connect-overview/"></app-customer-aware-header>
    </div>
    <div class="col-4" *ngIf="!isEmployee">
        <div class="float-right mb-3">
            <button i18n-label="@@bma_common_ordercloudconnect" pButton type="button" label="Order Cloud Connect" (click)="openCloudConnectOrderModal()" class="p-button-success pull-right"></button>
        </div>
    </div>
    <div class="col-12">
        <div class="card p-grid p-col-12 bg-white mb-5 mt-2 ml-0">
            <div class="ml-2" i18n="@@bma_privatenetworkcloudconnectlist_whatis">
                For more information on Cloud Connect, please visit our
                <a class="text-primary" href="https://www.leaseweb.com/en/products-services/network/cloud-connect">Website</a>
            </div>
        </div>
    </div>
</div>

<app-loader *ngIf="isLoading"></app-loader>

<div *ngIf="!isLoading && cloudConnectsData">
    <div class="cloud-connect-list">
        <div class="p-col-12 bg-white p-mb-2 p-py-4 text-center" *ngIf="cloudConnectsData.cloudConnects.length === 0">
            <div class="ml-2" i18n="@@bma_privatenetworkcloudconnectlist_whatis">
                For more information on Cloud Connect, please visit our
                <a class="text-primary" href="https://www.leaseweb.com/en/products-services/network/cloud-connect">Website</a>
            </div>
            <span i18n="@@bma_cloudconnectlist_nocloudconnects" *ngIf="isEmployee; else notAvailableForCustomer">No cloud connects found.</span>
            <ng-template #notAvailableForCustomer>
                <span i18n="@@bma_cloudconnectlist_nocloudconnectscustomer">You don't have cloud connects.</span>
            </ng-template>
        </div>
        <p-accordion [multiple]="true">
            <p-accordionTab *ngFor="let cloudConnect of cloudConnectsData.cloudConnects">
                <ng-template pTemplate="header">
                    <div class="row col-sm-12 align-items-center">
                        <div class="col-sm-3" id="cloud-connect-identification-container">
                            <h4 class="mt-2 cloud-connect-identification">
                                {{ cloudConnect.id }}
                            </h4>
                        </div>
                        <div class="col-sm-5">
                            <span class="h5">
                                <strong>{{ cloudConnect.contract.provider|default:'-' }}</strong>
                            </span>
                        </div>
                        <div class="col-sm-4">
                            <span class="h5">
                                <app-contract-status [status]="cloudConnect.contract.contractStatus|default:'UNASSIGNED'"></app-contract-status>
                            </span>
                        </div>
                    </div>
                </ng-template>
                <ng-template pTemplate="content" class="p-p-0">
                    <div class="p-grid border-bottom">
                        <div class="p-col-12 p-md-12 border-right">
                            <h5 class="p-ml-6 pt-2" i18n="@@bma_common_cloudconnectdetails">Cloud Connect Details</h5>
                            <div class="p-col-12 p-d-flex p-flex-col p-pb-0">
                                <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-6">
                                    <div class="p-grid grid-row">
                                        <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_contractid">Contract ID</strong></div>
                                        <div class="p-col-8 grid-content grid-content-border">{{ cloudConnect.id }}</div>
                                    </div>
                                    <div class="p-grid grid-row">
                                        <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_mastercontractid">Master Contract ID</strong></div>
                                        <div class="p-col-8 grid-content grid-content-border">{{ cloudConnect.contract.masterContractId }}</div>
                                    </div>
                                    <div class="p-grid grid-row">
                                        <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_contractstatus">Contract Status</strong></div>
                                        <div class="p-col-8 grid-content grid-content-border"><app-contract-status [status]="cloudConnect.contract.contractStatus|default:'UNASSIGNED'"></app-contract-status></div>
                                    </div>
                                </div>
                                <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-6">
                                    <div class="p-grid grid-row">
                                        <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_provider">Provider</strong></div>
                                        <div class="p-col-8 grid-content grid-content-border">{{ cloudConnect.contract.provider }}</div>
                                    </div>
                                    <div class="p-grid grid-row">
                                        <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_metro">Metro</strong></div>
                                        <div class="p-col-8 grid-content grid-content-border">{{ cloudConnect.contract.metro }}</div>
                                    </div>
                                    <div class="p-grid grid-row">
                                        <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_connectionspeed">Connection Speed</strong></div>
                                        <div class="p-col-8 grid-content grid-content-border">{{ cloudConnect.contract.connectionSpeed }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="p-grid p-pr-5 p-pt-3">
                        <a i18n-label="@@bma_common_manage" pButton [routerLink]="['/cloud-connects', cloudConnect.id]" class="p-button-primary ml-auto" label="Manage"></a>
                    </div>
                </ng-template>
            </p-accordionTab>
        </p-accordion>
    </div>
</div>

<div class="row mb-3" *ngIf="!isLoading && cloudConnectsData">
    <div class="col-12">
        <app-pagination [totalCount]="cloudConnectsData._metadata.totalCount" [limit]="cloudConnectsData._metadata.limit" [offset]="cloudConnectsData._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>
