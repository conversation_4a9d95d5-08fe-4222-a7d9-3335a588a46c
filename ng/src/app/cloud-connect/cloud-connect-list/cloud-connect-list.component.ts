import { ActivatedRoute, Router } from '@angular/router';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { Component, OnInit } from '@angular/core';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { Title } from '@angular/platform-browser';
import { SiteService } from 'src/app/services/site.service';
import { AlertService } from 'src/app/services/alert.service';
import { CloudConnectOrderModalComponent } from '../cloud-connect-order/cloud-connect-order-modal.component';

@Component({
  selector: 'app-cloud-connect-list',
  templateUrl: './cloud-connect-list.component.html',
  styleUrls: ['./cloud-connect-list.component.css'],
})
export class CloudConnectListComponent implements OnInit {
  customerId = '';
  salesOrgId = '';
  headerTitle = '';

  isLoading: boolean;
  isEmployee: boolean;

  filters: UntypedFormGroup;
  dynamicDialogRef: DynamicDialogRef;

  cloudConnectsData: any;

  constructor(
    private activatedRoute: ActivatedRoute,
    private alertService: AlertService,
    private modalService: ModalService,
    private currentUserService: CurrentUserService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private router: Router,
    private siteService: SiteService,
    private titleService: Title
  ) {}

  ngOnInit(): void {
    this.headerTitle = 'Cloud Connect';
    this.isEmployee = this.currentUserService.isEmployee();

    this.filters = this.formBuilder.group({
      limit: [null],
      offset: [null],
      site: [null],
      equipmentType: [null],
      detectionProfile: [null],
      customerId: [null],
      salesOrgId: [null],
    });

    this.alertService.clear();
    if (!this.isEmployee) {
      this.customerId = this.currentUserService.getCustomerId();
      this.salesOrgId = this.currentUserService.getSalesOrgId();
    }

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
    if (this.customerId) {
      this.headerTitle = this.headerTitle + ' | Customer ' + this.customerId + ' | Leaseweb';
    }

    this.titleService.setTitle(this.headerTitle);
  }

  onQueryParamsChanged(queryParams): void {
    this.filters.reset(queryParams);

    const params = this.sanitise(this.filters.getRawValue());

    if (this.isEmployee) {
      /*eslint-disable */
      const event = new Event('update_product_navbar');
      event['customerId'] = this.filters.get('customerId').value;
      event['salesOrgId'] = this.filters.get('salesOrgId').value;
      event['country'] = this.siteService.getCountry(this.filters.get('salesOrgId').value);
      window.dispatchEvent(event);
      /*eslint-enable */
    }

    this.isLoading = true;

    this.httpClient.get<any>('/_/internal/dedicatedserverapi/v2/cloudConnects', { params }).subscribe(
      (data: any) => {
        this.cloudConnectsData = data;
        this.isLoading = false;
      },
      () => {
        this.isLoading = false;
      }
    );
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }

  openCloudConnectOrderModal(): void {
    this.dynamicDialogRef = this.modalService.show(CloudConnectOrderModalComponent, {
      privateNetwork: {
        customerId: this.customerId,
        salesOrgId: this.salesOrgId,
      },
    });
  }
}
