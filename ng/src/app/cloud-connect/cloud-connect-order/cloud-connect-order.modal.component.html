<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '800px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_common_ordercloudconnect" class="modal-title">Order Cloud Connect</h3>
    </ng-template>

    <form [formGroup]="form" (ngSubmit)="orderCloudConnect()">
        <div class="modal-body">
            <div class="ml-3">
                <h4 i18n="@@bma_cloudconnect_order_description" class="row">The Cloud Connect service allows you to connect this product to other Leaseweb products and services.</h4>
            </div>

            <div class="form-group">
                <label i18n="@@bma_cloudconnect_order_publiccloudprovider" for="publicCloudProvider" class="col-form-label">Public Cloud provider</label>
                <select class="form-control" formControlName="publicCloudProvider" id="publicCloudProvider" autofocus>
                    <option i18n="@@bma_cloudconnect_order_publiccloudprovider" value="null">Public Cloud provider</option>
                    <option i18n="@@bma_cloudconnect_order_aws" value="AWS">AWS</option>
                    <option i18n="@@bma_cloudconnect_order_gcp" value="GCP">GCP</option>
                    <option i18n="@@bma_cloudconnect_order_azure" value="Azure">Azure</option>
                </select>
            </div>

            <div class="form-group">
                <label i18n="@@bma_common_selectuplinkcapacity" for="uplinkCapacity" class="col-form-label">Select an uplink capacity</label>
                <ng-container *ngIf="uplinkCapacities && uplinkCapacities.length > 0">
                    <select class="form-control" formControlName="uplinkCapacity" id="uplinkCapacity" autofocus>
                        <option i18n="@@bma_common_selectuplinkcapacity" value="null">Select an uplink capacity</option>
                        <ng-container *ngFor="let speed of uplinkCapacities">
                            <option value="{{speed}}">{{speed}}</option>
                        </ng-container>
                    </select>
                </ng-container>
                <ng-container *ngIf="!uplinkCapacities || objectKeys(uplinkCapacities).length === 0">
                    <p>No uplink capacities available.</p>
                </ng-container>
            </div>
            <div class="form-group">
                <label i18n="@@bma_cloudconnect_order_sidea" for="sidea" class="col-form-label">Side A</label>
                <select class="form-control" formControlName="sidea" id="sidea">
                    <option i18n="@@bma_cloudconnect_order_sidea" value="null">Side A</option>
                    <option value="AMS">AMS</option>
                    <option value="FRA">FRA</option>
                    <option value="LON">LON</option>
                    <option value="WDC">WDC</option>
                    <option value="SIN">SIN</option>
                    <option value="MTL">MTL</option>
                </select>
            </div>
            <div class="form-group">
                <label i18n="@@bma_cloudconnect_order_sideb" for="sideb" class="col-form-label">Side B</label>
                <input i18n-placeholder="@@bma_cloudconnect_order_sideb" pInputText type="text" class="form-control keyword" formControlName="sideb" placeholder="Side B" id="sideb" autofocus />
            </div>

            <div class="form-group" *ngIf="form.get('publicCloudProvider').value === 'AWS'">
                <label i18n="@@bma_cloudconnect_order_connectivitytype" for="awsHostedType" class="col-form-label">Connectivity Type</label>
                <select class="form-control" formControlName="awsHostedType" id="awsHostedType">
                    <option i18n="@@bma_cloudconnect_order_connectivitytype" value="null">Connectivity Type</option>
                    <option i18n="@@bma_cloudconnect_order_aws_hosted_vif" value="Hosted VIF">Hosted VIF</option>
                    <option i18n="@@bma_cloudconnect_order_aws_hosted_connection" value="Hosted Connection">Hosted Connection</option>
                </select>
            </div>

            <div class="ml-4 mt-3">
                <div class="row mt-3">
                    <small>
                        <span i18n-title="@@bma_common_kb" title="Knowledge Base" class="fa fa-knowledge mr-1" aria-hidden="true"></span>
                        <span i18n="@@bma_equipment_cloudconnectordrmodal_kb">For more information about Cloud Connect please visit our <a target="_blank" href="https://www.leaseweb.com/en/products-services/network/cloud-connect">KB page</a>.</span>
                    </small>
                </div>
            </div>
        </div>
    </form>
    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_cancel" pButton type="button" icon="pi pi-times" iconPos="left" label="Cancel" class="p-button-secondary" (click)="closeModal()"></button>
            <button i18n-label="@@bma_common_submit" pButton (click)="orderCloudConnect()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" [disabled]="!form.valid || form?.errors?.['ConnectionTypeValidatorError'] === true" label="Submit" class="p-button-success"></button>
        </div>
    </ng-template>
</p-dialog>
