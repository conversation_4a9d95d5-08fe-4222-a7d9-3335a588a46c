import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { PrivateNetworkService } from 'src/app/services/private-network.service';
import { AbstractControl, ValidationErrors, ValidatorFn, AbstractControlOptions } from '@angular/forms';

export const ConnectionTypeValidator: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {
  const publicCloudProvider = control.get('publicCloudProvider');
  const awsHostedType = control.get('awsHostedType');
  if (publicCloudProvider.value === 'AWS' && !awsHostedType.value) {
    return {
      ConnectionTypeValidatorError: true,
    };
  }
  return null;
};

@Component({
  selector: 'app-cloud-connect-order-modal',
  templateUrl: './cloud-connect-order.modal.component.html',
  styleUrls: ['./cloud-connect-order-modal.component.css'],
})
export class CloudConnectOrderModalComponent implements OnInit {
  customerId: any;
  salesOrgId: any;
  isSubmitting = false;
  uplinkCapacities = null;
  showDialog = true;
  form: UntypedFormGroup;

  constructor(
    private alertService: AlertService,
    private dynamicDialogRef: DynamicDialogRef,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private privateNetworkService: PrivateNetworkService,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.customerId = this.dynamicDialogConfig.data.customerId;
    this.salesOrgId = this.dynamicDialogConfig.data.salesOrgId;
    this.uplinkCapacities = this.privateNetworkService.getCloudConnectPortSpeeds();
    this.form = this.formBuilder.group(
      {
        uplinkCapacity: [null, Validators.required],
        sidea: [null, Validators.required],
        sideb: [null, Validators.required],
        publicCloudProvider: [null, Validators.required],
        awsHostedType: [null],
      },
      { validators: ConnectionTypeValidator } as AbstractControlOptions
    );
  }

  orderCloudConnect(): void {
    this.isSubmitting = true;
    this.alertService.clear();
    const body = {
      uplinkCapacity: this.form.get('uplinkCapacity').value,
      sidea: this.form.get('sidea').value,
      sideb: this.form.get('sideb').value,
      publicCloudProvider: this.form.get('publicCloudProvider').value,
      awsHostedType: this.form.get('awsHostedType').value,
    };

    this.httpClient.post<any>(`/_legacy/cloudConnect/order`, body).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;
        this.alertService.alert(
          {
            type: 'success',
            description: data.message,
          },
          true
        );
        this.closeModal();
      },
      error: (error: any) => {
        this.isSubmitting = false;
        this.alertService.alert(
          {
            type: 'error',
            description: error.error.error,
          },
          true
        );
        this.closeModal();
      },
    });
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }

  objectKeys(obj: any): string[] {
    return obj ? Object.keys(obj) : [];
  }
}
