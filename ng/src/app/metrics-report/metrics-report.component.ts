import { Component, OnInit, ElementRef } from '@angular/core';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { ActivatedRoute } from '@angular/router';
import { SiteService } from 'src/app/services/site.service';
import { DateFormatterService } from 'src/app/services/date-formatter.service';

@Component({
  selector: 'app-metrics-report',
  templateUrl: './metrics-report.component.html',
  styleUrls: ['./metrics-report.component.css'],
})
export class MetricsReportComponent implements OnInit {
  customerId = '';
  salesOrgId = '';
  from = '';
  to = '';
  isEmployee = false;

  constructor(
    private activatedRoute: ActivatedRoute,
    private elRef: ElementRef,
    private currentUserService: CurrentUserService,
    private siteService: SiteService,
    private dateFormatterService: DateFormatterService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    if (queryParams.customerId) {
      this.customerId = queryParams.customerId;
    }
    if (queryParams.salesOrgId) {
      this.salesOrgId = queryParams.salesOrgId;
    }
    if (queryParams.from) {
      this.from = queryParams.from;
    } else {
      this.from = this.dateFormatterService.startOfMonth().format();
    }
    if (queryParams.to) {
      this.to = queryParams.to;
    } else {
      this.dateFormatterService.addMonths(1).startOfMonth().format();
    }

    /*eslint-disable */
    if (queryParams.salesOrgId && queryParams.customerId) {
      const event = new Event('update_product_navbar');
      event['customerId'] = queryParams.customerId;
      event['salesOrgId'] = queryParams.salesOrgId;
      event['country'] = this.siteService.getCountry(queryParams.salesOrgId);
      window.dispatchEvent(event);
    }
    /*eslint-enable */
  }

  download() {
    const format = this.elRef.nativeElement.querySelector('#metrics_download_format').value;
    const granularity = this.elRef.nativeElement.querySelector('#metrics_download_frequency').value;

    let url = `${
      this.isEmployee ? '/emp' : '/bare-metals'
    }/metrics/reports/graphs/datatraffic?format=${format}&granularity=${granularity}`;

    if (this.isEmployee) {
      url += `&customerId=${this.customerId}&salesOrgId=${this.salesOrgId}`;
    }
    if (this.from) {
      url += `&from=${this.from}`;
    }
    if (this.to) {
      url += `&to=${this.to}`;
    }

    window.location.href = url;
  }
}
