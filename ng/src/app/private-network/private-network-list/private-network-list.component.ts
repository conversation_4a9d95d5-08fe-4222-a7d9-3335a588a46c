import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { SiteService } from 'src/app/services/site.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';

@Component({
  selector: 'app-private-network-list',
  templateUrl: './private-network-list.component.html',
  styleUrls: ['./private-network-list.component.css'],
})
export class PrivateNetworkListComponent implements OnInit {
  privateNetworks: any;
  isLoading = false;
  error = false;
  metros: string[];
  filters: UntypedFormGroup;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private siteService: SiteService,
    private titleService: Title
  ) {}

  ngOnInit(): void {
    this.filters = this.formBuilder.group({
      offset: [0],
      limit: [10],
      filter: [null],
      metro: [null],
      vlan: [null],
    });

    this.titleService.setTitle($localize`:@@bma_common_privatenetworks:Private Networks` + ' | Leaseweb');

    this.metros = this.siteService.metro();
    this.router.routeReuseStrategy.shouldReuseRoute = () => false;

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    this.isLoading = true;
    this.filters.reset(queryParams);
    const params = this.sanitise(this.filters.getRawValue());
    this.httpClient.get('/_/internal/nseapi/v2/privateNetworks?hideEmptyPrivateNetworks=true', { params }).subscribe(
      (data: any) => {
        this.isLoading = false;
        this.error = false;
        this.privateNetworks = data;
      },
      (error: any) => {
        this.isLoading = false;
        this.error = true;
        this.privateNetworks = { _metadata: { totalCount: 0 }, privateNetworks: [] };
      }
    );
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  clearFilters(): void {
    this.filters.reset();
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
    });
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
