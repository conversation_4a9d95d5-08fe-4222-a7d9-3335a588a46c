<div class="row mb-4">
    <div class="col">
        <h1 i18n="@@bma_privatenetworklist_title">
            Private Networks
            <small class="text-muted"> with at least one server </small>
        </h1>
    </div>
</div>

<form role="form" [formGroup]="filters" (ngSubmit)="submitFilters()">
    <div class="md-form mb-3">
        <input type="text" id="filter" formControlName="filter" class="form-control m-0" value="" i18n-placeholder="@@bma_common_filter" placeholder="Filter..." autofocus />
    </div>

    <div class="row">
        <div class="col-3">
            <select formControlName="metro" i18n-label="@@bma_common_metro" label="Metro" class="form-control">
                <option i18n="@@bma_common_metro" value="null">Metro</option>
                <option *ngFor="let metro of metros" [value]="metro">{{ metro }}</option>
            </select>
        </div>

        <div class="col-3">
            <input type="text" id="vlan" class="form-control m-0" placeholder="Vlan" formControlName="vlan" />
        </div>
    </div>

    <div class="row mb-3 mt-3">
        <div class="col text-right">
            <button pButton (click)="clearFilters()" i18n-label="@@bma_common_reset" label="Reset" icon="pi pi-refresh" type="button" class="p-button-secondary mr-2"></button>
            <button pButton [disabled]="isLoading" i18n-label="@@bma_common_search" label="Search" icon="pi pi-search"></button>
        </div>
    </div>
</form>

<app-loader *ngIf="isLoading"></app-loader>

<div class="table-responsive" *ngIf="!isLoading && privateNetworks">
    <table class="table table-striped network-device-table">
        <thead>
            <tr>
                <th scope="col" class="text-center table-col-size-1" i18n="@@bma_common_id">ID</th>
                <th scope="col" class="table-col-size-4" i18n="@@bma_common_customer">Customer</th>
                <th scope="col" class="text-center table-col-size-2" i18n="@@bma_privatenetworklist_metrovlan">Metro - VLAN</th>
                <th scope="col" class="text-center table-col-size-2" i18n="@@bma_common_created">Created</th>
                <th scope="col" class="text-center table-col-size-1" i18n="@@bma_common_servers">Servers</th>
                <th scope="col" class="text-center table-col-size-1" i18n="@@bma_common_dhcp">DHCP</th>
                <th scope="col" class="table-col-size-1">&nbsp;</th>
            </tr>
        </thead>

        <tbody>
            <tr *ngIf="privateNetworks.privateNetworks.length === 0 && !error">
                <td colspan="99" class="text-center p-4" i18n="@@bma_privatenetworklist_noprivatenetworks">No private networks found.</td>
            </tr>
            <tr *ngIf="error">
                <td i18n="@@bma_common_errorfetchingdata" colspan="99" class="text-center p-4 text-danger">Error fetching the data</td>
            </tr>
            <tr *ngFor="let privateNetwork of privateNetworks.privateNetworks">
                <td class="text-center text-nowrap">
                    <a [routerLink]="['/privateNetworks', privateNetwork.id]">{{ privateNetwork.id }}</a>
                </td>

                <td>
                    <app-country-flag salesOrgId="{{ privateNetwork.salesOrgId }}"></app-country-flag>

                    <span class="text-monospace">
                        {{ privateNetwork.customerId }}
                    </span>
                </td>

                <td class="text-center text-nowrap">
                    <ul class="list-unstyled mb-0">
                        <li *ngFor="let vlan of privateNetwork.vlans" class="text-monospace">{{ vlan.metro }} - {{ vlan.vlanId }}</li>
                    </ul>
                </td>

                <td class="text-center text-nowrap">
                    {{ privateNetwork.createdAt|timeAgo }}<br />
                    <span class="text-muted">
                        {{ privateNetwork.createdAt | lswDate }}
                    </span>
                </td>

                <td class="text-center">
                    {{ privateNetwork.equipmentCount }}
                </td>

                <td class="text-center text-nowrap">
                    <span class="btn py-0 disabled" [ngClass]="{'btn-outline-success': 'ENABLED' == privateNetwork.dhcp | default:'','btn-outline-danger': 'ENABLED' != privateNetwork.dhcp | default:''}">
                        <small>
                            {{ privateNetwork.dhcp | uppercase }}
                        </small>
                    </span>
                </td>

                <td class="text-right">
                    <a [routerLink]="['/servers']" [queryParams]="{customerId: privateNetwork.customerId, salesOrgId: privateNetwork.salesOrgId}" class="mr-1" i18n-title="@@bma_common_dedicatdservers" title="Dedicated Servers">
                        <i class="fa fa-server fa-lg"></i>
                    </a>

                    <a [routerLink]="['/privateNetworks', privateNetwork.id]" class="mr-1" i18n-title="@@bma_common_manage" title="Manage">
                        <i class="fa fa-privatenetwork fa-lg"></i>
                    </a>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<div *ngIf="!isLoading && privateNetworks">
    <app-pagination [totalCount]="privateNetworks._metadata.totalCount" [limit]="privateNetworks._metadata.limit" [offset]="privateNetworks._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
</div>
