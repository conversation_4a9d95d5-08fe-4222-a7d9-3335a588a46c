<div *ngIf="!isEmployee" class="alert alert-info d-flex" role="alert">
    <div class="float-left">
        <span class="fa fa-information"></span>
    </div>
    <div class="float-left ml-2">
        <p class="text-dark" i18n="@@bma_privatenetworkserverlist_description1">If you expected more dedicated servers to be available for adding to a private network, it might be that some are not prepared for private network yet.</p>
        <p class="text-dark" i18n="@@bma_privatenetworkserverlist_description2">If you would like to acquire dedicated servers with private network enabled, please <a href="/tickets/new?category=sales&subCategory=buy&subject=Add%20Private%20Network%20on%20Dedicated%20Servers&message=I%20would%20like%20to%20add%20private%20network%20on%20my%20dedicated%20servers:%20<write_here>" class="second">contact your sales representative</a>. To speed up the process please make sure to mention the dedicated servers you would like to add to private network in the ticket to your sales representative.</p>
    </div>
    <div class="clear"></div>
</div>

<form [formGroup]="filters" (ngSubmit)="submitFilters()" role="form">
    <div class="row mt-4 mb-4">
        <div class="col-6">
            <input type="text" class="form-control" formControlName="filter" i18n-placeholder="@@bma_common_searchplaceholder" placeholder="Filter for ID, IP address, MAC address, Contract ID, or reference" autofocus />
        </div>
        <div class="col-3">
            <button pButton [disabled]="isLoading" i18n-label="@@bma_common_search" label="Search" icon="pi pi-search"></button>
        </div>
        <div class="col-3 text-right">
            <a class="btn btn-success" href (click)="openPrivateNetworkAddServerModal(); false" i18n="@@bma_common_addtoprivatenetwork">Add to Private Network</a>
        </div>
    </div>
</form>

<div class="row mt-4 mb-4">
    <div class="col-md-12">
        <ul class="list-unstyled">
            <li class="font-weight-bold" i18n="@@bma_privatenetworkserverlist_tablelist">The table lists Dedicated Servers that are already associated with Private Network.</li>
        </ul>
    </div>
</div>

<app-loader *ngIf="isLoading"></app-loader>

<div *ngIf="!isLoading && servers">
    <div *ngIf="!isLoading && servers" class="private-network-server-list">
        <div class="p-grid p-col-12 bg-white p-mb-2 p-py-4 justify-content-center" i18n="@@bma_privatenetworkserverlist_noservers" *ngIf="servers.servers.length === 0">No Servers found</div>
        <p-accordion [multiple]="true">
            <p-accordionTab *ngFor="let server of servers.servers">
                <ng-template pTemplate="header">
                    <div class="row col-sm-12 align-items-center">
                        <div class="col-sm-2" id="server-identification-container">
                            <h4 class="mt-2 server-identification">
                                <i *ngIf="isEmployee" title="{{server.rack?.type}} RACK" [ngClass]="server.rack?.type == 'DEDICATED' ? 'fa fa-lg fa-privaterack' : 'fa fa-lg  fa-users'" aria-hidden="true"></i>
                                {{ server.id }}
                            </h4>
                        </div>
                        <div class="col-sm-3">
                            <span class="h5">
                                <strong>{{ server.contract.reference|default:'-' }}</strong>
                            </span>
                        </div>
                        <div class="col-sm-3">
                            <span class="h5">
                                <app-equipment-location [location]="server.location"></app-equipment-location>
                            </span>
                        </div>
                        <div class="col-sm-1">
                            <span class="h5">
                                <span *ngIf="server.privateNetworks.length > 0">{{ server.privateNetworks[0].linkSpeed|formatSpeed }}</span>
                                <span *ngIf="server.privateNetworks.length === 0" class="text-muted"> - </span>
                            </span>
                        </div>
                        <div class="col-sm-2 text-center">
                            <span class="subnet-details">
                                <div *ngIf="server.privateNetworks.length > 0">
                                    <span *ngIf="privateNetwork.dhcp === 'DISABLED'" i18n="@@bma_common_dhcpdisabled" class="text-muted">DHCP disabled</span>
                                    <span *ngIf="privateNetwork.dhcp === 'ENABLED'" class="text-monospace subnet-address">{{ server.privateNetworks[0].subnet }}</span>
                                    <br />
                                    <span class="text-monospace vlan-id">{{ server.privateNetworks[0].vlanId }}</span>
                                </div>
                                <span *ngIf="server.privateNetworks.length === 0" class="text-muted"> - </span>
                            </span>
                        </div>
                        <div class="col-sm-1">
                            <span *ngIf="server.privateNetworks.length > 0" class="badge" [class.badge-danger-outline]="server.privateNetworks[0].status === 'ERROR'" [class.badge-green-outline]="server.privateNetworks[0].status === 'CONFIGURED'" [class.badge-warning-outline]="server.privateNetworks[0].status === 'CONFIGURING' || server.privateNetworks[0].status === 'REMOVING'">{{ server.privateNetworks[0].status }}</span>
                            <span *ngIf="server.privateNetworks.length === 0" class="text-muted"> - </span>
                        </div>
                    </div>
                </ng-template>
                <ng-template pTemplate="content" class="p-p-0">
                    <div class="p-grid border-bottom">
                        <div class="p-col-12 p-md-10 border-right">
                            <h5 class="p-ml-6 pt-2" i18n="@@bma_common_serverdetails">Server Details</h5>
                            <div class="p-col-12 p-d-flex p-flex-col p-pb-0">
                                <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-2">
                                    <div class="p-grid grid-row">
                                        <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_serverid">Server ID</strong></div>
                                        <div class="p-col-8 grid-content grid-content-border">{{ server.id }}</div>
                                    </div>
                                    <div class="p-grid grid-row">
                                        <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_reference">Reference</strong></div>
                                        <div class="p-col-8 grid-content grid-content-border">{{ server.contract.reference|default:'-' }}</div>
                                    </div>
                                    <div class="p-grid grid-row">
                                        <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_ipaddress">IP Address</strong></div>
                                        <div class="p-col-8 grid-content grid-content-border">
                                            <ng-container *ngIf="(server?.networkInterfaces?.internal && server?.networkInterfaces?.internal?.ip); else notAvailable">
                                                <span class="selectable">{{ server.networkInterfaces.internal.ip|default:'-'|cidrToIp }}</span>
                                            </ng-container>
                                        </div>
                                    </div>
                                    <div class="p-grid grid-row">
                                        <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_location">Location</strong></div>
                                        <div class="p-col-8 grid-content grid-content-border">
                                            <app-equipment-location [location]="server.location"></app-equipment-location>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-5">
                                    <div class="p-grid grid-row">
                                        <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_vlanid">Vlan ID</strong></div>
                                        <div class="p-col-8 grid-content grid-content-border">
                                            <span *ngIf="server.privateNetworks.length > 0" class="text-monospace vlan-id">{{ server.privateNetworks[0].vlanId }}</span>
                                            <span *ngIf="server.privateNetworks.length === 0" class="text-muted"> - </span>
                                        </div>
                                    </div>
                                    <div class="p-grid grid-row">
                                        <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_subnet">Subnet</strong></div>
                                        <div class="p-col-8 grid-content grid-content-border">
                                            <span *ngIf="server.privateNetworks.length > 0 && privateNetwork.dhcp === 'ENABLED'">{{ server.privateNetworks[0].subnet }}</span>
                                            <span *ngIf="server.privateNetworks.length > 0 && privateNetwork.dhcp === 'DISABLED'" i18n="@@bma_common_dhcpdisabled" class="text-muted">DHCP disabled</span>
                                            <span *ngIf="server.privateNetworks.length === 0" class="text-muted"> - </span>
                                        </div>
                                    </div>
                                    <div class="p-grid grid-row">
                                        <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_portspeed">Port Speed</strong></div>
                                        <div class="p-col-8 grid-content grid-content-border">
                                            <span *ngIf="server.privateNetworks.length > 0">{{ server.privateNetworks[0].linkSpeed|formatSpeed }}</span
                                            >&nbsp;&nbsp;
                                            <span (click)="openPrivateNetworkModifyServerModal(server); false" *ngIf="serverCanBeModified(server); else noModify">
                                                <a href="javascript:void(0);" class="text-primary" i18n-label="@@bma_common_modify"><b>Modify</b></a>
                                            </span>
                                            <ng-template #noModify>
                                                <a class="text-muted" href="javascript:void(0);" i18n-label="@@bma_common_modify"><b>Modify</b></a>
                                            </ng-template>
                                            <span *ngIf="server.privateNetworks.length === 0" class="text-muted"> - </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="p-col-12 p-py-0" *ngIf="isEmployee">
                                <div class="p-col-12 p-py-0 p-pl-6 p-pr-2">
                                    <app-equipment-switch-port-status class="p-grid p-col-12" [equipment]="server" [equipmentType]="equipmentType" networkInterfaceType="internal"></app-equipment-switch-port-status>
                                </div>
                            </div>
                            <ng-template #notAvailable>
                                <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
                            </ng-template>
                        </div>
                        <div class="p-col-12 p-md-2 quick-actions">
                            <h5 class="p-2">Quick Actions</h5>
                            <div class="p-grid p-flex-column align-items-start">
                                <a pButton *ngIf="(server?.networkInterfaces?.internal|default:'') && (server?.networkInterfaces?.internal?.ports|default:''); else notDataGraph" [routerLink]="['/servers', server.id, 'graphs', 'privateNetwork']" i18n-label="@@bma_common_datagraphs" label="Datagraphs" class="p-button-link"></a>
                                <ng-template #notDataGraph>
                                    <button pButton i18n-label="@@bma_common_datagraphs" label="Datagraphs" disabled class="p-button-link"></button>
                                </ng-template>

                                <button pButton *ngIf="server.featureAvailability.remoteManagement; else noRemoteManagement" (click)="openRemoteManagementModal(server.id)" i18n-label="@@bma_common_remotemanagement" label="Remote Management" class="p-button-link"></button>
                                <ng-template #noRemoteManagement>
                                    <button pButton i18n-label="@@bma_common_remotemanagement" label="Remote Management" disabled class="p-button-link"></button>
                                </ng-template>

                                <button pButton *ngIf="isEmployee && server.privateNetworks.length > 0 && server.privateNetworks[0].status === 'ERROR'" (click)="retry(server); false" i18n-label="@@bma_common_retry" label="Retry" class="p-button-link"></button>

                                <button pButton *ngIf="server.privateNetworks.length > 0 && server.privateNetworks[0].status === 'CONFIGURED'" (click)="openPrivateNetworkRemoveModal(server); false" i18n-label="@@bma_common_remove" label="Remove" class="p-button-link"></button>

                                <a pButton href="/tickets/new?equipmentId={{ server.id }}&category=technical-assistance&subCategory=connectivity" *ngIf="!isEmployee" i18n-label="@@bma_common_createticket" label="Create Ticket" class="p-button-link"></a>
                            </div>
                        </div>
                    </div>
                    <div class="p-grid p-pr-5 p-pt-3">
                        <a pButton [routerLink]="['/servers', server.id]" class="p-button-primary ml-auto" i18n-label="@@bma_common_manage" label="Manage"></a>
                    </div>
                </ng-template>
            </p-accordionTab>
        </p-accordion>
    </div>
</div>

<div class="row mb-3" *ngIf="!isLoading && servers">
    <div class="col-12">
        <app-pagination [totalCount]="servers._metadata.totalCount" [limit]="servers._metadata.limit" [offset]="servers._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>
