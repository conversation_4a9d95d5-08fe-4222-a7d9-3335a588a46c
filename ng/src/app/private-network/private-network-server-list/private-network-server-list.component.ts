import { Component, Input } from '@angular/core';
import { PrivateNetwork } from 'src/app/v3/model/private-network.model';

@Component({
  selector: 'app-private-network-server-list',
  templateUrl: './private-network-server-list.component.html',
  styleUrls: ['./private-network-server-list.component.css'],
})
export class PrivateNetworkServerListComponent {
  @Input() privateNetwork: PrivateNetwork;

  navigate() {
    const params = new URLSearchParams({
      customerId: this.privateNetwork.customerId,
      salesOrgId: this.privateNetwork.salesOrgId,
    });
    window.location.href = `emp/servers?${params.toString()}`;
  }
}
