import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-private-network-server-list',
  templateUrl: './private-network-server-list.component.html',
  styleUrls: ['./private-network-server-list.component.css'],
})
export class PrivateNetworkServerListComponent {
  private readonly router = inject(Router);
  
  navigate() {
    this.router.navigate(['servers']);
  }
}
