import { Component, OnInit, Input, Ng<PERSON>one, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { ServerRemoteManagementModalComponent } from 'src/app/server/server-remote-management-modal/server-remote-management-modal.component';
import { PowerOperationModalComponent } from 'src/app/power-operation-modal/power-operation-modal.component';
import { CommerceService } from 'src/app/services/commerce.service';
import { first } from 'rxjs';
import { LswNotificationHubService } from '@lsw/notification-hub';
import { ServerEvent } from 'src/app/models/server-event.model';
import { MercureService } from 'src/app/services/mercure.service';
import { PrivateNetworkAddServerModalComponent } from '../private-network-add-server-modal/private-network-add-server-modal.component';
import { PrivateNetworkModifyServerModalComponent } from '../private-network-modify-server-modal/private-network-modify-server-modal.component';
import { PrivateNetworkRemoveModalComponent } from '../private-network-remove-modal/private-network-remove-modal.component';

@Component({
  selector: 'app-private-network-server-list',
  templateUrl: './private-network-server-list.component.html',
  styleUrls: ['./private-network-server-list.component.css'],
})
export class PrivateNetworkServerListComponent {
  @Input() privateNetwork: any;

  constructor(
    private router: Router,
    private cdr: ChangeDetectorRef,
  ) {}

  navigate() {
    this.router.navigate(['/servers'], {
      queryParams: {
        customerId: '10085996',
        salesOrgId: '2000'
      }
    }).then(() => {
      // Force change detection
      this.cdr.detectChanges();
    });
  }
  
}
