import { Component, OnInit, Input, Ng<PERSON>one } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { ServerRemoteManagementModalComponent } from 'src/app/server/server-remote-management-modal/server-remote-management-modal.component';
import { PowerOperationModalComponent } from 'src/app/power-operation-modal/power-operation-modal.component';
import { CommerceService } from 'src/app/services/commerce.service';
import { first } from 'rxjs';
import { LswNotificationHubService } from '@lsw/notification-hub';
import { ServerEvent } from 'src/app/models/server-event.model';
import { MercureService } from 'src/app/services/mercure.service';
import { PrivateNetworkAddServerModalComponent } from '../private-network-add-server-modal/private-network-add-server-modal.component';
import { PrivateNetworkModifyServerModalComponent } from '../private-network-modify-server-modal/private-network-modify-server-modal.component';
import { PrivateNetworkRemoveModalComponent } from '../private-network-remove-modal/private-network-remove-modal.component';

@Component({
  selector: 'app-private-network-server-list',
  templateUrl: './private-network-server-list.component.html',
  styleUrls: ['./private-network-server-list.component.css'],
})
export class PrivateNetworkServerListComponent implements OnInit {
  @Input() privateNetwork: any;

  servers: any;
  filters: UntypedFormGroup;
  isEmployee: boolean;
  isLoading: boolean;
  dynamicDialogRef: DynamicDialogRef;
  equipmentType = 'servers';

  constructor(
    private alertService: AlertService,
    private activatedRoute: ActivatedRoute,
    private modalService: ModalService,
    private currentUserService: CurrentUserService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private router: Router,
    private commerceService: CommerceService,
    private notificationHubService: LswNotificationHubService,
    private mercureService: MercureService,
    private zone: NgZone
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.filters = this.formBuilder.group({
      limit: [null],
      offset: [null],
      filter: [null],
    });
    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  loadServers(subscribeForMercure = false): void {
    this.isLoading = true;
    const params = this.sanitise(this.filters.getRawValue());
    params.privateNetworkEnabled = 'true';

    if (this.currentUserService.isEmployee()) {
      params.customerId = this.privateNetwork.customerId;
      params.salesOrgId = this.privateNetwork.salesOrgId;
    }

    this.httpClient.get<any>('/_/internal/dedicatedserverapi/v2/servers', { params }).subscribe(
      (data: any) => {
        this.servers = data;
        this.isLoading = false;
        if (subscribeForMercure) {
          this.subscribeForMercureUpdate();
        }
      },
      (error: any) => {
        this.isLoading = false;
      }
    );
  }

  onQueryParamsChanged(queryParams): void {
    this.filters.reset(queryParams);
    this.loadServers(true);
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  openPrivateNetworkAddServerModal() {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkAddServerModalComponent, {
      privateNetwork: this.privateNetwork,
    });
  }

  openPrivateNetworkModifyServerModal(server: any) {
    if (server.rack.type.includes('SHARED')) {
      window.open(
        this.commerceService.getCommerceConfigureProductUrl(server.contract.id, 'DEDSER02_MOD_PRIVATE_NETWORK')
      );
      return;
    }

    this.dynamicDialogRef = this.modalService.show(PrivateNetworkModifyServerModalComponent, {
      privateNetwork: this.privateNetwork,
      server,
    });
  }

  retry(server: any) {
    this.httpClient.post<any>(`/_/internal/nseapi/v2/privateNetworks/equipments/${server.id}/retryJob`, {}).subscribe(
      (data: any) => {
        this.alertService.alert({
          type: 'success',
          description: $localize`:@@bma_privatenetworkserverlist_requestinprogress:Request to retry ${server.id} is in progress. Please refresh the page for current status after few minutes.`,
        });
      },
      (error: any) => {
        this.alertService.alert({
          type: 'error',
          description: error.error.errorMessage,
        });
      }
    );
  }

  openPrivateNetworkRemoveModal(server: any) {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkRemoveModalComponent, {
      privateNetwork: this.privateNetwork,
      equipment: server,
      equipmentType: 'servers',
    });
  }

  openRemoteManagementModal(serverId: string) {
    this.modalService.show(ServerRemoteManagementModalComponent, { serverId });
  }

  openPowerOperationModal(equipment: any) {
    this.modalService.show(PowerOperationModalComponent, {
      equipment,
      equipmentType: 'servers',
    });
  }

  serverCanBeModified(server: any): boolean {
    if (server.privateNetworks.length > 0 && server.privateNetworks[0].status !== 'CONFIGURED') {
      return false; // if we have a status and it is an error, modifying is not allowed
    }
    if (server.rack.type === 'DEDICATED') {
      return true; // employees and customers may modify servers in dedicated racks
    }
    if (server.privateNetworks.length === 0) {
      if (this.isEmployee) {
        return true; // this means shared rack and the server is being moved. this is ok for employees
      }
      return false; // customers should not mess with this, if servers is being moved.
    }
    if (this.isEmployee) {
      return false; // server in shared rack, employee may not cause contract changes
    }
    return true; // customer, go ahead
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
  updateSingleServer(message) {
    let isServerInList = false;
    this.servers.servers.forEach((server) => {
      if (server.id === message.id) {
        isServerInList = true;
        server.privateNetworks.forEach((privateNetwork, key) => {
          if (privateNetwork.id === message.data.privateNetworkId) {
            server.privateNetworks[key] = message.data;
          }
        });
      }
    });
    if (!isServerInList) {
      this.filters.patchValue({ offset: null, limit: null, filter: null });
      this.loadServers();
    }
  }

  removeSingleServer(message) {
    let removedRow = -1;
    for (let i = 0; i < this.servers.servers.length; i++) {
      if (this.servers.servers[i].id === message.id) {
        removedRow = i;
        break;
      }
    }
    if (removedRow >= 0) {
      this.servers.servers.splice(removedRow, 1);
    }
  }

  subscribeForMercureUpdate() {
    this.notificationHubService.setTokenParams({
      customerId: this.privateNetwork.customerId,
      salesOrgId: this.privateNetwork.salesOrgId,
    });

    this.notificationHubService
      .getNotificationHubToken(this.mercureService.getTokenURL('nseapi'))
      .pipe(first())
      .subscribe({
        next: () => {
          // subscribe to topics...

          this.notificationHubService
            .sync<ServerEvent>(
              `${this.privateNetwork.customerId}/${this.privateNetwork.salesOrgId}/privateNetworkServers`
            )
            .subscribe({
              next: (event) => {
                this.zone.run(() => {
                  if (event.action === 'private-network-server-remove' && event.status === 'REMOVED') {
                    this.removeSingleServer(event);
                  } else {
                    this.updateSingleServer(event);
                  }
                });
              },
              error: (error) => console.log({ error }),
            });
        },
      });
  }
}
