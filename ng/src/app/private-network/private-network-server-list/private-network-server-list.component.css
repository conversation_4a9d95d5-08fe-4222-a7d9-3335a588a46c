.container {
  background-color: white;
  padding: 20px;
  width: 100%;
  .info {
    margin-bottom: 40px;
  }
}
:host ::ng-deep .p-accordion .p-accordion-content {
  padding: 0.5rem;
}
.grid-content {
  margin-bottom: 5px;
  padding-bottom: 4px;
  padding-left: 2px;
  font-size: 0.8rem !important;
}
.grid-content-border {
  border-bottom: 1px solid #ddd;
}
.grid-row {
  padding-right: 10px;
}
.show-data {
  cursor: pointer;
}

.quick-actions a,
.quick-actions button {
  padding-top: 4px;
  padding-bottom: 4px;
  font-size: 0.9rem;
  text-align: left;
}

.subnet-details .subnet-address {
  font-size: 0.8rem;
}
.subnet-details .vlan-id {
  font-size: 0.8rem;
}
.badge {
  font-size: 0.6rem;
}
