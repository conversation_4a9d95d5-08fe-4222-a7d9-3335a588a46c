<h3 class="mt-5 mb-2">
    <span i18n="@@bma_common_privatenetwork_information">Private Network Information</span>
</h3>
<ng-container *ngIf="equipment.featureAvailability.privateNetwork; else privateNetworkNotAvailable">
    <ng-container *ngIf="equipment.privateNetworks.length > 0; else enablePrivateNetwork">
        <table class="table table-sm mb-0">
            <tbody>
                <tr>
                    <th width="30%" i18n="@@bma_common_subnet">Subnet</th>
                    <td>
                        <span *ngIf="equipment.privateNetworks[0].dhcp === 'DISABLED'" i18n="@@bma_common_dhcpdisabled" class="text-muted">DHCP disabled</span>
                        <span *ngIf="equipment.privateNetworks[0].dhcp === 'ENABLED'" class="text-monospace subnet-address">{{ equipment.privateNetworks[0].subnet }}</span>
                    </td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_vlanid">Vlan ID</th>
                    <td>
                        <span class="text-monospace vlan-id">{{ equipment.privateNetworks[0].vlanId }}</span>
                    </td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_linkspeed">Link Speed</th>
                    <td>
                        {{ equipment.privateNetworks[0].linkSpeed|formatSpeed }}
                        <a [class.disabled]="!equipment.contract|default:''" pTooltip="Manage" i18n-pTooltip="@@bma_common_manage" *ngIf="!isEmployee" href="/bare-metals/privateNetwork/lookup" class="ml-1"><span class="fa fa-administration"></span></a>
                        <a [class.disabled]="!equipment.contract|default:''" pTooltip="Manage" i18n-pTooltip="@@bma_common_manage" *ngIf="isEmployee" href="/emp/privateNetwork/lookup?customerId={{ equipment.contract?.customerId }}&salesOrgId={{ equipment.contract?.salesOrgId }}" class="ml-1"><span class="fa fa-administration"></span></a>
                    </td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_privatenetwork_status">Status</th>
                    <td>
                        <span class="badge" [class.badge-danger-outline]="equipment.privateNetworks[0].status === 'ERROR'" [class.badge-green-outline]="equipment.privateNetworks[0].status === 'CONFIGURED'" [class.badge-warning-outline]="equipment.privateNetworks[0].status === 'CONFIGURING' || equipment.privateNetworks[0].status === 'REMOVING'">{{ equipment.privateNetworks[0].status }}</span>
                    </td>
                </tr>
            </tbody>
        </table>
    </ng-container>
    <table class="table table-sm" *ngIf="isEmployee && (equipment.privateNetworks.length > 0 || (equipment.privateNetworks.length == 0 && (equipment.isPrivateNetworkEnabled == true || equipment.contract?.privateNetworkPortSpeed)))">
        <tbody>
            <tr>
                <th width="30%" i18n="@@bma_common_contractstatus">Contract Status</th>
                <td>
                    <span i18n="@@bma_common_notactive" *ngIf="equipment.isPrivateNetworkEnabled == false" class="badge badge-warning">NOT ACTIVE</span>
                    <span i18n="@@bma_common_active" *ngIf="equipment.isPrivateNetworkEnabled == true" class="badge badge-success">ACTIVE</span>
                </td>
            </tr>
            <tr *ngIf="'DEDICATED' != (equipment.rack?.type|default|uppercase) && equipment.rack?.id|default">
                <th i18n="@@bma_common_contractlinkspeed">Contract Link Speed</th>
                <td>
                    <span *ngIf="equipment.contract?.privateNetworkPortSpeed">{{ equipment.contract?.privateNetworkPortSpeed|formatSpeed }}</span>
                    <span i18n="@@bma_common_notavailable" *ngIf="!equipment.contract?.privateNetworkPortSpeed" class="badge badge-warning uppercase">Not Available</span>
                </td>
            </tr>
        </tbody>
    </table>
    <ng-template #enablePrivateNetwork>
        <div>
            <p>
                <span i18n="@@bma_privatenetwork_notadded" class="pr-1">This equipment is not added to private network you can add it from</span>
                <a i18n="@@bma_common_here" *ngIf="!isEmployee" [disabled]="!equipment.contract|default:''" href="/bare-metals/privateNetwork/lookup">here</a>
                <a i18n="@@bma_common_here" *ngIf="isEmployee" [disabled]="!equipment.contract|default:''" href="/emp/privateNetwork/lookup?customerId={{ equipment.contract?.customerId }}&salesOrgId={{ equipment.contract?.salesOrgId }}">here</a>
            </p>
        </div>
    </ng-template>
</ng-container>
<ng-template #privateNetworkNotAvailable>
    <div>
        <p>
            <span i18n="@@bma_privatenetwork_notavaliable">Private network is not available for this equipment to know more about it </span>
            <ng-container [ngSwitch]="equipmentType">
                <a *ngSwitchCase="servers" i18n="@@bma_common_clickhere" href="https://kb.leaseweb.com/products/dedicated-server/managing-private-network-for-your-dedicated-servers">click here</a>
                <a *ngSwitchCase="colocations" i18n="@@bma_common_clickhere" href="https://kb.leaseweb.com/products/colocation/managing-private-network-for-colocation">click here</a>
                <a *ngSwitchDefault i18n="@@bma_common_clickhere" href="https://kb.leaseweb.com/network/private-network">click here</a>
            </ng-container>
        </p>
    </div>
</ng-template>
