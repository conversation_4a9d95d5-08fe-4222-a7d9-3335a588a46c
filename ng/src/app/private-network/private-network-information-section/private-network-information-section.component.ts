import { Component, Input, OnInit } from '@angular/core';
import { CurrentUserService } from 'src/app/services/current-user.service';

@Component({
  selector: 'app-private-network-information-section',
  templateUrl: './private-network-information-section.component.html',
  styleUrls: ['./private-network-information-section.component.css'],
})
export class PrivateNetworkInformationSectionComponent implements OnInit {
  @Input() equipment: any;
  @Input() equipmentType: any;

  isEmployee: boolean;
  constructor(private currentUserService: CurrentUserService) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
  }
}
