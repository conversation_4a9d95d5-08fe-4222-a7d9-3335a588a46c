import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { PrivateNetworkService } from 'src/app/services/private-network.service';

@Component({
  selector: 'app-private-network-add-dedicated-storage-modal',
  templateUrl: './private-network-add-dedicated-storage-modal.component.html',
  styleUrls: ['./private-network-add-dedicated-storage-modal.component.css'],
})
export class PrivateNetworkAddDedicatedStorageModalComponent implements OnInit {
  privateNetwork: any;
  options: any;
  filters: UntypedFormGroup;
  dedicatedStorages: any;
  error: any;
  isLoading = false;
  isSubmitting = false;
  showDialog = true;
  form: UntypedFormGroup;

  constructor(
    private alertService: AlertService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig,
    private currentUserService: CurrentUserService,
    private privateNetworkService: PrivateNetworkService
  ) {}

  ngOnInit(): void {
    this.privateNetwork = this.dynamicDialogConfig.data.privateNetwork;
    this.form = this.formBuilder.group({
      dedicatedStorage: [null, Validators.required],
      bonding: ['null', Validators.required],
    });
    this.filters = this.formBuilder.group({
      limit: [null],
      offset: [null],
      filter: [null],
    });
    this.loadDedicatedStorage({});

    this.options = this.privateNetworkService.getPrivateNetworkPricesForNetworkEquipment(
      this.privateNetwork.salesOrgId
    );
  }

  loadDedicatedStorage(params: any): void {
    if (this.currentUserService.isEmployee()) {
      params.customerId = this.privateNetwork.customerId;
      params.salesOrgId = this.privateNetwork.salesOrgId;
    }
    params.privateNetworkEnabled = 'false';
    params.privateNetworkCapable = 'true';

    this.isLoading = true;
    this.dedicatedStorages = null;
    this.form.reset();
    this.httpClient.get<any>(`/_/internal/dedicatedserverapi/v2/dedicatedStorages`, { params }).subscribe(
      (data: any) => {
        this.dedicatedStorages = data;
        this.isLoading = false;
      },
      (error: any) => {
        this.isLoading = false;
      }
    );
  }

  isDedicatedStorageAllowedToAdd(dedicatedStorageToAdd: any): any {
    // See: https://github.com/angular/angular/issues/11763
    // Workaround for `[attr.disabled]="expression that evaluates to null or anything other than null"`
    if (dedicatedStorageToAdd.privateNetworks.length > 0) {
      return false;
    }

    if (
      this.currentUserService.isEmployee() &&
      this.currentUserService.hasPermission('privatenetwork_manage') &&
      dedicatedStorageToAdd.rack.type &&
      dedicatedStorageToAdd.rack.type.includes('SHARED') &&
      !dedicatedStorageToAdd.contract?.privateNetworkPortSpeed
    ) {
      return false;
    }

    if (
      this.currentUserService.isCustomer() &&
      dedicatedStorageToAdd.rack.type &&
      dedicatedStorageToAdd.rack.type.includes('SHARED') &&
      dedicatedStorageToAdd.contract?.privateNetworkPortSpeed
    ) {
      return false;
    }

    if (dedicatedStorageToAdd.isRedundantPrivateNetworkCapable) {
      return false;
    }

    return null;
  }

  add() {
    const dedicatedStorage = this.form.get('dedicatedStorage').value;
    const bonding = this.form.get('bonding').value;

    this.isSubmitting = true;
    this.httpClient
      .put<any>(
        `/_/internal/dedicatedserverapi/v2/dedicatedStorages/${dedicatedStorage.id}/privateNetworks/${this.privateNetwork.id}`,
        {
          bonding,
        }
      )
      .subscribe(
        (data: any) => {
          this.isSubmitting = false;
          this.alertService.alert({
            type: 'success',
            description: $localize`:@@bma_privatenetworkadddedicatedstoragessmodal_requestinprogress:Request to add Dedicated Storage ${dedicatedStorage.id} is in progress. Please refresh the page for current status after few minutes.`,
          });
          this.closeModal({ dedicatedStorage });
        },
        (error: any) => {
          this.isSubmitting = false;
          this.error = error.error;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ dedicatedStorage });
        }
      );
  }

  onFiltersChange(): void {
    this.loadDedicatedStorage(this.sanitise(this.filters.getRawValue()));
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.onFiltersChange();
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
