<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '600px'}">
    <ng-template pTemplate="header">
        <h3 class="modal-title pull-left" i18n="@@bma_privatenetworkaddprivatecloudmodal_title">Add Private Cloud to Private Network {{ privateNetwork.id }}</h3>
    </ng-template>
    <div class="modal-body" [formGroup]="form">
        <div class="row">
            <div *ngIf="isLoading" class="col-12 text-center">
                <app-loader></app-loader>
            </div>
            <div *ngIf="!isLoading" class="col-12">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th i18n="@@bma_common_id" scope="col">ID</th>
                                <th i18n="@@bma_common_reference" scope="col">Reference</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngIf="!privateClouds || privateClouds?.privateClouds.length === 0">
                                <td colspan="999" class="text-center p-4" i18n="@@bma_privatenetworkaddprivatecloudmodal_noprivateclouds">No private clouds eligible for private network.</td>
                            </tr>
                            <tr *ngFor="let privateCloud of privateClouds?.privateClouds">
                                <td>
                                    <div class="form-check form-check-inline">
                                        <input [id]="privateCloud.id" type="radio" formControlName="privateCloud" [value]="privateCloud" class="form-check-input" autofocus />
                                        <label [for]="privateCloud.id" class="form-check-label text-monospace">{{ privateCloud.id }}</label>
                                    </div>
                                </td>
                                <td i18n="@@bma_common_notavailable">Not Available</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div *ngIf="form.get('privateCloud').value" class="form">
                    <p class="mt-3 mb-3 font-weight-bold" i18n="@@bma_privatenetworkaddprivatecloudmodal_selectportspeed">Select Port Speed for Private Cloud {{ form.get('privateCloud').value.id }}:</p>
                    <div>
                        <input id="100" type="radio" formControlName="linkSpeed" value="100" />
                        <label for="100" class="ml-1">100 Mbps ({{ '0.00'|formatCurrency:privateNetwork.salesOrgId }} / month)</label>
                    </div>
                    <div>
                        <input id="1000" type="radio" formControlName="linkSpeed" value="1000" />
                        <label for="1000" class="ml-1">1 Gbps ({{ '0.00'|formatCurrency:privateNetwork.salesOrgId }} / month)</label>
                        <span *ngIf="form.get('linkSpeed').value=='1000'" i18n="@@bma_privatenetworkaddprivatecloudmodal_notautomated"><br />This process is not automated yet, by selecting <strong>Add to Private Network</strong> our sales team will reach out to you. </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <span class="align-middle" *ngIf="error" class="text-danger">{{ error.errorMessage }}</span>
            <span *ngIf="form.valid && !error" class="align-middle mr-1">
                <span i18n="@@bma_common_totalprice">Total price:</span> <strong>{{ '0.00'|formatCurrency:privateNetwork.salesOrgId }}</strong> . <span i18n="@@bma_common_nextinvoice">You will see the change in your next invoice.</span>
            </span>
            <button pButton type="button" icon="pi pi-times" iconPos="left" i18n-label="@@bma_common_close" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button pButton (click)="add()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" class="p-button-primary" [disabled]="!form.valid || error" i18n-label="@@bma_common_addtoprivatenetwork" label="Add to Private Network"></button>
        </div>
    </ng-template>
</p-dialog>
