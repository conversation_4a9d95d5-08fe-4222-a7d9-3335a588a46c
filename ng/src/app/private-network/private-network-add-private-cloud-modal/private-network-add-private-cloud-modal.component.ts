import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';

@Component({
  selector: 'app-private-network-add-private-cloud-modal',
  templateUrl: './private-network-add-private-cloud-modal.component.html',
  styleUrls: ['./private-network-add-private-cloud-modal.component.css'],
})
export class PrivateNetworkAddPrivateCloudModalComponent implements OnInit {
  privateNetwork: any;
  privateClouds: any;
  error: any;
  isLoading = false;
  isSubmitting = false;
  showDialog = true;
  form: UntypedFormGroup;

  constructor(
    private alertService: AlertService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.privateNetwork = this.dynamicDialogConfig.data.privateNetwork;
    this.form = this.formBuilder.group({
      privateCloud: [null, Validators.required],
      linkSpeed: [null, Validators.required],
    });
    this.loadPrivateClouds({});
  }

  loadPrivateClouds(params: any): void {
    params.privateNetworkEnabled = 'false';

    this.isLoading = true;
    this.httpClient.get<any>('/_/internal/cloud/v2/privateClouds', { params }).subscribe(
      (data: any) => {
        this.privateClouds = data;
        this.isLoading = false;
      },
      (error: any) => {
        this.isLoading = false;
      }
    );
  }

  add() {
    const privateCloud = this.form.get('privateCloud').value;
    this.isSubmitting = true;
    if (this.form.get('linkSpeed').value === '1000') {
      this.order1GPrivateNetwork(privateCloud);
    } else {
      this.httpClient
        .put<any>(`/_/internal/cloud/v2/privateClouds/${privateCloud.id}/privateNetworks/${this.privateNetwork.id}`, {})
        .subscribe(
          (data: any) => {
            this.isSubmitting = false;
            this.closeModal({ privateCloud });
          },
          (error: any) => {
            this.isSubmitting = false;
            this.error = error.error;
          }
        );
    }
  }

  order1GPrivateNetwork(privateCloud): void {
    this.alertService.clear();
    const body = {
      equipment: privateCloud,
      uplinkCapacity: this.form.get('linkSpeed').value,
      equipmentType: 'privateCloud',
    };

    this.httpClient.post<any>(`/_legacy/privateNetwork/order`, body).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;
        this.alertService.alert(
          {
            type: 'success',
            description: data.message,
          },
          true
        );
        this.closeModal({ privateCloud });
      },
      error: (error: any) => {
        this.isSubmitting = false;
        this.alertService.alert(
          {
            type: 'error',
            description: error.error.error,
          },
          true
        );
        this.closeModal();
      },
    });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
