import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert.service';
import { CommerceService } from 'src/app/services/commerce.service';
import { CurrentUserService } from 'src/app/services/current-user.service';

@Component({
  selector: 'app-private-network-remove-modal',
  templateUrl: './private-network-remove-modal.component.html',
  styleUrls: ['./private-network-remove-modal.component.css'],
})
export class PrivateNetworkRemoveModalComponent implements OnInit {
  privateNetwork: any;
  equipment: any;
  equipmentType: any;
  equipmentName = 'dedicated server';
  isSubmitting = false;
  showDialog = true;
  error: any;
  isEmployee: boolean;

  constructor(
    private alertService: AlertService,
    private httpClient: HttpClient,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig,
    private commerceService: CommerceService,
    private currentUserService: CurrentUserService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.privateNetwork = this.dynamicDialogConfig.data.privateNetwork;
    this.equipment = this.dynamicDialogConfig.data.equipment;
    this.equipmentType = this.dynamicDialogConfig.data.equipmentType;
  }

  remove() {
    if (this.equipmentType === 'clouds') {
      this.removePrivateCloudWorkaround();
      return;
    }

    const equipmentTypesNeedsContractModification = ['servers'];

    if (!this.isEmployee) {
      if (equipmentTypesNeedsContractModification.includes(this.equipmentType)) {
        if (this.equipment.rack.type.includes('SHARED')) {
          window.open(
            this.commerceService.getCommerceConfigureProductUrl(
              this.equipment.contract.id,
              'DEDSER02_MOD_PRIVATE_NETWORK'
            )
          );
          return;
        }
      }
    }

    this.isSubmitting = true;
    this.httpClient
      .delete<any>(
        `/_/internal/dedicatedserverapi/v2/${this.equipmentType}/${this.equipment.id}/privateNetworks/${this.privateNetwork.id}`,
        {}
      )
      .subscribe(
        (data: any) => {
          if (this.equipmentType === 'privateRacks') {
            this.equipmentName = 'dedicated rack';
          }

          this.isSubmitting = false;
          this.equipment.privateNetworks = [];
          this.alertService.alert({
            type: 'success',
            description: $localize`:@@bma_privatenetworkremovemodal_requestinprogress:Request to remove ${this.equipmentName} ${this.equipment.id} is in progress. Please refresh the page for current status after few minutes.`,
          });
          this.closeModal({ equipmentRemoved: true, equipment: this.equipment });
        },
        (error: any) => {
          this.isSubmitting = false;
          this.error = error.error;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ equipmentRemoved: false });
        }
      );
  }

  removePrivateCloudWorkaround() {
    const body = {
      privateCloudId: this.equipment.id,
      privateNetworkId: this.privateNetwork.id,
    };
    this.isSubmitting = true;
    this.httpClient.post<any>(`/_legacy/privateNetwork/remove`, body).subscribe(
      (data: any) => {
        this.isSubmitting = false;
        this.equipment.privateNetworks = [];
        this.closeModal({ equipmentRemoved: true, equipment: this.equipment });
      },
      (error: any) => {
        this.isSubmitting = false;
        this.error = error.error;
      }
    );
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
