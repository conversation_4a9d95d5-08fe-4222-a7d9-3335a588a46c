<div *ngIf="!isEmployee" class="alert alert-info d-flex" role="alert">
    <div class="float-left">
        <span class="fa fa-information"></span>
    </div>
    <div class="float-left ml-2">
        <p class="text-dark" i18n="@@bma_privatenetworknetworkequipmentlist_description1">If you expected more dedicated network equipment to be available for adding to a private network, it might be that some are not prepared for private network yet.</p>
        <p class="text-dark" i18n="@@bma_privatenetworknetworkequipmentlist_description2">If you would like to acquire dedicated network equipment with private network enabled, click "Order Private Network" below.</p>
    </div>
    <div class="clear"></div>
</div>

<form [formGroup]="filters" (ngSubmit)="submitFilters()" role="form">
    <div class="row mt-4 mb-4">
        <div class="col-6">
            <input type="text" class="form-control" formControlName="filter" i18n-placeholder="@@bma_common_filterforid" placeholder="Filter for ID or reference" autofocus />
        </div>
        <div class="col-2">
            <button pButton [disabled]="isLoading" i18n-label="@@bma_common_search" label="Search" icon="pi pi-search"></button>
        </div>
        <div class="col-4 text-right">
            <a class="btn btn-success mr-2" *ngIf="isEmployee" href (click)="openPrivateNetworkAddNetworkEquipmentModal(); false" i18n="@@bma_common_addtoprivatenetwork">Add to Private Network</a>
            <a class="btn btn-success" *ngIf="!isEmployee" href (click)="openOrderPrivateNetworkModal(); false" i18n="@@bma_common_orderprivatenetwork">Order Private Network</a>
        </div>
    </div>
</form>

<div class="row mt-4 mb-4">
    <div class="col-md-12">
        <ul class="list-unstyled">
            <li class="font-weight-bold" i18n="@@bma_privatenetworknetworkequipmentlist_tablelist">The table lists Dedicated Network Equipment that are already associated with Private Network.</li>
        </ul>
    </div>
</div>

<app-loader *ngIf="isLoading"></app-loader>

<div *ngIf="!isLoading && networkEquipments" class="table-responsive">
    <div class="p-grid p-col-12 bg-white p-mb-2 p-py-4 justify-content-center" i18n="@@bma_privatenetworknetworkequipmentlist_noequipment" *ngIf="networkEquipments.networkEquipments.length === 0">No Dedicated network equipment currently in the Private Network.</div>
    <p-accordion [multiple]="true">
        <p-accordionTab *ngFor="let networkEquipment of networkEquipments.networkEquipments">
            <ng-template pTemplate="header">
                <div class="row col-sm-12 align-items-center">
                    <div class="col-sm-1">
                        <h4 class="mt-2">
                            {{ networkEquipment.id }}
                        </h4>
                    </div>
                    <div class="col-sm-3">
                        <span class="h5">
                            <strong>{{ networkEquipment.contract.reference|default:'-' }}</strong>
                        </span>
                    </div>
                    <div class="col-sm-1">
                        <span class="h5">
                            {{ networkEquipment.type|default:'' | replace : '_' : ' ' | titlecase }}
                        </span>
                    </div>
                    <div class="col-sm-3">
                        <span class="h5">
                            <app-equipment-location [location]="networkEquipment.location"></app-equipment-location>
                        </span>
                    </div>
                    <div class="col-sm-1">
                        <span class="h5">
                            <span *ngIf="networkEquipment.privateNetworks.length > 0" class="port-speed">{{ networkEquipment.privateNetworks[0].linkSpeed|formatSpeed }}</span>
                            <span *ngIf="networkEquipment.privateNetworks.length === 0" class="text-muted"> - </span>
                        </span>
                    </div>
                    <div class="col-sm-2 text-center">
                        <span class="subnet-details">
                            <div *ngIf="networkEquipment.privateNetworks.length > 0">
                                <span *ngIf="privateNetwork.dhcp === 'DISABLED'" class="text-muted" i18n="@@bma_common_dhcpdisabled">DHCP disabled</span>
                                <span *ngIf="privateNetwork.dhcp === 'ENABLED'" class="text-monospace subnet-address">{{ networkEquipment.privateNetworks[0].subnet }}</span>
                                <br />
                                <span class="text-monospace vlan-id">{{ networkEquipment.privateNetworks[0].vlanId }}</span>
                            </div>
                            <span *ngIf="networkEquipment.privateNetworks.length === 0" class="text-muted"> - </span>
                        </span>
                    </div>
                    <div class="col-sm-1">
                        <span *ngIf="networkEquipment.privateNetworks.length > 0" class="badge" [class.badge-danger-outline]="networkEquipment.privateNetworks[0].status === 'ERROR'" [class.badge-green-outline]="networkEquipment.privateNetworks[0].status === 'CONFIGURED'" [class.badge-warning-outline]="networkEquipment.privateNetworks[0].status === 'CONFIGURING' || networkEquipment.privateNetworks[0].status === 'REMOVING'">{{ networkEquipment.privateNetworks[0].status }}</span>
                        <span *ngIf="networkEquipment.privateNetworks.length === 0" class="text-muted"> - </span>
                    </div>
                </div>
            </ng-template>
            <ng-template pTemplate="content" class="p-p-0">
                <div class="p-grid border-bottom">
                    <div class="p-col-12 p-md-10 border-right">
                        <h5 class="p-ml-6 pt-2" i18n="@@bma_common_networkequipmentdetails">Network Equipment Details</h5>
                        <div class="p-col-12 p-d-flex p-flex-col p-pb-0">
                            <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-2">
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_name">Name</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <span class="selectable">{{ networkEquipment.name|default:'-' }}</span>
                                    </div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_equipmentid">Equipment ID</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <span class="selectable">{{ networkEquipment.id|default:'-' }}</span>
                                    </div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_devicetype">Device Type</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ networkEquipment.type|default:'' | replace : '_' : ' ' | titlecase }}</div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_ipaddress">IP Address</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <ng-container *ngIf="(networkEquipment?.networkInterfaces?.internal && networkEquipment?.networkInterfaces?.internal?.ip); else notAvailable">
                                            <span class="selectable">{{ networkEquipment.networkInterfaces.internal.ip|default:'-'|cidrToIp }}</span>
                                        </ng-container>
                                    </div>
                                </div>
                            </div>
                            <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-5">
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_vlanid">Vlan ID</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <span *ngIf="networkEquipment.privateNetworks.length > 0" class="text-monospace vlan-id">{{ networkEquipment.privateNetworks[0].vlanId }}</span>
                                        <span *ngIf="networkEquipment.privateNetworks.length === 0" class="text-muted"> - </span>
                                    </div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_portspeed">Port Speed</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <span *ngIf="networkEquipment.privateNetworks.length > 0">{{ networkEquipment.privateNetworks[0].linkSpeed|formatSpeed }}</span>
                                        <span *ngIf="networkEquipment.privateNetworks.length === 0" class="text-muted"> - </span>
                                        &nbsp;&nbsp;
                                        <a (click)="openPrivateNetworkModifyNetworkEquipmentModal(networkEquipment);" *ngIf="networkEquipmentCanBeModified(networkEquipment); else noModify" href="javascript:void(0);" class="text-primary" i18n-label="@@bma_common_modify"><b>Modify</b></a>
                                        <ng-template #noModify>
                                            <a class="text-muted" href="javascript:void(0);" i18n-label="@@bma_common_modify"><b>Modify</b></a>
                                        </ng-template>
                                    </div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_location">Location</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <app-equipment-location [location]="networkEquipment.location"></app-equipment-location>
                                    </div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_status">Status</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <span *ngIf="networkEquipment.privateNetworks.length > 0; else notAvailable" class="badge" [class.badge-danger-outline]="networkEquipment.privateNetworks[0].status === 'ERROR'" [class.badge-green-outline]="networkEquipment.privateNetworks[0].status === 'CONFIGURED'" [class.badge-warning-outline]="networkEquipment.privateNetworks[0].status === 'CONFIGURING' || networkEquipment.privateNetworks[0].status === 'REMOVING'">{{ networkEquipment.privateNetworks[0].status }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <ng-template #notAvailable>
                            <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
                        </ng-template>
                    </div>
                    <div class="p-col-12 p-md-2 quick-actions">
                        <h5 class="p-2" i18n="@@bma_common_quickactions">Quick Actions</h5>
                        <div class="p-grid p-flex-column align-items-start">
                            <a i18n-label="@@bma_common_credentials" pButton [routerLink]="['/networkEquipments',networkEquipment.id,'credentials']" class="p-button-link" label="Credentials" title="Credentials"></a>
                            <a i18n-label="@@bma_common_ips" pButton [routerLink]="['/networkEquipments',networkEquipment.id,'ips']" class="p-button-link" label="IPs" title="IPs"></a>
                            <a i18n-label="@@bma_common_nullroutehistory" pButton [routerLink]="['/networkEquipments',networkEquipment.id,'nullRouteHistory']" class="p-button-link" label="Null Route History" title="Null Route History"></a>
                            <a i18n-label="@@bma_common_graphs" pButton [routerLink]="['/networkEquipments',networkEquipment.id,'graphs']" class="p-button-link" [ngClass]="{'disabled' : networkEquipment['type'] !== 'SWITCH' || networkEquipment['type'] === 'RM'}">Graphs</a>
                            <a pButton class="p-button-link" *ngIf="isEmployee && networkEquipment.privateNetworks.length > 0 && networkEquipment.privateNetworks[0].status === 'CONFIGURED'" (click)="openPrivateNetworkRemoveModal(networkEquipment); false" i18n-label="@@bma_common_remove" label="Remove"></a>
                        </div>
                    </div>
                </div>
                <div class="p-grid p-pr-5 p-pt-3">
                    <a pButton [routerLink]="['/networkEquipments', networkEquipment.id]" class="p-button-primary ml-auto" i18n-label="@@bma_common_manage" label="Manage"></a>
                </div>
            </ng-template>
        </p-accordionTab>
    </p-accordion>
</div>

<div class="row mb-3" *ngIf="!isLoading && networkEquipments">
    <div class="col-12">
        <app-pagination [totalCount]="networkEquipments._metadata.totalCount" [limit]="networkEquipments._metadata.limit" [offset]="networkEquipments._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>
