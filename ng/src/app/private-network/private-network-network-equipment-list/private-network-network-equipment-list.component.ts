import { Component, OnInit, Input, NgZone } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { CommerceService } from 'src/app/services/commerce.service';
import { first } from 'rxjs';
import { LswNotificationHubService } from '@lsw/notification-hub';
import { ServerEvent } from 'src/app/models/server-event.model';
import { MercureService } from 'src/app/services/mercure.service';
import { NetworkEquipmentOrderPrivateNetworkModalComponent } from 'src/app/network-equipment/network-equipment-order-private-network-modal/network-equipment-order-private-network-modal.component';
import { PrivateNetworkAddDedicatedNetworkEquipmentModalComponent } from '../private-network-add-dedicated-network-equipment-modal/private-network-add-dedicated-network-equipment-modal.component';
import { PrivateNetworkModifyNetworkEquipmentModalComponent } from '../private-network-modify-network-equipment-modal/private-network-modify-network-equipment-modal.component';
import { PrivateNetworkRemoveModalComponent } from '../private-network-remove-modal/private-network-remove-modal.component';

@Component({
  selector: 'app-private-network-network-equipment-list',
  templateUrl: './private-network-network-equipment-list.component.html',
  styleUrls: ['./private-network-network-equipment-list.component.css'],
})
export class PrivateNetworkNetworkEquipmentListComponent implements OnInit {
  @Input() privateNetwork: any;

  networkEquipments: any;
  filters: UntypedFormGroup;
  isEmployee: boolean;
  isLoading: boolean;
  dynamicDialogRef: DynamicDialogRef;

  constructor(
    private activatedRoute: ActivatedRoute,
    private alertService: AlertService,
    private modalService: ModalService,
    private currentUserService: CurrentUserService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private router: Router,
    private commerceService: CommerceService,
    private notificationHubService: LswNotificationHubService,
    private mercureService: MercureService,
    private zone: NgZone
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.filters = this.formBuilder.group({
      limit: [null],
      offset: [null],
      filter: [null],
    });
    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  loadNetworkEquipments(subscribeForMercure = false): void {
    this.isLoading = true;
    const params = this.sanitise(this.filters.getRawValue());
    params.privateNetworkEnabled = 'true';

    if (this.isEmployee) {
      params.customerId = this.privateNetwork.customerId;
      params.salesOrgId = this.privateNetwork.salesOrgId;
    }

    this.httpClient.get<any>('/_/internal/dedicatedserverapi/v2/networkEquipments', { params }).subscribe(
      (data: any) => {
        this.networkEquipments = data;
        this.isLoading = false;
        if (subscribeForMercure) {
          this.subscribeForMercureUpdate();
        }
      },
      (error: any) => {
        this.isLoading = false;
      }
    );
  }

  onQueryParamsChanged(queryParams): void {
    this.filters.reset(queryParams);
    this.loadNetworkEquipments(true);
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  retry(networkEquipment: any) {
    networkEquipment.dirty = 'Retrying...';
    this.httpClient
      .post<any>(`/_/internal/nseapi/v2/privateNetworks/equipments/${networkEquipment.id}/retryJob`, {})
      .subscribe(
        (data: any) => {
          networkEquipment.dirty = 'Retried';
        },
        (error: any) => {
          networkEquipment.dirty = error.error.errorMessage;
        }
      );
  }

  openPrivateNetworkAddNetworkEquipmentModal() {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkAddDedicatedNetworkEquipmentModalComponent, {
      privateNetwork: this.privateNetwork,
    });
  }

  openPrivateNetworkRemoveModal(networkEquipment: any) {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkRemoveModalComponent, {
      privateNetwork: this.privateNetwork,
      equipment: networkEquipment,
      equipmentType: 'networkEquipments',
    });
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }

  networkEquipmentCanBeModified(networkEquipment: any): boolean {
    if (networkEquipment.privateNetworks.length > 0 && networkEquipment.privateNetworks[0].status !== 'CONFIGURED') {
      return false; // if we have a status and it is an error, modifying is not allowed
    }
    if (networkEquipment.rack.type === 'DEDICATED') {
      return true; // employees and customers may modify network equipments in dedicated racks
    }
    if (networkEquipment.privateNetworks.length === 0) {
      if (this.isEmployee) {
        return true; // this means shared rack and the network equipment is being moved. this is ok for employees
      }
      return false; // customers should not mess with this, if network equipments is being moved.
    }
    if (this.isEmployee) {
      return false; // network equipment in shared rack, employee may not cause contract changes
    }
    // TODO remove this code when Commerce support PN for network equipment.
    return false;

    //return true; // customer, go ahead for now customer can not do that as it is not supported by commerce
  }

  openPrivateNetworkModifyNetworkEquipmentModal(networkEquipment: any) {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkModifyNetworkEquipmentModalComponent, {
      privateNetwork: this.privateNetwork,
      networkEquipment,
    });

    this.dynamicDialogRef.onClose.subscribe((event) => {
      if (event?.portSpeedUpdated === true) {
        // Update network equipments with updated port speed without reloading the whole component;
        networkEquipment.privateNetworks[0].status = 'CONFIGURING';
        networkEquipment.privateNetworks[0].linkSpeed = event?.updatedPortSpeed;
      }
    });
  }

  openOrderPrivateNetworkModal() {
    this.modalService.show(NetworkEquipmentOrderPrivateNetworkModalComponent, {});
  }

  updateSingleNetworkEquipment(message) {
    let isNetworkEquipmentInList = false;
    this.networkEquipments.networkEquipments.forEach((networkEquipment) => {
      if (networkEquipment.id === message.id) {
        isNetworkEquipmentInList = true;
        networkEquipment.privateNetworks.forEach((privateNetwork, key) => {
          if (privateNetwork.id === message.data.privateNetworkId) {
            networkEquipment.privateNetworks[key] = message.data;
          }
        });
      }
    });
    if (!isNetworkEquipmentInList) {
      this.filters.patchValue({ offset: null, limit: null, filter: null });
      this.loadNetworkEquipments();
    }
  }

  removeSingleNetworkEquipment(message) {
    let removedRow = -1;
    for (let i = 0; i < this.networkEquipments.networkEquipments.length; i++) {
      if (this.networkEquipments.networkEquipments[i].id === message.id) {
        removedRow = i;
        break;
      }
    }
    if (removedRow >= 0) {
      this.networkEquipments.networkEquipments.splice(removedRow, 1);
    }
  }

  subscribeForMercureUpdate() {
    this.notificationHubService.setTokenParams({
      customerId: this.privateNetwork.customerId,
      salesOrgId: this.privateNetwork.salesOrgId,
    });

    this.notificationHubService
      .getNotificationHubToken(this.mercureService.getTokenURL('nseapi'))
      .pipe(first())
      .subscribe({
        next: () => {
          // subscribe to topics...

          this.notificationHubService
            .sync<ServerEvent>(
              `${this.privateNetwork.customerId}/${this.privateNetwork.salesOrgId}/privateNetworkNetworkEquipments`
            )
            .subscribe({
              next: (event) => {
                this.zone.run(() => {
                  if (event.action === 'private-network-network-equipment-remove' && event.status === 'REMOVED') {
                    this.removeSingleNetworkEquipment(event);
                  } else {
                    this.updateSingleNetworkEquipment(event);
                  }
                });
              },
              error: (error) => console.log({ error }),
            });
        },
      });
  }
}
