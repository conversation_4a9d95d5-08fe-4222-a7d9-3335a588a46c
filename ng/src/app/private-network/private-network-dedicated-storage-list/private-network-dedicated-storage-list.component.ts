import { Component, OnInit, Input, NgZone } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { first } from 'rxjs';
import { LswNotificationHubService } from '@lsw/notification-hub';
import { ServerEvent } from 'src/app/models/server-event.model';
import { MercureService } from 'src/app/services/mercure.service';
import { PrivateNetworkAddDedicatedStorageModalComponent } from '../private-network-add-dedicated-storage-modal/private-network-add-dedicated-storage-modal.component';
import { PrivateNetworkRemoveModalComponent } from '../private-network-remove-modal/private-network-remove-modal.component';
import { ToggleBondingModalComponent } from '../../toggle-bonding-modal/toggle-bonding-modal.component';

@Component({
  selector: 'app-private-network-dedicated-storage-list',
  templateUrl: './private-network-dedicated-storage-list.component.html',
  styleUrls: ['./private-network-dedicated-storage-list.component.css'],
})
export class PrivateNetworkDedicatedStorageListComponent implements OnInit {
  @Input() privateNetwork: any;

  dedicatedStorages: any;
  filters: UntypedFormGroup;
  isEmployee: boolean;
  isLoading: boolean;
  disableBondingButton: boolean;
  dynamicDialogRef: DynamicDialogRef;

  constructor(
    private activatedRoute: ActivatedRoute,
    private alertService: AlertService,
    private modalService: ModalService,
    private currentUserService: CurrentUserService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private router: Router,
    private notificationHubService: LswNotificationHubService,
    private mercureService: MercureService,
    private zone: NgZone
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.filters = this.formBuilder.group({
      limit: [null],
      offset: [null],
      filter: [null],
    });
    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  loadDedicaetdStorages(subscribeForMercure = false): void {
    this.isLoading = true;

    const params = this.sanitise(this.filters.getRawValue());
    params.privateNetworkEnabled = 'true';

    if (this.isEmployee) {
      params.customerId = this.privateNetwork.customerId;
      params.salesOrgId = this.privateNetwork.salesOrgId;
    }

    this.httpClient.get<any>('/_/internal/dedicatedserverapi/v2/dedicatedStorages', { params }).subscribe(
      (data: any) => {
        this.dedicatedStorages = data;
        this.isLoading = false;
        if (subscribeForMercure) {
          this.subscribeForMercureUpdate();
        }
      },
      (error: any) => {
        this.isLoading = false;
      }
    );
  }

  onQueryParamsChanged(queryParams): void {
    this.filters.reset(queryParams);
    this.loadDedicaetdStorages(true);
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  retry(dedicatedStorage: any) {
    dedicatedStorage.dirty = 'Retrying...';
    this.httpClient
      .post<any>(`/_/internal/nseapi/v2/privateNetworks/equipments/${dedicatedStorage.id}/retryJob`, {})
      .subscribe(
        (data: any) => {
          dedicatedStorage.dirty = 'Retried';
        },
        (error: any) => {
          dedicatedStorage.dirty = error.error.errorMessage;
        }
      );
  }

  openPrivateNetworkAddDedicatedStorageModal() {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkAddDedicatedStorageModalComponent, {
      privateNetwork: this.privateNetwork,
    });
  }

  openPrivateNetworkRemoveModal(dedicatedStorage: any) {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkRemoveModalComponent, {
      privateNetwork: this.privateNetwork,
      equipment: dedicatedStorage,
      equipmentType: 'dedicatedStorages',
    });
  }

  toggleBonding(equipment, action) {
    this.dynamicDialogRef = this.modalService.show(ToggleBondingModalComponent, {
      equipmentId: equipment.id,
      privateNetworkId: equipment.privateNetworks[0].id,
      action,
    });
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }

  updateSingleDedicatedStorage(message) {
    let isDedicatedStorageInList = false;
    this.dedicatedStorages.dedicatedStorages.forEach((dedicatedStorage) => {
      if (dedicatedStorage.id === message.id) {
        isDedicatedStorageInList = true;
        dedicatedStorage.privateNetworks.forEach((privateNetwork, key) => {
          if (privateNetwork.id === message.data.privateNetworkId) {
            dedicatedStorage.privateNetworks[key] = message.data;
          }
        });
      }
    });
    if (!isDedicatedStorageInList) {
      this.filters.patchValue({ offset: null, limit: null, filter: null });
      this.loadDedicaetdStorages();
    }
  }

  removeSingleDedicatedStorage(message) {
    let removedRow = -1;
    for (let i = 0; i < this.dedicatedStorages.dedicatedStorages.length; i++) {
      if (this.dedicatedStorages.dedicatedStorages[i].id === message.id) {
        removedRow = i;
        break;
      }
    }
    if (removedRow >= 0) {
      this.dedicatedStorages.dedicatedStorages.splice(removedRow, 1);
    }
  }

  subscribeForMercureUpdate() {
    this.notificationHubService.setTokenParams({
      customerId: this.privateNetwork.customerId,
      salesOrgId: this.privateNetwork.salesOrgId,
    });

    this.notificationHubService
      .getNotificationHubToken(this.mercureService.getTokenURL('nseapi'))
      .pipe(first())
      .subscribe({
        next: () => {
          // subscribe to topics...

          this.notificationHubService
            .sync<ServerEvent>(
              `${this.privateNetwork.customerId}/${this.privateNetwork.salesOrgId}/privateNetworkDedicatedStorages`
            )
            .subscribe({
              next: (event) => {
                this.zone.run(() => {
                  if (event.action === 'private-network-dedicated-storage-remove' && event.status === 'REMOVED') {
                    this.removeSingleDedicatedStorage(event);
                  } else {
                    this.updateSingleDedicatedStorage(event);
                  }
                });
              },
              error: (error) => console.log({ error }),
            });
        },
      });
  }
}
