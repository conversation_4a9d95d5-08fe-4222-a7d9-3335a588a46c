<div *ngIf="!isEmployee" class="alert alert-info d-flex" role="alert">
    <div class="float-left">
        <span class="fa fa-information"></span>
    </div>
    <div class="float-left ml-2">
        <p class="text-dark" i18n="@@bma_privatenetworkdedicatedstoragelist_description1">If you expected more dedicated storage to be available for adding to a private network, it might be that some are not prepared for private network yet.</p>
        <p class="text-dark" i18n="@@bma_privatenetworkdedicatedstoragelist_description2">If you would like to acquire dedicated storages with private network enabled, please <a href="/tickets/new?category=sales&subCategory=buy&subject=Add%20Private%20Network%20on%20Dedicated%20Storages&message=I%20would%20like%20to%20add%20private%20network%20on%20my%20dedicated%20storages:%20<write_here>" class="second">contact your sales representative</a>. To speed up the process please make sure to mention the dedicated storages you would like to add to private network in the ticket to your sales representative.".</p>
    </div>
    <div class="clear"></div>
</div>

<form [formGroup]="filters" (ngSubmit)="submitFilters()" role="form">
    <div class="row mt-4 mb-4">
        <div class="col-6">
            <input type="text" class="form-control" formControlName="filter" i18n-placeholder="@@bma_common_filterforid" placeholder="Filter for ID or reference" autofocus />
        </div>
        <div class="col-3">
            <button pButton [disabled]="isLoading" i18n-label="@@bma_common_search" label="Search" icon="pi pi-search"></button>
        </div>
        <div class="col-3 text-right">
            <a class="btn btn-success" *ngIf="isEmployee" href (click)="openPrivateNetworkAddDedicatedStorageModal(); false" i18n="@@bma_common_addtoprivatenetwork">Add to Private Network</a>
        </div>
    </div>
</form>

<div class="row mt-4 mb-4">
    <div class="col-md-12">
        <ul class="list-unstyled">
            <li class="font-weight-bold" i18n="@@bma_privatenetworkdedicatedstoragelist_tablelist">The table lists Dedicated Storage that are already associated with Private Network.</li>
        </ul>
    </div>
</div>

<app-loader *ngIf="isLoading"></app-loader>

<div *ngIf="!isLoading && dedicatedStorages" class="table-responsive">
    <div class="p-grid p-col-12 bg-white p-mb-2 p-py-4 justify-content-center" i18n="@@bma_privatenetworkdedicatedstoragelist_nocolocaton" *ngIf="dedicatedStorages.dedicatedStorages.length === 0">No dedicated storages currently in the Private Network.</div>
    <p-accordion [multiple]="true">
        <p-accordionTab *ngFor="let dedicatedStorage of dedicatedStorages.dedicatedStorages">
            <ng-template pTemplate="header">
                <div class="row col-sm-12 align-items-center">
                    <div class="col-sm-2">
                        <h4 class="mt-2">
                            {{ dedicatedStorage.id }}
                        </h4>
                    </div>
                    <div class="col-sm-3">
                        <span class="h5">
                            <strong>{{ dedicatedStorage.contract.reference|default:'-' }}</strong>
                        </span>
                    </div>
                    <div class="col-sm-3">
                        <span class="h5">
                            <app-equipment-location [location]="dedicatedStorage.location"></app-equipment-location>
                        </span>
                    </div>
                    <div class="col-sm-1">
                        <span class="h5">
                            <span *ngIf="dedicatedStorage.privateNetworks.length > 0" class="port-speed">{{ dedicatedStorage.privateNetworks[0].linkSpeed|formatSpeed }}</span>
                            <span *ngIf="dedicatedStorage.privateNetworks.length === 0" class="text-muted"> - </span>
                        </span>
                    </div>
                    <div class="col-sm-2 text-center">
                        <span class="subnet-details">
                            <div *ngIf="dedicatedStorage.privateNetworks.length > 0">
                                <span *ngIf="privateNetwork.dhcp === 'DISABLED'" class="text-muted" i18n="@@bma_common_dhcpdisabled">DHCP disabled</span>
                                <span *ngIf="privateNetwork.dhcp === 'ENABLED'" class="text-monospace subnet-address">{{ dedicatedStorage.privateNetworks[0].subnet }}</span>
                                <br />
                                <span class="text-monospace vlan-id">{{ dedicatedStorage.privateNetworks[0].vlanId }}</span>
                            </div>
                            <span *ngIf="dedicatedStorage.privateNetworks.length === 0" class="text-muted"> - </span>
                        </span>
                    </div>
                    <div class="col-sm-1">
                        <span *ngIf="dedicatedStorage.privateNetworks.length > 0" class="badge" [class.badge-danger-outline]="dedicatedStorage.privateNetworks[0].status === 'ERROR'" [class.badge-green-outline]="dedicatedStorage.privateNetworks[0].status === 'CONFIGURED'" [class.badge-warning-outline]="dedicatedStorage.privateNetworks[0].status === 'CONFIGURING' || dedicatedStorage.privateNetworks[0].status === 'REMOVING'">{{ dedicatedStorage.privateNetworks[0].status }}</span>
                        <span *ngIf="dedicatedStorage.privateNetworks.length === 0" class="text-muted"> - </span>
                    </div>
                </div>
            </ng-template>
            <ng-template pTemplate="content" class="p-p-0">
                <div class="p-grid border-bottom">
                    <div class="p-col-12 p-md-10 border-right">
                        <h5 class="p-ml-6 pt-2" i18n="@@bma_common_dedicatedstoragedetails">Dedicated Storage Details</h5>
                        <div class="p-col-12 p-d-flex p-flex-col p-pb-0">
                            <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-2">
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_equipmentid">Equipment ID</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ dedicatedStorage.id }}</div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_ipaddress">IP Address</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <ng-container *ngIf="(dedicatedStorage?.networkInterfaces?.internal && dedicatedStorage?.networkInterfaces?.internal?.ip); else notAvailable">
                                            <span class="selectable">{{ dedicatedStorage.networkInterfaces.internal.ip|default:'-'|cidrToIp }}</span>
                                        </ng-container>
                                    </div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_vlanid">Vlan ID</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <span *ngIf="dedicatedStorage.privateNetworks.length > 0" class="text-monospace vlan-id">{{ dedicatedStorage.privateNetworks[0].vlanId }}</span>
                                        <span *ngIf="dedicatedStorage.privateNetworks.length === 0" class="text-muted"> - </span>
                                    </div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_bonding_title">Bonding</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <ng-container *ngIf="dedicatedStorage.privateNetworks[0].bonding === true;else bondingDisabled">
                                            <span i18n="@@bma_bonding_enabled" class="mr-4 badge badge-success-outline badge-status">Enabled</span>
                                        </ng-container>
                                        <ng-template #bondingDisabled>
                                            <span i18n="@@bma_bonding_disabled" class="mr-4 badge badge-warning-outline badge-status">Disabled</span>
                                        </ng-template>
                                        <ng-container *ngIf="dedicatedStorage.privateNetworks[0].bonding === true">
                                            <a i18n="@@bma_bonding_disable" *ngIf="dedicatedStorage.networkInterfaces['internal'].ports.length == 4" class="btn btn-sm btn-danger float-right" (click)="toggleBonding(dedicatedStorage, 'disable')" href="javascript:void(0);"> Disable Bonding </a>
                                        </ng-container>
                                        <ng-container *ngIf="dedicatedStorage.privateNetworks[0].bonding === false">
                                            <a i18n="@@bma_bonding_enable" *ngIf="dedicatedStorage.networkInterfaces['internal'].ports.length == 4" class="btn btn-sm btn-primary float-right" (click)="toggleBonding(dedicatedStorage, 'enable')" href="javascript:void(0);"> Enable Bonding </a>
                                        </ng-container>
                                    </div>
                                </div>
                            </div>
                            <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-5">
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_portspeed">Port Speed</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <span *ngIf="dedicatedStorage.privateNetworks.length > 0">{{ dedicatedStorage.privateNetworks[0].linkSpeed|formatSpeed }}</span>
                                        <span *ngIf="dedicatedStorage.privateNetworks.length === 0" class="text-muted"> - </span>
                                    </div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_location">Location</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <app-equipment-location [location]="dedicatedStorage.location"></app-equipment-location>
                                    </div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_status">Status</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <span *ngIf="dedicatedStorage.privateNetworks.length > 0; else notAvailable" class="badge" [class.badge-danger-outline]="dedicatedStorage.privateNetworks[0].status === 'ERROR'" [class.badge-green-outline]="dedicatedStorage.privateNetworks[0].status === 'CONFIGURED'" [class.badge-warning-outline]="dedicatedStorage.privateNetworks[0].status === 'CONFIGURING' || dedicatedStorage.privateNetworks[0].status === 'REMOVING'">{{ dedicatedStorage.privateNetworks[0].status }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <ng-template #notAvailable>
                            <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
                        </ng-template>
                    </div>
                    <div class="p-col-12 p-md-2 quick-actions">
                        <h5 class="p-2" i18n="@@bma_common_quickactions">Quick Actions</h5>
                        <div class="p-grid p-flex-column align-items-start">
                            <a pButton *ngIf="(dedicatedStorage?.networkInterfaces?.public|default:'') && (dedicatedStorage?.networkInterfaces?.public?.ports|default:''); else notDataGraph" [routerLink]="['/dedicatedStorages', dedicatedStorage.id, 'graphs']" i18n-label="@@bma_common_datagraphs" label="Datagraphs" class="p-button-link"></a>
                            <ng-template #notDataGraph>
                                <button pButton i18n-label="@@bma_common_datagraphs" label="Datagraphs" disabled class="p-button-link"></button>
                            </ng-template>

                            <a pButton [routerLink]="['/dedicatedStorages', dedicatedStorage.id, 'credentials']" label="Credentials" class="p-button-link"></a>
                            <a pButton class="p-button-link" *ngIf="isEmployee && dedicatedStorage.privateNetworks.length > 0 && dedicatedStorage.privateNetworks[0].status === 'CONFIGURED'" (click)="openPrivateNetworkRemoveModal(dedicatedStorage); false" i18n-label="@@bma_common_remove" label="Remove"></a>
                        </div>
                    </div>
                </div>
                <div class="p-grid p-pr-5 p-pt-3">
                    <a pButton [routerLink]="['/dedicatedStorages', dedicatedStorage.id]" class="p-button-primary ml-auto" i18n-label="@@bma_common_manage" label="Manage"></a>
                </div>
            </ng-template>
        </p-accordionTab>
    </p-accordion>
</div>

<div class="row mb-3" *ngIf="!isLoading && dedicatedStorages">
    <div class="col-12">
        <app-pagination [totalCount]="dedicatedStorages._metadata.totalCount" [limit]="dedicatedStorages._metadata.limit" [offset]="dedicatedStorages._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>
