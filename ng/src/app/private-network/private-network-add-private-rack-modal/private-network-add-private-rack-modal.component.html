<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '600px'}">
    <ng-template pTemplate="header">
        <h3 class="modal-title pull-left" i18n="@@bma_privatenetworkaddprivaterackmodal_title">Add New Dedicated Rack to Private Network {{ privateNetwork.id }}</h3>
    </ng-template>
    <div class="modal-body" [formGroup]="form">
        <div class="row">
            <div class="col-12">
                <form [formGroup]="filters" (ngSubmit)="onFiltersChange()" role="form">
                    <div class="row mt-4 mb-4">
                        <div class="col-6">
                            <input type="text" class="form-control" formControlName="filter" i18n-placeholder="@@bma_common_searchplaceholder" placeholder="Filter for ID, IP address, MAC address, Contract ID, or reference" autofocus />
                        </div>
                        <div class="col-6">
                            <button pButton [disabled]="isLoading" i18n-label="@@bma_common_search" label="Search" icon="pi pi-search"></button>
                        </div>
                    </div>
                </form>

                <app-loader *ngIf="isLoading"></app-loader>

                <ng-container *ngIf="!isLoading && privateRacks">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th scope="col" i18n="@@bma_common_id">ID</th>
                                    <th scope="col" i18n="@@bma_common_reference">Reference</th>
                                    <th scope="col" i18n="@@bma_common_location">Location</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngIf="!privateRacks || privateRacks?.privateRacks.length === 0">
                                    <td colspan="999" class="text-center p-4" i18n="@@bma_privatenetworkaddprivaterackmodal_nodedicatedracks">No dedicated racks eligible for private network..</td>
                                </tr>
                                <tr *ngFor="let privateRack of privateRacks?.privateRacks" [ngClass]="{'disabled': isPrivateRackAllowedToAdd(privateRack) === false}">
                                    <td>
                                        <div class="form-check form-check-inline">
                                            <input [id]="privateRack.id" type="radio" formControlName="privateRack" [value]="privateRack" class="form-check-input" (change)="onPrivateRackSelected(privateRack)" [attr.disabled]="isPrivateRackAllowedToAdd(privateRack)" />
                                            <label [for]="privateRack.id" class="form-check-label text-monospace">{{ privateRack.id }}</label>
                                            &nbsp;<span *ngIf="privateRack.privateNetworks.length > 0" class="badge" [class.badge-danger-outline]="privateRack.privateNetworks[0].status === 'ERROR'" [class.badge-green-outline]="privateRack.privateNetworks[0].status === 'CONFIGURED'" [class.badge-warning-outline]="privateRack.privateNetworks[0].status === 'CONFIGURING' || privateRack.privateNetworks[0].status === 'REMOVING'">{{ privateRack.privateNetworks[0].status }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>{{ privateRack.contract.reference|default:'-' }}</strong>
                                    </td>
                                    <td>
                                        <app-equipment-location [location]="privateRack.location"></app-equipment-location>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <app-pagination [totalCount]="privateRacks._metadata.totalCount" [limit]="privateRacks._metadata.limit" [offset]="privateRacks._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
                </ng-container>

                <div *ngIf="form.get('privateRack').value" class="form">
                    <p class="mt-3 mb-3 font-weight-bold" i18n="@@bma_privatenetworkaddprivaterackmodal_portspeed">The port speed in the contract for dedicated rack {{ form.get('privateRack').value.id }} is set to 2x: {{ form.get('privateRack').value.contract.privateNetworkPortSpeed|formatSpeed }}</p>

                    <div class="mt-3">
                        <input class="form-check-input" type="checkbox" formControlName="allServers" id="allServers" />
                        <label class="form-check-label ml-3" for="allServers"> <b i18n="@@bma_privatenetworkaddprivaterackmodal_addallservers">Add all servers in the rack to the private network</b></label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <ng-template pTemplate="footer">
        <div class="mt-2">
            <span class="align-middle" *ngIf="error" class="text-danger">{{ error.errorMessage }}</span>
            <span *ngIf="currentUserService.isCustomer() && form.valid && !error" class="align-middle mr-1"
                ><span i18n="@@bma_common_totalprice">Total price:</span> <strong *ngIf="options">{{ options[form.get('linkSpeed').value].price|formatCurrency:privateNetwork.salesOrgId }}</strong
                >. <span i18n="@@bma_common_nextinvoice">You will see the change in your next invoice.</span></span
            >
            <button pButton type="button" icon="pi pi-times" iconPos="left" i18n-label="@@bma_common_close" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button pButton (click)="add(); false" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" class="p-button-primary" [disabled]="!form.valid || error" i18n-label="@@bma_common_addtoprivatenetwork" label="Add to Private Network"></button>
        </div>
    </ng-template>
</p-dialog>
