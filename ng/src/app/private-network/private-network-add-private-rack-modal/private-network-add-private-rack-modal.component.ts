import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { PrivateNetworkService } from 'src/app/services/private-network.service';

@Component({
  selector: 'app-private-network-add-private-rack-modal',
  templateUrl: './private-network-add-private-rack-modal.component.html',
  styleUrls: ['./private-network-add-private-rack-modal.component.css'],
})
export class PrivateNetworkAddPrivateRackModalComponent implements OnInit {
  privateNetwork: any;
  options: any;
  filters: UntypedFormGroup;
  privateRacks: any;
  error: any;
  isLoading = false;
  isSubmitting = false;
  showDialog = true;
  form: UntypedFormGroup;

  constructor(
    private alertService: AlertService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig,
    private currentUserService: CurrentUserService,
    private privateNetworkService: PrivateNetworkService
  ) {}

  ngOnInit(): void {
    this.privateNetwork = this.dynamicDialogConfig.data.privateNetwork;
    this.form = this.formBuilder.group({
      privateRack: [null, Validators.required],
      linkSpeed: [null, Validators.required],
      allServers: [false],
    });
    this.filters = this.formBuilder.group({
      limit: [null],
      offset: [null],
      filter: [null],
    });
    this.loadPrivateRacks({});

    this.options = this.privateNetworkService.getPrivateNetworkPricesForPrivateRack(this.privateNetwork.salesOrgId);
  }

  onPrivateRackSelected(privateRack: any): void {
    this.form.patchValue({ linkSpeed: null });
    if (this.currentUserService.isEmployee() && this.form.get('privateRack').value) {
      this.form.patchValue({ linkSpeed: this.form.get('privateRack').value.contract.privateNetworkPortSpeed });
    }
  }

  loadPrivateRacks(params: any): void {
    if (this.currentUserService.isEmployee()) {
      params.customerId = this.privateNetwork.customerId;
      params.salesOrgId = this.privateNetwork.salesOrgId;
    }
    params.privateNetworkEnabled = 'false';
    params.privateNetworkCapable = 'true';

    this.isLoading = true;
    this.privateRacks = null;
    this.form.reset();
    this.httpClient.get<any>(`/_/internal/dedicatedserverapi/v2/privateRacks`, { params }).subscribe(
      (data: any) => {
        this.privateRacks = data;
        this.isLoading = false;
      },
      (error: any) => {
        this.isLoading = false;
      }
    );
  }

  onPrivateRackselected(privateRack: any): void {
    if (!privateRack.rack || !privateRack.rack.capacity) {
      // Workaround because rack info is not available in the collection responses
      this.httpClient
        .get<any>(`/_/internal/dedicatedserverapi/v2/privateRacks/${privateRack.id}`)
        .subscribe((data: any) => {
          privateRack.rack = data.rack;
        });
    }

    this.form.patchValue({ linkSpeed: null });
    if (this.currentUserService.isEmployee() && this.form.get('privateRack').value) {
      this.form.patchValue({ linkSpeed: this.form.get('privateRack').value.contract.privateNetworkPortSpeed });
    }
  }

  isPrivateRackAllowedToAdd(privateRackToAdd: any): any {
    // See: https://github.com/angular/angular/issues/11763
    // Workaround for `[attr.disabled]="expression that evaluates to null or anything other than null"`

    if (this.currentUserService.isCustomer()) {
      return false;
    }

    if (privateRackToAdd.privateNetworks.length > 0) {
      return false;
    }

    if (
      this.currentUserService.isEmployee() &&
      this.currentUserService.hasPermission('privatenetwork_manage') &&
      !privateRackToAdd.contract.privateNetworkPortSpeed
    ) {
      return false;
    }

    if (privateRackToAdd.isRedundantPrivateNetworkCapable) {
      return false;
    }

    return null;
  }

  add(): void {
    let linkSpeed = 0;

    const privateRack = this.form.get('privateRack').value;
    if (this.currentUserService.isCustomer()) {
      linkSpeed = this.form.get('linkSpeed').value;
    } else {
      linkSpeed = privateRack.contract.privateNetworkPortSpeed;
    }

    const allServers = this.form.get('allServers').value;

    this.isSubmitting = true;
    this.httpClient
      .put<any>(
        `/_/internal/dedicatedserverapi/v2/privateRacks/${privateRack.id}/privateNetworks/${this.privateNetwork.id}`,
        {
          linkSpeed,
          allServers,
        }
      )
      .subscribe(
        (data: any) => {
          this.isSubmitting = false;
          this.alertService.alert({
            type: 'success',
            description: $localize`:@@bma_privatenetworkaddprivaterackmodal_requestinprogress:Request to add dedicated rack ${privateRack.id} is in progress. Please refresh the page for current status after few minutes.`,
          });
          this.closeModal({ privateRack });
        },
        (error: any) => {
          this.isSubmitting = false;
          this.error = error.error;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ privateRack });
        }
      );
  }

  onFiltersChange(): void {
    this.loadPrivateRacks(this.sanitise(this.filters.getRawValue()));
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.onFiltersChange();
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
