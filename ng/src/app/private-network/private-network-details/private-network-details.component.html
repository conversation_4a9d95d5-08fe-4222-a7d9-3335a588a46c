<div class="container">
    <div class="row">
        <app-customer-aware-header *ngIf="privateNetwork" [caption]="'Private Network ' + privateNetwork.id" [customerId]="privateNetwork.customerId" [salesOrgId]="privateNetwork.salesOrgId" [isEmployee]="isEmployee" knowledgeBaseLink="https://kb.leaseweb.com/network/private-network"></app-customer-aware-header>
    </div>
</div>

<app-loader *ngIf="isLoading"></app-loader>

<div *ngIf="!isLoading">
    <div class="row mb-3">
        <div class="col-8">
            <div *ngIf="!isEmployee" class="alert alert-info d-flex multiple-vlans-alert" role="alert">
                <div class="float-left">
                    <span class="fa fa-information"></span>
                </div>
                <div class="float-left ml-2">
                    <p class="text-dark" i18n="@@bma_privatenetworkdetails_multiplevlans">Multiple VLAN's within the Private Network? Learn how to enable it <a href="https://kb.leaseweb.com/network/private-network/how-to-configure-multiple-vlans-qinq-in-private-network" target="_blank">here</a>.</p>
                </div>
            </div>
        </div>
        <div class="col-4 text-right">
            <div class="text-right">
                <label class="mr-1 font-weight-bold" i18n="@@bma_privatenetworkdetails_dhcp">DHCP Reservations: </label>
                <span class="badge badge-green-outline mr-2" [ngClass]="{'badge-green-outline': privateNetwork.dhcp === 'ENABLED', 'badge-red-outline': privateNetwork.dhcp !== 'ENABLED'}">{{ privateNetwork.dhcp }}</span>
                <a href (click)="openPrivateNetworkDhcpModal(); false" i18n="@@bma_common_edit">Edit</a>
            </div>
            <div class="text-right" *ngIf="isEmployee">
                <label class="font-weight-bold"><span i18n="@@bma_common_download">Download</span>:&nbsp;</label>
                <a href (click)="openCsv(); false" i18n-title="@@bma_privatenetworkdetails_exportequipmentscsv" title="Export Private Network equipments as CSV">CSV</a>
            </div>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-12">
            <ul class="nav nav-pills">
                <li class="nav-item">
                    <a [routerLink]="['.']" routerLinkActive="active" [routerLinkActiveOptions]="{exact:true}" class="nav-link"><span class="fa fa-privatenetwork"></span><span class="ml-1" i18n="@@bma_common_dedicatedservers">Dedicated Servers</span></a>
                </li>
                <li class="nav-item">
                    <a [routerLink]="['colocations']" routerLinkActive="active" class="nav-link"><span class="fa fa-cloud"></span><span class="ml-1" i18n="@@bma_common_colocation">Colocation</span></a>
                </li>
                <li class="nav-item">
                    <a [routerLink]="['private-racks']" routerLinkActive="active" class="nav-link"><span class="fa fa-cloud"></span><span class="ml-1" i18n="@@bma_common_dedicatedrack">Dedicated Rack</span></a>
                </li>
                <li class="nav-item">
                    <a [routerLink]="['dedicated-network-equipments']" routerLinkActive="active" class="nav-link"><span class="fa fa-cloud"></span><span class="ml-1" i18n="@@bma_common_dedicatednetworkequipments">Dedicated Network Equipment</span></a>
                </li>
                <li class="nav-item">
                    <a [routerLink]="['dedicated-storages']" routerLinkActive="active" class="nav-link"><span class="fa fa-cloud"></span><span class="ml-1" i18n="@@bma_common_dedicatedstorages">Dedicated Storages</span></a>
                </li>
            </ul>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div *ngIf="privateNetwork">
                <router-outlet (activate)="onPrivateNetworkTabActivated($event)"></router-outlet>
            </div>
        </div>
    </div>
</div>
