import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { SafeResourceUrl, Title } from '@angular/platform-browser';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { ModalService } from 'src/app/services/modal.service';
import { SiteService } from 'src/app/services/site.service';
import { PrivateNetworkDhcpModalComponent } from '../private-network-dhcp-modal/private-network-dhcp-modal.component';

@Component({
  selector: 'app-private-network-details',
  templateUrl: './private-network-details.component.html',
  styleUrls: ['./private-network-details.component.css'],
})
export class PrivateNetworkDetailsComponent implements OnInit {
  privateNetwork: any;
  isEmployee: boolean;
  isLoading: boolean;
  dynamicDialogRef: DynamicDialogRef;
  public report: SafeResourceUrl;
  blobCsv = null;

  constructor(
    private activatedRoute: ActivatedRoute,
    private currentUserService: CurrentUserService,
    private httpClient: HttpClient,
    private titleService: Title,
    private modalService: ModalService,
    private siteService: SiteService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.activatedRoute.params.subscribe((parameters) => this.onUrlParamsChanged(parameters));
  }

  onUrlParamsChanged(urlParams): void {
    this.titleService.setTitle(
      $localize`:@@bma_common_privatenetwork:Private Network` + ' ' + urlParams.id + ' | Leaseweb'
    );
    this.isLoading = true;
    this.httpClient.get<any>(`/_/internal/nseapi/v2/privateNetworks/${urlParams.id}`).subscribe(
      (data: any) => {
        this.privateNetwork = data;
        this.isLoading = false;

        /*eslint-disable */
        const event = new Event('update_product_navbar');
        event['customerId'] = this.privateNetwork.customerId;
        event['salesOrgId'] = this.privateNetwork.salesOrgId;
        event['country'] = this.siteService.getCountry(this.privateNetwork.salesOrgId);
        window.dispatchEvent(event);
        /*eslint-enable */
      },
      (error: any) => {
        this.isLoading = false;
      }
    );
  }

  onPrivateNetworkTabActivated(component): void {
    component.privateNetwork = this.privateNetwork;
  }

  openPrivateNetworkDhcpModal() {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkDhcpModalComponent, {
      privateNetwork: this.privateNetwork,
    });
  }

  openCsv() {
    if (this.isEmployee) {
      if (!this.blobCsv) {
        this.loadCsv();
      } else {
        const fileLink = document.createElement('a');
        fileLink.href = this.blobCsv;
        fileLink.download = `private_network_${this.privateNetwork.id}_equipments.csv`;
        fileLink.click();
      }
    }
  }

  loadCsv(): void {
    this.httpClient
      .get(`/_/internal/nseapi/v2/privateNetworks/${this.privateNetwork.id}/exportToCsv`, {
        responseType: 'arraybuffer',
      })
      .subscribe({
        next: (data: any) => {
          const blob = new Blob([data], { type: 'application/csv' });
          this.blobCsv = URL.createObjectURL(blob);

          this.openCsv();
        },
        error: (error: any) => {
          this.blobCsv = null;
        },
      });
  }
}
