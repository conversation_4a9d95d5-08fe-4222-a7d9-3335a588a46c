import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-private-network-dhcp-modal',
  templateUrl: './private-network-dhcp-modal.component.html',
  styleUrls: ['./private-network-dhcp-modal.component.css'],
})
export class PrivateNetworkDhcpModalComponent implements OnInit {
  privateNetwork: any;
  public newReservation = false;
  reservationToBeRemoved: any;
  isSubmitting = false;
  isToggling = false;
  error: any;
  showDialog = true;

  form: UntypedFormGroup;

  reservations: any;
  isLoading: boolean;

  constructor(
    private httpClient: HttpClient,
    private formBuilder: UntypedFormBuilder,
    private dynamicDialogConfig: DynamicDialogConfig,
    private dynamicDialogRef: DynamicDialogRef
  ) {}

  ngOnInit(): void {
    this.privateNetwork = this.dynamicDialogConfig.data.privateNetwork;
    this.loadDhcpReservations({});

    this.form = this.formBuilder.group({
      ip: [null, Validators.required],
      mac: [null, Validators.required],
      sticky: [null, Validators.required],
    });
  }

  loadDhcpReservations(params: any): void {
    this.newReservation = false;
    this.error = false;
    this.isLoading = true;
    this.reservations = null;
    this.httpClient
      .get<any>(`/_/internal/nseapi/v2/privateNetworks/${this.privateNetwork.id}/reservations`, { params })
      .subscribe(
        (data: any) => {
          this.reservations = data;
          this.isLoading = false;
        },
        (error: any) => {
          this.isLoading = false;
        }
      );
  }

  showDeleteConfirmation(selectedReservation: any) {
    this.reservationToBeRemoved = selectedReservation;
  }

  cancelDeleteReservation() {
    this.reservationToBeRemoved = null;
  }

  removeReservation(selectedReservation: any): void {
    this.isSubmitting = true;
    this.httpClient
      .delete<any>(
        `/_/internal/nseapi/v2/privateNetworks/${this.privateNetwork.id}/reservations/${selectedReservation.ip}`
      )
      .subscribe({
        next: (data: any) => {
          this.loadDhcpReservations({});
          this.isSubmitting = false;
          this.reservationToBeRemoved = null;
        },
        error: (error: any) => {
          this.error = error.error;
          this.isSubmitting = false;
          this.reservationToBeRemoved = null;
        },
      });
  }

  onPageChange(event): void {
    this.loadDhcpReservations({ offset: event.offset, limit: event.limit });
  }

  toggleDhcp(newStatus: string) {
    const params = {
      dhcp: newStatus,
    };

    this.isToggling = true;
    this.httpClient
      .put<any>(`/_/internal/nseapi/v2/privateNetworks/${this.privateNetwork.id}`, params)
      .subscribe((data: any) => {
        this.isToggling = false;
        this.privateNetwork.dhcp = data.dhcp;
        if (this.privateNetwork.dhcp === 'ENABLED') {
          this.loadDhcpReservations({});
        }
      });
  }

  createReservation(): void {
    this.isSubmitting = true;
    const params = {
      ip: this.form.get('ip').value,
      mac: this.form.get('mac').value,
      sticky: this.form.get('sticky').value,
    };
    this.httpClient
      .post<any>(`/_/internal/nseapi/v2/privateNetworks/${this.privateNetwork.id}/reservations`, params)
      .subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.form.reset();
          this.refreshReservations();
        },
        error: (error: any) => {
          this.error = error.error;
          this.isSubmitting = false;
        },
      });
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }

  addNewReservationRow() {
    this.newReservation = true;
  }

  removeNewReservationRow() {
    this.newReservation = false;
  }

  refreshReservations(): void {
    const params = {
      limit: 20,
      offset: 0,
    };

    this.loadDhcpReservations(params);
  }
}
