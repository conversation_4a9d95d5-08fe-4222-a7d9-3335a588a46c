<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '600px'}">
    <ng-template pTemplate="header">
        <h3 class="modal-title">Private Network {{ privateNetwork.id }} DHCP Reservations</h3>
    </ng-template>

    <div class="modal-body">
        <div class="row">
            <div class="col-7">
                <p class="align-middle">
                    <small i18n="@@bma_privatenetworkdhcpmodal_status_text">The DHCP service for Private Network {{ privateNetwork.id }} is currently </small>
                    <span class="badge badge-green-outline mr-2" [ngClass]="{'badge-green-outline': privateNetwork.dhcp === 'ENABLED', 'badge-red-outline': privateNetwork.dhcp !== 'ENABLED'}">{{ privateNetwork.dhcp }}</span>
                </p>
                <small i18n="@@bma_privatenetworkdhcpmodal_list_active_dhcp_reservations" class="mt-3 mb-3">List of active DHCP reservations.</small>
            </div>
            <div class="col-5 text-right">
                <button pButton *ngIf="privateNetwork.dhcp === 'ENABLED'" [disabled]="privateNetwork.salesOrgId === 1700" i18n-label="@@bma_privatenetwork_disable_dhcp" [loading]="isToggling" class="p-button-danger p-button-sm" (click)="toggleDhcp('DISABLED')" label="Disable DHCP"></button>
                <button pButton *ngIf="privateNetwork.dhcp === 'DISABLED'" [disabled]="privateNetwork.salesOrgId === 1700" i18n-label="@@bma_privatenetwork_enable_dhcp" [loading]="isToggling" class="p-button-primary p-button-sm" (click)="toggleDhcp('ENABLED')" label="Enable DHCP"></button>
                <button pButton (click)="addNewReservationRow()" type="button" i18n-label="@@bma_privatenetwork_create_dhcp" label="Create DHCP Reservation" icon="pi pi-plus" class="p-button-success float-right"></button>
            </div>
        </div>
        <div class="row">
            <div class="col-3 mx-auto">
                <span class="align-middle" *ngIf="error" class="text-danger text-center">{{ error.errorMessage }}</span>
            </div>
        </div>

        <div *ngIf="privateNetwork.dhcp === 'ENABLED'" class="row mt-3">
            <div *ngIf="isLoading" class="col-12 text-center">
                <app-loader></app-loader>
            </div>

            <div *ngIf="!isLoading" class="col-12">
                <form [formGroup]="form" (ngSubmit)="createReservation()" class="form-horizontal">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th scope="col" i18n="@@bma_common_ipaddress">IP Address</th>
                                <th scope="col" i18n="@@bma_common_macadress">Mac Address</th>
                                <th scope="col" i18n="@@bma_privatenetworkdhcpmodal_sticky">Sticky</th>
                                <th scope="col" i18n="@@bma_common_actions">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngIf="!reservations || reservations?.reservations.length === 0">
                                <td colspan="999" class="text-center p-4" i18n="@@bma_privatenetworkdhcpmodal_nodhcp">No dhcp reservation found.</td>
                            </tr>
                            <tr *ngIf="newReservation">
                                <td>
                                    <input type="text" class="form-control" formControlName="ip" id="ip" autofocus />
                                </td>
                                <td>
                                    <input type="text" class="form-control" formControlName="mac" id="mac" />
                                </td>
                                <td>
                                    <input type="checkbox" formControlName="sticky" id="sticky" />
                                </td>
                                <td>
                                    <button pButton (click)="removeNewReservationRow()" type="button" i18n-label="@@bma_common_cancel" label="Cancel" class="p-button-secondary"></button>
                                    <button pButton [loading]="isSubmitting" i18n-label="@@bma_common_save" label="Save"></button>
                                </td>
                            </tr>
                            <tr *ngFor="let reservation of reservations?.reservations">
                                <td>{{ reservation.ip }}</td>
                                <td>{{ reservation.mac }}</td>
                                <td>{{ reservation.sticky }}</td>
                                <td>
                                    <a *ngIf="!reservationToBeRemoved || reservationToBeRemoved.ip != reservation.ip" href="#" (click)="showDeleteConfirmation(reservation); false" class="text-danger">
                                        <i class="fa fa-delete"></i>
                                        <span i18n-label="@@bma_common_remove">Remove</span>
                                    </a>
                                    <button *ngIf="reservationToBeRemoved && reservationToBeRemoved.ip == reservation.ip && !isSubmitting" (click)="removeReservation(reservation)" pButton pRipple type="button" icon="pi pi-check" class="p-button-rounded p-button-secondary"></button>
                                    <button *ngIf="reservationToBeRemoved && reservationToBeRemoved.ip == reservation.ip && !isSubmitting" (click)="cancelDeleteReservation()" pButton pRipple type="button" icon="pi pi-times" class="p-button-rounded p-button-danger"></button>
                                    <span *ngIf="reservationToBeRemoved && reservationToBeRemoved.ip == reservation.ip && isSubmitting" class="badge badge-red-outline mr-2" i18n-label="@@bma_common_removing">Removing</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>

                <app-pagination [totalCount]="reservations._metadata.totalCount" [limit]="reservations._metadata.limit" [offset]="reservations._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
            </div>
        </div>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button pButton type="button" class="p-button-secondary" i18n-label="@@bma_common_close" label="Close" (click)="closeModal()"></button>
        </div>
    </ng-template>
</p-dialog>
