<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '800px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_common_orderprivatenetwork" class="modal-title">Order Private Network</h3>
    </ng-template>

    <form [formGroup]="form" (ngSubmit)="orderPrivateNetwork()">
        <div class="modal-body">
            <div class="form-group">
                <div class="ml-4">
                    <div i18n="@@bma_equipmentprivatenetworkredundancymodal_description1" class="row">The Private Network connectivity service allows you to connect this product to other Leaseweb products and services.</div>
                    <div class="row mt-3 mb-3 d-block">
                        <p i18n="@@bma_common_followcapacities">We offer the following capacities:</p>
                    </div>
                    <div class="row">
                        <h4 i18n="@@bma_common_selectuplinkcapacity">Select an uplink capacity</h4>
                    </div>

                    <ng-container *ngIf="uplinkCapacities && objectKeys(uplinkCapacities).length > 0">
                        <div class="row" *ngFor="let speed of objectKeys(uplinkCapacities)">
                            <div class="form-check form-check-inline">
                                <input type="radio" class="form-check-input" formControlName="uplinkCapacity" [id]="'uplink_capacity_' + speed" [value]="speed" autofocus />
                                <label class="form-check-label" [for]="'uplink_capacity_' + speed"> {{ uplinkCapacities[speed]?.value }} {{ uplinkCapacities[speed]?.price }} </label>
                            </div>
                        </div>
                    </ng-container>

                    <ng-container *ngIf="!uplinkCapacities || objectKeys(uplinkCapacities).length === 0">
                        <p>No uplink capacities available.</p>
                    </ng-container>
                </div>
                <div class="ml-4 mt-3">
                    <div class="row mt-3">
                        <small>
                            <span i18n-title="@@bma_common_kb" title="Knowledge Base" class="fa fa-knowledge mr-1" aria-hidden="true"></span>
                            <span i18n="@@bma_equipment_privatenetworkredundancymodal_kb">For more information about Private Network Redundancy please visit our <a target="_blank" href="https://kb.leaseweb.com/network/private-network">KB page</a>.</span>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_cancel" pButton type="button" icon="pi pi-times" iconPos="left" label="Cancel" class="p-button-secondary" (click)="closeModal()"></button>
            <button i18n-label="@@bma_common_submit" pButton (click)="orderPrivateNetwork()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" [disabled]="!uplinkCapacities" label="Submit" class="p-button-success"></button>
        </div>
    </ng-template>
</p-dialog>
