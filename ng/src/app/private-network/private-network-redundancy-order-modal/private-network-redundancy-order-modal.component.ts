import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { PrivateNetworkService } from 'src/app/services/private-network.service';

@Component({
  selector: 'app-private-network-redundancy-order-modal',
  templateUrl: './private-network-redundancy-order-modal.component.html',
  styleUrls: ['./private-network-redundancy-order-modal.component.css'],
})
export class PrivateNetworkRedundancyOrderModalComponent implements OnInit {
  equipment: any;
  equipmentType: string;
  isSubmitting = false;
  uplinkCapacities = null;
  showDialog = true;
  form: UntypedFormGroup;

  constructor(
    private alertService: AlertService,
    private dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private privateNetworkService: PrivateNetworkService
  ) {}

  ngOnInit(): void {
    this.equipment = this.dynamicDialogConfig.data.equipment;
    this.equipmentType = this.dynamicDialogConfig.data.equipmentType;
    this.uplinkCapacities = this.privateNetworkService.getPrivateNetworkRedundancyPortSpeeds(
      this.equipmentType,
      this.equipment.contract.salesOrgId
    );

    this.form = this.formBuilder.group({
      uplinkCapacity: this.getFirstUplinkCapacity(),
      equipmentType: this.equipmentType,
    });
  }

  orderPrivateNetwork(): void {
    this.isSubmitting = true;
    this.alertService.clear();
    const body = {
      equipment: this.equipment,
      uplinkCapacity: this.form.get('uplinkCapacity').value,
      equipmentType: this.transformEquipmentType(this.form.get('equipmentType').value),
      isPrivateNetworkRedundancy: true,
    };

    this.httpClient.post<any>(`/_legacy/privateNetwork/order`, body).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;
        this.alertService.alert(
          {
            type: 'success',
            description: data.message,
          },
          true
        );
        this.closeModal();
      },
      error: (error: any) => {
        this.isSubmitting = false;
        this.alertService.alert(
          {
            type: 'error',
            description: error.error.error,
          },
          true
        );
        this.closeModal();
      },
    });
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }

  objectKeys(obj: any): string[] {
    return obj ? Object.keys(obj) : [];
  }

  getFirstUplinkCapacity(): string {
    return this.uplinkCapacities && Object.keys(this.uplinkCapacities).length > 0
      ? Object.keys(this.uplinkCapacities)[0]
      : '';
  }

  transformEquipmentType(type: string): string {
    if (!type) {
      return '';
    }

    // TODO: Standardize equipment type names when creating a ticket order from EMP
    const mapping: Record<string, string> = {
      loadbalancer: 'LOAD_BALANCER',
      firewall: 'FIREWALL',
      dedicatedServer: 'server',
      dedicatedRack: 'privateRack',
    };

    return mapping[type.toLowerCase()] || type;
  }
}
