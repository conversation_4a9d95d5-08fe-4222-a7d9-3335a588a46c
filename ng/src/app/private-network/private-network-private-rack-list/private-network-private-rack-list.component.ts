import { Component, OnInit, Input, NgZone } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { first } from 'rxjs';
import { LswNotificationHubService } from '@lsw/notification-hub';
import { ServerEvent } from 'src/app/models/server-event.model';
import { MercureService } from 'src/app/services/mercure.service';
import { PrivateNetworkRemoveModalComponent } from '../private-network-remove-modal/private-network-remove-modal.component';
import { PrivateNetworkAddPrivateRackModalComponent } from '../private-network-add-private-rack-modal/private-network-add-private-rack-modal.component';

@Component({
  selector: 'app-private-network-private-rack-list',
  templateUrl: './private-network-private-rack-list.component.html',
  styleUrls: ['./private-network-private-rack-list.component.css'],
})
export class PrivateNetworkPrivateRackListComponent implements OnInit {
  @Input() privateNetwork: any;

  privateRacks: any;
  filters: UntypedFormGroup;
  isEmployee: boolean;
  isLoading: boolean;
  dynamicDialogRef: DynamicDialogRef;

  constructor(
    private activatedRoute: ActivatedRoute,
    private alertService: AlertService,
    private modalService: ModalService,
    private currentUserService: CurrentUserService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private router: Router,
    private notificationHubService: LswNotificationHubService,
    private mercureService: MercureService,
    private zone: NgZone
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.filters = this.formBuilder.group({
      limit: [null],
      offset: [null],
      filter: [null],
    });
    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  loadPrivateRacks(subscribeForMercure = false): void {
    this.isLoading = true;
    const params = this.sanitise(this.filters.getRawValue());
    params.privateNetworkEnabled = 'true';

    if (this.isEmployee) {
      params.customerId = this.privateNetwork.customerId;
      params.salesOrgId = this.privateNetwork.salesOrgId;
    }

    this.httpClient.get<any>('/_/internal/dedicatedserverapi/v2/privateRacks', { params }).subscribe(
      (data: any) => {
        this.privateRacks = data;
        this.isLoading = false;
        if (subscribeForMercure) {
          this.subscribeForMercureUpdate();
        }
      },
      (error: any) => {
        this.isLoading = false;
      }
    );
  }

  onQueryParamsChanged(queryParams): void {
    this.filters.reset(queryParams);
    this.loadPrivateRacks(true);
  }

  addToPrivateNetwork(): void {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkAddPrivateRackModalComponent, {
      privateNetwork: this.privateNetwork,
    });
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  remove(privateRack: any): void {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkRemoveModalComponent, {
      privateNetwork: this.privateNetwork,
      equipment: privateRack,
      equipmentType: 'privateRacks',
    });
  }

  retry(privateRack: any) {
    privateRack.dirty = 'Retrying...';
    this.httpClient
      .post<any>(`/_/internal/nseapi/v2/privateNetworks/equipments/${privateRack.id}/retryJob`, {})
      .subscribe(
        (data: any) => {
          privateRack.dirty = 'Retried';
        },
        (error: any) => {
          privateRack.dirty = error.error.errorMessage;
        }
      );
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }

  updateSinglePrivateRack(message) {
    let isPrivateRackInList = false;
    this.privateRacks.privateRacks.forEach((privateRack) => {
      if (privateRack.id === message.id) {
        isPrivateRackInList = true;
        privateRack.privateNetworks.forEach((privateNetwork, key) => {
          if (privateNetwork.id === message.data.privateNetworkId) {
            privateRack.privateNetworks[key] = message.data;
          }
        });
      }
    });
    if (!isPrivateRackInList) {
      this.filters.patchValue({ offset: null, limit: null, filter: null });
      this.loadPrivateRacks();
    }
  }

  removeSinglePrivateRack(message) {
    let removedRow = -1;
    for (let i = 0; i < this.privateRacks.privateRacks.length; i++) {
      if (this.privateRacks.privateRacks[i].id === message.id) {
        removedRow = i;
        break;
      }
    }
    if (removedRow >= 0) {
      this.privateRacks.privateRacks.splice(removedRow, 1);
    }
  }

  subscribeForMercureUpdate() {
    this.notificationHubService.setTokenParams({
      customerId: this.privateNetwork.customerId,
      salesOrgId: this.privateNetwork.salesOrgId,
    });

    this.notificationHubService
      .getNotificationHubToken(this.mercureService.getTokenURL('nseapi'))
      .pipe(first())
      .subscribe({
        next: () => {
          // subscribe to topics...

          this.notificationHubService
            .sync<ServerEvent>(
              `${this.privateNetwork.customerId}/${this.privateNetwork.salesOrgId}/privateNetworkPrivateRacks`
            )
            .subscribe({
              next: (event) => {
                this.zone.run(() => {
                  if (event.action === 'private-network-private-rack-remove' && event.status === 'REMOVED') {
                    this.removeSinglePrivateRack(event);
                  } else {
                    this.updateSinglePrivateRack(event);
                  }
                });
              },
              error: (error) => console.log({ error }),
            });
        },
      });
  }
}
