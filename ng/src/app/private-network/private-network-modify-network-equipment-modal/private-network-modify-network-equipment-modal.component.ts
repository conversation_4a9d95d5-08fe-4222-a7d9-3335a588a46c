import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { PrivateNetworkService } from 'src/app/services/private-network.service';

@Component({
  selector: 'app-private-network-modify-network-equipment-modal',
  templateUrl: './private-network-modify-network-equipment-modal.component.html',
  styleUrls: ['./private-network-modify-network-equipment-modal.component.css'],
})
export class PrivateNetworkModifyNetworkEquipmentModalComponent implements OnInit {
  privateNetwork: any;
  networkEquipment: any;
  error: any;
  options: any;
  isLoading = false;
  isSubmitting = false;
  showDialog = true;
  form: UntypedFormGroup;
  isEmployee: boolean;

  constructor(
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig,
    private currentUserService: CurrentUserService,
    private privateNetworkService: PrivateNetworkService
  ) {}

  ngOnInit(): void {
    this.privateNetwork = this.dynamicDialogConfig.data.privateNetwork;
    this.networkEquipment = this.dynamicDialogConfig.data.networkEquipment;
    this.isEmployee = this.currentUserService.isEmployee();
    this.form = this.formBuilder.group({
      linkSpeed: [this.networkEquipment.privateNetworks[0].linkSpeed, Validators.required],
    });

    if (!this.networkEquipment.rack || !this.networkEquipment.rack.capacity) {
      // Workaround because rack info is not available in the collection responses
      this.isLoading = true;
      this.httpClient
        .get<any>(`/_/internal/dedicatedserverapi/v2/networkEquipments/${this.networkEquipment.id}`)
        .subscribe(
          (data: any) => {
            this.isLoading = false;
            this.networkEquipment.rack = data.rack;
          },
          (error: any) => {
            this.isLoading = false;
            this.networkEquipment.rack = [];
            this.error = $localize`:@@bma_privatenetworkmodifynetworkequipmentmodal_loaderrormessage: There is some issue in loading network equipment.`;
          }
        );
    }

    this.options = this.privateNetworkService.getPrivateNetworkPricesForNetworkEquipment(
      this.privateNetwork.salesOrgId
    );
  }

  modify() {
    const body = {
      linkSpeed: this.form.get('linkSpeed').value,
    };

    this.isSubmitting = true;
    this.httpClient
      .put<any>(
        `/_/internal/dedicatedserverapi/v2/networkEquipments/${this.networkEquipment.id}/privateNetworks/${this.privateNetwork.id}`,
        body
      )
      .subscribe(
        (data: any) => {
          this.isSubmitting = false;
          this.closeModal({
            portSpeedUpdated: true,
            updatedPortSpeed: body.linkSpeed,
          });
        },
        (error: any) => {
          this.error = error.error;
          this.isSubmitting = false;
          this.closeModal({ portSpeedUpdated: false });
        }
      );
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
