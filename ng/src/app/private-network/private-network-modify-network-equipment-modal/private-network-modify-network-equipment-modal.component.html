<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '600px'}">
    <ng-template pTemplate="header">
        <h3 class="modal-title pull-left" i18n="@@bma_privatenetworkmodifynetworkequipmentmodal_title">Upgrade / Downgrade Port Speed of {{ networkEquipment.id }}</h3>
    </ng-template>
    <div class="modal-body" [formGroup]="form">
        <app-loader *ngIf="isLoading"></app-loader>
        <div class="row" *ngIf="!isLoading && networkEquipment.rack && options">
            <div class="col-12">
                <p class="mt-3 mb-3 font-weight-bold" [ngClass]="{'text-muted' : (networkEquipment.rack.type != 'DEDICATED' && isEmployee)}" i18n="@@bma_privatenetworkmodifynetworkequipmentmodal_selectportspeed">Select Port Speed for Network Equipment {{ networkEquipment.id }}:</p>
                <div *ngFor="let option of options|keyvalue">
                    <input [id]="option.key" type="radio" formControlName="linkSpeed" [value]="+option.key" autofocus />
                    <label [for]="option.key" class="ml-1">{{ option.key|formatSpeed }} {{ (networkEquipment.rack.type !== 'DEDICATED') ? '(' + (option.value.price|formatCurrency:privateNetwork.salesOrgId) + ' / month)' : ''}}</label>
                </div>
            </div>
        </div>
    </div>
    <ng-template pTemplate="footer">
        <div class="mt-2">
            <span class="align-middle" *ngIf="error" class="text-danger">{{ error.errorMessage }}</span>
            <span *ngIf="form.valid && !error && networkEquipment.rack.type != 'DEDICATED'" class="align-middle mr-1"
                ><span i18n="@@bma_common_totalprice">Total price:</span> <strong *ngIf="options">{{ options[form.get('linkSpeed').value].price|formatCurrency:privateNetwork.salesOrgId }}</strong
                >. <span i18n="@@bma_common_nextinvoice">You will see the change in your next invoice.</span>
            </span>
            <button pButton type="button" icon="pi pi-times" iconPos="left" i18n-label="@@bma_common_close" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button pButton (click)="modify()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" [disabled]="!form.valid || form.get('linkSpeed').value === networkEquipment.privateNetworks[0].linkSpeed || error" class="p-button-primary" i18n-label="@@bma_privatenetworkmodifynetworkequipmentmodal_submit" label="{{ form.get('linkSpeed').value < networkEquipment.privateNetworks[0].linkSpeed ? 'Downgrade' : 'Upgrade' }} Port Speed"></button>
        </div>
    </ng-template>
</p-dialog>
