import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { PrivateNetworkService } from 'src/app/services/private-network.service';

@Component({
  selector: 'app-private-network-add-colocation-modal',
  templateUrl: './private-network-add-colocation-modal.component.html',
  styleUrls: ['./private-network-add-colocation-modal.component.css'],
})
export class PrivateNetworkAddColocationModalComponent implements OnInit {
  privateNetwork: any;
  options: any;
  filters: UntypedFormGroup;
  colocations: any;
  error: any;
  isLoading = false;
  isSubmitting = false;
  showDialog = true;
  form: UntypedFormGroup;

  constructor(
    private alertService: AlertService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig,
    private currentUserService: CurrentUserService,
    private privateNetworkService: PrivateNetworkService
  ) {}

  ngOnInit(): void {
    this.privateNetwork = this.dynamicDialogConfig.data.privateNetwork;
    this.form = this.formBuilder.group({
      colocation: [null, Validators.required],
      linkSpeed: [null, Validators.required],
    });
    this.filters = this.formBuilder.group({
      limit: [null],
      offset: [null],
      filter: [null],
    });
    this.loadColocations({});

    this.options = this.privateNetworkService.getPrivateNetworkPricesForColocation(this.privateNetwork.salesOrgId);
  }

  loadColocations(params: any): void {
    if (this.currentUserService.isEmployee()) {
      params.customerId = this.privateNetwork.customerId;
      params.salesOrgId = this.privateNetwork.salesOrgId;
    }
    params.privateNetworkEnabled = 'false';
    params.privateNetworkCapable = 'true';

    this.isLoading = true;
    this.colocations = null;
    this.form.reset();
    this.httpClient.get<any>(`/_/internal/dedicatedserverapi/v2/colocations`, { params }).subscribe(
      (data: any) => {
        this.colocations = data;
        this.isLoading = false;
      },
      (error: any) => {
        this.isLoading = false;
      }
    );
  }

  onColocationSelected(): void {
    this.form.patchValue({ linkSpeed: null });
    if (this.currentUserService.isEmployee() && this.form.get('colocation').value) {
      this.form.patchValue({ linkSpeed: this.form.get('colocation').value.contract.privateNetworkPortSpeed });
    }
  }

  isColocationAllowedToAdd(colocationToAdd: any): any {
    // See: https://github.com/angular/angular/issues/11763
    // Workaround for `[attr.disabled]="expression that evaluates to null or anything other than null"`

    if (this.currentUserService.isCustomer()) {
      return false;
    }

    if (colocationToAdd.privateNetworks.length > 0) {
      return false;
    }

    if (
      this.currentUserService.isEmployee() &&
      this.currentUserService.hasPermission('privatenetwork_manage') &&
      !colocationToAdd.contract.privateNetworkPortSpeed
    ) {
      return false;
    }

    return null;
  }

  add() {
    let linkSpeed = 0;

    const colocation = this.form.get('colocation').value;
    if (colocation) {
      linkSpeed = colocation.contract?.privateNetworkPortSpeed;
    }

    this.isSubmitting = true;
    this.httpClient
      .put<any>(
        `/_/internal/dedicatedserverapi/v2/colocations/${colocation.id}/privateNetworks/${this.privateNetwork.id}`,
        {
          linkSpeed,
        }
      )
      .subscribe(
        (data: any) => {
          this.isSubmitting = false;
          this.alertService.alert({
            type: 'success',
            description: $localize`:@@bma_privatenetworkaddcolocationmodal_requestinprogress:Request to add colocation ${colocation.id} is in progress. Please refresh the page for current status after few minutes.`,
          });
          this.closeModal({ colocation });
        },
        (error: any) => {
          this.isSubmitting = false;
          this.error = error.error;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ colocation });
        }
      );
  }

  onFiltersChange(): void {
    this.loadColocations(this.sanitise(this.filters.getRawValue()));
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.onFiltersChange();
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
