<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '600px'}">
    <ng-template pTemplate="header">
        <h3 class="modal-title pull-left" i18n="@@bma_privatenetworkaddnetworkequipmentsmodal_title">Add New Network Equipment to Private Network {{ privateNetwork.id }}</h3>
    </ng-template>
    <div class="modal-body" [formGroup]="form">
        <div class="row">
            <div class="col-12">
                <form [formGroup]="filters" (ngSubmit)="onFiltersChange()" role="form">
                    <div class="row mt-4 mb-4">
                        <div class="col-6">
                            <input type="text" class="form-control" formControlName="filter" i18n-placeholder="@@bma_common_searchplaceholder" placeholder="Filter for ID, IP address, MAC address, Contract ID, or reference" autofocus />
                        </div>
                        <div class="col-6">
                            <button pButton [disabled]="isLoading" i18n-label="@@bma_common_search" label="Search" icon="pi pi-search"></button>
                        </div>
                    </div>
                </form>

                <app-loader *ngIf="isLoading"></app-loader>

                <ng-container *ngIf="!isLoading && networkEquipments">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th scope="col" i18n="@@bma_common_id">ID</th>
                                    <th scope="col" i18n="@@bma_common_reference">Reference</th>
                                    <th scope="col" i18n="@@bma_common_location">Location</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngIf="!networkEquipments || networkEquipments?.networkEquipments.length === 0">
                                    <td colspan="999" class="text-center p-4" i18n="@@bma_privatenetworkaddnetworkequipmentmodal_nonetworkEquipments">No Network Equipments eligible for private network.</td>
                                </tr>
                                <tr *ngFor="let networkEquipment of networkEquipments?.networkEquipments" [ngClass]="{'disabled': isNetworkEquipmentAllowedToAdd(networkEquipment) === false}">
                                    <td>
                                        <div class="form-check form-check-inline">
                                            <input [id]="networkEquipment.id" type="radio" formControlName="networkEquipment" [value]="networkEquipment" class="form-check-input" (change)="onNetworkEquipmentSelected()" [attr.disabled]="isNetworkEquipmentAllowedToAdd(networkEquipment)" />
                                            <label [for]="networkEquipment.id" class="form-check-label text-monospace">{{ networkEquipment.id }}</label>
                                            &nbsp;<span *ngIf="networkEquipment.privateNetworks.length > 0" class="badge" [class.badge-danger-outline]="networkEquipment.privateNetworks[0].status === 'ERROR'" [class.badge-green-outline]="networkEquipment.privateNetworks[0].status === 'CONFIGURED'" [class.badge-warning-outline]="networkEquipment.privateNetworks[0].status === 'CONFIGURING' || networkEquipment.privateNetworks[0].status === 'REMOVING'">{{ networkEquipment.privateNetworks[0].status }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>{{ networkEquipment.contract.reference|default:'-' }}</strong>
                                    </td>
                                    <td>
                                        <app-equipment-location [location]="networkEquipment.location"></app-equipment-location>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <app-pagination [totalCount]="networkEquipments._metadata.totalCount" [limit]="networkEquipments._metadata.limit" [offset]="networkEquipments._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
                </ng-container>

                <div *ngIf="form.get('networkEquipment').value" class="form">
                    <p class="mt-3 mb-3 font-weight-bold" i18n="@@bma_privatenetworkaddnetworkequipmentmodal_portspeed">The port speed in the contract for network equipment rack {{ form.get('networkEquipment').value.id }} is set to {{ form.get('networkEquipment').value.contract.privateNetworkPortSpeed|formatSpeed }}</p>
                </div>
            </div>
        </div>
    </div>
    <ng-template pTemplate="footer">
        <div class="mt-2">
            <span class="align-middle" *ngIf="error" class="text-danger">{{ error.errorMessage }}</span>
            <button pButton type="button" icon="pi pi-times" iconPos="left" i18n-label="@@bma_common_close" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button pButton (click)="add()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" class="p-button-primary" [disabled]="!form.valid || error" i18n-label="@@bma_common_addtoprivatenetwork" label="Add to Private Network"></button>
        </div>
    </ng-template>
</p-dialog>
