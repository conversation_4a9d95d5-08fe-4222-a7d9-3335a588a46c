import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { PrivateNetworkService } from 'src/app/services/private-network.service';

@Component({
  selector: 'app-private-network-modify-server-modal',
  templateUrl: './private-network-modify-server-modal.component.html',
  styleUrls: ['./private-network-modify-server-modal.component.css'],
})
export class PrivateNetworkModifyServerModalComponent implements OnInit {
  privateNetwork: any;
  server: any;
  error: any;
  options: Array<string>;
  isLoadingRack = false;
  isLoadingOptions = false;
  isSubmitting = false;
  showDialog = true;
  form: UntypedFormGroup;

  constructor(
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig,
    private currentUserService: CurrentUserService,
    private privateNetworkService: PrivateNetworkService
  ) {}

  ngOnInit(): void {
    this.privateNetwork = this.dynamicDialogConfig.data.privateNetwork;
    this.server = this.dynamicDialogConfig.data.server;
    this.form = this.formBuilder.group({
      linkSpeed: [this.server.privateNetworks[0].linkSpeed, Validators.required],
    });

    if (!this.server.rack || !this.server.rack.capacity) {
      // Workaround because rack info is not available in the collection responses
      this.isLoadingRack = true;
      this.httpClient.get<any>(`/_/internal/dedicatedserverapi/v2/servers/${this.server.id}`).subscribe(
        (data: any) => {
          this.isLoadingRack = false;
          this.server.rack = data.rack;
        },
        (error: any) => {
          this.isLoadingRack = false;
        }
      );
    }
    this.options = this.privateNetworkService.getServerPortSpeeds();
  }

  isLinkSpeedSelectable(linkSpeed: any): any {
    // See: https://github.com/angular/angular/issues/11763
    // Workaround for `[attr.disabled]="expression that evaluates to null or anything other than null"`
    if (+linkSpeed === 25000 && this.server.rack?.type !== 'SHARED_200GE') {
      return false;
    }
    if (+linkSpeed === 10000 && this.server.rack?.capacity !== '10G') {
      return false;
    }
    if (+linkSpeed === this.server.privateNetworks[0].linkSpeed) {
      return false;
    }
    return null;
  }

  modify() {
    const body = {
      linkSpeed: this.form.get('linkSpeed').value,
    };

    this.isSubmitting = true;
    this.httpClient
      .put<any>(
        `/_/internal/dedicatedserverapi/v2/servers/${this.server.id}/privateNetworks/${this.privateNetwork.id}`,
        body
      )
      .subscribe(
        (data: any) => {
          this.isSubmitting = false;
          this.server.privateNetworks[0].status = 'CONFIGURING';
          this.server.privateNetworks[0].linkSpeed = body.linkSpeed;
          this.closeModal({ server: this.server });
        },
        (error: any) => {
          this.error = error.error;
          this.isSubmitting = false;
        }
      );
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
