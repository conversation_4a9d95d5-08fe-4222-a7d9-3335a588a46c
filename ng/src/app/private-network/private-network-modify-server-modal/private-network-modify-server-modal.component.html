<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '600px'}">
    <ng-template pTemplate="header">
        <h3 class="modal-title pull-left" i18n="@@bma_privatenetworkmodifyservermodal_title">Upgrade / Downgrade Port Speed of {{ server.id }}</h3>
    </ng-template>
    <div class="modal-body" [formGroup]="form">
        <app-loader *ngIf="isLoadingRack"></app-loader>
        <div class="row" *ngIf="!isLoadingRack && server.rack && options">
            <div class="col-12">
                <p class="mt-3 mb-3 font-weight-bold" [ngClass]="{'text-muted' : (server.rack.type != 'DEDICATED' && currentUserService.isEmployee())}" i18n="@@bma_privatenetworkmodifyservermodal_selectportspeed">Select Port Speed for server {{ server.id }}:</p>
                <div *ngFor="let option of options">
                    <input [id]="option" type="radio" formControlName="linkSpeed" [value]="+option" [attr.disabled]="isLinkSpeedSelectable(option)" autofocus />
                    <label [for]="option" [class.text-muted]="isLinkSpeedSelectable(option) === false" class="ml-1">{{ option|formatSpeed }}</label>
                </div>
            </div>
        </div>
    </div>
    <ng-template pTemplate="footer">
        <div class="mt-2">
            <span class="align-middle" *ngIf="error" class="text-danger">{{ error.errorMessage }}</span>
            <button pButton type="button" icon="pi pi-times" iconPos="left" i18n-label="@@bma_common_close" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button pButton (click)="modify()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" [disabled]="!form.valid || form.get('linkSpeed').value === server.privateNetworks[0].linkSpeed || error" class="p-button-primary" i18n-label="@@bma_privatenetworkmodifyservermodal_submit" label="{{ form.get('linkSpeed').value < server.privateNetworks[0].linkSpeed ? 'Downgrade' : 'Upgrade' }} Port Speed"></button>
        </div>
    </ng-template>
</p-dialog>
