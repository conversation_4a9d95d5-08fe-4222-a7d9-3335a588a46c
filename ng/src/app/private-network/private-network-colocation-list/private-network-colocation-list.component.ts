import { Component, OnInit, Input, NgZone } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { first } from 'rxjs';
import { LswNotificationHubService } from '@lsw/notification-hub';
import { ServerEvent } from 'src/app/models/server-event.model';
import { MercureService } from 'src/app/services/mercure.service';
import { PrivateNetworkAddColocationModalComponent } from '../private-network-add-colocation-modal/private-network-add-colocation-modal.component';
import { PrivateNetworkRemoveModalComponent } from '../private-network-remove-modal/private-network-remove-modal.component';

@Component({
  selector: 'app-private-network-colocation-list',
  templateUrl: './private-network-colocation-list.component.html',
  styleUrls: ['./private-network-colocation-list.component.css'],
})
export class PrivateNetworkColocationListComponent implements OnInit {
  @Input() privateNetwork: any;

  colocations: any;
  filters: UntypedFormGroup;
  isEmployee: boolean;
  isLoading: boolean;
  dynamicDialogRef: DynamicDialogRef;

  constructor(
    private activatedRoute: ActivatedRoute,
    private alertService: AlertService,
    private modalService: ModalService,
    private currentUserService: CurrentUserService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private router: Router,
    private notificationHubService: LswNotificationHubService,
    private mercureService: MercureService,
    private zone: NgZone
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.filters = this.formBuilder.group({
      limit: [null],
      offset: [null],
      filter: [null],
    });
    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  loadColocations(subscribeForMercure = false): void {
    this.isLoading = true;
    const params = this.sanitise(this.filters.getRawValue());
    params.privateNetworkEnabled = 'true';

    if (this.isEmployee) {
      params.customerId = this.privateNetwork.customerId;
      params.salesOrgId = this.privateNetwork.salesOrgId;
    }

    this.httpClient.get<any>('/_/internal/dedicatedserverapi/v2/colocations', { params }).subscribe(
      (data: any) => {
        this.colocations = data;
        this.isLoading = false;
        if (subscribeForMercure) {
          this.subscribeForMercureUpdate();
        }
      },
      (error: any) => {
        this.isLoading = false;
      }
    );
  }

  onQueryParamsChanged(queryParams): void {
    this.filters.reset(queryParams);
    this.loadColocations(true);
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  retry(colocation: any) {
    colocation.dirty = 'Retrying...';
    this.httpClient
      .post<any>(`/_/internal/nseapi/v2/privateNetworks/equipments/${colocation.id}/retryJob`, {})
      .subscribe(
        (data: any) => {
          colocation.dirty = 'Retried';
        },
        (error: any) => {
          colocation.dirty = error.error.errorMessage;
        }
      );
  }

  openPrivateNetworkAddColocationModal() {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkAddColocationModalComponent, {
      privateNetwork: this.privateNetwork,
    });
  }

  openPrivateNetworkRemoveModal(colocation: any) {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkRemoveModalComponent, {
      privateNetwork: this.privateNetwork,
      equipment: colocation,
      equipmentType: 'colocations',
    });
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }

  updateSingleColocation(message) {
    let isColocationInList = false;
    this.colocations.colocations.forEach((colocation) => {
      if (colocation.id === message.id) {
        isColocationInList = true;
        colocation.privateNetworks.forEach((privateNetwork, key) => {
          if (privateNetwork.id === message.data.privateNetworkId) {
            colocation.privateNetworks[key] = message.data;
          }
        });
      }
    });
    if (!isColocationInList) {
      this.filters.patchValue({ offset: null, limit: null, filter: null });
      this.loadColocations();
    }
  }

  removeSingleColocation(message) {
    let removedRow = -1;
    for (let i = 0; i < this.colocations.colocations.length; i++) {
      if (this.colocations.colocations[i].id === message.id) {
        removedRow = i;
        break;
      }
    }
    if (removedRow >= 0) {
      this.colocations.colocations.splice(removedRow, 1);
    }
  }

  subscribeForMercureUpdate() {
    this.notificationHubService.setTokenParams({
      customerId: this.privateNetwork.customerId,
      salesOrgId: this.privateNetwork.salesOrgId,
    });

    this.notificationHubService
      .getNotificationHubToken(this.mercureService.getTokenURL('nseapi'))
      .pipe(first())
      .subscribe({
        next: () => {
          // subscribe to topics...

          this.notificationHubService
            .sync<ServerEvent>(
              `${this.privateNetwork.customerId}/${this.privateNetwork.salesOrgId}/privateNetworkColocations`
            )
            .subscribe({
              next: (event) => {
                this.zone.run(() => {
                  if (event.action === 'private-network-colocation-remove' && event.status === 'REMOVED') {
                    this.removeSingleColocation(event);
                  } else {
                    this.updateSingleColocation(event);
                  }
                });
              },
              error: (error) => console.log({ error }),
            });
        },
      });
  }
}
