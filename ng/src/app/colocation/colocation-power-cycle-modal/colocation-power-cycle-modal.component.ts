import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert.service';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-colocation-power-cycle-modal',
  templateUrl: './colocation-power-cycle-modal.component.html',
  styleUrls: ['./colocation-power-cycle-modal.component.css'],
})
export class ColocationPowerCycleModalComponent implements OnInit {
  colocationId: string;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    private alertService: AlertService,
    private dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.colocationId = this.dynamicDialogConfig.data.colocationId;
  }

  powerCycleColocation(): void {
    this.isSubmitting = true;
    this.alertService.clear();
    if (this.colocationId) {
      this.httpClient.post<any>(`/_/internal/bmpapi/v2/colocations/${this.colocationId}/powerCycle`, {}).subscribe(
        (data) => {
          this.alertService.alert(
            {
              type: 'success',
              description: $localize`:@@bma_colocationpowercyclemodal_performed:Colocation power cycle performed. Please wait 10 minutes for your colocation to come up again.`,
            },
            true
          );
          this.isSubmitting = false;
          this.closeModal();
        },
        (error) => {
          this.alertService.alert(
            {
              type: 'error',
              description:
                $localize`:@@bma_colocationpowercyclemodal_errorpowercycling:Error while power cycling colocation` +
                ' ' +
                this.colocationId,
            },
            true
          );
          this.isSubmitting = false;
          this.closeModal();
        }
      );
    } else {
      this.closeModal();
    }
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }
}
