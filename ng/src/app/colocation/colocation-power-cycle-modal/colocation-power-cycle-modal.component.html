<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '400px' }">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_colocationpowercyclemodal_title" class="modal-title">Power Cycle Colocation</h3>
    </ng-template>
    <div i18n="@@bma_colocationpowercyclemodal_confirmation" class="modal-body">Are you sure you want to power cycle the colocation {{ colocationId }}?</div>
    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button pButton i18n-label="@@bma_common_cancel" type="button" icon="pi pi-times" iconPos="left" label="Cancel" class="p-button-secondary" (click)="closeModal()"></button>
            <button pButton i18n-label="@@bma_common_ok" (click)="powerCycleColocation()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" label="OK"></button>
        </div>
    </ng-template>
</p-dialog>
