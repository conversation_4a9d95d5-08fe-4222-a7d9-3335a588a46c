import { Component, Input, ViewChild } from '@angular/core';
import { NetworkDeviceManageSpeedModalComponent } from 'src/app/network-device/network-device-manage-speed-modal/network-device-manage-speed-modal.component';

@Component({
  selector: 'app-colocation-network-details',
  templateUrl: './colocation-network-details.component.html',
  styleUrls: ['./colocation-network-details.component.css'],
})
export class ColocationNetworkDetailsComponent {
  @Input() colocation: any;
  @Input() errorInformation: string[];
  @ViewChild(NetworkDeviceManageSpeedModalComponent, { static: false })
  capUncapComponent: NetworkDeviceManageSpeedModalComponent;

  equipmentType = 'colocations';
  networkInterfaceOrder = ['public'];
}
