<div class="row mb-2">
    <div class="col-12">
        <h3>
            <i class="fa fa-transfer mr-1" aria-hidden="true"></i>
            <span i18n="@@bma_common_networkdetails">Network Details</span>
        </h3>
    </div>
</div>
<ng-container *ngIf="colocation.networkInterfaces else interfacesNotAvailable">
    <p-accordion [multiple]="true" *ngIf="!isLoading">
        <p-accordionTab *ngFor="let networkInterfaceKey of networkInterfaceOrder">
            <ng-template pTemplate="header">
                <div class="row col-sm-12 align-items-center">
                    <div class="col-sm-3">
                        <h5 class="mt-2">{{ networkInterfaceKey | uppercase}}</h5>
                        <span class="h5" i18n="@@bma_colocationnetworkdetails_notavailable" *ngIf="!colocation.networkInterfaces[networkInterfaceKey]"> - Not Available </span>
                    </div>
                    <div class="col-sm-3" *ngIf="colocation.networkInterfaces[networkInterfaceKey].ports">
                        <span class="h5 badge" [ngClass]="{'badge-success': colocation.networkInterfaces[networkInterfaceKey].ports.length >= 1,'badge-warning': colocation.networkInterfaces[networkInterfaceKey].ports.length == 0}">
                            {{ colocation.networkInterfaces[networkInterfaceKey].ports.length }} Port{{ colocation.networkInterfaces[networkInterfaceKey].ports.length > 1 ?
                                's' : '' }}
                        </span>
                    </div>
                </div>
            </ng-template>
            <ng-template pTemplate="content" class="p-p-0">
                <div class="p-grid p-flex-column flex-nowrap">
                    <app-switch-port-status [equipment]="colocation" [equipmentType]="equipmentType" [networkInterfaceType]="networkInterfaceKey"></app-switch-port-status>
                </div>
            </ng-template>
        </p-accordionTab>
    </p-accordion>
</ng-container>
<ng-template #interfacesNotAvailable>
    <div class="p-col-12 p-py-0  p-pr-2">
        <div class="p-grid grid-row">
            <span i18n="@@bma_common_networkinterfacenotavailable" class="text-muted">Network Interface Not Available</span>
        </div>
    </div>
</ng-template>
