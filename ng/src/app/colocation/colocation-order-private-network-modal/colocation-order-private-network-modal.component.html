<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '800px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_common_orderprivatenetwork" class="modal-title">Order Private Network</h3>
    </ng-template>

    <form [formGroup]="form" (ngSubmit)="orderPrivateNetwork()">
        <div class="modal-body">
            <div class="form-group">
                <div class="ml-4">
                    <div i18n="@@bma_colocationorderprivatenetworkmodal_description1" class="row">The Private Network connectivity service offers the possibility to connect your Colocation rack to other Leaseweb products.</div>
                    <div class="row mt-3 mb-3 d-block">
                        <p i18n="@@bma_colocationorderprivatenetworkmodal_description2" class="mb-0">The service consists of 2 redundant router uplinks delivered to your Colocation rack.</p>
                        <p i18n="@@bma_common_followcapacity">We offer the following capacity:</p>
                    </div>
                    <div class="row">
                        <h4 i18n="@@bma_common_selectuplinkcapacity">Select an uplink capacity</h4>
                    </div>

                    <ng-container *ngIf="uplinkCapacities">
                        <div class="row">
                            <div class="form-check form-check-inline">
                                <input type="radio" class="form-check-input" formControlName="uplinkCapacity" id="uplink_capacity_10000" value="10000" autofocus />
                                <label class="form-check-label" for="uplink_capacity_10000">
                                    {{ (uplinkCapacities['10000']?.value|default) }}
                                    (
                                    <ng-container *ngIf="uplinkCapacities['10000']?.price|default; else free">
                                        {{ uplinkCapacities['10000']?.price|default|formatCurrency:colocation.contract.salesOrgId }} /
                                        <span i18n="@@bma_common_month">month</span>
                                    </ng-container>
                                    <ng-template #free>
                                        <span i18n="@@bma_common_free">Free</span>
                                    </ng-template>
                                    )
                                </label>
                            </div>
                        </div>

                        <div class="row">
                            <div class="form-check form-check-inline">
                                <input type="radio" formControlName="uplinkCapacity" class="form-check-input" [attr.disabled]="blacklisted40GbUplinkCapacitySites.indexOf(colocation.location.site) == -1 ? null : true" id="uplink_capacity_40000" value="40000" />
                                <label class="form-check-label" for="uplink_capacity_40000">
                                    {{ (uplinkCapacities['40000']?.value|default) }}
                                    (
                                    <ng-container *ngIf="uplinkCapacities['40000']?.price|default; else contactsales">
                                        {{ uplinkCapacities['40000']?.price|default|formatCurrency:colocation.contract.salesOrgId }} /
                                        <span i18n="@@bma_common_month">month</span>
                                    </ng-container>
                                    <ng-template #contactsales>
                                        <span i18n="@@bma_common_contactsales">Contact our Sales Department</span>
                                    </ng-template>
                                    )
                                </label>
                            </div>
                        </div>

                        <div class="row">
                            <div class="form-check form-check-inline">
                                <input type="radio" formControlName="uplinkCapacity" class="form-check-input" [attr.disabled]="blacklisted100GbUplinkCapacitySites.indexOf(colocation.location.site) == -1 ? null : true" id="uplink_capacity_100000" value="100000" />
                                <label class="form-check-label" for="uplink_capacity_100000">
                                    {{ uplinkCapacities['100000']?.value }}
                                    (
                                    <ng-container *ngIf="uplinkCapacities['100000']?.price|default; else contactsales">
                                        {{ (uplinkCapacities['100000']?.price|default|formatCurrency:colocation.contract.salesOrgId)}} /
                                        <span i18n="@@bma_common_month">month</span>
                                    </ng-container>
                                    <ng-template #contactsales>
                                        <span i18n="@@bma_common_contactsales">Contact our Sales Department</span>
                                    </ng-template>
                                    )
                                </label>
                            </div>
                        </div>
                    </ng-container>
                </div>
                <div class="ml-4 mt-3">
                    <div i18n="@@bma_colocationorderprivatenetworkmodal_switchdescription1" class="row">By default, switches are not included in the service however there is an option to lease a switch from us.</div>
                    <div i18n="@@bma_colocationorderprivatenetworkmodal_switchdescription2" class="row mt-3 mb-3">Leaseweb will do the initial basic setup. Further management and monitoring of the switch are your responsibility.</div>
                    <div class="row">
                        <h4 i18n="@@bma_colocationorderprivatenetworkmodal_switchoption">Select a switch option</h4>
                    </div>
                    <div class="row">
                        <div class="form-check form-check-inline">
                            <input type="radio" formControlName="switch" class="form-check-input" id="switch_yes" value="yes" />
                            <label i18n="@@bma_common_yes" class="form-check-label" for="switch_yes">Yes</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input type="radio" formControlName="switch" class="form-check-input" checked="checked" id="switch_no" value="no" />
                            <label i18n="@@bma_common_no" class="form-check-label" for="switch_no">No</label>
                        </div>
                    </div>
                    <div i18n="@@bma_colocationorderprivatenetworkmodal_aftersubmit" class="row">After submitting this form, you will be contacted by our Sales representatives.</div>
                    <div class="row mt-3">
                        <small>
                            <span i18n-title="@@bma_common_kb" title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></span>
                            <span i18n="@@bma_colocationorderprivatenetworkmodal_kb">For more information about Private Network for Colocation please visit our <a target="_blank" href="https://kb.leaseweb.com/network/private-network">KB page</a>.</span>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_cancel" pButton type="button" icon="pi pi-times" iconPos="left" label="Cancel" class="p-button-secondary" (click)="closeModal()"></button>
            <button i18n-label="@@bma_common_submit" pButton (click)="orderPrivateNetwork()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" [disabled]="!uplinkCapacities" label="Submit" class="p-button-success"></button>
        </div>
    </ng-template>
</p-dialog>
