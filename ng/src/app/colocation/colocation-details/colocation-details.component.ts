import { Component, OnInit, Input } from '@angular/core';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { ReferenceEditModalComponent } from 'src/app/reference-edit-modal/reference-edit-modal.component';
import { ModalService } from 'src/app/services/modal.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { PowerbarOutletOperationModalComponent } from 'src/app/powerbar/powerbar-outlet-operation-modal/powerbar-outlet-operation-modal.component';

@Component({
  selector: 'app-colocation-details',
  templateUrl: './colocation-details.component.html',
  styleUrls: ['./colocation-details.component.css'],
})
export class ColocationDetailsComponent implements OnInit {
  @Input() colocation: any;
  powerPortName: any;
  powerPort: any;
  dataUsed: any;
  equipmentType = 'colocations';
  displayAllSubnets = true;
  isEmployee = false;
  dynamicDialogRef: DynamicDialogRef;

  constructor(
    private modalService: ModalService,
    private currentUserService: CurrentUserService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
  }

  selectedPowerOutlet(powerPortName: string, powerPort: string): void {
    this.powerPortName = powerPortName;
    this.powerPort = powerPort;
  }

  openReferenceEditModal() {
    this.dynamicDialogRef = this.modalService.show(ReferenceEditModalComponent, {
      equipment: this.colocation,
      equipmentType: this.equipmentType,
    });

    this.dynamicDialogRef.onClose.subscribe((reason: any) => {
      if (typeof reason === 'string') {
        this.colocation.contract.reference = reason;
      }
    });
  }

  openPowerbarOutletOperationModal(name: string, outlet: string) {
    this.dynamicDialogRef = this.modalService.show(PowerbarOutletOperationModalComponent, {
      name,
      outlet,
    });
  }

  onDisplayAdditionalSubnets(): void {
    this.displayAllSubnets = !this.displayAllSubnets;
  }

  getRemoteHandsType(): string {
    return null !== this.colocation.contract.remoteHands.type && this.colocation.contract.remoteHands.type.length > 0
      ? this.colocation.contract.remoteHands.type
      : $localize`:@@bma_common_basic:Basic`;
  }
}
