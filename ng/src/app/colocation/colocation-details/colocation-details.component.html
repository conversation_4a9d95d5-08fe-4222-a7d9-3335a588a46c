<div class="row">
    <div class="col-md-6">
        <h3>
            <span i18n="@@bma_common_technicaldetails" class="mr-1">Technical Details</span>
            <sup>
                <a href="https://kb.leaseweb.com/kb/colocation/colocation-managing-your-colocation-details/" target="_blank">
                    <i i18n-title="@@bma_common_kb" title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i>
                </a>
            </sup>
        </h3>
        <table class="table table-sm">
            <colgroup>
                <col class="table-col-25" />
                <col class="table-col-75" />
            </colgroup>
            <tbody>
                <tr>
                    <th i18n="@@bma_common_publicip">Public IP</th>
                    <td>
                        <span class="text-muted" i18n="@@bma_common_notavailable">Not Available</span>
                    </td>
                </tr>
            </tbody>
        </table>

        <h3 class="mb-2" i18n="@@bma_common_pduinformation">PDU Information</h3>
        <table class="table table-sm">
            <thead>
                <tr>
                    <th i18n="@@bma_common_pdu" scope="col">PDU</th>
                    <th i18n="@@bma_common_outlet" scope="col">Outlet</th>
                    <th scope="col"></th>
                </tr>
            </thead>

            <tbody>
                <ng-container *ngIf="(colocation.powerPorts | keyvalue)?.length > 0; else noPowerPorts">
                    <tr *ngFor="let powerPort of colocation.powerPorts">
                        <td *ngIf="isEmployee">
                            <a [routerLink]="['/powerbars', powerPort.name]">
                                {{ powerPort.name | uppercase }}
                            </a>
                        </td>
                        <td *ngIf="!isEmployee">{{ powerPort.name | uppercase }}</td>

                        <td>{{ powerPort.port | uppercase }}</td>
                        <td class="text-right" *ngIf="isEmployee">
                            <a href="javascript:void(0);" (click)="openPowerbarOutletOperationModal(powerPort.name, powerPort.port)">
                                <span class="fa fa-administration"></span>
                            </a>
                        </td>
                    </tr>
                </ng-container>
                <ng-template #noPowerPorts>
                    <tr>
                        <td i18n="@@bma_colocationdetails_nopduoutlets" colspan="3" class="text-center p-4">There are no PDU outlets available for this colocation</td>
                    </tr>
                </ng-template>
            </tbody>
        </table>
    </div>

    <div class="col-md-6">
        <h3 class="mb-2">
            <span i18n="@@bma_common_administrativedetails" class="mr-1">Administrative Details</span>
            <sup>
                <a href="https://kb.leaseweb.com/kb/colocation/colocation-managing-your-colocation-details/#ManagingyourColocationdetails-ManagingaColocatedServerorRack" target="_blank">
                    <i i18n-title="@@bma_common_kb" title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i>
                </a>
            </sup>
        </h3>

        <table class="table table-sm">
            <tbody>
                <tr *ngIf="colocation.contract?.aggregationPackId|default">
                    <th i18n="@@bma_common_aggregationpack">Aggregation Pack</th>
                    <td>
                        <a [routerLink]="['/aggregationPacks', colocation.contract?.aggregationPackId]">
                            <span class="text-monospace">{{ colocation.contract?.aggregationPackId }}</span>
                        </a>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_deliverystatus">Delivery Status</th>
                    <td>
                        <app-contract-delivery-status [deliveryStatus]="colocation.contract?.deliveryStatus|default:'UNASSIGNED'"> </app-contract-delivery-status>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_contractstatus">Contract Status</th>
                    <td>
                        <app-contract-status [status]="colocation.contract?.status|default:'UNASSIGNED'"></app-contract-status>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_id">ID</th>
                    <td>{{ colocation.id }}</td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_product">Product</th>
                    <td>{{ colocation.type }}</td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_location">Location</th>
                    <td>
                        <app-equipment-location [location]="colocation.location"></app-equipment-location>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_serviceid">Service ID</th>
                    <td>
                        <ng-container *ngIf="colocation.contract.id|default; else notAvailable">
                            {{ colocation.contract.id }}
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_reference">Reference</th>
                    <td>
                        <ng-container *ngIf="colocation.contract.reference|default; else notAvailable">
                            {{ colocation.contract.reference|default }}
                        </ng-container>
                        <a i18n-title="@@bma_common_editreference" href (click)="openReferenceEditModal(); false" class="pull-right" title="Edit Reference">
                            <span class="fa fa-change" aria-hidden="true"></span>
                        </a>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_billingfrequency">Billing Frequency</th>
                    <td>
                        <ng-container *ngIf="colocation.contract; else notAvailable">
                            {{ colocation.contract.billingCycle | default }}
                            {{ colocation.contract.billingFrequency | default | lowercase }}(s)
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_contractterm">Contract Term</th>
                    <td>
                        <ng-container *ngIf="colocation.contract; else notAvailable">
                            {{ colocation.contract.contractTerm }} month(s)
                            <ng-container *ngIf="colocation.contract.contractType != 'NORMAL'">
                                &nbsp;
                                <span class="badge badge-primary"> {{ colocation.contract.contractType|capitalize }} Contract </span>
                            </ng-container>
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_startdate">Start Date</th>
                    <td>
                        <ng-container *ngIf="colocation.contract.startsAt|default; else notAvailable">
                            {{ colocation.contract.startsAt|lswDate }}
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_enddate">End Date</th>
                    <td>
                        <ng-container *ngIf="colocation.contract.endsAt|default; else notAvailable">
                            {{ colocation.contract.endsAt|lswDate }}
                        </ng-container>
                    </td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_subnets">Subnets</th>
                    <td>
                        <ng-container *ngIf="(colocation.contract.subnets|default:[]).length > 0; else notAvailable">
                            <ng-container *ngFor="let subnet of colocation.contract.subnets |keyvalue| slice:0:3">
                                {{ subnet.value.quantity }} x IPV{{ subnet.value.version }} :
                                {{ subnet.value.description }}
                                <br *ngIf="subnet.key < 2" />
                            </ng-container>
                            <div *ngIf="(colocation.contract.subnets|default:[]).length > 3" [ngClass]="{'d-none': displayAllSubnets}">
                                <ng-container *ngFor="let subnet of colocation.contract.subnets |keyvalue| slice:3">
                                    {{ subnet.value.quantity }} x IPV{{ subnet.value.version }} :
                                    {{ subnet.value.description }}
                                    <br />
                                </ng-container>
                            </div>
                            <ng-container *ngIf="(colocation.contract.subnets|default:[]).length > 3">
                                <a (click)="onDisplayAdditionalSubnets()" class="pull-right" i18n-title="@@bma_common_additionalsubnets" title="Additional subnets">
                                    <span i18n-title="@@bma_common_additionalsubnets" title="Additional subnets" class="fa fa-details" aria-hidden="true"></span>
                                </a>
                            </ng-container>
                        </ng-container>
                    </td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_connectivity">Connectivity</th>
                    <td>
                        <app-data-pack-display [equipment]="colocation" [equipmentType]="equipmentType" [isEmployee]="isEmployee"></app-data-pack-display>
                    </td>
                </tr>

                <tr>
                    <th i18n-title="@@bma_common_dataused">Data used</th>
                    <td class="data_used">
                        <app-data-usage [equipment]="colocation" [equipmentType]="equipmentType"></app-data-usage>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_remotehandspackage">Remote Hands Package</th>
                    <td>{{ getRemoteHandsType() }}</td>
                </tr>
            </tbody>
        </table>
        <app-private-network-information-section [equipment]="colocation" [equipmentType]="equipmentType"></app-private-network-information-section>
    </div>
</div>
<ng-template #notAvailable>
    <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
</ng-template>
<ng-template #pendingContractModification>
    <span title="Pending contract modification" class="float-right text-muted">
        <i class="fa fa-refresh"></i>
        <span i18n-title="@@bma_common_pendingcontractmodification" class="sr-only">Pending contract modification</span>
    </span>
</ng-template>
