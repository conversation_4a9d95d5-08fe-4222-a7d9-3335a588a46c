import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { CommerceService } from 'src/app/services/commerce.service';
import { IpV6RequestModalComponent } from 'src/app/ip/ip-v6-request-modal/ip-v6-request-modal.component';
import { ModalService } from 'src/app/services/modal.service';
import { SiteService } from 'src/app/services/site.service';
import { ColocationOrderPrivateNetworkModalComponent } from '../colocation-order-private-network-modal/colocation-order-private-network-modal.component';
import { ColocationPowerCycleModalComponent } from '../colocation-power-cycle-modal/colocation-power-cycle-modal.component';

@Component({
  selector: 'app-colocation-tabs',
  templateUrl: './colocation-tabs.component.html',
  styleUrls: ['./colocation-tabs.component.css'],
})
export class ColocationTabsComponent implements OnInit {
  isLoading = false;
  colocation: any;
  colocationId: string;
  isEmployee = false;

  constructor(
    private activatedRoute: ActivatedRoute,
    private titleService: Title,
    private currentUserService: CurrentUserService,
    private httpClient: HttpClient,
    private modalService: ModalService,
    private router: Router,
    private siteService: SiteService,
    private commerceService: CommerceService
  ) {}

  ngOnInit(): void {
    this.colocationId = this.activatedRoute.snapshot.url[1].path;
    this.isEmployee = this.currentUserService.isEmployee();

    this.isLoading = true;

    this.httpClient.get<any>(`/_/internal/dedicatedserverapi/v2/colocations/${this.colocationId}`).subscribe({
      next: (data: any) => {
        this.isLoading = false;
        this.colocation = data;
        this.titleService.setTitle(
          `${this.colocation.id} - Colocation ${
            this.colocation.contract.reference ? this.colocation.contract.reference : ''
          }| Leaseweb`
        );

        /*eslint-disable */
        const event = new Event('update_product_navbar');
        event['customerId'] = this.colocation.contract.customerId;
        event['salesOrgId'] = this.colocation.contract.salesOrgId;
        event['country'] = this.siteService.getCountry(this.colocation.contract.salesOrgId);
        window.dispatchEvent(event);
        /*eslint-enable */
      },
      error: (error: any) => {
        this.isLoading = false;
        this.router.navigate(['**']);
      },
    });
  }

  openOrderPrivateNetworkModal() {
    this.modalService.show(ColocationOrderPrivateNetworkModalComponent, {
      colocation: this.colocation,
    });
  }

  openPowerCycleModal() {
    this.modalService.show(ColocationPowerCycleModalComponent, {
      colocationId: this.colocation.id,
    });
  }

  openIpV6RequestModal() {
    this.modalService.show(IpV6RequestModalComponent, {
      equipment: this.colocation,
      equipmentType: 'colocations',
    });
  }

  getCommerceRequestMoreIpsUrl(): string {
    if (this.colocation.type === 'Shared Colocation') {
      return this.commerceService.getCommerceConfigureProductUrl(this.colocation.contract.id, 'SRDCOLO02_MOD_IPV4');
    }
    return this.commerceService.getCommerceConfigureProductUrl(this.colocation.contract.id, 'COLOCA02_MOD_IPV4');
  }

  onColocationLoaded(component) {
    if (this.colocation) {
      component.equipment = this.colocation;
      component.colocation = this.colocation;
      component.equipmentId = this.colocation.id;
      component.equipmentType = 'colocations';
    }
  }
}
