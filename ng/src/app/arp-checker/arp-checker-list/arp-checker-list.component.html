<div class="row mb-4">
    <div class="col">
        <h1>ARP Checker</h1>
        <div class="mt-4"><p i18n="@@bma_arpchecker_header">This ARP Checker page does not include any arp changes for IP Addresses routed to cr01.ams-01, cr02.ams-01, cr07.ams-01 and cr08.ams-01. These routers are considered legacy and we do not support them.</p></div>
    </div>
</div>

<form role="form" [formGroup]="filters" (ngSubmit)="submitFilters()">
    <div class="row mb-3">
        <div class="col">
            <input type="text" formControlName="filter" class="form-control m-0" placeholder="Filter by Subnet, IP or MAC Address..." autofocus />
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-3">
            <select class="form-control" formControlName="salesOrgId">
                <option value="null">Sales Org ID</option>
                <option *ngFor="let salesOrgId of getSalesOrgIds()" [value]="salesOrgId">{{ salesOrgId }}</option>
            </select>
        </div>
        <div class="col-3">
            <select class="form-control" formControlName="site">
                <option value="null">Site</option>
                <option *ngFor="let site of getSites()" [value]="site">{{ site }}</option>
            </select>
        </div>
        <div class="col-3">
            <input type="text" formControlName="router" class="form-control m-0" placeholder="Filter by Router" />
        </div>
        <div class="col-3">
            <input type="text" formControlName="interface" class="form-control m-0" placeholder="Filter by Interface" />
        </div>
        <div class="col-6">&nbsp;</div>
    </div>
    <div class="row margin-left-0">
        <label i18n="@@bma_arpchecker_latestip_entry" class="col-form-label" for="latestIp">Latest IP Entry</label>
        <div class="col-sm-9 margin-top-6">
            <div class="checkbox">
                <input type="checkbox" checked formControlName="latestIp" class="form-check-input" id="latestIp" />
            </div>
        </div>
    </div>

    <div class="row mb-3 mt-3">
        <div class="col text-right">
            <button pButton type="button" (click)="clearFilters()" label="Reset" icon="pi pi-refresh" class="p-button-secondary mr-2 text-white"></button>
            <button pButton [disabled]="isLoading" label="Search" icon="pi pi-search"></button>
        </div>
    </div>
</form>
<app-loader *ngIf="isLoading"></app-loader>

<div *ngIf="!isLoading && arpWatchEntries" class="table-responsive">
    <table class="table table-striped">
        <thead>
            <tr>
                <th scope="col" class="text-center table-col-size-2">Time</th>
                <th scope="col" class="text-center table-col-size-2">Ip Address</th>
                <th scope="col" class="text-center table-col-size-2">MAC Address</th>
                <th scope="col" class="text-center table-col-size-2">Router</th>
                <th scope="col" class="text-center table-col-size-2">Site</th>
                <th scope="col" class="text-center table-col-size-2">Sales Org Id</th>
                <th scope="col" class="text-center table-col-size-2">Interface</th>
            </tr>
        </thead>

        <tbody>
            <tr *ngIf="arpWatchEntries.arpwatchEntries.length === 0">
                <td colspan="999" class="text-center p-3">No ARP Entries found.</td>
            </tr>
            <tr *ngFor="let arpCheckerEntry of arpWatchEntries.arpwatchEntries">
                <td class="text-center text-nowrap">
                    {{ arpCheckerEntry.timestamp|timeAgo }} ago
                    <br />
                    <small class="text-muted">{{ arpCheckerEntry.timestamp|lswDateTime }}</small>
                </td>

                <td class="text-center text-nowrap">
                    <span class="text-monospace selectable">
                        <a [routerLink]="['/']" [queryParams]="{ipAddress: arpCheckerEntry.ip}">{{ arpCheckerEntry.ip }}</a>
                    </span>
                </td>

                <td class="text-center text-nowrap">
                    <a [routerLink]="['/']" [queryParams]="{macAddress: arpCheckerEntry.mac}">{{ arpCheckerEntry.mac }}</a>
                </td>

                <td class="text-center text-nowrap">
                    <a [routerLink]="['/networkDevices', arpCheckerEntry.router]">{{ arpCheckerEntry.router }}</a>
                </td>
                <td class="text-center text-nowrap">
                    {{ arpCheckerEntry.site }}
                </td>
                <td class="text-center text-nowrap">
                    {{ arpCheckerEntry.salesOrgId }}
                </td>
                <td class="text-center text-nowrap">
                    {{ arpCheckerEntry.interface }}
                </td>
            </tr>
        </tbody>
    </table>
</div>

<div class="row mb-3" *ngIf="!isLoading && arpWatchEntries">
    <div class="col-12">
        <app-pagination [totalCount]="arpWatchEntries._metadata.totalCount" [limit]="arpWatchEntries._metadata.limit" [offset]="arpWatchEntries._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>
