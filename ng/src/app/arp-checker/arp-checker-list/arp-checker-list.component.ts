import { AlertService } from 'src/app/services/alert.service';
import { SiteService } from 'src/app/services/site.service';
import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { CustomValidator } from 'src/app/validators/custom.validator';

@Component({
  selector: 'app-arp-checker-list',
  templateUrl: './arp-checker-list.component.html',
  styleUrls: ['./arp-checker-list.component.css'],
})
export class ArpCheckerListComponent implements OnInit {
  arpWatchEntries: any;
  filters: UntypedFormGroup;
  isLoading = false;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private httpClient: HttpClient,
    private titleService: Title,
    public currentUserService: CurrentUserService,
    private readonly formBuilder: UntypedFormBuilder,
    private customValidator: CustomValidator,
    private alertService: AlertService,
    private siteService: SiteService
  ) {}

  ngOnInit(): void {
    this.filters = this.formBuilder.group({
      filter: [null],
      interface: [null],
      router: [null],
      limit: [null],
      offset: [null],
      site: [null],
      salesOrgId: [null],
      latestIp: [null],
    });

    this.titleService.setTitle('Arp Checker | Leaseweb');

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    this.alertService.clear();
    this.filters.reset(queryParams);
    const params = this.sanitise(this.filters.getRawValue());

    if (this.filters.valid) {
      if (params.filter) {
        if (this.customValidator.isValidIp(params.filter) || this.customValidator.isValidCidr(params.filter)) {
          params.ip = params.filter;
        } else if (this.customValidator.isValidMacAddress(params.filter)) {
          params.mac = params.filter;
        } else {
          this.alertService.alert(
            {
              type: 'error',
              description:
                $localize`:@@bma_arpchecker_invalidip:Please enter a valid Subnet, IP or MAC Address` +
                ': ' +
                params.filter,
            },
            true
          );
          return;
        }
      }

      this.isLoading = true;
      this.httpClient.get<any>('/_/internal/nseapi/v2/arpwatchEntries', { params }).subscribe({
        next: (data: any) => {
          this.isLoading = false;
          this.arpWatchEntries = data;
          this.titleService.setTitle('ARP Checker | Leaseweb');
        },
        error: (error: any) => {
          this.isLoading = false;
          this.alertService.alertApiError(error.error, true);
        },
      });
    }
  }

  getSalesOrgIds(): Array<any> {
    return this.siteService.salesOrgIds();
  }

  getSites(): Array<any> {
    return this.siteService.sites();
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  clearFilters(): void {
    this.filters.reset();
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
    });
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
