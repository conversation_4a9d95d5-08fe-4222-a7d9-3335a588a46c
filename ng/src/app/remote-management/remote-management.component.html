<div class="container">
    <div class="row">
        <div class="col-md-12">
            <h1>
                <span i18n="@@bma_common_remotemanagement" class="mr-1">Remote Management</span>
                <sup>
                    <a href="https://kb.leaseweb.com/kb/remote-management-of-your-dedicated-server/remote-management-of-your-dedicated-server-overview" target="_blank">
                        <span title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></span>
                    </a>
                </sup>
            </h1>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <div class="col-sm">
                <h2 i18n="@@bma_remotemanagement_vpncredentials">VPN Login Credentials</h2>
                <p i18n="@@bma_remotemanagement_vpncredentialsdescription">With the below login credentials you can login to our Remote Management network. A password can be auto generated. If you lost your password simply generate a new one.</p>
                <div>
                    <label i18n="@@bma_remotemanagement_username">Username:</label>
                    <span>
                        <strong> {{ customerId }} </strong>
                    </span>
                </div>
                <div *ngIf="!password">
                    <button pButton (click)="generateNewPassword()" [loading]="isGenerating" i18n-label="bma_remotemanagement_generatevpnpassword" label="Generate VPN Password"></button>
                </div>
                <div *ngIf="password">
                    <label i18n="@@bma_remotemanagement_password">Password:</label>
                    <span>
                        <code> {{ password }} </code>
                        <button pButton type="button" icon="pi pi-copy" class="p-button-sm ml-1" (click)="doCopy(password)" style="font-size: .05rem;"></button>
                    </span>
                </div>
            </div>
            <div class="col-sm">
                <h2 i18n="@@bma_remotemanagement_downloadvpnprofiles">Download OpenVPN Configuration Profiles</h2>
                <app-loader *ngIf="isLoadingProfiles"></app-loader>
                <ul *ngIf="!isLoadingProfiles">
                    <li *ngFor="let profile of profiles">
                        <a i18n="@@bma_remotemanagement_downloadconfigurationfile" class="btn-link" href (click)="downloadOpenVPNProfile(profile.remote); false">
                            Download Configuration File for
                            {{ profile.satelliteDatacenters.join(', ') }}
                        </a>
                    </li>
                </ul>

                <h2 i18n="@@bma_remotemanagement_downloadvpnclient">Download OpenVPN Client</h2>
                <ul>
                    <li>
                        <a i18n="@@bma_remotemanagement_downloadopenvpn" target="_blank" class="btn-link" href="https://openvpn.net/community-downloads/"> Download OpenVPN (Windows and Linux users) </a>
                    </li>
                    <li>
                        <a i18n="@@bma_remotemanagement_downloadtunnelblick" target="_blank" class="btn-link" href="https://tunnelblick.net/downloads.html"> Download Tunnelblick (MacOS users) </a>
                    </li>
                </ul>

                <p i18n="@@bma_remotemanagement_openvpnsetup">
                    For information on setting up the OpenVPN Client, please visit our
                    <a href="https://kb.leaseweb.com/kb/network/network-remote-management-vpn-connection" target="_blank"> Knowledge Base </a>
                </p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="alert alert-info" role="alert">
                <p i18n="@@bma_remotemanagement_kvmremotecontrol">"KVM Remote Control" provides you with the necessary functionality to monitor, troubleshoot, and repair your dedicated server anytime, from anywhere in the world.</p>

                <h4 i18n="@@bma_remotemanagement_kvmbenefits" class="alert-heading ml-0 pl-0">Key Benefits:</h4>
                <ul>
                    <li i18n="@@bma_remotemanagement_kvmbenefits1">Access your dedicated server after a failure, power outage, or loss of network connection</li>
                    <li i18n="@@bma_remotemanagement_kvmbenefits2">Remotely view your dedicated servers internal event logs and power-on self-test codes for diagnostic purposes</li>
                    <li i18n="@@bma_remotemanagement_kvmbenefits3">Boot your dedicated server from an ISO file located on your client computer</li>
                    <li i18n="@@bma_remotemanagement_kvmbenefits4">Diagnose and restart a dedicated server after a failure</li>
                    <li i18n="@@bma_remotemanagement_kvmbenefits5">Readouts on all sensors including power, fans, disks, temperature, and voltage</li>
                </ul>
                <p i18n="@@bma_remotemanagement_enablekvm">To enable this feature, please contact <a href="mailto:<EMAIL>">support&#64;leaseweb.com</a>.</p>
            </div>
        </div>
    </div>
</div>
