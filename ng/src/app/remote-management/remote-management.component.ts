import { Component, OnInit } from '@angular/core';
import { Clipboard } from '@angular/cdk/clipboard';
import { HttpClient } from '@angular/common/http';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-remote-management',
  templateUrl: './remote-management.component.html',
  styleUrls: ['./remote-management.component.css'],
})
export class RemoteManagementComponent implements OnInit {
  profiles: Array<any> = [];
  customerId: string;
  password: string;
  isGenerating = false;
  isLoadingProfiles = false;
  charset = 'abcdefghijklnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789$@$^!#)(%*?&';
  regularExpression = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$@$^!#)(%*?&])[A-Za-z\d$@$^!#)(%*?&]{16,}/;

  constructor(
    private httpClient: HttpClient,
    private titleService: Title,
    private currentUserService: CurrentUserService,
    private clipboard: Clipboard
  ) {}

  ngOnInit(): void {
    this.titleService.setTitle($localize`:@@bma_remotemanagement_title:Remote Management` + ' | Leaseweb');
    this.customerId = this.currentUserService.getCustomerId();

    this.isLoadingProfiles = true;
    this.httpClient.get<any>('/_/internal/nseapi/v2/remoteManagement/profiles').subscribe({
      next: (data: any) => {
        this.isLoadingProfiles = false;
        this.profiles = data.profiles;
      },
      error: (error: any) => {
        this.isLoadingProfiles = false;
        this.profiles = [];
      },
    });
  }

  downloadOpenVPNProfile(remote: string) {
    this.httpClient
      .get(`/_/internal/nseapi/v2/remoteManagement/profiles/${remote}.ovpn`, { responseType: 'blob' })
      .subscribe({
        next: (data: any) => {
          // see: https://jslim.net/blog/2018/03/13/Angular-4-download-file-from-server-via-http/
          const url = window.URL.createObjectURL(data);
          const a = document.createElement('a');
          document.body.appendChild(a);
          a.setAttribute('style', 'display: none');
          a.href = url;
          a.download = `${remote}.ovpn`;
          a.click();
          window.URL.revokeObjectURL(url);
          a.remove();
        },
      });
  }

  generateNewPassword() {
    const password = this.generatePassword();

    this.isGenerating = true;

    this.httpClient.post<any>('/_/internal/nseapi/v2/remoteManagement/changeCredentials', { password }).subscribe({
      next: (data: any) => {
        this.isGenerating = false;
        this.password = password;
      },
      error: (error: any) => {
        this.isGenerating = false;
        this.password = '';
      },
    });
  }

  generatePassword(): string {
    let password = '';

    for (let i = 0, n = this.charset.length; i < 16; ++i) {
      password += this.charset.charAt(Math.floor(Math.random() * n));
    }

    if (!this.regularExpression.test(password)) {
      return this.generatePassword();
    }

    return password;
  }

  doCopy(password: string): void {
    this.clipboard.copy(password);
  }
}
