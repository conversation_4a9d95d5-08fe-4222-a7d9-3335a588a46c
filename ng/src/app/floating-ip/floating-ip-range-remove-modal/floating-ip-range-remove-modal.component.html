<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '600px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_floatingip_removetitle" class="modal-title pull-left">Remove Floating IP Range</h3>
    </ng-template>
    <div class="modal-body">
        <div i18n="@@bma_floatingip_alertremove" class="alert alert-warning" role="alert">The removal of this Floating IP range will only update IPAM. There will not be any contract modification in SAP</div>

        <p i18n="@@bma_floatingip_usedbycustomer">
            Floating IP range <strong>{{ range.range }}</strong> is used by customer <strong>{{ range.customerId }}</strong
            >.
        </p>

        <div class="form-group">
            <input type="text" i18n-placeholder="@@bma_floatingip_removerangeplaceholder" class="form-control" autocomplete="off" placeholder="Please type in the customer number to confirm" #customerId autofocus />
        </div>

        <p i18n="@@bma_floatingip_cancelunderstand">I understand the consequences, cancel Floating IP Range.</p>
    </div>
    <ng-template pTemplate="footer">
        <div class="mt-2">
            <span class="align-middle" *ngIf="error" class="text-danger">{{ error.errorMessage }}</span>
            <button i18n-label="@@bma_common_close" pButton type="button" icon="pi pi-times" iconPos="left" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button i18n-label="@@bma_common_remove" pButton (click)="remove()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" [disabled]="customerId.value != range.customerId" label="Remove"></button>
        </div>
    </ng-template>
</p-dialog>
