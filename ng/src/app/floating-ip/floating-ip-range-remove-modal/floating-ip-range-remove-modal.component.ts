import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { UntypedFormBuilder } from '@angular/forms';

@Component({
  selector: 'app-floating-ip-range-remove-modal',
  templateUrl: './floating-ip-range-remove-modal.component.html',
  styleUrls: ['./floating-ip-range-remove-modal.component.css'],
})
export class FloatingIpRangeRemoveModalComponent implements OnInit {
  range: any;
  error: any;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    private formBuilder: UntypedFormBuilder,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.range = this.dynamicDialogConfig.data.range;
  }

  remove() {
    this.isSubmitting = true;

    this.httpClient.delete<any>(`/_/internal/nseapi/v2/ranges/${this.range.id}`).subscribe(
      (data: any) => {
        this.isSubmitting = false;
        this.closeModal({ rangeRemoved: true });
      },
      (error: any) => {
        this.error = error.error;
        this.isSubmitting = false;
      }
    );
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
