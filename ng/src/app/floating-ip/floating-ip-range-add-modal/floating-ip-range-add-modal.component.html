<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '600px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_floatingip_create" class="modal-title pull-left">Create Floating IP Range</h3>
    </ng-template>
    <div class="modal-body">
        <div i18n="@@bma_floatingip_info" class="alert alert-warning alert-dismissible" role="alert">This form should ONLY be used for manual deliveries of Floating IP's. The process is already automated within the Customer Portal</div>
        <form [formGroup]="form">
            <div class="form-group row">
                <label i18n="@@bma_common_customerid" class="col-sm-3 col-form-label text-right">Customer ID</label>
                <div class="col-sm-9">
                    <input formControlName="customerId" class="form-control" autofocus />
                    <p *ngIf="form.get('customerId').errors?.api" class="text-danger">{{ form.get('customerId').errors.api }}</p>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-3 col-form-label text-right">Sales Org ID</label>
                <div class="col-sm-9">
                    <select class="form-control" formControlName="salesOrgId">
                        <option value="null">Sales Org ID</option>
                        <option *ngFor="let salesOrgId of siteService.salesOrgIds()" [value]="salesOrgId">{{ salesOrgId }} ({{ siteService.getCountry(salesOrgId).toUpperCase() }})</option>
                    </select>
                    <small i18n="@@bma_floatingip_selectsalesorgid" class="form-text text-muted">Select the salesOrgId of the Floating IP Range</small>
                    <p *ngIf="form.get('salesOrgId').errors?.api" class="text-danger">{{ form.get('salesOrgId').errors.api }}</p>
                </div>
            </div>
            <div class="form-group row">
                <label i18n="@@bma_common_type" class="col-sm-3 col-form-label text-right">Type</label>
                <div class="col-sm-9">
                    <select class="form-control" formControlName="type" (change)="onTypeChange($event)">
                        <option i18n="@@bma_common_type" value="null">Type</option>
                        <option i18n="@@bma_common_site" value="SITE">Site</option>
                        <option i18n="@@bma_common_metro" value="METRO">Metro</option>
                    </select>
                    <small i18n="@@bma_floatingip_selecttype" class="form-text text-muted">Select the type of the Floating IP Range</small>
                    <p *ngIf="form.get('type').errors?.api" class="text-danger">{{ form.get('type').errors.api }}</p>
                </div>
            </div>
            <div class="form-group row">
                <label i18n="@@bma_common_prefixlength" class="col-sm-3 col-form-label text-right">Prefix Length</label>
                <div class="col-sm-9">
                    <select class="form-control" formControlName="prefixLength">
                        <option i18n="@@bma_common_prefixlength" value="null">Prefix Length</option>
                        <option *ngFor="let prefixLengthOption of prefixLengthOptions" [ngValue]="prefixLengthOption">{{ prefixLengthOption }}</option>
                    </select>
                    <small i18n="@@bma_floatingip_selectprefixlength" class="form-text text-muted">Select the prefix length of the Floating IP Range</small>
                    <p *ngIf="form.get('prefixLength').errors?.api" class="text-danger">{{ form.get('prefixLength').errors.api }}</p>
                </div>
            </div>
            <div class="form-group row">
                <label i18n="@@bma_common_location" class="col-sm-3 col-form-label text-right">Location</label>
                <div class="col-sm-9">
                    <select class="form-control" formControlName="location">
                        <option i18n="@@bma_common_location" value="null">Location</option>
                        <option *ngFor="let location of getFloatingIpLocations()" [value]="location">{{ location }}</option>
                    </select>
                    <small i18n="@@bma_floatingip_selectlocation" class="form-text text-muted">Select the location of the Floating IP Range</small>
                    <p *ngIf="form.get('location').errors?.api" class="text-danger">{{ form.get('location').errors.api }}</p>
                </div>
            </div>
            <div class="form-group row">
                <label i18n="@@bma_floatingip_ipannouncement" class="col-sm-3 col-form-label text-right" for="ipAnnouncement">IP Announcement</label>
                <div class="col-sm-9 mt-2">
                    <div class="checkbox">
                        <input type="checkbox" checked formControlName="ipAnnouncement" class="form-check-input" id="ipAnnouncement" />
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <label i18n="@@bma_common_equipmentid" class="col-sm-3 col-form-label text-right">Equipment ID</label>
                <div class="col-sm-9">
                    <input formControlName="equipmentId" class="form-control" [attr.disabled]="form.get('ipAnnouncement').value ? 'disabled' : null" />
                    <p *ngIf="form.get('equipmentId').errors?.api" class="text-danger">{{ form.get('equipmentId').errors.api }}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-3">&nbsp;</div>
                <div class="col-sm-9">
                    <small> <span i18n="@@bma_floatingip_productinformation" title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></span> For more information about Floating IPs <a target="_blank" href="https://wiki.ocom.com/display/KC/Floating+IP+product">click here </a> for our KB related article.</small>
                </div>
            </div>
        </form>
    </div>

    <div *ngIf="form.errors?.api?.errorMessage" class="alert alert-danger" role="alert">
        <span>{{ form.errors.api.errorMessage }}</span>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_close" pButton type="button" icon="pi pi-times" iconPos="left" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button i18n-label="@@bma_common_create" pButton (click)="create()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" [disabled]="!form.valid" label="Create"></button>
        </div>
    </ng-template>
</p-dialog>
