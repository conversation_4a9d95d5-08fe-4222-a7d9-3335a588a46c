import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { SiteService } from 'src/app/services/site.service';

@Component({
  selector: 'app-floating-ip-range-add-modal',
  templateUrl: './floating-ip-range-add-modal.component.html',
  styleUrls: ['./floating-ip-range-add-modal.component.css'],
})
export class FloatingIpRangeAddModalComponent implements OnInit {
  isSubmitting = false;
  form: UntypedFormGroup;
  prefixLengthOptions = [];
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    private formBuilder: UntypedFormBuilder,
    public dynamicDialogRef: DynamicDialogRef,
    public siteService: SiteService
  ) {}

  ngOnInit(): void {
    this.form = this.formBuilder.group({
      customerId: [null, Validators.required],
      salesOrgId: [null, Validators.required],
      prefixLength: [null, Validators.required],
      type: [null, Validators.required],
      location: [null, Validators.required],
      equipmentId: [null],
      ipAnnouncement: [true],
    });
  }

  getFloatingIpLocations(): Array<any> {
    const locations = [];
    const salesOrgId = this.form.get('salesOrgId').value;

    if (!salesOrgId) {
      return locations;
    }

    if (this.form.get('type').value === 'METRO') {
      locations.push(...this.siteService.getFloatingIpMetros(salesOrgId));
    }

    if (this.form.get('type').value === 'SITE') {
      locations.push(...this.siteService.getFloatingIpSites(salesOrgId));
    }

    return locations;
  }

  create() {
    if (!this.form.valid) {
      return;
    }

    this.isSubmitting = true;

    const body: any = {};
    body.customerId = this.form.get('customerId').value;
    body.salesOrgId = this.form.get('salesOrgId').value;
    body.prefixLength = this.form.get('prefixLength').value;
    body.type = this.form.get('type').value;
    body.location = this.form.get('location').value;
    if (!this.form.get('ipAnnouncement').value) {
      body.equipmentId = this.form.get('equipmentId').value;
    }

    this.httpClient.post<any>('/_/internal/nseapi/v2/ranges', body).subscribe(
      (data: any) => {
        this.isSubmitting = false;
        this.closeModal({ rangeCreated: true });
      },
      (error: any) => {
        this.isSubmitting = false;
        this.form.setErrors({ api: error.error });
        Object.entries(error.error.errorDetails || {}).forEach(([key, value]) => {
          this.form.get(key).setErrors({ api: value[0] });
        });
      }
    );
  }

  onTypeChange(event): void {
    if (event.target.value === 'METRO') {
      this.prefixLengthOptions = [32, 31, 30, 29, 28, 27];
    } else if (event.target.value === 'SITE') {
      this.prefixLengthOptions = [32, 31, 30, 29, 28, 27, 26, 25, 24];
    }
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
