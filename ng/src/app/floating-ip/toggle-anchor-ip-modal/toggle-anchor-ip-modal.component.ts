import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-toggle-anchor-ip-modal',
  templateUrl: './toggle-anchor-ip-modal.component.html',
  styleUrls: ['./toggle-anchor-ip-modal.component.css'],
})
export class ToggleAnchorIpModalComponent implements OnInit {
  floatingIpDefinition: any;
  error: any;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.floatingIpDefinition = this.dynamicDialogConfig.data.floatingIpDefinition;
  }

  toggle() {
    this.isSubmitting = true;
    this.httpClient
      .post<any>(
        `/_/internal/nseapi/v2/ranges/${this.floatingIpDefinition.rangeId}/floatingIpDefinitions/${this.floatingIpDefinition.id}/toggleAnchorIp`,
        {}
      )
      .subscribe(
        (data: any) => {
          this.isSubmitting = false;
          this.closeModal({ floatingIpDefinitionToggled: true });
        },
        (error: any) => {
          this.error = error.error;
          this.isSubmitting = false;
        }
      );
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
