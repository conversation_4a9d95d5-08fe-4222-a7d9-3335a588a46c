<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '600px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_floatingip_toggle_anchorip" class="modal-title pull-left">Toggle Anchor IP</h3>
    </ng-template>
    <div class="modal-body">
        <p i18n="@@bma_floatingip_toggleipconfirmation">Are you sure you want to toogle anchor IPs for Floating IP {{ floatingIpDefinition.floatingIp }}?</p>
        <p i18n="@@bma_floatingip_toggleipconfirmation_switch_places">Passive and active anchor IPs will switch places:</p>
        <p id="anchor-ip-preview">
            <span>{{ floatingIpDefinition.anchorIp }}</span
            ><i class="fa fa-transfer" id="switch-icon"></i><strong i18n-title="@@bma_floatingip_toggleipconfirmation_new_anchor_ip" title="New active anchor IP">{{ floatingIpDefinition.passiveAnchorIp }}</strong
            ><span id="new-anchor-label" class="badge badge-green-outline mr-2" i18n="@@bma_floatingip_toggleipconfirmation_new_anchor">NEW ANCHOR</span>
        </p>
    </div>
    <ng-template pTemplate="footer">
        <div class="mt-2">
            <span class="align-middle" *ngIf="error" class="text-danger">{{ error.errorMessage }}</span>
            <button i18n-label="@@bma_common_close" pButton type="button" icon="pi pi-times" iconPos="left" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button i18n-label="@@bma_common_toggle" pButton (click)="toggle()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" label="Toggle"></button>
        </div>
    </ng-template>
</p-dialog>
