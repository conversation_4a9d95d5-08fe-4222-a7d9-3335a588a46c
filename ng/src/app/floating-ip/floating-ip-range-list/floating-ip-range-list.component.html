<div class="container">
    <div class="row">
        <div class="col">
            <a i18n-label="@@bma_floatingip_request" *ngIf="!isEmployee" pButton type="button" label="Request Floating IPs" target="_blank" [href]="getCommerceCreateContractUrl()" icon="pi pi-shopping-cart" class="p-button-success pull-right"></a>
            <button i18n-label="@@bma_floatingip_create" *ngIf="isEmployee" pButton type="button" label="Create Floating IP Range" (click)="openFloatingIpRangeAddModal()" icon="pi pi-plus" class="p-button-success pull-right"></button>
            <app-customer-aware-header caption="Floating IP ranges" [customerId]="filters.get('customerId').value" [salesOrgId]="filters.get('salesOrgId').value" [isEmployee]="isEmployee" knowledgeBaseLink="https://kb.leaseweb.com/network/floating-ips"></app-customer-aware-header>
        </div>
    </div>

    <form [formGroup]="filters" (ngSubmit)="submitFilters()" role="form">
        <div class="row mb-3">
            <div class="col">
                <input type="text" i18n-placeholder="@@bma_floatingip_placeholderemployee" *ngIf="isEmployee; else inputCustomer" formControlName="filter" placeholder="Floating IP range Or Customer ID..." class="form-control m-0" autofocus />
                <ng-template #inputCustomer>
                    <input type="text" i18n-placeholder="@@bma_floatingip_placeholdercustomer" formControlName="filter" placeholder="Floating IP range" class="form-control m-0" autofocus />
                </ng-template>
            </div>
        </div>

        <div class="row mb-3 align-items-center">
            <div class="col-3">
                <select class="form-control" formControlName="type">
                    <option i18n="@@bma_common_type" value="null">Type</option>
                    <option i18n="@@bma_common_site" value="SITE">Site</option>
                    <option i18n="@@bma_common_metro" value="METRO">Metro</option>
                </select>
            </div>
            <div class="col-3">
                <select class="form-control" formControlName="location">
                    <option i18n="@@bma_common_location" value="null">Location</option>
                    <option *ngFor="let location of getFloatingIpLocations()" [value]="location">{{ location }}</option>
                </select>
            </div>
            <div class="col text-right">
                <button i18n-label="@@bma_common_reset" pButton (click)="clearFilters()" label="Reset" icon="pi pi-refresh" type="button" class="p-button-secondary mr-2"></button>
                <button i18n-label="@@bma_common_search" pButton [disabled]="isLoading" label="Search" icon="pi pi-search"></button>
            </div>
        </div>
    </form>
</div>

<app-loader *ngIf="isLoading"></app-loader>

<div *ngIf="!isLoading && ranges">
    <div class="p-grid p-col-12 bg-white p-mb-2 p-py-4 justify-content-center" *ngIf="ranges.ranges.length === 0">
        <span *ngIf="isEmployee" i18n="@@bma_floatingip_nofloatingipsfound">No Floating IPs found.</span>
        <span *ngIf="!isEmployee" i18n="@@bma_floatingip_youdonthavefloatingips">You don't have Floating IPs</span>
    </div>
    <p-accordion [multiple]="true">
        <p-accordionTab *ngFor="let range of ranges.ranges">
            <ng-template pTemplate="header">
                <div class="row col-sm-12 align-items-center">
                    <div *ngIf="isEmployee" class="col-sm-3">
                        <app-country-flag salesOrgId="{{ range.salesOrgId }}"></app-country-flag>
                        <span class="h5">{{ range.customerId }}</span>
                    </div>
                    <div class="col-sm-3">
                        <span class="h5">{{ range.type }}</span>
                    </div>
                    <div class="col-sm-3">
                        <span class="h5">{{ range.location }}</span>
                    </div>
                    <div class="col-sm-3">
                        <span class="h5">{{ range.range }}</span>
                    </div>
                </div>
            </ng-template>
            <ng-template pTemplate="content" class="p-p-0">
                <div class="p-grid border-bottom">
                    <div class="p-col-12 p-md-10 border-right">
                        <div class="p-col-12 p-d-flex p-flex-col p-pb-0">
                            <div *ngIf="isEmployee" class="p-col-12 p-md-3 p-py-0 p-pl-6 p-pr-5">
                                <div class="p-grid grid-row">
                                    <div class="p-col-12 grid-content grid-content-border"><span i18n="@@bma_common_customer">Customer</span>: {{ range.customerId }}</div>
                                </div>
                            </div>
                            <div class="p-col-12 p-md-3 p-py-0 p-pl-6 p-pr-5">
                                <div class="p-grid grid-row">
                                    <div class="p-col-12 grid-content grid-content-border"><span i18n="@@bma_common_type">Type</span>: {{ range.type }}</div>
                                </div>
                            </div>
                            <div class="p-col-12 p-md-3 p-py-0 p-pl-6 p-pr-5">
                                <div class="p-grid grid-row">
                                    <div class="p-col-12 grid-content grid-content-border"><span i18n="@@bma_common_location">Location</span>: {{ range.location }}</div>
                                </div>
                            </div>
                            <div class="p-col-12 p-md-3 p-py-0 p-pl-6 p-pr-2">
                                <div class="p-grid grid-row">
                                    <div class="p-col-12 grid-content grid-content-border">
                                        <span i18n="@@bma_floatingip_range" class="d-block">Floating IP Range:</span> <a [routerLink]="[range.id]">{{ range.range }}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-col-12 p-md-2 quick-actions">
                        <h5 i18n="@@bma_common_quickactions" class="p-2">Quick Actions</h5>
                        <div class="p-grid p-flex-column align-items-start">
                            <button i18n-label="@@bma_common_remove" pButton [disabled]="!isEmployee" class="p-button-link" label="Remove" title="Remove" (click)="openFloatingIpRangeRemoveModal(range); false"></button>
                        </div>
                    </div>
                </div>
                <div class="p-grid p-pr-5 p-pt-3">
                    <a i18n-label="@@bma_common_manage" pButton [routerLink]="[range.id]" class="p-button-primary ml-auto" label="Manage"></a>
                </div>
            </ng-template>
        </p-accordionTab>
    </p-accordion>
</div>

<div class="row mb-3" *ngIf="!isLoading && ranges">
    <div class="col-12">
        <app-pagination [totalCount]="ranges._metadata.totalCount" [limit]="ranges._metadata.limit" [offset]="ranges._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>
