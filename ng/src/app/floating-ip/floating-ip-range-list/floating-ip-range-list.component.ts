import { ActivatedRoute, Router } from '@angular/router';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { Component, OnInit } from '@angular/core';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { Title } from '@angular/platform-browser';
import { SiteService } from 'src/app/services/site.service';
import { AlertService } from 'src/app/services/alert.service';
import { CommerceService } from 'src/app/services/commerce.service';
import { FloatingIpRangeAddModalComponent } from '../floating-ip-range-add-modal/floating-ip-range-add-modal.component';
import { FloatingIpRangeRemoveModalComponent } from '../floating-ip-range-remove-modal/floating-ip-range-remove-modal.component';

@Component({
  selector: 'app-floating-ip-range-list',
  templateUrl: './floating-ip-range-list.component.html',
  styleUrls: ['./floating-ip-range-list.component.css'],
})
export class FloatingIpRangeListComponent implements OnInit {
  ranges: any;
  filters: UntypedFormGroup;
  isEmployee: boolean;
  isLoading: boolean;
  dynamicDialogRef: DynamicDialogRef;

  constructor(
    private activatedRoute: ActivatedRoute,
    private alertService: AlertService,
    private modalService: ModalService,
    private currentUserService: CurrentUserService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private router: Router,
    private siteService: SiteService,
    private titleService: Title,
    private commerceService: CommerceService
  ) {}

  ngOnInit(): void {
    this.titleService.setTitle('Floating IP ranges | Leaseweb');
    this.isEmployee = this.currentUserService.isEmployee();
    this.filters = this.formBuilder.group({
      limit: [null],
      offset: [null],
      filter: [null],
      type: [null],
      location: [null],
      customerId: [null],
      salesOrgId: [null],
    });
    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    this.filters.reset(queryParams);

    if (this.isEmployee && this.filters.get('customerId').value) {
      /*eslint-disable */
      const event = new Event('update_product_navbar');
      event['customerId'] = this.filters.get('customerId').value;
      event['salesOrgId'] = this.filters.get('salesOrgId').value;
      event['country'] = this.siteService.getCountry(this.filters.get('salesOrgId').value);
      window.dispatchEvent(event);
      /*eslint-enable */
    }

    const params = this.sanitise(this.filters.getRawValue());
    if (params.filter) {
      if (params.filter.indexOf('.') !== -1 || !this.isEmployee) {
        params.ranges = params.filter;
      } else {
        params.customerId = params.filter;
      }
      delete params.filter;
    }

    this.isLoading = true;
    this.httpClient.get<any>('/_/internal/nseapi/v2/ranges', { params }).subscribe(
      (data: any) => {
        this.ranges = data;
        this.isLoading = false;
      },
      (error: any) => {
        this.isLoading = false;
      }
    );
  }

  getFloatingIpLocations(): Array<any> {
    const salesOrgId = this.isEmployee ? this.filters.get('salesOrgId').value : this.currentUserService.getSalesOrgId();
    const locations = [];

    if (this.filters.get('type').value === 'METRO') {
      locations.push(...this.siteService.getFloatingIpMetros(salesOrgId));
    }

    if (this.filters.get('type').value === 'SITE') {
      locations.push(...this.siteService.getFloatingIpSites(salesOrgId));
    }

    return locations;
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  clearFilters(): void {
    if (this.isEmployee) {
      this.filters.get('filter').reset();
      this.filters.get('type').reset();
      this.filters.get('location').reset();
      this.changeRouterParams();
    } else {
      this.filters.reset();
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
      });
    }
  }

  openFloatingIpRangeRemoveModal(range: any) {
    if (!this.isEmployee) {
      return;
    }

    this.dynamicDialogRef = this.modalService.show(FloatingIpRangeRemoveModalComponent, {
      range,
    });

    this.dynamicDialogRef.onClose.subscribe((data: any) => {
      if (data?.rangeRemoved === true) {
        this.alertService.clear();
        this.alertService.alert(
          {
            type: 'success',
            description: $localize`:@@bma_floatingip_successremoveiprange:Succesfully removed Floating IP Range`,
          },
          false
        );
        this.onQueryParamsChanged(this.filters.getRawValue());
      }
    });
  }

  openFloatingIpRangeAddModal() {
    if (!this.isEmployee) {
      return;
    }

    this.dynamicDialogRef = this.modalService.show(FloatingIpRangeAddModalComponent, {});
    this.dynamicDialogRef.onClose.subscribe((data: any) => {
      if (data?.rangeCreated === true) {
        this.alertService.clear();
        this.alertService.alert(
          {
            type: 'success',
            description: $localize`:@@bma_floatingip_successcreateiprange:Succesfully created Floating IP Range`,
          },
          false
        );
        this.onQueryParamsChanged(this.filters.getRawValue());
      }
    });
  }

  getCommerceCreateContractUrl(): string {
    return this.commerceService.getCommerceCreateContractUrl('floating-ip');
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
