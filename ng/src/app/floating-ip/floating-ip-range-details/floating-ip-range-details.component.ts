import { Activated<PERSON>out<PERSON>, Router } from '@angular/router';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { SiteService } from 'src/app/services/site.service';
import { first } from 'rxjs';
import { LswNotificationHubService } from '@lsw/notification-hub';
import { ServerEvent } from 'src/app/models/server-event.model';
import { MercureService } from 'src/app/services/mercure.service';
import { FloatingIpDefinitionModalComponent } from '../floating-ip-definition-modal/floating-ip-definition-modal.component';
import { FloatingIpDefinitionRemoveModalComponent } from '../floating-ip-definition-remove-modal/floating-ip-definition-remove-modal.component';
import { ToggleAnchorIpModalComponent } from '../toggle-anchor-ip-modal/toggle-anchor-ip-modal.component';

@Component({
  selector: 'app-floating-ip-range-details',
  templateUrl: './floating-ip-range-details.component.html',
  styleUrls: ['./floating-ip-range-details.component.css'],
})
export class FloatingIpRangeDetailsComponent implements OnInit {
  range: any;
  rangeId: string;
  floatingIpDefinitions: any;
  isEmployee: boolean;
  isLoading: boolean;
  isLoadingDetails = [];
  equipments = [];
  dynamicDialogRef: DynamicDialogRef;
  error = [];

  constructor(
    private activatedRoute: ActivatedRoute,
    private alertService: AlertService,
    private modalService: ModalService,
    private currentUserService: CurrentUserService,
    private httpClient: HttpClient,
    private router: Router,
    private siteService: SiteService,
    private notificationHubService: LswNotificationHubService,
    private mercureService: MercureService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.activatedRoute.params.subscribe((parameters) => this.onUrlParamsChanged(parameters));
    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onUrlParamsChanged(urlParams): void {
    this.rangeId = urlParams.rangeId;
    this.floatingIpDefinitions = null;
    this.isLoading = true;
    this.httpClient.get<any>(`/_/internal/nseapi/v2/ranges/${this.rangeId}`).subscribe(
      (data: any) => {
        this.range = data;
        this.isLoading = false;

        /*eslint-disable */
        const event = new Event('update_product_navbar');
        event['customerId'] = this.range.customerId;
        event['salesOrgId'] = this.range.salesOrgId;
        event['country'] = this.siteService.getCountry(this.range.salesOrgId);
        window.dispatchEvent(event);
        /*eslint-enable */

        this.subscribeForMercureUpdate();
      },
      (error: any) => {
        this.isLoading = false;
      }
    );
  }

  onQueryParamsChanged(queryParams): void {
    this.floatingIpDefinitions = null;
    this.httpClient
      .get<any>(`/_/internal/nseapi/v2/ranges/${this.rangeId}/floatingIpDefinitions`, { params: queryParams })
      .subscribe(
        (data: any) => {
          this.floatingIpDefinitions = data;
        },
        (error: any) => {
          this.floatingIpDefinitions = null;
        }
      );
  }

  openFloatingIpDefinitionModal(floatingIpDefinition: any) {
    this.dynamicDialogRef = this.modalService.show(FloatingIpDefinitionModalComponent, {
      range: this.range,
      floatingIpDefinition,
    });

    this.dynamicDialogRef.onClose.subscribe((data: any) => {
      if (data?.floatingIpDefinitionUpdated === true) {
        this.alertService.clear();
        this.alertService.alert(
          {
            type: 'success',
            description: $localize`:@@bma_floatingip_successupdateip:Succesfully updated Floating IP Definition`,
          },
          false
        );
        this.onQueryParamsChanged({});
      }
    });
  }

  openFloatingIpDefinitionRemoveModal(floatingIpDefinition: any) {
    this.dynamicDialogRef = this.modalService.show(FloatingIpDefinitionRemoveModalComponent, {
      floatingIpDefinition,
    });

    this.dynamicDialogRef.onClose.subscribe((data: any) => {
      if (data?.floatingIpDefinitionRemoved === true) {
        this.alertService.clear();
        this.alertService.alert(
          {
            type: 'success',
            description: $localize`:@@bma_floatingip_successremoveip:Succesfully removed Floating IP Definition`,
          },
          false
        );
        this.onQueryParamsChanged({});
      }
    });
  }

  updateSingleFloatingIpDefinition(message) {
    this.floatingIpDefinitions.floatingIpDefinitions = this.floatingIpDefinitions.floatingIpDefinitions.map(
      (definition) => (definition.id === message.id ? message.data : definition)
    );
  }

  removeSingleFloatingIpDefinition(message) {
    const floatingIpDefinitions = this.floatingIpDefinitions.floatingIpDefinitions.filter(
      (floatingIpDefinition) => floatingIpDefinition.id !== message.id
    );
    this.floatingIpDefinitions.floatingIpDefinitions = floatingIpDefinitions;
  }

  subscribeForMercureUpdate() {
    this.notificationHubService.setTokenParams({
      customerId: this.range.customerId,
      salesOrgId: this.range.salesOrgId,
    });

    this.notificationHubService
      .getNotificationHubToken(this.mercureService.getTokenURL('nseapi'))
      .pipe(first())
      .subscribe({
        next: () => {
          // subscribe to topics...

          this.notificationHubService
            .sync<ServerEvent>(`${this.range.customerId}/${this.range.salesOrgId}/floatingIpDefinitions`)
            .subscribe({
              next: (event) => {
                if (event.action === 'floating-ip-remove' && event.status === 'REMOVED') {
                  this.removeSingleFloatingIpDefinition(event);
                } else {
                  this.updateSingleFloatingIpDefinition(event);
                }
              },
              error: (error) => console.log({ error }),
            });
        },
      });
  }
  openToggleAnchorIpModal(floatingIpDefinition: any) {
    this.dynamicDialogRef = this.modalService.show(ToggleAnchorIpModalComponent, {
      floatingIpDefinition,
    });

    this.dynamicDialogRef.onClose.subscribe((data: any) => {
      if (data?.floatingIpDefinitionToggled === true) {
        this.alertService.clear();
        this.alertService.alert(
          {
            type: 'success',
            description: $localize`:@@bma_floatingip_successtoggleanchor:Succesfully toggled Anchor IP`,
          },
          false
        );
        this.onQueryParamsChanged({});
      }
    });
  }

  onTabOpen(event) {
    const floatingIpDefinitions = this.floatingIpDefinitions.floatingIpDefinitions;
    const floatingIpDefinition = floatingIpDefinitions[Object.keys(floatingIpDefinitions)[event.index]];

    if (this.equipments[floatingIpDefinition.id] || this.error[floatingIpDefinition.id]) {
      return;
    }

    this.isLoadingDetails[floatingIpDefinition.id] = true;
    this.httpClient
      .get<any>(`/_/internal/nseapi/v2/ranges/${this.rangeId}/floatingIpDefinitions/${floatingIpDefinition.id}`)
      .subscribe(
        (data: any) => {
          this.isLoadingDetails[floatingIpDefinition.id] = false;
          const equipment = {
            equipmentId: data.equipmentId,
            equipmentType: data.equipmentType,
            reference: data.reference,
          };
          this.equipments[floatingIpDefinition.id] = equipment;
        },
        (error: any) => {
          this.isLoadingDetails[floatingIpDefinition.id] = false;
          this.error[floatingIpDefinition.id] =
            $localize`:@@bma_floatingip_equipmentnotloaded:The equipment cannot be loaded, please try again later`;
        }
      );
  }

  onPageChange(event): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: {
        limit: event.limit,
        offset: event.offset,
      },
    });
  }
}
