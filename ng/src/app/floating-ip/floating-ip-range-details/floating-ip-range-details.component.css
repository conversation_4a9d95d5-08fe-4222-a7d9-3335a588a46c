.table-col-size-1 {
  width: calc(100% / 12 * 1);
}
.table-col-size-2 {
  width: calc(100% / 12 * 2);
}
.table-col-size-3 {
  width: calc(100% / 12 * 3);
}

:host ::ng-deep .p-accordion .p-accordion-content {
  padding: 0.5rem;
}
.grid-content {
  margin-bottom: 5px;
  padding-bottom: 4px;
  padding-left: 2px;
  font-size: 0.8rem !important;
}
.grid-content-border {
  border-bottom: 1px solid #ddd;
}
.grid-row {
  padding-right: 10px;
}
.quick-actions a,
.quick-actions button {
  padding-top: 4px;
  padding-bottom: 4px;
  font-size: 0.9rem;
  text-align: left;
}
.p-grid.grid-row:last-of-type .grid-content-border {
  border-bottom: none;
}
.disabled {
  color: currentColor;
  cursor: not-allowed;
  opacity: 0.5;
  pointer-events: none;
  text-decoration: none;
}

.margin-top-40 {
  margin-top: 40px;
}
