<div class="container">
    <div class="row">
        <app-customer-aware-header *ngIf="range" [caption]="'Floating IP range ' + range.range" [customerId]="range.customerId" [salesOrgId]="range.salesOrgId" [isEmployee]="isEmployee" knowledgeBaseLink="https://kb.leaseweb.com/network/floating-ips"></app-customer-aware-header>
    </div>
</div>

<div class="row mb-3">
    <div class="col-12">
        <button i18n-label="@@bma_floatingip_addipdefinition" pButton type="button" label="Add Floating IP Definition" (click)="openFloatingIpDefinitionModal(null)" icon="pi pi-plus" class="p-button-success pull-right"></button>
        <h3 i18n="@@bma_floatingip_ipdefinitions">Floating IP Definitions</h3>
    </div>
</div>

<app-loader *ngIf="isLoading"></app-loader>

<div *ngIf="!isLoading && floatingIpDefinitions">
    <div class="p-grid p-col-12 bg-white p-mb-2 p-py-4 justify-content-center" *ngIf="floatingIpDefinitions.floatingIpDefinitions.length === 0">No Floating IP definitions created in {{ range.range }}</div>
    <p-accordion [multiple]="true" (onOpen)="onTabOpen($event)">
        <p-accordionTab *ngFor="let floatingIpDefinition of floatingIpDefinitions.floatingIpDefinitions">
            <ng-template pTemplate="header">
                <div class="row col-sm-12 align-items-center">
                    <div class="col-sm-3">
                        <span class="h5"><span i18n="@@bma_common_floatingip">Floating IP</span>: {{ floatingIpDefinition.floatingIp }}</span>
                    </div>
                    <div class="col-sm-3">
                        <span class="h5"><span i18n="@@bma_common_anchorip">Anchor IP</span>: {{ floatingIpDefinition.anchorIp }}</span>
                    </div>
                    <div class="col-sm-2">
                        <span class="h5"><span i18n="@@bma_common_type">Type</span>: {{ floatingIpDefinition.type }}</span>
                    </div>
                    <div class="col-sm-3">
                        <span i18n="@@bma_common_floatingiplocation" class="h5">location: {{ floatingIpDefinition.location }}</span>
                    </div>
                    <div class="col-sm-1 float-right">
                        <span class="btn btn-sm py-0" [ngClass]="{'btn-success': ['ACTIVE'].includes(floatingIpDefinition.status), 'btn-info': ['CREATING', 'UPDATING'].includes(floatingIpDefinition.status), 'btn-danger': ['REMOVING', 'ERROR'].includes(floatingIpDefinition.status)}">
                            {{ floatingIpDefinition.status }}
                        </span>
                    </div>
                </div>
            </ng-template>
            <ng-template pTemplate="content" class="p-p-0">
                <app-loader inline="true" *ngIf="isLoadingDetails[floatingIpDefinition.id]"></app-loader>
                <div class="p-grid">
                    <div *ngIf="error[floatingIpDefinition.id]" class="p-col-12 p-md-10 margin-top-40 text-danger text-center">{{ error[floatingIpDefinition.id] }}</div>
                    <div class="p-col-12 p-md-10 border-right" *ngIf="!isLoadingDetails[floatingIpDefinition.id] && equipments[floatingIpDefinition.id]">
                        <div class="p-col-12 p-d-flex p-flex-col p-pb-0">
                            <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-2">
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_floatingip ">Floating IP</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ floatingIpDefinition.floatingIp }}</div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_type">Type</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ floatingIpDefinition.type }}</div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_location">Location</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ floatingIpDefinition.location }}</div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_anchorip">Anchor IP</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ floatingIpDefinition.anchorIp }}</div>
                                </div>
                            </div>
                            <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-5">
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_passive_anchorip">Passive Anchor IP</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <ng-container *ngIf="floatingIpDefinition.passiveAnchorIp; else notAvailable">{{ floatingIpDefinition.passiveAnchorIp }}</ng-container>
                                    </div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_equipmentType">Equipment Type</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ equipments[floatingIpDefinition.id].equipmentType }}</div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_equipmentid">Equipment ID</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <a *ngIf="equipments[floatingIpDefinition.id].equipmentType === 'DEDICATED_SERVER'" [routerLink]="['/servers', equipments[floatingIpDefinition.id].equipmentId]">{{ equipments[floatingIpDefinition.id].equipmentId }}</a>
                                    </div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_reference">Reference</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ equipments[floatingIpDefinition.id].reference }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-col-12 p-md-2 quick-actions" *ngIf="(!isLoadingDetails[floatingIpDefinition.id] && equipments[floatingIpDefinition.id]) || error[floatingIpDefinition.id]">
                        <h5 i18n="@@bma_common_quickactions" class="p-2">Quick Actions</h5>
                        <div class="p-grid p-flex-column align-items-start">
                            <button i18n-label="@@bma_common_modify" pButton [disabled]="'ACTIVE' != floatingIpDefinition.status" class="p-button-link" label="Modify" title="Modify" (click)="openFloatingIpDefinitionModal(floatingIpDefinition); false"></button>
                            <button i18n-label="@@bma_floatingip_toggle_anchorip" pButton [disabled]="'ACTIVE' != floatingIpDefinition.status || !floatingIpDefinition.passiveAnchorIp" class="p-button-link" label="Toggle Anchor IP" title="Toggle Anchor IP" (click)="openToggleAnchorIpModal(floatingIpDefinition); false"></button>
                            <button i18n-label="@@bma_common_remove" pButton [disabled]="'ACTIVE' != floatingIpDefinition.status" class="p-button-link" label="Remove" title="Remove" (click)="openFloatingIpDefinitionRemoveModal(floatingIpDefinition); false"></button>
                        </div>
                    </div>
                </div>
            </ng-template>
        </p-accordionTab>
    </p-accordion>
</div>

<ng-template #notAvailable>
    <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
</ng-template>
<div class="row mb-3" *ngIf="!isLoading && floatingIpDefinitions">
    <div class="col-12">
        <app-pagination [totalCount]="floatingIpDefinitions._metadata.totalCount" [limit]="floatingIpDefinitions._metadata.limit" [offset]="floatingIpDefinitions._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>
