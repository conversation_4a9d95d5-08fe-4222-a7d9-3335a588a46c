<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '600px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_floatingip_title" class="modal-title pull-left">Floating IP Definition</h3>
    </ng-template>
    <div class="modal-body">
        <dl class="row" [formGroup]="form">
            <dt i18n="@@bma_common_range" class="col-md-3 text-right">Range</dt>
            <dd class="col-md-9 mb-2">
                {{ range.range }}
                <br />
                <small i18n="@@bma_floatingip_descriptionmetro" *ngIf="range.type === 'METRO'" class="text-muted"> This floating IP range is of type "{{ range.type }}". This means that you can float to an anchor IP belonging to your equipment in any site in the "{{ range.location }}" metropolitan area. </small>
                <small i18n="@@bma_floatingip_descriptionsite" *ngIf="range.type === 'SITE'" class="text-muted"> This floating IP range is of type "{{ range.type }}". This means that you can float to an anchor IP belonging to your equipment in site "{{ range.location }}". </small>
            </dd>
            <dt i18n="@@bma_common_floatingip" class="col-md-3 text-right">Floating IP</dt>
            <dd class="col-md-9" *ngIf="floatingIpDefinition">
                <input type="text" class="form-control" value="{{ floatingIpDefinition.floatingIp }}" disabled />
                <small i18n="@@bma_floatingip_notallowed" class="text-muted"> Changing the floating IP definition is not allowed. </small>
            </dd>
            <dd class="col-md-9" *ngIf="!floatingIpDefinition">
                <input type="text" class="form-control" formControlName="floatingIp" placeholder="***********/32 or ***********/30" autofocus />
                <small i18n="@@bma_floatingip_cidr" class="text-muted"> Floating IPs need to be defined using the CIDR notation (e.g. x.x.x.x/yy). </small>
                <p *ngIf="form.get('floatingIp').errors?.api" class="text-danger">{{ form.get('floatingIp').errors.api }}</p>
            </dd>
            <dt i18n="@@bma_common_anchorip" class="col-md-3 text-right">Anchor IP</dt>
            <dd class="col-md-9">
                <input type="text" class="form-control" formControlName="anchorIp" placeholder="*************" />
                <small i18n="@@bma_anchorip_description" class="text-muted"> Anchor IPs should belong to a dedicated server, dedicated rack, or colocation IP address that belong to this account. </small>
                <p *ngIf="form.get('anchorIp').errors?.api" class="text-danger">{{ form.get('anchorIp').errors.api }}</p>
            </dd>
            <dt i18n="@@bma_common_passive_anchorip" class="col-md-3 text-right">Passive Anchor IP</dt>
            <dd class="col-md-9">
                <input type="text" class="form-control" formControlName="passiveAnchorIp" placeholder="*************" />
                <small i18n="@@bma_passive_anchorip_description" class="text-muted"> Passive anchor IP can be used for toggling with Anchor IP. </small>
                <p *ngIf="form.get('passiveAnchorIp').errors?.api" class="text-danger">{{ form.get('passiveAnchorIp').errors.api }}</p>
            </dd>
            <dt class="col-md-3 text-right">&nbsp;</dt>
            <dd class="col-md-9">
                <small> <span i18n-title="@@bma_common_kb" title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></span> <span i18n="@@bma_floatingip_moreinformation"> For more information about Floating IPs </span><a i18n="@@bma_common_clickhere" target="_blank" href="https://kb.leaseweb.com/network/floating-ips">click here</a> <span i18n="@@bma_common_article">for our KB related article.</span></small>
            </dd>
        </dl>
    </div>

    <div *ngIf="form.errors?.api?.errorMessage" class="alert alert-danger" role="alert">
        <span>{{ form.errors.api.errorMessage }}</span>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_close" pButton type="button" icon="pi pi-times" iconPos="left" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button i18n-label="@@bma_common_save" pButton (click)="save()" label="Save" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" [disabled]="!form.valid"></button>
        </div>
    </ng-template>
</p-dialog>
