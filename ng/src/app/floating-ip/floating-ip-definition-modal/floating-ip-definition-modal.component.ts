import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';

@Component({
  selector: 'app-floating-ip-definition-modal',
  templateUrl: './floating-ip-definition-modal.component.html',
  styleUrls: ['./floating-ip-definition-modal.component.css'],
})
export class FloatingIpDefinitionModalComponent implements OnInit {
  floatingIpDefinition: any;
  range: any;
  form: UntypedFormGroup;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    private formBuilder: UntypedFormBuilder,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.floatingIpDefinition = this.dynamicDialogConfig.data.floatingIpDefinition;
    this.range = this.dynamicDialogConfig.data.range;
    if (this.floatingIpDefinition) {
      this.form = this.formBuilder.group({
        anchorIp: [null, Validators.required],
        passiveAnchorIp: [null],
      });
    } else {
      this.form = this.formBuilder.group({
        floatingIp: [null, Validators.required],
        anchorIp: [null, Validators.required],
        passiveAnchorIp: [null],
      });
    }
  }

  save() {
    if (!this.form.valid) {
      return;
    }

    const body = this.form.getRawValue();
    const method = this.floatingIpDefinition ? 'PUT' : 'POST';

    let url = `/_/internal/nseapi/v2/ranges/${this.range.id}/floatingIpDefinitions`;
    if (this.floatingIpDefinition) {
      url += `/${this.floatingIpDefinition.id}`;
    }

    this.isSubmitting = true;

    this.httpClient.request<any>(method, url, { body }).subscribe(
      (data: any) => {
        this.isSubmitting = false;
        this.closeModal({ floatingIpDefinitionUpdated: true });
      },
      (error: any) => {
        this.isSubmitting = false;
        this.form.setErrors({ api: error.error });
        Object.entries(error.error.errorDetails || {}).forEach(([key, value]) => {
          this.form.get(key).setErrors({ api: value[0] });
        });
      }
    );
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
