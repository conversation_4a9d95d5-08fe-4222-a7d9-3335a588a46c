<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '600px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_floatingip_removeip" class="modal-title pull-left">Remove Floating IP</h3>
    </ng-template>
    <div i18n="@@bma_floatingip_removeipconfirmation" class="modal-body">Are you sure you want to remove Floating IP {{ floatingIpDefinition.floatingIp }}?</div>
    <ng-template pTemplate="footer">
        <div class="mt-2">
            <span class="align-middle" *ngIf="error" class="text-danger">{{ error.errorMessage }}</span>
            <button i18n-label="@@bma_common_close" pButton type="button" icon="pi pi-times" iconPos="left" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button i18n-label="@@bma_common_remove" pButton (click)="remove()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" label="Remove"></button>
        </div>
    </ng-template>
</p-dialog>
