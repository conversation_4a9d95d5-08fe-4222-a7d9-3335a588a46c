import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-floating-ip-definition-remove-modal',
  templateUrl: './floating-ip-definition-remove-modal.component.html',
  styleUrls: ['./floating-ip-definition-remove-modal.component.css'],
})
export class FloatingIpDefinitionRemoveModalComponent implements OnInit {
  floatingIpDefinition: any;
  error: any;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.floatingIpDefinition = this.dynamicDialogConfig.data.floatingIpDefinition;
  }

  remove() {
    this.isSubmitting = true;
    this.httpClient
      .delete<any>(
        `/_/internal/nseapi/v2/ranges/${this.floatingIpDefinition.rangeId}/floatingIpDefinitions/${this.floatingIpDefinition.id}`
      )
      .subscribe(
        (data: any) => {
          this.isSubmitting = false;
          this.closeModal({ floatingIpDefinitionRemoved: true });
        },
        (error: any) => {
          this.error = error.error;
          this.isSubmitting = false;
        }
      );
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
