<div class="mb-4">
    <h1>Vlans</h1>
</div>

<form role="form" [formGroup]="filters" (ngSubmit)="submitFilters()">
    <div class="row mb-3">
        <div class="col">
            <input i18n-placeholder="@@bma_vlan_multipletags" formControlName="tags" type="text" class="form-control m-0" placeholder="Tags (For multiple tags search please provide comma separated list, eg: customer_*,test,-bar)..." autofocus />
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-2">
            <input i18n-placeholder="@@bma_vlan_id" type="text" class="form-control m-0" placeholder="Vlan Id" formControlName="vlanId" />
        </div>
        <div class="col-2">
            <input i18n-placeholder="@@bma_common_location" type="text" class="form-control m-0" placeholder="Location" formControlName="location" />
        </div>
        <div class="col-3">
            <select formControlName="pool" label="Pool" class="form-control">
                <option i18n="@@bma_common_public" value="PUBLIC">PUBLIC</option>
                <option i18n="@@bma_common_internal" value="INTERNAL">INTERNAL</option>
                <option i18n="@@bma_common_rm" value="RM">RM</option>
            </select>
        </div>

        <div class="col-5 text-right">
            <button i18n-label="@@bma_common_reset" pButton (click)="clearFilters()" label="Reset" icon="pi pi-refresh" type="button" class="p-button-secondary mr-2"></button>
            <button i18n-label="@@bma_common_search" pButton [disabled]="isLoading" label="Search" icon="pi pi-search"></button>
        </div>
    </div>
</form>

<app-loader *ngIf="isLoading"></app-loader>

<div class="table-responsive" *ngIf="!isLoading && vlans">
    <table class="table table-striped">
        <thead>
            <tr>
                <th i18n="@@bma_vlan_id" class="text-center">Vlan Id</th>
                <th i18n="@@bma_vlan_pool" class="text-center">Pool</th>
                <th i18n="@@bma_common_location" class="text-center">Location</th>
                <th i18n="@@bma_vlan_tags">Tags</th>
            </tr>
        </thead>

        <tbody>
            <tr *ngIf="vlans.vlans.length === 0 && !error">
                <td i18n="@@bma_vlan_noresults" colspan="99" class="text-center p-4">No Vlan found.</td>
            </tr>
            <tr *ngIf="error">
                <td i18n="@@bma_common_errorfetchingdata" colspan="99" class="text-center p-4 text-danger">Error fetching the data</td>
            </tr>
            <tr *ngFor="let vlan of vlans.vlans">
                <td class="text-center text-nowrap">
                    {{ vlan.vlanId }}
                </td>
                <td class="text-center">
                    {{ vlan.pool }}
                </td>
                <td class="text-center text-nowrap">
                    {{ vlan.location }}
                </td>
                <td>
                    <div class="d-inline-flex flex-wrap">
                        <span *ngFor="let tag of vlan.tags" class="mr-1 mb-1 badge badge-primary">{{tag}}</span>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</div>
<div *ngIf="!isLoading && vlans">
    <app-pagination [totalCount]="vlans._metadata.totalCount" [limit]="vlans._metadata.limit" [offset]="vlans._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
</div>
