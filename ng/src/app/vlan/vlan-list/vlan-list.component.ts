import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';

@Component({
  selector: 'app-vlan-list',
  templateUrl: './vlan-list.component.html',
  styleUrls: ['./vlan-list.component.css'],
})
export class VlanListComponent implements OnInit {
  vlans: any;
  isLoading = false;
  error = false;
  filters: UntypedFormGroup;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private titleService: Title
  ) {}

  ngOnInit(): void {
    this.filters = this.formBuilder.group({
      offset: [0],
      limit: [20],
      tags: [null],
      vlanId: [null],
      location: [null],
      pool: [null],
    });

    this.titleService.setTitle('Vlans | Leaseweb');

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    this.isLoading = true;
    this.filters.reset(queryParams);
    const params = this.sanitise(this.filters.getRawValue());
    this.httpClient.get<any>('/_/internal/vlanapi/v2/vlans', { params }).subscribe(
      (data: any) => {
        this.isLoading = false;
        this.error = false;
        this.vlans = data;
      },
      (error: any) => {
        this.isLoading = false;
        this.error = true;
        this.vlans = { _metadata: { totalCount: 0 }, vlans: [] };
      }
    );
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  clearFilters(): void {
    this.filters.reset();
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
    });
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
