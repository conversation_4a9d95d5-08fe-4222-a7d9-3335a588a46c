import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class CustomValidator {
  isValidIp(value: any): boolean {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

    return ipRegex.test(value);
  }

  isValidCidr(value: any): boolean {
    const ipRegex =
      /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/(?:[0-9]|[12][0-9]|3[0-2])$/;

    return ipRegex.test(value);
  }

  isValidMacAddress(value: any): boolean {
    const macAddressRegex = /^([0-9a-fA-F][0-9a-fA-F]:){5}([0-9a-fA-F][0-9a-fA-F])$/;

    return macAddressRegex.test(value);
  }

  isValidNetworkDevice(value: any): boolean {
    const networkdevicePurpose = /^[A-Z]{3}-[0-9]{2}.*(PUB|RM|INT|PUBRM|DISTRO|EDGE).*$/;

    return networkdevicePurpose.test(value);
  }

  isValidFqdn(value: any, minParts = 1): boolean {
    const hostnameRegex = new RegExp(
      '^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.){' +
        (minParts - 1) +
        ',}([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\\-]*[A-Za-z0-9])$'
    );

    return hostnameRegex.test(value);
  }

  isValidHttpUrl(value: any): boolean {
    let url;

    try {
      url = new URL(value);
    } catch (e) {
      return false;
    }

    return url.protocol === 'http:' || url.protocol === 'https:';
  }
}
