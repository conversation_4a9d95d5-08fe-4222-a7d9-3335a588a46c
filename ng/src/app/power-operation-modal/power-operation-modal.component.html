<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '600px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_poweroperationmodal_servertitle" class="modal-title" *ngIf="'servers' == equipmentType">Perform Power Operations on a Dedicated Server</h3>
        <h3 i18n="@@bma_poweroperationmodal_networkequipmenttitle" class="modal-title" *ngIf="'networkEquipments' == equipmentType">Perform Power Operations on a Network Equipment</h3>
    </ng-template>
    <form class="form-horizontal" [formGroup]="form" (ngSubmit)="performPowerOperation()">
        <div class="modal-body">
            <div class="row mb-3">
                <div class="col-3">
                    <label i18n="@@bma_common_operation" for="type">Operation</label>
                </div>
                <div class="col-6">
                    <select id="action" class="form-control" formControlName="action" autofocus>
                        <option i18n="@@bma_common_poweron" value="powerOn">Power On</option>
                        <option i18n="@@bma_common_poweroff" value="powerOff">Power Off</option>
                        <option i18n="@@bma_common_powercycle" value="powerCycle">Power Cycle</option>
                    </select>
                </div>
            </div>
            <div *ngIf="'powerCycle'==form.get('action').value" id="action-powercycle-body-text">
                <p class="ml-1">
                    <span class="fa fa-alert icon-gray fa-lg"></span>
                    <span i18n="@@bma_poweroperationmodal_serverconfirmation" *ngIf="'servers' == equipmentType">Are you sure you want to power cycle dedicated server </span>
                    <span i18n="@@bma_poweroperationmodal_networkequipmentconfirmation" *ngIf="'networkEquipments' == equipmentType">Are you sure you want to power cycle network equipment </span>
                    <span class="text-monospace">{{ equipment.id }} </span>?
                </p>

                <div *ngIf="'servers' == equipmentType">
                    <label i18n="@@bma_poweroperationmodal_schedulehardwarescan" for="power_operation_schedule_hw_scan"><input type="checkbox" formControlName="scheduleHwScan" id="schedule_hw_scan" value="0" /> Schedule Hardware Scan</label>

                    <p i18n="@@bma_poweroperationmodal_description1" class="ml-1">By periodically performing a hardware scan in your dedicated server, you will be able to gather completely up-to-date information not just about inventory, but about the whole hardware customizations present in your dedicated server.</p>
                    <p i18n="@@bma_poweroperationmodal_description2" class="ml-1">A couple of examples about gathered information are: RAID controllers, and network interfaces currently present in your dedicated server, which will be displayed in the "Hardware Details" tab.</p>
                    <p i18n="@@bma_poweroperationmodal_description3" class="ml-1">
                        For more details about what information hardware scan will generate for you, please visit our Knowledge Base
                        <a target="_blank" href="https://kb.leaseweb.com/kb/dedicated-server/dedicated-server-viewing-dedicated-server-hardware-details/"> Dedicated Server Management: Hardware </a>
                        section.
                    </p>
                </div>
            </div>
        </div>
    </form>
    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_cancel" pButton class="p-button-secondary" label="Cancel" (click)="closeModal()" type="button"></button>
            <button i18n-label="@@bma_common_ok" pButton (click)="performPowerOperation()" [loading]="isSubmitting" label="OK"></button>
        </div>
    </ng-template>
</p-dialog>
