<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '600px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_ipv6reversednsmodal_header" class="modal-title">IPv6 Reverse DNS</h3>
    </ng-template>

    <div class="modal-body">
        <div class="row">
            <div class="col-12 text-right">
                <button i18n-label="@@bma_ipv6reversednsmodal_addipv6reversedns" pButton (click)="addNewReverseDnsRow()" type="button" label="Add IPv6 Reverse DNS" icon="pi pi-plus" class="p-button-success float-right"></button>
            </div>
        </div>
        <div class="row mt-3">
            <div *ngIf="isLoading" class="col-12 text-center">
                <app-loader></app-loader>
            </div>

            <div *ngIf="!isLoading" class="col-12">
                <div class="row">
                    <div class="col-3 mx-auto">
                        <span class="align-middle" *ngIf="error" class="text-danger text-center">{{ error.errorMessage }}</span>
                    </div>
                </div>
                <form [formGroup]="form" (ngSubmit)="createIPv6ReverseDNSEntry()" class="form-horizontal">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th i18n="@@bma_common_ipaddress" scope="col" width="35%">IP Address</th>
                                <th i18n="@@bma_common_reversednsurl" scope="col">Reverse DNS Url</th>
                                <th i18n="@@bma_common_actions" scope="col" class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngIf="newReverseDns" class="new-reverse-dns-entry">
                                <td>
                                    <input type="text" class="form-control" [disabled]="isSubmitting" formControlName="ip" id="ip" autofocus />
                                </td>
                                <td>
                                    <input type="text" class="form-control" [disabled]="isSubmitting" formControlName="url" id="url" />
                                </td>
                                <td class="actions-container">
                                    <button i18n-label="@@bma_common_cancel" *ngIf="!isSubmitting" pButton (click)="removeNewReverseDnsRow()" type="button" label="Cancel" class="p-button-secondary"></button>
                                    <button i18n-label="@@bma_common_save" pButton [loading]="isSubmitting" label="Save"></button>
                                </td>
                            </tr>
                            <tr *ngIf="!reverseDNSList || reverseDNSList?.reverseLookups.length === 0">
                                <td i18n="@@bma_ipv6reversednsmodal_noipv6reversedns" colspan="999" class="text-center p-4">No Reverse DNS entries found.</td>
                            </tr>
                            <tr *ngFor="let reverseDns of reverseDNSList?.reverseLookups">
                                <td>{{ reverseDns.ip }}</td>
                                <td>{{ reverseDns.reverseLookup }}</td>
                                <td class="text-center">
                                    <div class="dns-actions" *ngIf="!reverseDNSToBeRemoved">
                                        <a href="#" (click)="enableEditingReverseDns(reverseDns); false">
                                            <i class="fa fa-change"></i>
                                        </a>
                                        <a *ngIf="!reverseDNSToBeRemoved || reverseDNSToBeRemoved.ip != reverseDns.ip" href="#" (click)="showDeleteConfirmation(reverseDns); false" class="text-danger">
                                            <i class="fa fa-delete"></i>
                                        </a>
                                    </div>

                                    <button *ngIf="reverseDNSToBeRemoved && reverseDNSToBeRemoved.ip == reverseDns.ip && !isSubmitting" (click)="removeReverseDNSEntry(reverseDns)" pButton pRipple type="button" icon="pi pi-check" class="p-button-rounded p-button-secondary"></button>
                                    <button *ngIf="reverseDNSToBeRemoved && reverseDNSToBeRemoved.ip == reverseDns.ip && !isSubmitting" (click)="cancelDeleteReverseDNS()" pButton pRipple type="button" icon="pi pi-times" class="p-button-rounded p-button-danger"></button>
                                    <span i18n="@@bma_common_removing" *ngIf="reverseDNSToBeRemoved && reverseDNSToBeRemoved.ip == reverseDns.ip && isSubmitting" class="badge badge-red-outline mr-2">Removing</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
    </div>
    <div class="row mb-3" *ngIf="!isLoading && reverseDNSList" id="dns-pagination">
        <div class="col-12">
            <app-pagination [totalCount]="reverseDNSList._metadata.totalCount" [limit]="reverseDNSList._metadata.limit" [offset]="reverseDNSList._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
        </div>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n="@@bma_common_close" pButton type="button" class="p-button-secondary" label="Close" (click)="closeModal()"></button>
        </div>
    </ng-template>
</p-dialog>
