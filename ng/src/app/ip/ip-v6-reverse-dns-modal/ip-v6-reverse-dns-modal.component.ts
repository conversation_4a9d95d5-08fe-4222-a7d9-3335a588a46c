import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-ip-v6-reverse-dns-modal',
  templateUrl: './ip-v6-reverse-dns-modal.component.html',
  styleUrls: ['./ip-v6-reverse-dns-modal.component.css'],
})
export class IPv6ReverseDNSModalComponent implements OnInit {
  privateNetwork: any;
  equipment: any;
  isSubmitting = false;
  isToggling = false;
  error: any;
  showDialog = true;
  ipAddress: any;
  equipmentType: any;

  form: UntypedFormGroup;

  reverseDNSList: any;
  isLoading: boolean;
  reverseDNSToBeRemoved: any;
  newReverseDns: boolean;

  constructor(
    private httpClient: HttpClient,
    private formBuilder: UntypedFormBuilder,
    private dynamicDialogConfig: DynamicDialogConfig,
    private dynamicDialogRef: DynamicDialogRef
  ) {}

  ngOnInit(): void {
    this.isLoading = true;
    this.privateNetwork = this.dynamicDialogConfig.data.privateNetwork;
    this.ipAddress = this.dynamicDialogConfig.data.ipAddress;
    this.equipment = this.dynamicDialogConfig.data.equipment;
    this.equipmentType = this.dynamicDialogConfig.data.equipmentType;

    this.loadIPv6ReverseDNSList({});

    this.form = this.formBuilder.group({
      ip: [null, Validators.required],
      url: [null, Validators.required],
    });
  }

  loadIPv6ReverseDNSList(params: any): void {
    this.error = false;
    this.isLoading = true;

    const ipRange = this.getIpFormattedAsParameter(this.ipAddress);
    const equipmentId = this.equipment.assetId ?? this.equipment.id;

    this.httpClient
      .get<any>(
        `/_/internal/dedicatedserverapi/v2/${this.equipmentType}/${equipmentId}/ips/${ipRange}/reverselookups`,
        { params }
      )
      .subscribe({
        next: (data: any) => {
          this.reverseDNSList = data;
          this.isLoading = false;
        },
        error: (error: any) => {
          this.isLoading = false;
        },
      });
  }

  showDeleteConfirmation(selectedReverseDNS: any) {
    this.reverseDNSToBeRemoved = selectedReverseDNS;
  }

  cancelDeleteReverseDNS() {
    this.reverseDNSToBeRemoved = null;
  }

  removeReverseDNSEntry(reverseDNSEntry: any): void {
    this.isSubmitting = true;
    this.error = false;
    this.isLoading = true;
    const ipRange = this.getIpFormattedAsParameter(this.ipAddress);
    const equipmentId = this.equipment.assetId ?? this.equipment.id;

    const params = {
      ip: reverseDNSEntry.ip,
      reverseLookup: null,
    };

    this.httpClient
      .put<any>(
        `/_/internal/dedicatedserverapi/v2/${this.equipmentType}/${equipmentId}/ips/${ipRange}/reverselookups`,
        params
      )
      .subscribe({
        next: (data: any) => {
          this.loadIPv6ReverseDNSList({});
          this.isLoading = false;
          this.isSubmitting = false;
          this.reverseDNSToBeRemoved = null;
        },
        error: (error: any) => {
          this.isLoading = false;
          this.isSubmitting = false;
          this.reverseDNSToBeRemoved = null;
          this.error = error.error;
        },
      });
  }

  onPageChange(event): void {
    this.loadIPv6ReverseDNSList({ offset: event.offset, limit: event.limit });
  }

  createIPv6ReverseDNSEntry(): void {
    this.isSubmitting = true;
    const ip = this.getIpFormattedAsParameter(this.ipAddress);
    const equipmentId = this.equipment.assetId ?? this.equipment.id;
    const params = {
      ip: this.form.get('ip').value,
      reverseLookup: this.form.get('url').value,
    };

    this.httpClient
      .put<any>(
        `/_/internal/dedicatedserverapi/v2/${this.equipmentType}/${equipmentId}/ips/${ip}/reverselookups`,
        params
      )
      .subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.form.reset();
          this.removeNewReverseDnsRow();
          this.loadIPv6ReverseDNSList({});
        },
        error: (error: any) => {
          this.error = error.error;
          this.isSubmitting = false;
        },
      });
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }

  addNewReverseDnsRow() {
    this.newReverseDns = true;
  }

  removeNewReverseDnsRow() {
    this.newReverseDns = false;
    this.error = null;
  }

  enableEditingReverseDns(reverseDNSEntry: any) {
    this.addNewReverseDnsRow();
    const ip = reverseDNSEntry.ip;
    this.form.setValue({
      ip,
      url: reverseDNSEntry.reverseLookup,
    });
  }

  refreshIPv6ReverseDnsEntries(): void {
    const params = {
      limit: 20,
      offset: 0,
    };

    this.loadIPv6ReverseDNSList(params);
  }

  getIpWithoutMask(ip) {
    return ip.replace(/\_.+/g, '');
  }

  getIpFormattedAsParameter(ip) {
    return ip.replace(/\/[0-9]+/g, '');
  }
}
