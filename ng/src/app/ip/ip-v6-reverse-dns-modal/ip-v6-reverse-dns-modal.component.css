:host ::ng-deep .p-button.p-button-icon-only.p-button-rounded {
  height: 1.357rem !important;
}

:host ::ng-deep .btn,
:host ::ng-deep .p-button.p-button-info,
:host ::ng-deep .p-button.p-button-danger,
:host ::ng-deep .p-button.p-button-warning,
:host ::ng-deep .p-button.p-button-success,
:host ::ng-deep .p-button.p-button-secondary,
:host ::ng-deep .p-button {
  padding-top: 0.45rem !important;
  padding-bottom: 0.3rem !important;
}

:host ::ng-deep .p-button.p-button-icon-only {
  width: 1.357rem !important;
  padding: 0.5rem 0 !important;
}

:host ::ng-deep .pi {
  font-size: 0.6rem !important;
}

:host ::ng-deep .p-button.p-button-icon-only {
  width: 1.357rem !important;
  padding: 0 0.3rem 0 0 !important;
  margin-left: 0.5rem;
}

:host ::ng-deep .p-button-icon {
  padding-left: 0.2rem !important;
}

.new-reverse-dns-entry .p-inputgroup .p-inputgroup-addon {
  border-radius: 0 0 inherit inherit;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.new-reverse-dns-entry .p-inputgroup input#ip {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  padding: 8px !important;
  line-height: 36px;
  height: 36.5px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  flex: 1;
}

td.actions-container {
  text-align: center;
}

td.actions-container button:first-of-type {
  margin-right: 10px;
}

.dns-actions a {
  margin-right: 10px;
}
.dns-actions a:last-of-type {
  margin-right: 0;
}
