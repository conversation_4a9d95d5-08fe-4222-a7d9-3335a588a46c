<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeIpV6Modal()" [style]="{width: '100%', 'max-height': '500px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_ipv6requestmodal_header" class="modal-title">Request IPv6</h3>
    </ng-template>

    <form [formGroup]="form" (ngSubmit)="ipV6Request()">
        <div class="modal-body">
            <app-loader *ngIf="isLoadingOptions"></app-loader>

            <div class="form-group" *ngIf="!isLoadingOptions && ipv6">
                <p class="alert-body">
                    <span i18n="@@bma_ipv6requestmodal_description1" *ngIf="ipv6=='112'"> You will be assigned a /112 address block from a shared /64 subnet. </span>
                    <span i18n="@@bma_ipv6requestmodal_description2" *ngIf="ipv6=='64'">You will be assigned a /64 address.</span>
                    <span i18n="@@bma_common_requestprocess" *ngIf="equipmentType!='servers'">Your request will be processed during business hours.</span>
                </p>
                <div i18n="@@bma_common_ipv6addresses" class="col-form-label">IPv6 address: /{{ ipv6 }} IPv6</div>
            </div>
        </div>
    </form>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <span class="align-middle" *ngIf="error" class="text-danger">{{ error.error }}</span>
            <button i18n-label="@@bma_common_cancel" pButton type="button" icon="pi pi-times" iconPos="left" label="Cancel" class="p-button-secondary" (click)="closeIpV6Modal()"></button>
            <button i18n-label="@@bma_common_ok" pButton (click)="ipV6Request()" icon="pi pi-check" iconPos="left" [disabled]="!ipv6" [loading]="isSubmitting" label="OK"></button>
        </div>
    </ng-template>
</p-dialog>
