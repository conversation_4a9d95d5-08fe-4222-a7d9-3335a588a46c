import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-ip-v6-request-modal',
  templateUrl: './ip-v6-request-modal.component.html',
  styleUrls: ['./ip-v6-request-modal.component.css'],
})
export class IpV6RequestModalComponent implements OnInit {
  equipment: any;
  form: UntypedFormGroup;
  isSubmitting = false;
  ipv6 = '112';
  isLoadingOptions = false;
  showDialog = true;
  error: any;
  equipmentType: string;

  constructor(
    private alertService: AlertService,
    private dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient
  ) {}

  ngOnInit(): void {
    this.equipment = this.dynamicDialogConfig.data.equipment;
    this.equipmentType = this.dynamicDialogConfig.data.equipmentType;
    this.form = this.formBuilder.group({
      ipv6: [null, Validators.required],
    });
    if (this.equipmentType !== 'servers') {
      this.getIpOptions();
    }
  }

  async getIpOptions() {
    this.isLoadingOptions = true;
    this.alertService.clear();
    await this.httpClient
      .post<any>('/_legacy/requestIpv6/options', {
        contractItemId: this.equipment.contract.id,
        site: this.equipment.location.site,
        type: this.equipment.type,
      })
      .toPromise()
      .then(
        (data: any) => {
          this.isLoadingOptions = false;
          if (data) {
            this.ipv6 = data.ipv6;
          }
        },
        (error) => {
          this.alertService.alertApiError(error.error, true);
          this.closeIpV6Modal();
        }
      );
  }

  ipV6Request(): void {
    this.isSubmitting = true;
    this.alertService.clear();
    if (this.equipmentType === 'servers') {
      this.httpClient
        .post<any>(`/_/internal/dedicatedserverapi/v2/${this.equipmentType}/${this.equipment.id}/requestIpv6`, {})
        .subscribe({
          next: (data: any) => {
            this.isSubmitting = false;
            this.alertService.alert(
              {
                type: 'success',
                description: data.message,
              },
              true
            );
            this.closeIpV6Modal();
          },
          error: (error: any) => {
            this.isSubmitting = false;
            this.alertService.alertApiError(error.error, true);
            this.closeIpV6Modal();
          },
        });
    } else {
      const body: any = {};
      body.contractItemId = this.equipment.contract.id;
      body.ipv6 = this.ipv6;
      body.equipmentId = this.equipment.id;
      body.equipment = this.equipment;

      this.httpClient.post<any>('/_legacy/requestIpv6/request', body).subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.alertService.alert(
            {
              type: 'success',
              description: data.message,
            },
            true
          );
          this.closeIpV6Modal();
        },
        error: (error: any) => {
          this.isSubmitting = false;
          this.alertService.alert(
            {
              type: 'error',
              description: error.error.error,
            },
            true
          );
          this.closeIpV6Modal();
        },
      });
    }
  }

  closeIpV6Modal() {
    this.dynamicDialogRef.close();
  }
}
