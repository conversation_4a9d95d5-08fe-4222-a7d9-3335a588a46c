import { Component, OnInit, Input, ViewChild } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';
import { ReverseDnsEditModalComponent } from 'src/app/reverse-dns-edit-modal/reverse-dns-edit-modal.component';
import { NullRouteIpNullModalComponent } from 'src/app/null-route-ip/null-route-ip-null-modal/null-route-ip-null-modal.component';
import { NullRouteIpUnnullModalComponent } from 'src/app/null-route-ip/null-route-ip-unnull-modal/null-route-ip-unnull-modal.component';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { ModalService } from 'src/app/services/modal.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { CidrToIpPipe } from 'src/app/pipes/cidr-to-ip.pipe';
import { CommerceService } from 'src/app/services/commerce.service';
import { IPv6ReverseDNSModalComponent } from '../ip-v6-reverse-dns-modal/ip-v6-reverse-dns-modal.component';

@Component({
  selector: 'app-ip-list',
  templateUrl: './ip-list.component.html',
  styleUrls: ['./ip-list.component.css'],
})
export class IpListComponent implements OnInit {
  @ViewChild(NullRouteIpNullModalComponent, { static: false })
  nullIpModal: NullRouteIpNullModalComponent;

  @ViewChild(NullRouteIpUnnullModalComponent, { static: false })
  unNullRouteIpNullModal: NullRouteIpUnnullModalComponent;

  @Input() equipment: any;
  @Input() equipmentType: string;

  ipList: any;
  isLoading = false;
  selectedIpAddressesForCancellation = [];
  cancellableIps = [];
  selectAllIpsForCancellation = false;
  isEmployee = false;
  dynamicDialogRef: DynamicDialogRef;
  filters: UntypedFormGroup;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private commerceService: CommerceService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    private modalService: ModalService,
    private cidrToIpPipe: CidrToIpPipe
  ) {}

  ngOnInit(): void {
    this.filters = this.formBuilder.group({
      offset: [0],
      limit: [20],
      filter: [null],
    });

    this.isEmployee = this.currentUserService.isEmployee();
    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    this.isLoading = true;
    this.filters.reset(queryParams);
    const params = this.sanitise(this.filters.getRawValue());

    if (params.filter) {
      params.ips = params.filter;
      delete params.filter;
    }

    this.httpClient
      .get(`/_/internal/dedicatedserverapi/v2/${this.equipmentType}/${this.equipment.id}/ips`, { params })
      .subscribe({
        next: (data: any) => {
          this.isLoading = false;
          this.ipList = this.equipmentType === 'servers' ? this.updateIpsListForServers(data) : data;
        },
        error: (error: any) => {
          this.isLoading = false;
          this.ipList = null;
        },
      });
  }

  openIpMultipleCancelModal() {
    if (this.selectedIpAddressesForCancellation.length === 0) {
      return;
    }
    window.open(
      this.commerceService.getCommerceModifyContractUrl(this.equipment.contract.id, 'ip/cancel', {
        ipaddresses: this.selectedIpAddressesForCancellation.map((ip) => this.cidrToIpPipe.transform(ip)).join(','),
      })
    );
  }

  getCommerceCancelIpUrl(ip: string): string {
    return this.commerceService.getCommerceModifyContractUrl(this.equipment.contract.id, 'ip/cancel', {
      ipaddresses: this.cidrToIpPipe.transform(ip),
    });
  }

  openReverseDnsEditModal(ip) {
    if (4 === ip.version) {
      return this.openIPv4ReverseDnsEditModal(ip.ip, ip.reverseLookup);
    }

    return this.openIPv6ReverseDnsEditModal(ip.ip, ip.reverseLookup);
  }

  openIPv4ReverseDnsEditModal(ip: string, reverseLookup: string) {
    this.dynamicDialogRef = this.modalService.show(ReverseDnsEditModalComponent, {
      equipment: this.equipment,
      equipmentType: this.equipmentType,
      ipAddress: ip,
      reverseLookup,
    });

    this.dynamicDialogRef.onClose.subscribe((event) => {
      if (event?.reverseDnsUpdated === true) {
        // Update ip details with updated reverse dns without reloading the whole component;
        this.ipList.ips.map((ipDetails) =>
          ipDetails.ip === ip ? (ipDetails.reverseLookup = event?.updatedReverseDns) : null
        );
      }
    });
  }

  openIPv6ReverseDnsEditModal(ip: string, reverseLookup: string) {
    this.dynamicDialogRef = this.modalService.show(IPv6ReverseDNSModalComponent, {
      equipment: this.equipment,
      equipmentType: this.equipmentType,
      ipAddress: ip,
      reverseLookup,
    });
  }

  openUnnullIpModal(ipAddress: any) {
    this.dynamicDialogRef = this.modalService.show(NullRouteIpUnnullModalComponent, {
      equipment: this.equipment,
      equipmentType: this.equipmentType,
      ipAddress: this.cidrToIpPipe.transform(ipAddress),
    });

    this.dynamicDialogRef.onClose.subscribe((event: any) => {
      if (event?.unnulled === true) {
        this.ipList.ips.map((ipDetails) => (ipDetails.ip === ipAddress ? (ipDetails.nullRouted = false) : null));
      }
    });
  }

  openNullRouteIpNullModal(ipAddress: any) {
    this.dynamicDialogRef = this.modalService.show(NullRouteIpNullModalComponent, {
      equipment: this.equipment,
      equipmentType: this.equipmentType,
      ipAddress: this.cidrToIpPipe.transform(ipAddress),
    });

    this.dynamicDialogRef.onClose.subscribe((event: any) => {
      if (event?.nulled === true) {
        this.ipList.ips.map((ipDetails) => (ipDetails.ip === ipAddress ? (ipDetails.nullRouted = true) : null));
      }
    });
  }

  ipDataChangedHandler(isDataUpdated): void {
    if (isDataUpdated) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { offset: 0 },
        queryParamsHandling: 'merge',
      });
    }
  }

  ddosIpProtectionUpgradedHandler(isDdosIpProtectionTypeUpdated): void {
    if (isDdosIpProtectionTypeUpdated) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { offset: 0 },
        queryParamsHandling: 'merge',
      });
    }
  }

  selectOrUnselectAllIpsForCancellation() {
    this.selectAllIpsForCancellation = !this.selectAllIpsForCancellation;
    this.ipList.ips = this.ipList.ips.map((obj) => ({ ...obj, isSelected: this.selectAllIpsForCancellation }), {
      selectAllIps: this.selectAllIpsForCancellation,
    });
    this.setIpAddressesForCancellation();
  }

  setIpAddressesForCancellation(): void {
    const selectedIps = this.ipList.ips
      .filter(
        (item) =>
          !item.mainIp &&
          !item.floatingIp &&
          item.networkType === 'PUBLIC' &&
          item.version === 4 &&
          item.isSelected === true
      )
      .map((item) => item.ip);
    if (!this.selectAllIpsForCancellation) {
      const nonSelectedIps = this.ipList.ips
        .filter(
          (item) =>
            !item.mainIp &&
            !item.floatingIp &&
            item.networkType === 'PUBLIC' &&
            item.version === 4 &&
            item.isSelected !== true
        )
        .map((item) => item.ip);
      this.selectedIpAddressesForCancellation = this.selectedIpAddressesForCancellation.filter(
        (item) => !nonSelectedIps.includes(item)
      );
    }
    this.selectedIpAddressesForCancellation = [
      ...new Set([...this.selectedIpAddressesForCancellation, ...selectedIps]),
    ];
  }

  selectOrUnSelectIpForCancellation(event, index) {
    this.ipList.ips[index].isSelected = event.target.checked;
    if (event.target.checked) {
      this.selectedIpAddressesForCancellation.push(this.ipList.ips[index].ip);
    } else {
      this.selectedIpAddressesForCancellation = this.selectedIpAddressesForCancellation.filter(
        (item) => item !== this.ipList.ips[index].ip
      );
    }
    this.selectAllIpsForCancellation = this.ipList.ips.every(
      (item) =>
        !item.mainIp &&
        !item.floatingIp &&
        item.networkType === 'PUBLIC' &&
        item.version === 4 &&
        item.isSelected === true
    );

    this.selectedIpAddressesForCancellation = [...new Set(this.selectedIpAddressesForCancellation)];
  }

  resetIpSelection(): void {
    this.selectAllIpsForCancellation = false;
    this.ipList.ips = this.ipList.ips.map((obj) => ({ ...obj, isSelected: this.selectAllIpsForCancellation }), {
      selectAllIps: this.selectAllIpsForCancellation,
    });
    this.selectedIpAddressesForCancellation = [];
  }

  updateIpsListForServers(data): any {
    this.selectAllIpsForCancellation = false;
    const ips = data.ips.map(
      (obj) => {
        const isSelected = this.selectedIpAddressesForCancellation.includes(obj.ip) ? true : false;
        return { ...obj, isSelected };
      },
      { selectedIpAddresses: this.selectedIpAddressesForCancellation }
    );

    this.cancellableIps = ips.filter(
      (item) => !item.mainIp && !item.floatingIp && item.networkType === 'PUBLIC' && item.version === 4
    );

    data.ips = ips;

    return data;
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  clearFilters(): void {
    this.filters.reset();
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
    });
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }

  isUsableIp(ip) {
    return ip.type === 'NORMAL_IP' || ip.type === 'IPMI';
  }

  cidrToNetmask(prefixLength) {
    const mask = [];
    let n;

    for (let i = 0; i < 4; i++) {
      n = Math.min(prefixLength, 8);
      mask.push(256 - Math.pow(2, 8 - n));
      prefixLength -= n;
    }
    return mask.join('.');
  }
}
