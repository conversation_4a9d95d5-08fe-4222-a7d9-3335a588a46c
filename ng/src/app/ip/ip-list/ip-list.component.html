<div class="row">
    <div class="col-8">
        <h3 class="mb-3">
            <span i18n="@@bma_iplist_header_no_dedicatedstorage" *ngIf="equipmentType!='dedicatedStorages'">IP's and DDoS IP Protection</span>
            <span i18n="@@bma_iplist_header" *ngIf="equipmentType=='dedicatedStorages'">IP's</span>
            <sup>
                <a href="https://kb.leaseweb.com/kb/ddos-ip-protection/ddos-ip-protection-overview" target="_blank">
                    <i title="Knowledge Base" class="fa fa-knowledge class ml-1" aria-hidden="true"></i>
                    <span i18n="@@bma_ipv6requestmodal_knowledgebase" class="sr-only">Knowledge Base</span>
                </a>
            </sup>
        </h3>
    </div>
    <div class="col-4" *ngIf="equipmentType!='dedicatedStorages'">
        <app-ddos-notification-setting-list [equipment]="equipment" [equipmentType]="equipmentType"></app-ddos-notification-setting-list>
    </div>
</div>
<div class="row">
    <div class="col-12">
        <form [formGroup]="filters" (ngSubmit)="submitFilters()">
            <fieldset>
                <div class="row mb-3">
                    <div class="col">
                        <input i18n-placeholder="@@bma_iplist_filterbyip" type="text" id="filter" class="form-control m-0" formControlName="filter" placeholder="Filter for IP addresses..." autofocus />
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-12 text-right">
                        <button i18n-label="@@bma_common_reset" pButton type="button" (click)="clearFilters()" label="Reset" icon="pi pi-refresh" class="p-button-secondary mr-2"></button>
                        <button i18n-label="@@bma_common_search" pButton [disabled]="isLoading" label="Search" icon="pi pi-search"></button>
                    </div>
                </div>
            </fieldset>
        </form>
    </div>
</div>
<div class="row" *ngIf="!isEmployee && !isLoading && ipList && ipList?.ips.length > 0">
    <div class="col-12 mt-2 mb-3">
        <ng-container *ngIf="cancellableIps?.length > 0">
            <span class="form-check form-check-inline pl-2 mb-2"> <input class="form-check-input" (click)="$event.stopPropagation();" name="selectAllIps" [attr.title]="selectAllIpsForCancellation ? 'Unselect all Ips' : 'Select all Ips'" [checked]="selectAllIpsForCancellation" (change)="selectOrUnselectAllIpsForCancellation()" type="checkbox" id="selectAllIps" />&nbsp;Select all cancellable IPs </span>
        </ng-container>
        <button i18n-label="@@bma_iplist_cancelips" pButton label="Cancel IPs" icon="pi pi-times" [disabled]="selectedIpAddressesForCancellation?.length < 1" class="p-button-primary mr-2" role="button" (click)="openIpMultipleCancelModal()" [class.disabled]="equipment.isSharedEol"></button>
        <button i18n-label="@@bma_iplist_reset" pButton label="Reset IP selection" icon="pi pi-refresh" [disabled]="selectedIpAddressesForCancellation?.length < 1" (click)="resetIpSelection()" class="p-button-secondary"></button>
    </div>
</div>

<app-loader *ngIf="isLoading"></app-loader>
<div *ngIf="!isLoading && ipList">
    <div i18n="@@bma_iplist_nopublicip" class="col-12 bg-white p-mb-2 p-mt-2 p-py-4 text-center" *ngIf="ipList.ips.length === 0">No public IP addresses found.</div>
    <p-accordion [multiple]="true">
        <p-accordionTab *ngFor="let ip of ipList.ips; index as i">
            <ng-template pTemplate="header">
                <div class="row col-sm-12 align-items-center">
                    <div class="col-sm-3">
                        <ng-container *ngIf="ip.ip; else notAvailable">
                            <span class="h5">{{ ip.ip }}</span>
                        </ng-container>
                    </div>
                    <div class="col-sm-2">
                        <ng-container *ngIf="ip.floatingIp; else elseIfGatewayBlock"> <span i18n="@@bma_common_floatingip" class="h5">Floating IP</span></ng-container>
                        <ng-template #elseIfGatewayBlock>
                            <ng-container *ngIf="ip.gateway; else notAvailable">
                                <span i18n="@@bma_iplist_gateway" class="h5">Gateway</span><br />
                                <span class="h5">{{ ip.gateway }}</span>
                            </ng-container>
                        </ng-template>
                    </div>
                    <div class="col-sm-3" *ngIf="equipmentType != 'dedicatedStorages'">
                        <ng-container *ngIf="ip.reverseLookup; else elseIfReverseLookupBlock">
                            <span class="h5"> {{ ip.reverseLookup }}</span>
                        </ng-container>
                        <ng-template #elseIfReverseLookupBlock>
                            <span i18n="@@bma_common_notavailable" class="text-muted" *ngIf="ip.version === 4">Not Available</span>
                        </ng-template>
                        <ng-container *ngIf="ip.version === 6">
                            <span i18n="@@bma_iplist_availableinedit" class="text-muted"> Available in edit modal</span>
                        </ng-container>
                    </div>
                    <div class="col-sm-2">
                        <span i18n="@@bma_iplist_ddosipprotection" class="h5" *ngIf="!ip.floatingIp && ip.ddos && equipmentType != 'networkEquipments' && ip.networkType == 'PUBLIC'"> {{ ip.ddos.protectionType|titlecase }} DDoS IP Protection </span>
                    </div>
                    <div class="col-sm-1" *ngIf="ip.networkType == 'PUBLIC' && ip.nullRouted">
                        <span i18n="@@bma_common_nullrouted" class="badge badge-red-outline">Null Routed</span>
                    </div>
                    <div class="col-sm-1" *ngIf="!isUsableIp(ip)">
                        <span i18n="@@bma_common_unusableip" class="badge badge-secondary-outline">Unusable IP</span>
                    </div>
                </div>
            </ng-template>
            <ng-template pTemplate="content" class="p-p-0">
                <div class="p-grid">
                    <div class="p-col-12 border-right" [ngClass]="{'p-md-10': !isEmployee ,'p-md-12': isEmployee}">
                        <div class="p-col-12 p-pb-0 p-d-flex p-flex-col">
                            <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-2">
                                <h5 i18n="@@bma_common_ipdetails" class="pt-2 pb-2 title-h5">IP Details</h5>
                                <div class="p-grid">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_ip">IP</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        {{ ip.ip }}
                                        <ng-container *ngIf="!isEmployee && ip.ip">
                                            <span *ngIf="equipmentType == 'servers' && !ip.mainIp && ip.networkType == 'PUBLIC' && ip.version == 4 && !ip.floatingIp && ip.ddos?.protectionType !== 'ADVANCED'" class="form-check form-check-inline pl-2 float-right">
                                                <input class="form-check-input" name="{{ip.ip}}" [attr.value]="ip.isSelected" [checked]="ip.isSelected" type="checkbox" (change)="selectOrUnSelectIpForCancellation($event, i)" id="select-ips-{{ip.ip}}" />
                                                &nbsp;Select to cancel
                                            </span>
                                        </ng-container>
                                    </div>
                                </div>
                                <div class="p-grid">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_subnetmask">Subnet Mask</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <ng-container *ngIf="ip.prefixLength; else notAvailable">
                                            {{ cidrToNetmask(ip.prefixLength) }}
                                        </ng-container>
                                    </div>
                                </div>
                                <div class="p-grid" *ngIf="!ip.floatingIp">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_gatewayip">Gateway IP</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <ng-container *ngIf="ip.gateway; else notAvailable">
                                            {{ ip.gateway }}
                                        </ng-container>
                                    </div>
                                </div>
                                <div class="p-grid" *ngIf="ip.networkType == 'PUBLIC'">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_nullrouted">Null Routed</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <span class="badge" [ngClass]="{'badge-red-outline': ip.nullRouted,'badge-green-outline':!ip.nullRouted }">
                                            {{ ip.nullRouted ? 'Yes' : 'No' }}
                                        </span>
                                    </div>
                                </div>
                                <div class="p-grid">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_usableip">Usable IP</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <span class="badge" [ngClass]="{'badge-red-outline': !isUsableIp(ip), 'badge-green-outline': isUsableIp(ip)}">
                                            {{ isUsableIp(ip) ? 'Yes' : 'No' }}
                                        </span>
                                    </div>
                                </div>
                                <div class="p-grid">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_iptype">IP Type</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <ng-container *ngIf="ip.type; else notAvailable">
                                            {{ ip.type }}
                                        </ng-container>
                                    </div>
                                </div>
                                <div class="p-grid" *ngIf="equipmentType != 'dedicatedStorages'">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_reversedns">Reverse DNS</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <ng-container *ngIf="ip.reverseLookup; else elseIfReverseLookupBlock">
                                            {{ ip.reverseLookup }}
                                        </ng-container>
                                        <ng-template #elseIfReverseLookupBlock>
                                            <span i18n="@@bma_common_notavailable" class="text-muted" *ngIf="ip.version === 4">Not Available</span>
                                        </ng-template>
                                        <ng-container *ngIf="ip.version === 6">
                                            <span i18n="@@bma_iplist_availableinedit" class="text-muted"> Available in edit modal</span>
                                        </ng-container>
                                    </div>
                                </div>
                            </div>
                            <ng-container *ngIf="!ip.floatingIp && equipmentType != 'networkEquipments' && equipmentType != 'dedicatedStorages' && ip.networkType == 'PUBLIC'">
                                <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-2">
                                    <h5 i18n="@@bma_iplist_ddosipprotectiondetails" class="pt-2 pb-2 title-h5">DDoS IP Protection Details</h5>
                                    <app-ddos-profile-display [ipAddress]="ip" [equipment]="equipment" [equipmentType]="equipmentType"></app-ddos-profile-display>
                                </div>
                            </ng-container>
                        </div>
                        <ng-template #notAvailable>
                            <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
                        </ng-template>
                    </div>
                    <div class="p-col-12 p-md-2 quick-actions" *ngIf="!isEmployee">
                        <h5 i18n="@@bma_common_quickactions" class="p-2">Quick Actions</h5>
                        <div class="p-grid p-flex-column align-items-start">
                            <a i18n-label="@@bma_iplist_editreversedns" pButton (click)="openReverseDnsEditModal(ip)" label="Edit Reverse DNS" class="p-button-link"></a>
                            <ng-container *ngIf="ip.version == 4">
                                <a i18n-label="@@bma_iplist_nullip" pButton *ngIf="!ip.nullRouted" href="javascript:void(0);" label="Null IP Address" (click)="openNullRouteIpNullModal(ip.ip)" class="p-button-link"></a>
                                <a i18n-label="@@bma_iplist_unnullip" pButton *ngIf="ip.nullRouted" href="javascript:void(0);" label="Unnull IP Address" (click)="openUnnullIpModal(ip.ip)" class="p-button-link"></a>
                            </ng-container>
                            <ng-container *ngIf="equipmentType == 'servers' && !ip.mainIp && ip.networkType == 'PUBLIC' && ip.version == 4 && !ip.floatingIp && ip.ddos?.protectionType !== 'ADVANCED'">
                                <a i18n="@@bma_common_cancelipaddress" *ngIf="!equipment.contract.status|default:'ACTIVE';else disableIpCancelBlock" pButton label="Cancel IP Address" target="_blank" [href]="getCommerceCancelIpUrl(ip.ip)" class="p-button-link" [class.disabled]="equipment.isSharedEol"></a>
                                <ng-template #disableIpCancelBlock>
                                    <a i18n-label="@@bma_common_cancelipaddress" title="Disabled due to modification" pButton label="Cancel IP Address" class="p-button-link disabled text-muted disabled-cancel-ip"></a>
                                </ng-template>
                            </ng-container>
                            <a i18n="@@bma_common_createticket" pButton href="/tickets/new?equipmentId={{ equipment.id }}&category=technical-assistance&SubCategory=connectivity" label="Create ticket" class="p-button-link"></a>
                        </div>
                    </div>
                </div>
            </ng-template>
        </p-accordionTab>
    </p-accordion>
</div>
<div *ngIf="!isLoading && ipList">
    <app-pagination [totalCount]="ipList._metadata.totalCount" [limit]="ipList._metadata.limit" [offset]="ipList._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
</div>
<router-outlet></router-outlet>
