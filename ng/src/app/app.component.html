<p-toast></p-toast>
<p-toast key="exception">
    <ng-template let-message pTemplate="message">
        <div>
            <span>{{message.data.error.errorMessage}}</span
            ><br />
            <small>Reference ID: {{message.data.error.correlationId}}</small
            ><br />
            <small>Error code: {{message.data.error.errorCode}}</small>
            <ng-container *ngIf="message.data.error.errorDetails && message.data.error.errorDetails.length">
                <br />
                <small>Error details:</small>
                <br />
                <ul class="ng-star-inserted validation-list">
                    <li *ngFor="let detail of message.data.error.errorDetails">
                        <small class="ng-star-inserted validation-error-details">{{ detail[0] }}: {{ detail[1] }}</small>
                    </li>
                </ul>
            </ng-container>
        </div>
    </ng-template>
</p-toast>
<router-outlet></router-outlet>
