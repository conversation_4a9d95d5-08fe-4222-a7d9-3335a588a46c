import { Component, Input } from '@angular/core';
import { CommerceService } from 'src/app/services/commerce.service';

@Component({
  selector: 'app-data-pack-display',
  templateUrl: './data-pack-display.component.html',
  styleUrls: ['./data-pack-display.component.css'],
})
export class DataPackDisplayComponent {
  @Input() equipment: any;
  @Input() isEmployee: boolean;
  @Input() equipmentType: any;
  @Input() isAggregationPack = false;

  constructor(private commerceService: CommerceService) {}

  getCommerceUpgradeConnectivityUrl(): string {
    if (this.equipmentType === 'servers') {
      return this.commerceService.getCommerceConfigureProductUrl(this.equipment.contract.id, 'DEDSER02_MOD_DATAPACK');
    }
    if (this.equipmentType === 'privateRacks') {
      return this.commerceService.getCommerceConfigureProductUrl(this.equipment.contract.id, 'DEDRAC02_MOD_DATAPACK');
    }
    if (this.equipmentType === 'colocations' && this.equipment.type === 'Shared Colocation') {
      return this.commerceService.getCommerceConfigureProductUrl(this.equipment.contract.id, 'SRDCOLO02_MOD_DATAPACK');
    }
    if (this.equipmentType === 'colocations' && this.equipment.type !== 'Shared Colocation') {
      return this.commerceService.getCommerceConfigureProductUrl(this.equipment.contract.id, 'COLOCA02_MOD_DATAPACK');
    }
    return '';
  }
}
