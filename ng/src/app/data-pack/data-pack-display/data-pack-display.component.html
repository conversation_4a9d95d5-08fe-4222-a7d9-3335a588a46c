<span i18n="@@bma_common_notavailable" *ngIf="!equipment.contract" class="text-muted">Not Available</span>
<ng-container *ngIf="((equipment.contract?.networkTraffic.type|default:'') == '95TH' || (equipment.contract?.networkTraffic.type|default:'') == 'DATATRAFFIC'); else flatfee">
    {{ equipment.contract.networkTraffic.type }}
    {{ equipment.contract.networkTraffic.datatrafficLimit }}
    {{ equipment.contract.networkTraffic.datatrafficUnit }}
    ({{ equipment.contract.networkTraffic.trafficType }})
    <ng-container *ngIf="!isEmployee">
        <ng-container *ngIf="equipment.contract && equipment.contract.status == 'ACTIVE'; else pendingContractModification">
            <a [class.disabled]="equipmentType=='server' && equipment.isSharedEol" i18n-title="@@bma_common_upgradeconnectivity" title="Upgrade Connectivity" id="bandwidth-upgrades-btn" class="float-right" target="_blank" [href]="getCommerceUpgradeConnectivityUrl()">
                <i class="fa fa-upgrade" aria-hidden="true"></i>
            </a>
        </ng-container>
        <ng-template #pendingContractModification>
            <span title="Pending contract modification" class="float-right text-muted">
                <i class="fa fa-refresh"></i>
                <span i18n-title="@@bma_common_pendingcontractmodification" class="sr-only">Pending contract modification</span>
            </span>
        </ng-template>
    </ng-container>
</ng-container>
<ng-template #flatfee>
    <ng-container *ngIf="(equipment.contract?.networkTraffic.type|default:'') == 'FLATFEE'; else bandwidthunmetered">
        Bandwidth (Flat) -
        {{ equipment.contract.networkTraffic.datatrafficLimit }}
        {{ equipment.contract.networkTraffic.datatrafficUnit }}
        ({{ equipment.contract.networkTraffic.trafficType }})
    </ng-container>
</ng-template>
<ng-template #bandwidthunmetered>
    <ng-container *ngIf="(equipment.contract?.networkTraffic.type|default:'') == 'BANDWIDTH_UNMETERED'; else other">
        Bandwidth (UNMETERED) -
        {{ equipment.contract.networkTraffic.datatrafficLimit }}
        {{ equipment.contract.networkTraffic.datatrafficUnit }}
        ({{ equipment.contract.networkTraffic.trafficType }})
    </ng-container>
</ng-template>
<ng-template #other>
    <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
</ng-template>
