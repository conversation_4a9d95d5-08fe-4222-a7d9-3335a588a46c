import { Component, OnInit, Input } from '@angular/core';

@Component({
  selector: 'app-contract-delivery-status',
  templateUrl: './contract-delivery-status.component.html',
  styleUrls: ['./contract-delivery-status.component.css'],
})
export class ContractDeliveryStatusComponent implements OnInit {
  @Input() deliveryStatus = 'UNASSIGNED';

  deliveryStatusCheck: Array<any> = ['IN_QUARANTINE', 'UNASSIGNED', 'ACTIVE', 'PENDING', 'FREE'];

  ngOnInit(): void {
    this.deliveryStatus = this.deliveryStatus.toUpperCase();
  }
}
