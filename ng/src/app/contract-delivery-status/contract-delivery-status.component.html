<span *ngIf="deliveryStatus=='UNASSIGNED' || deliveryStatus=='FREE'" class="badge badge-info">
    {{ deliveryStatus }}
</span>
<span *ngIf="deliveryStatus=='ACTIVE'" class="badge badge-success">
    {{ deliveryStatus }}
</span>
<span *ngIf="deliveryStatus=='PENDING'" class="badge badge-warning">
    {{ deliveryStatus }}
</span>
<span *ngIf="deliveryStatus=='IN_QUARANTINE'" i18n="@@bma_contractdeliverystatus_quarantine" class="badge badge-danger"> IN QUARANTINE </span>
<span *ngIf="deliveryStatusCheck.indexOf(deliveryStatus) < 0" i18n="@@bma_contractdeliverystatus_unknown" class="badge badge-info"> UNKNOWN </span>
<br />
