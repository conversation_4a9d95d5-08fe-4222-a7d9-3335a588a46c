<div class="mb-4">
    <h1 i18n="@@bma_activeanomalies_header">{{ currentSite }} Active Anomalies</h1>
</div>

<div class="row">
    <div class="col-2">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a [routerLink]="['.']" [ngClass]="{'active': !currentSite}" class="nav-link"><span class="text-muted fa fa-cloud"></span>&nbsp;<span i18n="@@bma_activeanomalies_overview">Overview</span></a>
            </li>
            <li *ngFor="let site of sites" class="nav-item">
                <a [routerLink]="['.']" [queryParams]="{site: site}" routerLinkActive="active" class="nav-link"><span class="text-muted fa fa-cloud"></span>&nbsp;{{ site }}</a>
            </li>
        </ul>
    </div>

    <div class="col-10">
        <app-loader *ngIf="isLoading"></app-loader>

        <div class="col-md-12 table-responsive information" *ngIf="currentSite && anomalies.length > 0">
            <table class="table table-sm mt-5 bg-white" *ngFor="let anomaly of anomalies">
                <tbody>
                    <tr>
                        <th i18n="@@bma_common_ipaddress" scope="col">IP Address</th>
                        <td class="text-nowrap">{{ anomaly.ip }}</td>
                        <th i18n="@@bma_common_customerid" scope="col" class="text-nowrap">Customer ID</th>
                        <td>
                            <a [routerLink]="['/customers', anomaly.customerId]">{{ anomaly.customerId }}</a>
                        </td>
                    </tr>
                    <tr>
                        <th i18n="@@bma_common_equipmentid" scope="col" class="text-nowrap">Equipment ID</th>
                        <td>
                            <a [routerLink]="['/']" [queryParams]="{equipmentId: anomaly.equipmentId}">{{ anomaly.equipmentId }}</a>
                        </td>
                        <th i18n="@@bma_activeanomalies_anomaly" scope="col">Anomaly</th>
                        <td class="text-nowrap">{{ anomaly.anomaly }}</td>
                    </tr>
                    <tr>
                        <th i18n="@@bma_common_action" scope="col">Action</th>
                        <td class="text-nowrap">{{ anomaly.action }}</td>
                        <th i18n="@@bma_activeanomalies_duration" scope="col">Duration</th>
                        <td class="text-nowrap">{{ anomaly.duration }}</td>
                    </tr>
                    <tr>
                        <th scope="col">Pkts/s</th>
                        <td class="text-nowrap">{{ anomaly.pktsPerSec }}</td>
                        <th scope="col">Bits/s</th>
                        <td class="text-nowrap">{{ anomaly.bitsPerSec }}</td>
                    </tr>
                    <tr>
                        <th i18n="@@bma_activeanomalies_totalpackets" scope="col" class="text-nowrap">Total Packets</th>
                        <td class="text-nowrap">{{ anomaly.packets }}</td>
                        <th i18n="@@bma_activeanomalies_totalbits" scope="col" class="text-nowrap">Total Bits</th>
                        <td class="text-nowrap">{{ anomaly.bits }}</td>
                    </tr>
                    <tr>
                        <th i18n="@@bma_activeanomalies_profile" scope="col" class="text-nowrap">Profile</th>
                        <td class="text-nowrap">{{ anomaly.thresholdTemplateName }}</td>
                        <th i18n="@@bma_activeanomalies_currenttraffic" scope="col" class="text-nowrap">Current Traffic</th>
                        <td class="text-nowrap">{{ anomaly.currentTraffic }}</td>
                    </tr>
                    <tr>
                        <th i18n="@@bma_activeanomalies_anomalyid" scope="col" class="text-nowrap">Anomaly ID</th>
                        <td class="text-nowrap">{{ anomaly.anomalyId }}</td>
                        <th i18n="@@bma_activeanomalies_starttime" scope="col" class="text-nowrap">Start Time</th>
                        <td class="text-nowrap">{{ anomaly.startTime }}</td>
                    </tr>
                    <tr>
                        <th i18n="@@bma_activeanomalies_latestalarm" scope="col" class="text-nowrap">Latest Alarm</th>
                        <td class="text-nowrap">{{ anomaly.latestAlarm }}</td>
                        <th i18n="@@bma_activeanomalies_expectedexpiry" scope="col" class="text-nowrap">Expected Expiry</th>
                        <td class="text-nowrap">{{ anomaly.expectedExpiry }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="col-md-12" *ngIf="currentSite && anomalies.length > 0">
            <p class="small" i18n="@@bma_activeanomalies_durationdesc">* The 'DURATION' field is the total duration when traffic was over the anomaly threshold.</p>
        </div>
        <div i18n="@@bma_activeanomalies_noanomalies" *ngIf="!isLoading && currentSite && anomalies.length === 0">Couldn't find any anomalies for {{ currentSite }}</div>
        <div *ngIf="!currentSite || !anomalies">
            <p i18n="@@bma_activeanomalies_description">This is an overview of all Wanguard sites and their anomalies.</p>
            <p i18n="@@bma_activeanomalies_viewanomalies">Use the menu on the left to view active anomalies for a specific site.</p>
        </div>
    </div>
</div>
