import { ActivatedRoute } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Title } from '@angular/platform-browser';
import { SiteService } from 'src/app/services/site.service';
import { FormatSizeUnitPipe } from 'src/app/pipes/format-size-unit.pipe';

@Component({
  selector: 'app-active-anomalies',
  templateUrl: './active-anomalies.component.html',
  styleUrls: ['./active-anomalies.component.css'],
})
export class ActiveAnomaliesComponent implements OnInit {
  anomalies: any = [];
  currentSite: string;
  sites: string[];
  isLoading = false;

  constructor(
    private activatedRoute: ActivatedRoute,
    private siteService: SiteService,
    private httpClient: HttpClient,
    private titleService: Title,
    private formatSizeUnitPipe: FormatSizeUnitPipe
  ) {}

  ngOnInit(): void {
    this.sites = this.siteService.sites();
    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    this.currentSite = queryParams.site;

    if (this.currentSite) {
      this.titleService.setTitle(
        this.currentSite + ' ' + $localize`:@@bma_activeanomalies_title:Active Anomalies` + ' | Leaseweb'
      );
      this.getAnomalies(this.currentSite);
    } else {
      this.titleService.setTitle($localize`:@@bma_activeanomalies_title:Active Anomalies` + ' | Leaseweb');
    }
  }

  getAnomalies(site): void {
    this.isLoading = true;
    this.anomalies = [];
    this.httpClient.get<any>(`/_/internal/nseapi/v2/ddos/activeAnomalies/${site}`).subscribe(
      (data: any) => {
        if (data.anomalies !== undefined) {
          this.anomalies = data.anomalies;
          this.anomalies.forEach((anomaly, key) => {
            this.attachCustomerId(anomaly, key);
            this.unitConversion(anomaly, key);
          });
        }
        this.isLoading = false;
      },
      (error: any) => {
        this.isLoading = false;
      }
    );
  }

  attachCustomerId(anomaly, index): void {
    this.httpClient.get<any>(`/_/internal/ipam/ips/${anomaly.ip}`).subscribe({
      next: (data: any) => {
        if (data.equipmentId) {
          this.anomalies[index].equipmentId = data.equipmentId;
        } else {
          this.anomalies[index].equipmentId = '';
        }

        if (data.customer) {
          this.anomalies[index].customerId = data.customer.id;
        } else {
          this.anomalies[index].customerId = '';
        }
      },
      error: (error: any) => {
        this.anomalies[index].equipmentId = '';
        this.anomalies[index].customerId = '';
      },
    });
  }

  unitConversion(anomaly, index): void {
    const packetsConversionKeys = ['packets', 'pktsPerSec'];
    const bitsConversionKeys = ['bits'];
    const bytesPerSecConversionKeys = ['bitsPerSec', 'totalTraffic', 'currentTraffic'];

    for (const key in anomaly) {
      if (packetsConversionKeys.indexOf(key) >= 0) {
        anomaly[key] = this.formatSizeUnitPipe.transform(anomaly[key], false, 'packet');
      } else if (bitsConversionKeys.indexOf(key) >= 0) {
        anomaly[key] = this.formatSizeUnitPipe.transform(anomaly[key], true, 'bytes');
      } else if (bytesPerSecConversionKeys.indexOf(key) >= 0) {
        anomaly[key] = this.formatSizeUnitPipe.transform(anomaly[key], false, 'bytes/s');
      }
    }

    this.anomalies[index] = anomaly;
  }
}
