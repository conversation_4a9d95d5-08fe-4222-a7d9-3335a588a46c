import 'chartjs-adapter-date-fns';
import {
  LswDataViewComponent,
  LswPageHeaderComponent,
  LswTagComponent,
  LswMessageComponent,
} from '@lsw/building-blocks';
import { LswPipesModule } from '@lsw/pipes';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { BrowserModule, Title } from '@angular/platform-browser';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';
import { ReactiveFormsModule } from '@angular/forms';
import { CalendarModule } from 'primeng/calendar';
import { MenuModule } from 'primeng/menu';
import { DialogModule } from 'primeng/dialog';
import { DynamicDialogModule, DialogService } from 'primeng/dynamicdialog';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { AccordionModule } from 'primeng/accordion';
import { ProgressBarModule } from 'primeng/progressbar';
import { PaginatorModule } from 'primeng/paginator';
import { PanelModule } from 'primeng/panel';
import { ChartModule } from 'primeng/chart';
import { MultiSelectModule } from 'primeng/multiselect';
import { ButtonModule } from 'primeng/button';
import { BadgeModule } from 'primeng/badge';
import { RadioButtonModule } from 'primeng/radiobutton';
import { TagModule } from 'primeng/tag';
import { DropdownModule } from 'primeng/dropdown';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { TriStateCheckboxModule } from 'primeng/tristatecheckbox';
import { TabViewModule } from 'primeng/tabview';
import { TableModule } from 'primeng/table';
import { TreeTableModule } from 'primeng/treetable';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { InputTextModule } from 'primeng/inputtext';
import { InputGroupModule } from 'primeng/inputgroup';
import { FloatLabelModule } from 'primeng/floatlabel';
import { FileUploadModule } from 'primeng/fileupload';
import { InputSwitchModule } from 'primeng/inputswitch';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { SliderModule } from 'primeng/slider';
import { NOTIFICATION_HUB_CONFIG_TOKEN } from '@lsw/notification-hub';
import { AppRoutingModule } from './app-routing.module';
import { httpInterceptorProviders } from './http-interceptors';
import { LoaderComponent } from './loader/loader.component';
import { AggregationPackListComponent } from './aggregation-pack/aggregation-pack-list/aggregation-pack-list.component';
import { AggregationPackTabsComponent } from './aggregation-pack/aggregation-pack-tabs/aggregation-pack-tabs.component';
import { AppComponent } from './app.component';
import { AuditLogsComponent } from './audit-logs/audit-logs.component';
import { ColocationDetailsComponent } from './colocation/colocation-details/colocation-details.component';
import { ColocationTabsComponent } from './colocation/colocation-tabs/colocation-tabs.component';
import { ColocationNetworkDetailsComponent } from './colocation/colocation-network-details/colocation-network-details.component';
import { NetworkEquipmentListComponent } from './network-equipment/network-equipment-list/network-equipment-list.component';
import { DedicatedStorageDetailsComponent } from './dedicated-storage/dedicated-storage-details/dedicated-storage-details.component';
import { DedicatedStorageNetworkDetailsComponent } from './dedicated-storage/dedicated-storage-network-details/dedicated-storage-network-details.component';
import { DedicatedStorageTabsComponent } from './dedicated-storage/dedicated-storage-tabs/dedicated-storage-tabs.component';
import { DdosNotificationSettingListComponent } from './ddos-notification-setting/ddos-notification-setting-list/ddos-notification-setting-list.component';
import { DdosNotificationSettingEditModalComponent } from './ddos-notification-setting/ddos-notification-setting-edit-modal/ddos-notification-setting-edit-modal.component';
import { ContractDeliveryStatusComponent } from './contract-delivery-status/contract-delivery-status.component';
import { ContractStatusComponent } from './contract-status/contract-status.component';
import { CustomerAwareHeaderComponent } from './customer-aware-header/customer-aware-header.component';
import { CustomerListComponent } from './customer/customer-list/customer-list.component';
import { CidrToIpPipe } from './pipes/cidr-to-ip.pipe';
import { DdosIpProtectionListComponent } from './ddos-ip-protection/ddos-ip-protection-list/ddos-ip-protection-list.component';
import { DdosIpProtectionAdvancedListComponent } from './ddos-ip-protection/ddos-ip-protection-advanced-list/ddos-ip-protection-advanced-list.component';
import { DefaultPipe } from './pipes/default.pipe';
import { DhcpReservationListComponent } from './dhcp-reservation/dhcp-reservation-list/dhcp-reservation-list.component';
import { EquipmentLocationComponent } from './equipment-location/equipment-location.component';
import { FirmwareVersionCheckComponent } from './server/server-hardware-details/firmware-version-check.component';
import { FirmwareVersionsListComponent } from './firmware-versions/firmware-versions-list/firmware-versions-list.component';
import { GraphBandwidthComponent } from './graph/graph-bandwidth/graph-bandwidth.component';
import { GraphDatatrafficComponent } from './graph/graph-datatraffic/graph-datatraffic.component';
import { GraphLatencyComponent } from './graph/graph-latency/graph-latency.component';
import { GraphReachabilityComponent } from './graph/graph-reachability/graph-reachability.component';
import { GraphHopsComponent } from './graph/graph-hops/graph-hops.component';
import { GraphPacketLossComponent } from './graph/graph-packet-loss/graph-packet-loss.component';
import { RangeCalendarComponent } from './graph/range-calendar/range-calendar.component';
import { IsCustomerDirective } from './directives/is-customer.directive';
import { IsEmployeeDirective } from './directives/is-employee.directive';
import { LookupComponent } from './lookup/lookup.component';
import { JobWizardComponent } from './job/job-wizard/job-wizard.component';
import { JobWizardServersComponent } from './job/job-wizard/job-wizard-servers/job-wizard-servers.component';
import { JobWizardJobComponent } from './job/job-wizard/job-wizard-job/job-wizard-job.component';
import { JobWizardConfirmationComponent } from './job/job-wizard/job-wizard-confirmation/job-wizard-confirmation.component';
import { JobWizardExecutionComponent } from './job/job-wizard/job-wizard-execution/job-wizard-execution.component';
import { FloatingIpDefinitionModalComponent } from './floating-ip/floating-ip-definition-modal/floating-ip-definition-modal.component';
import { FloatingIpRangeAddModalComponent } from './floating-ip/floating-ip-range-add-modal/floating-ip-range-add-modal.component';
import { FloatingIpRangeDetailsComponent } from './floating-ip/floating-ip-range-details/floating-ip-range-details.component';
import { FloatingIpRangeListComponent } from './floating-ip/floating-ip-range-list/floating-ip-range-list.component';
import { FloatingIpRangeRemoveModalComponent } from './floating-ip/floating-ip-range-remove-modal/floating-ip-range-remove-modal.component';
import { FloatingIpDefinitionRemoveModalComponent } from './floating-ip/floating-ip-definition-remove-modal/floating-ip-definition-remove-modal.component';
import { ToggleAnchorIpModalComponent } from './floating-ip/toggle-anchor-ip-modal/toggle-anchor-ip-modal.component';
import { NetworkDeviceDetailsComponent } from './network-device/network-device-details/network-device-details.component';
import { NetworkDeviceListComponent } from './network-device/network-device-list/network-device-list.component';
import { NetworkDeviceLldpComponent } from './network-device/network-device-lldp/network-device-lldp.component';
import { NetworkDeviceTabsComponent } from './network-device/network-device-tabs/network-device-tabs.component';
import { NetworkDeviceAttributeAddModalComponent } from './network-device/network-device-attribute-add-modal/network-device-attribute-add-modal.component';
import { NetworkDeviceManageSpeedModalComponent } from './network-device/network-device-manage-speed-modal/network-device-manage-speed-modal.component';
import { NetworkDeviceNetworkInterfacesComponent } from './network-device/network-device-network-interfaces/network-device-network-interfaces.component';
import { NetworkDeviceAddModalComponent } from './network-device/network-device-add-modal/network-device-add-modal.component';
import { NetworkDeviceGenerateConfigComponent } from './network-device/network-device-generate-config/network-device-generate-config.component';
import { NetworkDeviceBackupConfigComponent } from './network-device/network-device-backup-config/network-device-backup-config.component';
import { NetworkDeviceDownloadConfigComponent } from './network-device/network-device-download-config/network-device-download-config.component';
import { NetworkDeviceRestoreConfigComponent } from './network-device/network-device-restore-config/network-device-restore-config.component';
import { NetworkDeviceRemoveModalComponent } from './network-device/network-device-remove-modal/network-device-remove-modal.component';
import { NetworkDeviceEditModalComponent } from './network-device/network-device-edit-modal/network-device-edit-modal.component';
import { NetworkDeviceQueryInterfacesComponent } from './network-device/network-device-query-interfaces/network-device-query-interfaces.component';
import { IpPerformanceMeasurementAddModalComponent } from './ip-performance/ip-performance-measurement-add-modal/ip-performance-measurement-add-modal.component';
import { IpPerformanceMeasurementEditModalComponent } from './ip-performance/ip-performance-measurement-edit-modal/ip-performance-measurement-edit-modal.component';
import { IpPerformanceMeasurementTracerouteModalComponent } from './ip-performance/ip-performance-measurement-traceroute-modal/ip-performance-measurement-traceroute-modal.component';
import { IpPerformanceMeasurementListComponent } from './ip-performance/ip-performance-measurement-list/ip-performance-measurement-list.component';
import { PageNotFoundComponent } from './page-not-found/page-not-found.component';
import { PaginationComponent } from './pagination/pagination.component';
import { PowerbarAddModalComponent } from './powerbar/powerbar-add-modal/powerbar-add-modal.component';
import { PowerbarRemoveModalComponent } from './powerbar/powerbar-remove-modal/powerbar-remove-modal.component';
import { PowerbarDetailsComponent } from './powerbar/powerbar-details/powerbar-details.component';
import { PowerbarEditModalComponent } from './powerbar/powerbar-edit-modal/powerbar-edit-modal.component';
import { PowerbarGenerateConfigComponent } from './powerbar/powerbar-generate-config/powerbar-generate-config.component';
import { PowerbarListComponent } from './powerbar/powerbar-list/powerbar-list.component';
import { PowerbarTabsComponent } from './powerbar/powerbar-tabs/powerbar-tabs.component';
import { PowerbarOutletOperationModalComponent } from './powerbar/powerbar-outlet-operation-modal/powerbar-outlet-operation-modal.component';
import { ReplacePipe } from './pipes/replace.pipe';
import { SiteConfigurationComponent } from './site-configuration/site-configuration.component';
import { ServerInfoPopupModalComponent } from './server/server-info-popup-modal/server-info-popup-modal.component';
import { TimeAgoPipe } from './pipes/time-ago.pipe';
import { VlanListComponent } from './vlan/vlan-list/vlan-list.component';
import { PrivateNetworkListComponent } from './private-network/private-network-list/private-network-list.component';
import { PrivateNetworkRemoveModalComponent } from './private-network/private-network-remove-modal/private-network-remove-modal.component';
import { NullRouteIpNullModalComponent } from './null-route-ip/null-route-ip-null-modal/null-route-ip-null-modal.component';
import { NullRouteIpUnnullModalComponent } from './null-route-ip/null-route-ip-unnull-modal/null-route-ip-unnull-modal.component';
import { ReverseDnsEditModalComponent } from './reverse-dns-edit-modal/reverse-dns-edit-modal.component';
import { IPv6ReverseDNSModalComponent } from './ip/ip-v6-reverse-dns-modal/ip-v6-reverse-dns-modal.component';
import { DataPackDisplayComponent } from './data-pack/data-pack-display/data-pack-display.component';
import { DdosProfileDisplayComponent } from './ddos-profile/ddos-profile-display/ddos-profile-display.component';
import { DdosProfileChangeModalComponent } from './ddos-profile/ddos-profile-change-modal/ddos-profile-change-modal.component';
import { DdosMitigationReportComponent } from './ddos-profile/ddos-mitigation-report/ddos-mitigation-report.component';
import { NotificationListComponent } from './notification/notification-list/notification-list.component';
import { NotificationAddModalComponent } from './notification/notification-add-modal/notification-add-modal.component';
import { NotificationEditModalComponent } from './notification/notification-edit-modal/notification-edit-modal.component';
import { CredentialListComponent } from './credential/credential-list/credential-list.component';
import { CredentialAddModalComponent } from './credential/credential-add-modal/credential-add-modal.component';
import { CredentialEditModalComponent } from './credential/credential-edit-modal/credential-edit-modal.component';
import { ColocationOrderPrivateNetworkModalComponent } from './colocation/colocation-order-private-network-modal/colocation-order-private-network-modal.component';
import { NetworkEquipmentOrderPrivateNetworkModalComponent } from './network-equipment/network-equipment-order-private-network-modal/network-equipment-order-private-network-modal.component';
import { PrivateRackOrderPrivateNetworkModalComponent } from './private-rack/private-rack-order-private-network-modal/private-rack-order-private-network-modal.component';
import { MetricsReportComponent } from './metrics-report/metrics-report.component';
import { FormatCurrencyPipe } from './pipes/format-currency.pipe';
import { FormatSizeUnitPipe } from './pipes/format-size-unit.pipe';
import { NullRouteIpHistoryListComponent } from './null-route-ip/null-route-ip-history-list/null-route-ip-history-list.component';
import { FormatDatePipe } from './pipes/format-date.pipe';
import { ColocationPowerCycleModalComponent } from './colocation/colocation-power-cycle-modal/colocation-power-cycle-modal.component';
import { IpV6RequestModalComponent } from './ip/ip-v6-request-modal/ip-v6-request-modal.component';
import { DataUsageComponent } from './data-usage/data-usage.component';
import { PrivateRackTabsComponent } from './private-rack/private-rack-tabs/private-rack-tabs.component';
import { IpTransitTabsComponent } from './ip-transit/ip-transit-tabs/ip-transit-tabs.component';
import { IpTransitDetailsComponent } from './ip-transit/ip-transit-details/ip-transit-details.component';
import { IpTransitListComponent } from './ip-transit/ip-transit-list/ip-transit-list.component';
import { DirectInternetAccessTabsComponent } from './direct-internet-access/direct-internet-access-tabs/direct-internet-access-tabs.component';
import { DirectInternetAccessDetailsComponent } from './direct-internet-access/direct-internet-access-details/direct-internet-access-details.component';
import { DirectInternetAccessListComponent } from './direct-internet-access/direct-internet-access-list/direct-internet-access-list.component';
import { PrivateRackNetworkDetailsComponent } from './private-rack/private-rack-network-details/private-rack-network-details.component';
import { PrivateRackDetailsComponent } from './private-rack/private-rack-details/private-rack-details.component';
import { ServerRemoteManagementModalComponent } from './server/server-remote-management-modal/server-remote-management-modal.component';
import { NetworkEquipmentRemoteManagementModalComponent } from './network-equipment/network-equipment-remote-management-modal/network-equipment-remote-management-modal.component';
import { SwitchPortStatusComponent } from './switch-port/switch-port-status/switch-port-status.component';
import { ReferenceEditModalComponent } from './reference-edit-modal/reference-edit-modal.component';
import { PowerOperationModalComponent } from './power-operation-modal/power-operation-modal.component';
import { PowerOperationModalV3Component } from './v3/ui/power-operation-modal/power-operation-modal-v3.component';
import { ServerListComponent } from './v3/features/equipments/server/server-list/server-list.component';
import { ServerTabsComponent } from './server/server-tabs/server-tabs.component';
import { IpListComponent } from './ip/ip-list/ip-list.component';
import { ServerDetailsComponent } from './server/server-details/server-details.component';
import { NotificationRemoveModalComponent } from './notification/notification-remove-modal/notification-remove-modal.component';
import { ServerRescueModeModalComponent } from './server/server-rescue-mode-modal/server-rescue-mode-modal.component';
import { ServerHardwareDetailsComponent } from './server/server-hardware-details/server-hardware-details.component';
import { ServerHardwareDetailsDisksComponent } from './server/server-hardware-details/server-hardware-details-disks.component';
import { TrafficGraphsComponent } from './graph/traffic-graphs/traffic-graphs.component';
import { IpPerformanceMeasurementGraphsComponent } from './graph/ip-performance-measurement-graphs/ip-performance-measurement-graphs.component';
import { CredentialRemoveModalComponent } from './credential/credential-remove-modal/credential-remove-modal.component';
import { ServerWidgetComponent } from './server/server-widget/server-widget.component';
import { ServerWidgetPowerComponent } from './server/server-widget/server-widget-power/server-widget-power.component';
import { ServerWidgetSwitchPortsComponent } from './server/server-widget/server-widget-switch-ports/server-widget-switch-ports.component';
import { ServerWidgetCustomInstallationComponent } from './server/server-widget/server-widget-custom-installation/server-widget-custom-installation.component';
import { ServerWidgetActiveJobComponent } from './server/server-widget/server-widget-active-job/server-widget-active-job.component';
import { ServerWidgetNullRouteComponent } from './server/server-widget/server-widget-null-route/server-widget-null-route.component';
import { ServerWidgetContractComponent } from './server/server-widget/server-widget-contract/server-widget-contract.component';
import { ServerNetworkDetailsComponent } from './server/server-network-details/server-network-details.component';
import { ServerConfigureHardwareRaidModalComponent } from './server/server-configure-hardware-raid-modal/server-configure-hardware-raid-modal.component';
import { ServerCustomInstallationModalComponent } from './server/server-custom-installation-modal/server-custom-installation-modal.component';
import { ServerInstallWizardComponent } from './server/server-install-wizard/server-install-wizard.component';
import { ServerFirmwareUpdateModalComponent } from './server/server-firmware-update-modal/server-firmware-update-modal.component';
import { ServerHardwareScanModalComponent } from './server/server-hardware-scan-modal/server-hardware-scan-modal.component';
import { SwitchPortCloseModalComponent } from './switch-port/switch-port-close-modal/switch-port-close-modal.component';
import { PrivateNetworkColocationListComponent } from './private-network/private-network-colocation-list/private-network-colocation-list.component';
import { PrivateNetworkInformationSectionComponent } from './private-network/private-network-information-section/private-network-information-section.component';
import { PrivateNetworkNetworkEquipmentListComponent } from './private-network/private-network-network-equipment-list/private-network-network-equipment-list.component';
import { PrivateNetworkPrivateRackListComponent } from './private-network/private-network-private-rack-list/private-network-private-rack-list.component';
import { PrivateNetworkServerListComponent } from './private-network/private-network-server-list/private-network-server-list.component';
import { PrivateNetworkDetailsComponent } from './private-network/private-network-details/private-network-details.component';
import { PrivateNetworkDedicatedStorageListComponent } from './private-network/private-network-dedicated-storage-list/private-network-dedicated-storage-list.component';
import { PrivateNetworkAddServerModalComponent } from './private-network/private-network-add-server-modal/private-network-add-server-modal.component';
import { PrivateNetworkAddServerModalV3Component } from './v3/ui/private-network/private-network-add-server-modal/private-network-add-server-modal-v3.component';
import { PrivateNetworkAddColocationModalComponent } from './private-network/private-network-add-colocation-modal/private-network-add-colocation-modal.component';
import { PrivateNetworkAddDedicatedNetworkEquipmentModalComponent } from './private-network/private-network-add-dedicated-network-equipment-modal/private-network-add-dedicated-network-equipment-modal.component';
import { PrivateNetworkAddDedicatedStorageModalComponent } from './private-network/private-network-add-dedicated-storage-modal/private-network-add-dedicated-storage-modal.component';
import { PrivateNetworkAddPrivateRackModalComponent } from './private-network/private-network-add-private-rack-modal/private-network-add-private-rack-modal.component';
import { PrivateNetworkAddPrivateCloudModalComponent } from './private-network/private-network-add-private-cloud-modal/private-network-add-private-cloud-modal.component';
import { PrivateNetworkModifyServerModalComponent } from './private-network/private-network-modify-server-modal/private-network-modify-server-modal.component';
import { PrivateNetworkDhcpModalComponent } from './private-network/private-network-dhcp-modal/private-network-dhcp-modal.component';
import { PrivateNetworkModifyNetworkEquipmentModalComponent } from './private-network/private-network-modify-network-equipment-modal/private-network-modify-network-equipment-modal.component';
import { FormatSpeedPipe } from './pipes/format-speed.pipe';
import { FormatDiskPerformanceTypePipe } from './pipes/format-disk-performance-type.pipe';
import { FormatDatabaseTypePipe } from './pipes/format-database-type.pipe';
import { RoundPipe } from './pipes/round.pipe';
import { JoinPipe } from './pipes/join.pipe';
import { ValuesPipe } from './pipes/values.pipe';
import { JobListComponent } from './job/job-list/job-list.component';
import { ServerJobDetailsComponent } from './server/server-job-details/server-job-details.component';
import { ServerPrepareForSaleModalComponent } from './server/server-prepare-for-sale-modal/server-prepare-for-sale-modal.component';
import { ServerJobListComponent } from './server/server-job-list/server-job-list.component';
import { NetworkEquipmentTabsComponent } from './network-equipment/network-equipment-tabs/network-equipment-tabs.component';
import { NetworkEquipmentDetailsComponent } from './network-equipment/network-equipment-details/network-equipment-details.component';
import { NetworkEquipmentNetworkDetailsComponent } from './network-equipment/network-equipment-network-details/network-equipment-network-details.component';
import { RemoteManagementComponent } from './remote-management/remote-management.component';
import { SelectTextDirective } from './directives/select-text.directive';
import { ActiveAnomaliesComponent } from './active-anomalies/active-anomalies.component';
import { IpPerformanceMeasurementRemoveModalComponent } from './ip-performance/ip-performance-measurement-remove-modal/ip-performance-measurement-remove-modal.component';
import { IpPerformanceMeasurementTabComponent } from './ip-performance/ip-performance-measurement-tabs/ip-performance-measurement-tabs.component';
import { IpPerformanceMeasurementTracemonComponent } from './ip-performance/ip-performance-measurement-tracemon/ip-performance-measurement-tracemon.component';
import { IpPerformanceMeasurementLatencymonComponent } from './ip-performance/ip-performance-measurement-latencymon/ip-performance-measurement-latencymon.component';
import { JobCancelModalComponent } from './job/job-cancel-modal/job-cancel-modal.component';
import { JobExpireModalComponent } from './job/job-expire-modal/job-expire-modal.component';
import { JobRetryModalComponent } from './job/job-retry-modal/job-retry-modal.component';
import { JobTaskDetailsModalComponent } from './job/job-task-details-modal/job-task-details-modal.component';
import { DhcpReservationRemoveModalComponent } from './dhcp-reservation/dhcp-reservation-remove-modal/dhcp-reservation-remove-modal.component';
import { NetworkDeviceAttributeRemoveModalComponent } from './network-device/network-device-attribute-remove-modal/network-device-attribute-remove-modal.component';
import { NetworkDevicePortActionModalComponent } from './network-device/network-device-port-action-modal/network-device-port-action-modal.component';
import { ArpCheckerListComponent } from './arp-checker/arp-checker-list/arp-checker-list.component';
import { ServerInstallWizardConfirmationModalComponent } from './server/server-install-wizard/server-install-wizard-confirmation-modal/server-install-wizard-confirmation-modal.component';
import { EquipmentSwitchPortStatusComponent } from './equipment-switch-port-status/equipment-switch-port-status.component';
import { CountryFlagComponent } from './country-flag/country-flag.component';
import { ServerInstallWizardConfirmationComponent } from './server/server-install-wizard/server-install-wizard-confirmation/server-install-wizard-confirmation.component';
import { ServerInstallWizardSoftwareComponent } from './server/server-install-wizard/server-install-wizard-software/server-install-wizard-software.component';
import { ServerInstallWizardPartitioningComponent } from './server/server-install-wizard/server-install-wizard-partitioning/server-install-wizard-partitioning.component';
import { ServerInstallWizardSettingsComponent } from './server/server-install-wizard/server-install-wizard-settings/server-install-wizard-settings.component';
import { LocalStorageEditorComponent } from './local-storage-editor/local-storage-editor.component';
import { SshKeysSelectorComponent } from './ssh-keys-selector/ssh-keys-selector.component';
import { CommunityStringComponent } from './community-string/community-string.component';
import { ServerNewFeaturesModalComponent } from './server/server-new-features-modal/server-new-features-modal.component';
import { ToggleBondingModalComponent } from './toggle-bonding-modal/toggle-bonding-modal.component';
import { DiskDetailsComponent } from './disk/disk-details/disk-details.component';
import { DiskJobSmartctlAttributesModalComponent } from './disk/disk-job-smartctl-attributes-modal/disk-job-smartctl-attributes-modal.component';
import { RemoteHandsListComponent } from './remote-hands-list/remote-hands-list.component';
import { CredentialListTableComponent } from './credential/credential-list-table/credential-list-table.component';
import { CopyPasswordComponent } from './credential/credential-list-table/copy-password/copy-password.component';
import { DragDirective } from './local-storage-editor/dragDrop.directive';
import { PowerbarExportToCsvModalComponent } from './powerbar/powerbar-export-to-csv-modal/powerbar-export-to-csv-modal.component';
import { CloudConnectListComponent } from './cloud-connect/cloud-connect-list/cloud-connect-list.component';
import { CloudConnectDetailsComponent } from './cloud-connect/cloud-connect-details/cloud-connect-details.component';
import { PrivateNetworkRedundancyOrderModalComponent } from './private-network/private-network-redundancy-order-modal/private-network-redundancy-order-modal.component';
import { JobPayloadGeneratorComponent } from './job/job-payload-generator/job-payload-generator.component';
import { CloudConnectOrderModalComponent } from './cloud-connect/cloud-connect-order/cloud-connect-order-modal.component';
import { PrivateNetworkAddRackModalV3Component } from './v3/ui/private-network/private-network-add-rack-modal/private-network-add-rack-modal-v3.component';
import { PrivateNetworkAddDedicatedStorageV3Component } from './v3/ui/private-network/private-network-add-dedicated-storage-modal/private-network-add-dedicated-storage-modal.component-v3';
import { EquipmentShowStatusV3Component } from './v3/features/equipments/private-rack/equipment-show-status/equipment-show-status.component-v3';
import { PrivateRackListComponent } from './v3/features/equipments/private-rack/private-rack-list/private-rack-list.component';
import { DedicatedStorageListComponent } from './v3/features/equipments/dedicated-storage/dedicated-storage-list/dedicated-storage-list.component';
import { PrivateNetworkModifyServerSpeedModalV3Component } from './v3/ui/private-network/private-network-modify-server-speed-modal/private-network-modify-server-speed-modal-v3.component';
import { ColocationListComponent } from './v3/features/equipments/colocation/colocation-list/colocation-list.component';
import { PrivateNetworkAddColocationV3Component } from './v3/ui/private-network/private-network-add-colocation-modal/private-network-add-colocation-modal.component-v3';
import { PrivateNetworkColocationPowerCycleV3Component } from './v3/ui/private-network/private-network-colocation-power-cycle-modal/private-network-colocation-power-cycle-modal.component';
import { PrivateNetworkBondingModalV3Component } from './v3/ui/private-network/private-network-bonding-modal/private-network-bonding-modal-v3.component';

@NgModule({
  declarations: [
    LoaderComponent,
    AppComponent,
    AuditLogsComponent,
    ColocationDetailsComponent,
    ColocationTabsComponent,
    ColocationNetworkDetailsComponent,
    NetworkEquipmentListComponent,
    DdosNotificationSettingListComponent,
    DdosNotificationSettingEditModalComponent,
    ContractDeliveryStatusComponent,
    ContractStatusComponent,
    CustomerAwareHeaderComponent,
    CustomerListComponent,
    CidrToIpPipe,
    DdosIpProtectionListComponent,
    DdosIpProtectionAdvancedListComponent,
    DefaultPipe,
    DhcpReservationListComponent,
    EquipmentLocationComponent,
    FirmwareVersionCheckComponent,
    FirmwareVersionsListComponent,
    GraphBandwidthComponent,
    GraphDatatrafficComponent,
    GraphLatencyComponent,
    GraphReachabilityComponent,
    GraphHopsComponent,
    GraphPacketLossComponent,
    RangeCalendarComponent,
    TrafficGraphsComponent,
    IpPerformanceMeasurementGraphsComponent,
    IsCustomerDirective,
    IsEmployeeDirective,
    LookupComponent,
    NetworkDeviceDetailsComponent,
    NetworkDeviceQueryInterfacesComponent,
    NetworkDeviceListComponent,
    NetworkDeviceLldpComponent,
    NetworkDeviceTabsComponent,
    NetworkDeviceAttributeAddModalComponent,
    NetworkDeviceManageSpeedModalComponent,
    NetworkDeviceNetworkInterfacesComponent,
    NetworkDeviceAddModalComponent,
    NetworkDeviceGenerateConfigComponent,
    NetworkDeviceBackupConfigComponent,
    NetworkDeviceDownloadConfigComponent,
    NetworkDeviceRestoreConfigComponent,
    NetworkDeviceRemoveModalComponent,
    NetworkDeviceEditModalComponent,
    IpPerformanceMeasurementAddModalComponent,
    IpPerformanceMeasurementEditModalComponent,
    IpPerformanceMeasurementTracerouteModalComponent,
    IpPerformanceMeasurementListComponent,
    PageNotFoundComponent,
    PaginationComponent,
    PowerbarAddModalComponent,
    PowerbarRemoveModalComponent,
    PowerbarDetailsComponent,
    PowerbarEditModalComponent,
    PowerbarGenerateConfigComponent,
    PowerbarListComponent,
    PowerbarTabsComponent,
    PowerbarOutletOperationModalComponent,
    PowerbarExportToCsvModalComponent,
    ReplacePipe,
    SiteConfigurationComponent,
    ServerInfoPopupModalComponent,
    ServerWidgetComponent,
    ServerWidgetPowerComponent,
    ServerWidgetSwitchPortsComponent,
    ServerWidgetCustomInstallationComponent,
    ServerWidgetActiveJobComponent,
    ServerWidgetNullRouteComponent,
    ServerWidgetContractComponent,
    TimeAgoPipe,
    VlanListComponent,
    JobWizardComponent,
    JobPayloadGeneratorComponent,
    JobWizardServersComponent,
    JobWizardJobComponent,
    JobWizardConfirmationComponent,
    JobWizardExecutionComponent,
    FloatingIpDefinitionModalComponent,
    FloatingIpRangeAddModalComponent,
    FloatingIpRangeDetailsComponent,
    FloatingIpRangeListComponent,
    FloatingIpRangeRemoveModalComponent,
    FloatingIpDefinitionRemoveModalComponent,
    ToggleAnchorIpModalComponent,
    PrivateNetworkColocationListComponent,
    PrivateNetworkInformationSectionComponent,
    PrivateNetworkPrivateRackListComponent,
    PrivateNetworkServerListComponent,
    PrivateNetworkDetailsComponent,
    PrivateNetworkDedicatedStorageListComponent,
    PrivateNetworkListComponent,
    PrivateNetworkNetworkEquipmentListComponent,
    NullRouteIpNullModalComponent,
    NullRouteIpUnnullModalComponent,
    ReverseDnsEditModalComponent,
    IPv6ReverseDNSModalComponent,
    DataPackDisplayComponent,
    DdosProfileDisplayComponent,
    DdosProfileChangeModalComponent,
    DdosMitigationReportComponent,
    DiskDetailsComponent,
    NotificationListComponent,
    NotificationAddModalComponent,
    NotificationEditModalComponent,
    ColocationListComponent,
    CredentialListComponent,
    CredentialAddModalComponent,
    CredentialEditModalComponent,
    ColocationOrderPrivateNetworkModalComponent,
    NetworkEquipmentOrderPrivateNetworkModalComponent,
    PrivateRackOrderPrivateNetworkModalComponent,
    FormatCurrencyPipe,
    FormatSizeUnitPipe,
    NullRouteIpHistoryListComponent,
    FormatDatePipe,
    ColocationPowerCycleModalComponent,
    PrivateNetworkColocationPowerCycleV3Component,
    IpV6RequestModalComponent,
    DataUsageComponent,
    PrivateRackTabsComponent,
    IpTransitTabsComponent,
    IpTransitDetailsComponent,
    IpTransitListComponent,
    DirectInternetAccessTabsComponent,
    DirectInternetAccessDetailsComponent,
    DirectInternetAccessListComponent,
    PrivateRackNetworkDetailsComponent,
    MetricsReportComponent,
    PrivateRackDetailsComponent,
    ServerRemoteManagementModalComponent,
    NetworkEquipmentRemoteManagementModalComponent,
    SwitchPortStatusComponent,
    PrivateRackListComponent,
    ReferenceEditModalComponent,
    PowerOperationModalComponent,
    PowerOperationModalV3Component,
    ServerListComponent,
    ServerTabsComponent,
    IpListComponent,
    ServerDetailsComponent,
    NotificationRemoveModalComponent,
    ServerRescueModeModalComponent,
    ServerHardwareDetailsComponent,
    ServerHardwareDetailsDisksComponent,
    CredentialRemoveModalComponent,
    ServerNetworkDetailsComponent,
    ServerConfigureHardwareRaidModalComponent,
    ServerCustomInstallationModalComponent,
    ServerInstallWizardComponent,
    ServerFirmwareUpdateModalComponent,
    ServerHardwareScanModalComponent,
    SwitchPortCloseModalComponent,
    PrivateNetworkDhcpModalComponent,
    PrivateNetworkModifyServerModalComponent,
    PrivateNetworkModifyServerSpeedModalV3Component,
    PrivateNetworkAddServerModalComponent,
    PrivateNetworkAddDedicatedStorageV3Component,
    PrivateNetworkAddServerModalV3Component,
    PrivateNetworkAddRackModalV3Component,
    PrivateNetworkAddColocationModalComponent,
    PrivateNetworkAddColocationV3Component,
    PrivateNetworkAddDedicatedNetworkEquipmentModalComponent,
    PrivateNetworkAddDedicatedStorageModalComponent,
    PrivateNetworkAddPrivateRackModalComponent,
    PrivateNetworkAddPrivateCloudModalComponent,
    PrivateNetworkRemoveModalComponent,
    PrivateNetworkModifyNetworkEquipmentModalComponent,
    PrivateNetworkRedundancyOrderModalComponent,
    CloudConnectOrderModalComponent,
    RoundPipe,
    JoinPipe,
    ValuesPipe,
    FormatSpeedPipe,
    FormatDiskPerformanceTypePipe,
    FormatDatabaseTypePipe,
    JobListComponent,
    ServerJobDetailsComponent,
    ServerPrepareForSaleModalComponent,
    ServerJobListComponent,
    NetworkEquipmentTabsComponent,
    NetworkEquipmentDetailsComponent,
    NetworkEquipmentNetworkDetailsComponent,
    DedicatedStorageListComponent,
    DedicatedStorageDetailsComponent,
    DedicatedStorageNetworkDetailsComponent,
    DedicatedStorageTabsComponent,
    RemoteManagementComponent,
    SelectTextDirective,
    AggregationPackListComponent,
    AggregationPackTabsComponent,
    ActiveAnomaliesComponent,
    IpPerformanceMeasurementRemoveModalComponent,
    IpPerformanceMeasurementTabComponent,
    IpPerformanceMeasurementTracemonComponent,
    IpPerformanceMeasurementLatencymonComponent,
    JobCancelModalComponent,
    JobExpireModalComponent,
    JobRetryModalComponent,
    JobTaskDetailsModalComponent,
    DhcpReservationRemoveModalComponent,
    NetworkDeviceAttributeRemoveModalComponent,
    NetworkDevicePortActionModalComponent,
    ArpCheckerListComponent,
    ServerInstallWizardConfirmationModalComponent,
    EquipmentSwitchPortStatusComponent,
    EquipmentShowStatusV3Component,
    CountryFlagComponent,
    ServerInstallWizardConfirmationComponent,
    ServerInstallWizardSoftwareComponent,
    ServerInstallWizardPartitioningComponent,
    ServerInstallWizardSettingsComponent,
    LocalStorageEditorComponent,
    SshKeysSelectorComponent,
    CommunityStringComponent,
    ServerNewFeaturesModalComponent,
    ToggleBondingModalComponent,
    PrivateNetworkBondingModalV3Component,
    DiskJobSmartctlAttributesModalComponent,
    RemoteHandsListComponent,
    CredentialListTableComponent,
    CopyPasswordComponent,
    DragDirective,
    CloudConnectListComponent,
    CloudConnectDetailsComponent,
  ],
  imports: [
    LswDataViewComponent,
    LswPageHeaderComponent,
    LswTagComponent,
    LswPipesModule,
    LswMessageComponent,
    ChartModule,
    MultiSelectModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    BrowserModule,
    FormsModule,
    HttpClientModule,
    CalendarModule,
    MenuModule,
    AccordionModule,
    DialogModule,
    DynamicDialogModule,
    OverlayPanelModule,
    ProgressBarModule,
    PaginatorModule,
    PanelModule,
    ButtonModule,
    BadgeModule,
    RadioButtonModule,
    TagModule,
    DropdownModule,
    ToastModule,
    ReactiveFormsModule,
    TriStateCheckboxModule,
    TabViewModule,
    TableModule,
    TreeTableModule,
    CardModule,
    CheckboxModule,
    FileUploadModule,
    CheckboxModule,
    InputTextModule,
    InputGroupModule,
    FloatLabelModule,
    InputSwitchModule,
    IconFieldModule,
    InputIconModule,
    SliderModule,
  ],
  providers: [
    httpInterceptorProviders,
    MessageService,
    {
      provide: DialogService,
    },
    Title,
    { provide: `ROLES`, useValue: window.ROLES ?? '' },
    { provide: `PERMISSIONS`, useValue: window.PERMISSIONS ?? '' },
    { provide: `CUSTOMERID`, useValue: window.CUSTOMERID ?? '' },
    { provide: `SALESORGID`, useValue: window.SALESORGID ?? '' },
    { provide: `COMMERCE_BASE_URL`, useValue: window.COMMERCE_BASE_URL ?? '' },
    { provide: `BASE_ELEMENT`, useValue: document.querySelector('base') },
    {
      provide: NOTIFICATION_HUB_CONFIG_TOKEN,
      useValue: {
        notificationTokenUrl: window.NOTIFICATION_TOKEN_URL,
        notificationHubUrl: window.NOTIFICATION_HUB_URL,
        notificationHubTopicUrl: window.NOTIFICATION_HUB_TOPIC_URL,
      },
    },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
