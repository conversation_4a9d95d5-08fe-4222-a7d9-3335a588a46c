<app-customer-aware-header i18n-caption="@@bma_iptransitlist_caption" caption="IP Transits" [customerId]="customerId" [salesOrgId]="salesOrgId" [isEmployee]="isEmployee"></app-customer-aware-header>

<app-loader *ngIf="isLoading"></app-loader>

<div *ngIf="!isLoading && ipTransits">
    <div class="p-grid p-col-12 bg-white p-mb-2 p-py-4 justify-content-center" *ngIf="ipTransits.ipTransits.length === 0">
        <span i18n="@@bma_iptransitlist_noiptransits" *ngIf="isEmployee; else notAvailableForCustomer">No ip transits found.</span>
        <ng-template #notAvailableForCustomer>
            <span i18n="@@bma_iptransitlist_noiptransitscustomer">You don't have ip transits.</span>
        </ng-template>
    </div>
    <p-accordion [multiple]="true">
        <p-accordionTab *ngFor="let ipTransit of ipTransits.ipTransits">
            <ng-template pTemplate="header">
                <div class="row col-sm-12 align-items-center">
                    <div class="col-sm-3">
                        <h3 class="mt-2">{{ ipTransit.id }}</h3>
                    </div>
                    <div class="col-sm-4">
                        <h3 class="mt-2">{{ ipTransit?.contract?.id ? ipTransit?.contract?.id : '-' }}</h3>
                    </div>
                    <div class="col-sm-3">
                        <h3 class="mt-2">{{ ipTransit?.contract?.deliveryStatus ? ipTransit?.contract?.deliveryStatus : '-' }}</h3>
                    </div>
                    <div class="col-sm-2">
                        <span class="h5">
                            {{ ipTransit?.contract?.reference ? ipTransit?.contract?.reference : '-' }}
                        </span>
                    </div>
                </div>
            </ng-template>
            <ng-template pTemplate="content" class="p-p-0">
                <div class="p-grid border-bottom">
                    <div class="p-col-12 p-md-10 border-right">
                        <h5 i18n="@@bma_common_iptransitdetails" class="p-ml-6 pt-2">IP Transit Details</h5>
                        <div class="p-col-12 p-d-flex p-flex-col p-pb-0">
                            <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-2">
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_iptransitid">IP Transit ID</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ ipTransit.id }}</div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_ipaddress">IP Address</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <ng-container *ngIf="(ipTransit?.networkInterfaces?.public && ipTransit?.networkInterfaces?.public?.ip); else notAvailable">
                                            <span class="selectable">{{ ipTransit.networkInterfaces.public.ip|default:'-'|cidrToIp }}</span>
                                        </ng-container>
                                    </div>
                                </div>
                            </div>
                            <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-2">
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_contractid">Contract ID</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ ipTransit?.contract?.id ? ipTransit?.contract?.id : '-' }}</div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_deliverystatus">Delivery Status</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ ipTransit?.contract?.deliveryStatus ? ipTransit?.contract?.deliveryStatus : '-' }}</div>
                                </div>
                            </div>
                        </div>
                        <ng-template #notAvailable>
                            <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
                        </ng-template>
                    </div>
                    <div class="p-col-12 p-md-2 quick-actions">
                        <h5 i18n="@@bma_common_quickactions" class="p-2">Quick Actions</h5>
                        <div class="p-grid p-flex-column align-items-start">
                            <a pButton i18n-label="@@bma_common_datagraphs" *ngIf="(ipTransit?.networkInterfaces?.public|default:'') && (ipTransit?.networkInterfaces?.public?.ports|default:''); else notDataGraph" [routerLink]="['/ipTransits', ipTransit.id, 'graphs']" label="Datagraphs" class="p-button-link"></a>
                            <ng-template #notDataGraph>
                                <button pButton i18n-label="@@bma_common_datagraphs" label="Datagraphs" disabled class="p-button-link"></button>
                            </ng-template>
                        </div>
                    </div>
                </div>
                <div class="p-grid p-pr-5 p-pt-3">
                    <a i18n-label="@@bma_common_manage" pButton [routerLink]="['/ipTransits', ipTransit.id]" class="p-button-primary ml-auto" label="Manage"></a>
                </div>
            </ng-template>
        </p-accordionTab>
    </p-accordion>
</div>

<div *ngIf="!isLoading && ipTransits">
    <app-pagination [totalCount]="ipTransits._metadata.totalCount" [limit]="ipTransits._metadata.limit" [offset]="ipTransits._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
</div>
