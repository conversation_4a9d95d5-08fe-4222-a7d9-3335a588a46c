import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { SiteService } from 'src/app/services/site.service';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';

@Component({
  selector: 'app-ip-transit-list',
  templateUrl: './ip-transit-list.component.html',
  styleUrls: ['./ip-transit-list.component.css'],
})
export class IpTransitListComponent implements OnInit {
  isLoading = false;
  ipTransits: any;
  customerId = '';
  salesOrgId = '';
  isEmployee = false;
  filters: UntypedFormGroup;

  constructor(
    private activatedRoute: ActivatedRoute,
    private titleService: Title,
    private currentUserService: CurrentUserService,
    private httpClient: HttpClient,
    private router: Router,
    private siteService: SiteService,
    private readonly formBuilder: UntypedFormBuilder
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.isLoading = true;
    this.filters = this.formBuilder.group({
      offset: [0],
      limit: [20],
      filter: [null],
      customerId: [null],
      salesOrgId: [null],
    });

    if (!this.isEmployee) {
      this.customerId = this.currentUserService.getCustomerId();
      this.salesOrgId = this.currentUserService.getSalesOrgId();
    }

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));

    /*eslint-disable */
    const event = new Event('update_product_navbar');
    event['customerId'] = this.customerId;
    event['salesOrgId'] = this.salesOrgId;
    event['country'] = this.siteService.getCountry(this.salesOrgId);
    window.dispatchEvent(event);
    /*eslint-enable */

    if (this.customerId) {
      this.titleService.setTitle(
        $localize`:@@bma_iptransitlist_customertitle:IP Transits | Customer ${this.customerId}` + ' | Leaseweb'
      );
    }
    this.titleService.setTitle($localize`:@@bma_iptransitlist_title:IP Transits` + ' | Leaseweb');
  }

  onQueryParamsChanged(queryParams): void {
    this.isLoading = true;
    this.filters.reset(queryParams);
    const params = this.sanitise(this.filters.getRawValue());

    if (queryParams.customerId) {
      this.customerId = queryParams.customerId;
    }

    if (queryParams.salesOrgId) {
      this.salesOrgId = queryParams.salesOrgId;
    }

    this.httpClient.get<any>(`/_/internal/dedicatedserverapi/v2/ipTransits`, { params }).subscribe({
      next: (data: any) => {
        this.isLoading = false;
        this.ipTransits = data;
      },
      error: (error: any) => {
        this.isLoading = false;
        this.ipTransits = [];
      },
    });
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
