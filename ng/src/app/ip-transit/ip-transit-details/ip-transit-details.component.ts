import { Component, OnInit, Input } from '@angular/core';
import { ModalService } from 'src/app/services/modal.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ReferenceEditModalComponent } from 'src/app/reference-edit-modal/reference-edit-modal.component';
import { CurrentUserService } from 'src/app/services/current-user.service';

@Component({
  selector: 'app-ip-transit-details',
  templateUrl: './ip-transit-details.component.html',
  styleUrls: ['./ip-transit-details.component.css'],
})
export class IpTransitDetailsComponent implements OnInit {
  @Input() ipTransit: any;

  isEmployee: boolean;
  dynamicDialogRef: DynamicDialogRef;
  networkPorts = [];

  constructor(
    private modalService: ModalService,
    private currentUserService: CurrentUserService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    if (this.ipTransit.networkInterfaces) {
      const networkInterfaceOrder = ['public'];
      networkInterfaceOrder.forEach((networkInterfacerKey) => {
        if (
          this.ipTransit.networkInterfaces[networkInterfacerKey] &&
          this.ipTransit.networkInterfaces[networkInterfacerKey].ports.length > 0
        ) {
          const networkPorts = this.ipTransit.networkInterfaces[networkInterfacerKey].ports;
          networkPorts.forEach((networkPort) => {
            networkPort.networkType = networkInterfacerKey;
            this.networkPorts.push(networkPort);
          });
        }
      });
    }
  }
  openReferenceEditModal() {
    this.dynamicDialogRef = this.modalService.show(ReferenceEditModalComponent, {
      equipment: this.ipTransit,
      equipmentType: 'ipTransits',
    });

    this.dynamicDialogRef.onClose.subscribe((reason: any) => {
      if (typeof reason === 'string') {
        if (this.ipTransit.contract) {
          this.ipTransit.contract.reference = reason;
        }
      }
    });
  }
}
