import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { SiteService } from 'src/app/services/site.service';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-ip-transit-tabs',
  templateUrl: './ip-transit-tabs.component.html',
  styleUrls: ['./ip-transit-tabs.component.css'],
})
export class IpTransitTabsComponent implements OnInit {
  isLoading = false;
  ipTransit: any;
  ipTransitId: string;
  isEmployee = false;

  constructor(
    private activatedRoute: ActivatedRoute,
    private titleService: Title,
    private currentUserService: CurrentUserService,
    private httpClient: HttpClient,
    private router: Router,
    private siteService: SiteService
  ) {}

  ngOnInit(): void {
    this.ipTransitId = this.activatedRoute.snapshot.url[1].path;
    this.isEmployee = this.currentUserService.isEmployee();

    this.isLoading = true;

    this.httpClient.get<any>(`/_/internal/dedicatedserverapi/v2/ipTransits/${this.ipTransitId}`).subscribe({
      next: (data: any) => {
        this.isLoading = false;
        this.ipTransit = data;
        this.titleService.setTitle(
          `${this.ipTransit.id} - Ip Transit ${
            this.ipTransit.contract.reference ? this.ipTransit.contract.reference : ''
          }| Leaseweb`
        );

        /*eslint-disable */
        const event = new Event('update_product_navbar');
        event['customerId'] = this.ipTransit.contract.customerId;
        event['salesOrgId'] = this.ipTransit.contract.salesOrgId;
        event['country'] = this.siteService.getCountry(this.ipTransit.contract.salesOrgId);
        window.dispatchEvent(event);
        /*eslint-enable */
      },
      error: (error: any) => {
        this.isLoading = false;
        this.router.navigate(['**']);
      },
    });
  }

  onIpTransitLoaded(component): void {
    if (this.ipTransit) {
      component.equipment = this.ipTransit;
      component.ipTransit = this.ipTransit;
      component.equipmentId = this.ipTransit.id;
      component.equipmentType = 'ipTransits';
    }
  }
}
