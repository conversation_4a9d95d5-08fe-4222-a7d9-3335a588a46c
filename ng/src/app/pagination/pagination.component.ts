import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-pagination',
  templateUrl: './pagination.component.html',
  styleUrls: ['./pagination.component.css'],
})
export class PaginationComponent {
  @Input() totalCount = 0;
  @Input() limit = 0;
  @Input() offset = 0;

  @Output() pageChange = new EventEmitter<any>();

  paginate(event: any): void {
    if (event.first !== this.offset || event.rows !== this.limit) {
      this.pageChange.emit({
        offset: event.first,
        limit: event.rows,
      });
    }
  }
}
