/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { CurrencyService } from 'src/app/services/currency.service';
import { CurrentUserService } from 'src/app/services/current-user.service';

@Injectable({
  providedIn: 'root',
})
export class PrivateNetworkService {
  serverPortSpeeds = ['100', '1000', '10000', '25000'];
  cloudConnectPortSpeeds = ['100', '500', '1000', '2000', '5000', '10000'];

  blacklistedPrivatenetworkCololocation40GbSites = [
    'CHI-11',
    'DAL-13',
    'FRA-10',
    'FRA-14',
    'HKG-10',
    'HKG-12',
    'LON-11',
    'LON-12',
    'MIA-11',
    'NYC-01',
    'PHX-01',
    'SEA-11',
    'SFO-12',
    'SIN-10',
    'SIN-11',
    'SIN-12',
    'SYD-10',
    'SYD-11',
    'SYD-12',
    'TYO-11',
  ];
  blacklistedPrivatenetworkCololocation100GbSites = [
    'CHI-11',
    'DAL-13',
    'FRA-10',
    'FRA-14',
    'HKG-10',
    'HKG-12',
    'LON-11',
    'LON-12',
    'MIA-11',
    'NYC-01',
    'PHX-01',
    'SEA-11',
    'SFO-12',
    'SIN-10',
    'SIN-11',
    'SIN-12',
    'SYD-10',
    'SYD-11',
    'SYD-12',
    'TYO-11',
  ];
  blacklistedPrivatenetworkPrivateRack40GbSites = [
    'DAL-10',
    'HKG-10 ',
    'HKG-12',
    'NYC-01',
    'SEA-11',
    'SFO-12',
    'SYD-10',
    'SYD-11',
    'SYD-12',
  ];
  blacklistedPrivatenetworkPrivateRack100GbSites = [
    'DAL-10',
    'FRA-10',
    'HKG-10',
    'HKG-12',
    'NYC-01',
    'SEA-11',
    'SFO-12',
    'SYD-10',
    'SYD-11',
    'SYD-12',
    'WDC-02',
  ];

  colocation10GbpsPrice = {
    AUD: '173.30',
    CAD: '163.80',
    EUR: '108.00',
    GBP: '91.80',
    HKD: '966.00',
    JPY: '14081.00',
    SGD: '175.20',
    USD: '126.00',
  };

  colocation40GbpsPrice = {
    AUD: '425.30',
    CAD: 'xx.yy', // This is not available in USD
    EUR: '270.00',
    GBP: '232.20',
    HKD: '2415.00',
    JPY: 'xx.yy', // This is not available in TYO
    SGD: '440.70',
    USD: 'xx.yy', // This is not available in USD
  };

  colocation100GbpsPrice = {
    AUD: 'xx.yy', //# This is not available in SYD
    CAD: 'xx.yy', //# This is not available in USD
    EUR: '540.00',
    GBP: '459.00',
    HKD: 'xx.yy', //# This is not available in HKD
    JPY: 'xx.yy', //# This is not available in TYO
    SGD: '875.80',
    USD: 'xx.yy', // # This is not available in USD
  };

  privateRack10GbpsPrice = {
    AUD: '341.30',
    CAD: '334.50',
    EUR: '216.00',
    GBP: '194.40',
    HKD: '1995.00',
    JPY: '28162.00',
    SGD: '367.30',
    USD: '257.30',
  };

  privateRack40GbpsPrice = {
    AUD: '682.50',
    CAD: 'xx.yy', // This is not available in USD
    EUR: '432.00',
    GBP: '388.80',
    HKD: 'xx.yy',
    JPY: 'xx.yy', // This is not available in TYO
    SGD: '734.50',
    USD: 'xx.yy', //This is not available in USD
  };

  privateRack100GbpsPrice = {
    AUD: 'xx.yy', // This is not available in AUD
    CAD: 'xx.yy', // This is not available in CAD
    EUR: '864.00',
    GBP: '777.60',
    HKD: 'xx.yy', //This is not available in HKD
    JPY: 'xx.yy', //This is not available in TYO
    SGD: '1469.00',
    USD: 'xx.yy', //This is not available in USD
  };

  networkEquipment100MbpsPrice = {
    AUD: '9.40',
    CAD: '8.80',
    EUR: '5.40',
    GBP: '4.60',
    HKD: '48.30',
    JPY: '704.00',
    SGD: '9.00',
    USD: '6.80',
  };

  networkEquipment1000MbpsPrice = {
    AUD: '17.80',
    CAD: '17.80',
    EUR: '10.80',
    GBP: '9.20',
    HKD: '95.50',
    JPY: '1318.00',
    SGD: '18.10',
    USD: '13.70',
  };

  constructor(
    private currencyService: CurrencyService,
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService
  ) {}

  getServerPortSpeeds(): string[] {
    return this.serverPortSpeeds;
  }

  getCloudConnectPortSpeeds(): string[] {
    return this.cloudConnectPortSpeeds;
  }

  getBlacklistedPrivateNetworkColocation40GbSites(): string[] {
    return this.blacklistedPrivatenetworkCololocation40GbSites;
  }

  getBlacklistedPrivateNetworkColocation100GbSites(): string[] {
    return this.blacklistedPrivatenetworkCololocation100GbSites;
  }

  getBlacklistedPrivateNetworkPrivateRack40GbSites(): string[] {
    return this.blacklistedPrivatenetworkPrivateRack40GbSites;
  }

  getBlacklistedPrivateNetworkPrivateRack100GbSites(): string[] {
    return this.blacklistedPrivatenetworkPrivateRack100GbSites;
  }

  getPrivateNetworkPricesForColocation(salesOrgId: string) {
    const currencyCode = this.currencyService.salesOrgIdToCurrencyCode(salesOrgId);
    return {
      10000: { value: '10Gbps', price: this.colocation10GbpsPrice[currencyCode] },
      40000: { value: '40Gbps', price: this.colocation40GbpsPrice[currencyCode] },
      100000: { value: '100Gbps', price: this.colocation100GbpsPrice[currencyCode] },
    };
  }
  getPrivateNetworkPricesForPrivateRack(salesOrgId: string) {
    const currencyCode = this.currencyService.salesOrgIdToCurrencyCode(salesOrgId);
    return {
      10000: { value: '10Gbps', price: this.privateRack10GbpsPrice[currencyCode] },
      40000: { value: '40Gbps', price: this.privateRack40GbpsPrice[currencyCode] },
      100000: { value: '100Gbps', price: this.privateRack100GbpsPrice[currencyCode] },
    };
  }
  getPrivateNetworkPricesForNetworkEquipment(salesOrgId: string) {
    const currencyCode = this.currencyService.salesOrgIdToCurrencyCode(salesOrgId);
    return {
      100: { value: '100Mbps', price: this.networkEquipment100MbpsPrice[currencyCode] },
      1000: { value: '1Gbps', price: this.networkEquipment1000MbpsPrice[currencyCode] },
    };
  }

  getPrivateNetworkRedundancyPortSpeeds(equipmentType: string, salesOrgId: number) {
    const validPortSpeeds: Record<string, Record<number, { value: string; price?: Record<number, string> }>> = {
      dedicatedServer: {
        10000: { value: '10Gbps', price: { 2000: '€47.25', 2600: '€47.25', 1300: 'GBP 40.60', 2700: '$91.88' } },
        25000: { value: '25Gbps', price: { 2000: '€66.15', 2600: '€66.15', 1300: 'GBP 56.84', 2700: '$128.63' } },
      },
      dedicatedRack: {
        10000: { value: '10Gbps', price: { 2000: '€47.25', 2600: '€47.25', 1300: 'GBP 40.60', 2700: '$91.88' } },
        25000: { value: '25Gbps', price: { 2000: '€66.15', 2600: '€66.15', 1300: 'GBP 56.84', 2700: '$128.63' } },
      },
      loadbalancer: {
        1000: { value: '1Gbps', price: { 2000: '€18.90', 2600: '€18.90', 1300: 'GBP 16.10', 2700: '$23.98' } },
        10000: { value: '10Gbps', price: { 2000: '€47.25', 2600: '€47.25', 1300: 'GBP 40.60', 2700: '$91.88' } },
      },
      firewall: {
        1000: { value: '1Gbps', price: { 2000: '€18.90', 2600: '€18.90', 1300: 'GBP 16.10', 2700: '$23.98' } },
        10000: { value: '10Gbps', price: { 2000: '€47.25', 2600: '€47.25', 1300: 'GBP 40.60', 2700: '$91.88' } },
      },
    };

    const speeds = validPortSpeeds[equipmentType] || {};

    const filteredSpeeds: Record<number, { value: string; price?: string }> = {};
    for (const [speed, details] of Object.entries(speeds)) {
      filteredSpeeds[+speed] = {
        value: details.value,
        price: details.price?.[salesOrgId] || 'N/A',
      };
    }

    return filteredSpeeds;
  }

  async getPrivateNetwork(customerId: string, salesOrgId: string) {
    let params = new HttpParams();
    let privateNetwork: any;

    if (this.currentUserService.isEmployee()) {
      params = new HttpParams().set('customerId', customerId).set('salesOrgId', salesOrgId);
    }

    await this.httpClient
      .get<any>(`/_/internal/nseapi/v2/privateNetworks`, { params })
      .toPromise()
      .then(
        (response: any) => {
          privateNetwork = response.privateNetworks.find(
            (privateNetworkInfo) => privateNetworkInfo.salesOrgId === salesOrgId
          );
        },
        (error) => {
          console.log('error', 'Unable to get private network');
        }
      );

    if (!privateNetwork) {
      const body = {
        customerId,
        salesOrgId,
      };

      await this.httpClient
        .post<any>(`/_/internal/nseapi/v2/privateNetworks`, body)
        .toPromise()
        .then(
          (response) => {
            privateNetwork = response;
          },
          (error) => {
            console.log('error', 'Unable to get private network');
          }
        );
    }

    return privateNetwork;
  }
}
