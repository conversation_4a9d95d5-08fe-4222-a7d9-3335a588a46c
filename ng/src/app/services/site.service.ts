/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class SiteService {
  floatingIpSites = {
    1300: ['LON-01', 'LON-12'],
    1400: ['SYD-10', 'SYD-12'],
    1600: ['TYO-11'],
    1700: ['MTL-01'],
    2000: ['AMS-01'],
    2600: ['FRA-01'],
    2700: ['CHI-11', 'DAL-13', 'LAX-12', 'MIA-11', 'NYC-01', 'PHX-01', 'SEA-11', 'SFO-12', 'WDC-02'],
    2800: ['SIN-01', 'SIN-12', 'SIN-15'],
  };

  floatingIpMetros = {
    1300: ['LON'],
    1400: ['SYD'],
    1500: ['HKG'],
    1700: ['MTL'],
    2000: ['AMS'],
    2800: ['SIN'],
  };

  salesOrgIds(): string[] {
    return ['1300', '1400', '1500', '1600', '1700', '2000', '2600', '2700', '2800'];
  }

  getCountry(salesOrgId): string {
    switch (salesOrgId) {
      case '1300':
        return 'uk';
      case '1400':
        return 'au';
      case '1500':
        return 'hk';
      case '1600':
        return 'jp';
      case '1700':
        return 'ca';
      case '2000':
      case '2200':
        return 'nl';
      case '2600':
        return 'de';
      case '2700':
        return 'us';
      case '2800':
        return 'sg';
      default:
        return '';
    }
  }

  sites(): string[] {
    return [
      'AMS-01',
      'AMS-02',
      'CHI-11',
      'DAL-13',
      'FRA-01',
      'FRA-10',
      'FRA-14',
      'HKG-10',
      'HKG-12',
      'LAX-12',
      'LON-01',
      'LON-11',
      'LON-12',
      'MIA-11',
      'MTL-01',
      'MTL-02',
      'MTL-03',
      'NYC-01',
      'PHX-01',
      'SEA-11',
      'SFO-12',
      'SIN-01',
      'SIN-10',
      'SIN-11',
      'SIN-12',
      'SIN-15',
      'SYD-10',
      'SYD-11',
      'SYD-12',
      'TYO-11',
      'WDC-02',
    ];
  }

  metro(): string[] {
    return ['AMS', 'CHI', 'DAL', 'FRA', 'HKG', 'LAX', 'LON', 'MIA', 'MTL', 'SEA', 'SFO', 'SIN', 'SYD', 'TYO', 'WDC'];
  }

  getFloatingIpMetros(salesOrgId: string): string[] {
    if (salesOrgId) {
      const metros = this.floatingIpMetros[salesOrgId];
      return metros ? metros.sort() : [];
    }
    return Object.entries(this.floatingIpMetros)
      .reduce((acc, [key, val]) => {
        acc.push(...val);
        return acc;
      }, [])
      .sort();
  }

  getFloatingIpSites(salesOrgId: string): string[] {
    if (salesOrgId) {
      const sites = this.floatingIpSites[salesOrgId];
      return sites ? sites.sort() : [];
    }
    return Object.entries(this.floatingIpSites)
      .reduce((acc, [key, val]) => {
        acc.push(...val);
        return acc;
      }, [])
      .sort();
  }
}
