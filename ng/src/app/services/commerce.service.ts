import { Injectable, Inject } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class CommerceService {
  constructor(@Inject('COMMERCE_BASE_URL') private baseUrl: string) {}

  getConfigureUrl(link: string, params: any = {}): string {
    if (link.charAt(0) !== '/') {
      link = '/' + link;
    }

    const url = new URL(`${this.baseUrl}${link}`);

    if (params) {
      if (!('from' in params)) {
        params.from = window.location;
      }
      url.search = new URLSearchParams(params).toString();
    }

    return url.toString();
  }

  getCommerceConfigureProductUrl(contractItemId: string, type: string, params: any = {}): string {
    return this.getConfigureUrl(`/configure/vc/product/entityKey/_${type}_${contractItemId}`, params);
  }

  getCommerceModifyContractUrl(contractItemId: string, action: string, params: any = {}): string {
    return this.getConfigureUrl(`/modify-contract/${contractItemId}/${action}`, params);
  }

  getCommerceCreateContractUrl(type: string, params: any = {}): string {
    return this.getConfigureUrl(`/c/${type}`, params);
  }
}
