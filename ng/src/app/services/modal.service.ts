import { Injectable, Type } from '@angular/core';
import { DialogService } from 'primeng/dynamicdialog';

@Injectable({
  providedIn: 'root',
})
export class ModalService {
  constructor(public dialogService: DialogService) {}

  show(content: Type<any>, data: any) {
    const config = {
      data,
      showHeader: false,
      dismissableMask: true,
      width: '50%',
      height: '0',
      contentStyle: { overflow: 'auto', background: 'none' },
      baseZIndex: 10000,
    };

    return this.dialogService.open(content, config);
  }
}
