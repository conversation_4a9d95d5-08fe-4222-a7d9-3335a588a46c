import { Injectable, Inject } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class CurrentUserService {
  roles: Array<string>;
  permissions: Array<string>;
  customerId: string;
  salesOrgId: string;

  constructor(
    @Inject('ROLES') private rolesList: string,
    @Inject('PERMISSIONS') private permissionsList: string,
    @Inject('CUSTOMERID') private customerIdentifier: string,
    @Inject('SALESORGID') private salesOrgIdentifier: string
  ) {
    this.roles = rolesList.split(',');
    this.permissions = permissionsList.split(',');
    this.customerId = customerIdentifier;
    this.salesOrgId = salesOrgIdentifier;
  }

  hasPermission(permission: string): boolean {
    return this.permissions.indexOf(permission) > -1;
  }

  hasRole(role: string): boolean {
    return this.roles.indexOf(role) > -1;
  }

  isEmployee(): boolean {
    return !this.isCustomer();
  }

  isDev(): boolean {
    return this.hasRole('ROLE_DPMT_DEVELOPMENT');
  }

  isFiberring(): boolean {
    return this.getSalesOrgId() === '2200';
  }

  isCustomer(): boolean {
    return this.roles.indexOf('ROLE_CUSTOMER') > -1;
  }

  getCustomerId(): string | null {
    return this.isCustomer() ? this.customerId : null;
  }

  getSalesOrgId(): string | null {
    return this.isCustomer() ? this.salesOrgId : null;
  }
}
