/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable, Inject } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class LocationService {
  constructor(@Inject('BASE_ELEMENT') private baseElement: HTMLBaseElement) {}

  getAbsoluteUrlWithBase(url) {
    if (!this.baseElement) {
      return url;
    }

    const href = this.baseElement.href.replace(/\/+$/, '');
    return href + url;
  }

  navigateToWithBase(url) {
    const absoluteUrl = this.getAbsoluteUrlWithBase(url);
    window.location.href = absoluteUrl;
  }

  navigateTo(url) {
    window.location.href = url;
  }
}
