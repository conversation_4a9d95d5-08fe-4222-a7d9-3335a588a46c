import { Injectable } from '@angular/core';
import {
  format,
  differenceInDays,
  addDays,
  subHours,
  subDays,
  subWeeks,
  subMonths,
  addWeeks,
  addMonths,
  addYears,
  startOfMonth,
  startOfWeek,
  startOfYear,
  isBefore,
} from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';
import { UTCDate } from '@date-fns/utc';

@Injectable({
  providedIn: 'root',
})
export class DateFormatterService {
  private date: Date;

  init(date: Date | string): this {
    this.date = new Date(date);

    return this;
  }

  durationDiffAsDays(date1: Date | string, date2: Date | string): number {
    return differenceInDays(date1, date2);
  }

  utc(): this {
    this.date = new UTCDate();
    return this;
  }

  utcNowIsBeforeTo(date: string): boolean {
    const now = new UTCDate();
    return isBefore(now, new UTCDate(date));
  }

  utcToLocalTimestamp(timestamp: string) {
    return new Date(new UTCDate(timestamp));
  }

  localTimestampToUtc(localTime: string | Date) {
    const date = new Date(localTime);
    const offsetMinutes = date.getTimezoneOffset();
    const utcDate = new Date(date.getTime() + offsetMinutes * 60000);
    return utcDate.toISOString();
  }

  toDate(): Date {
    return this.date;
  }

  averageFactor(from: string, to: string, downLength: number): number {
    const fromUtc = new UTCDate(from);
    const toUtc = new UTCDate(to);
    const now = new UTCDate();
    return isBefore(now, toUtc) ? differenceInDays(now, fromUtc) : downLength;
  }

  today(): this {
    this.date = new Date();
    return this;
  }

  format(formatString: string = 'yyyy-MM-dd', utc: boolean = false): string {
    if (utc) {
      return formatInTimeZone(this.date, 'UTC', formatString);
    }

    return format(this.date, formatString);
  }

  subtractHours(hours: number): this {
    this.date = subHours(this.date, hours);
    return this;
  }

  subtractDays(days: number): this {
    this.date = subDays(this.date, days);
    return this;
  }

  subtractWeeks(weeks: number): this {
    this.date = subWeeks(this.date, weeks);
    return this;
  }

  subtractMonths(months: number): this {
    this.date = subMonths(this.date, months);
    return this;
  }

  addDays(days: number): this {
    this.date = addDays(this.date, days);
    return this;
  }

  addWeeks(weeks: number): this {
    this.date = addWeeks(this.date, weeks);
    return this;
  }

  addMonths(months: number): this {
    this.date = addMonths(this.date, months);
    return this;
  }

  addYears(years: number): this {
    this.date = addYears(this.date, years);
    return this;
  }

  startOfMonth(): this {
    this.date = startOfMonth(this.date);
    return this;
  }

  startOfWeek(): this {
    this.date = startOfWeek(this.date, { weekStartsOn: 1 });
    return this;
  }

  startOfYear(): this {
    this.date = startOfYear(this.date);
    return this;
  }
}
