import { Injectable } from '@angular/core';
import { Router, NavigationStart } from '@angular/router';
import { MessageService } from 'primeng/api';

@Injectable({
  providedIn: 'root',
})
export class AlertService {
  private keepAfterRouteChange = false;

  constructor(
    private router: Router,
    private messageService: MessageService
  ) {
    // clear alert messages on route change unless 'keepAfterRouteChange' flag is true
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationStart) {
        if (this.keepAfterRouteChange) {
          // only keep for a single route change
          this.keepAfterRouteChange = false;
        } else {
          this.clear();
        }
      }
    });
  }

  alert(message: any, keepAfterRouteChange = false) {
    this.keepAfterRouteChange = keepAfterRouteChange;

    if (message.type === 'exception') {
      if (message.error && message.error.errorDetails) {
        message.error.errorDetails = this.processErrorDetails(message.error.errorDetails);
      }

      this.messageService.add({
        key: 'exception',
        severity: 'error',
        data: { error: message.error },
        sticky: false,
        life: 4000,
      });

      return;
    }

    this.messageService.add({
      severity: message.type === 'warning' ? 'warn' : message.type,
      detail: message.description,
      sticky: false,
      life: 4000,
    });
  }

  alertApiError(error: any, keepAfterRouteChange = false) {
    this.alert(
      {
        type: 'exception',
        error,
      },
      keepAfterRouteChange
    );
  }

  clear() {
    this.messageService.clear();
  }

  private processErrorDetails(details: any) {
    const processedDetails = [];

    if (typeof details === 'string') {
      processedDetails.push(['error', details]);
    } else if (Array.isArray(details)) {
      for (const key in details) {
        if (details.hasOwnProperty(key)) {
          if (typeof details[key] === 'object') {
            Object.entries(details[key]).forEach(([errorKey, errorValue]) => {
              processedDetails.push([errorKey, errorValue]);
            });
          } else if (typeof details[key] === 'string') {
            processedDetails.push(['error', details[key]]);
          }
        }
      }
    }

    return processedDetails;
  }
}
