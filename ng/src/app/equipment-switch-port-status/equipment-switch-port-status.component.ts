import { Component, OnInit, Input } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { NetworkDeviceManageSpeedModalComponent } from 'src/app/network-device/network-device-manage-speed-modal/network-device-manage-speed-modal.component';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { AlertService } from 'src/app/services/alert.service';
import { SwitchPortCloseModalComponent } from 'src/app/switch-port/switch-port-close-modal/switch-port-close-modal.component';
import { ModalService } from 'src/app/services/modal.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-equipment-switch-port-status',
  templateUrl: './equipment-switch-port-status.component.html',
  styleUrls: ['./equipment-switch-port-status.component.css'],
})
export class EquipmentSwitchPortStatusComponent implements OnInit {
  @Input() equipment: any;
  @Input() equipmentType: any;
  @Input() networkInterfaceType: any;

  switchPorts: any = {};
  isEmployee = false;
  isLoading = false;
  isError = false;
  showPortStatus = false;
  dynamicDialogRef: DynamicDialogRef;
  closedPortStates: any = {};

  constructor(
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    private alertService: AlertService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
  }

  showSwitchPortStatus(): void {
    this.getSwitchPortStatus(this.networkInterfaceType);
  }

  updateSpeedHandler(speedUpdated): void {
    if (speedUpdated) {
      this.getSwitchPortStatus(this.networkInterfaceType);
    }
  }

  getSwitchPortStatus(type): void {
    this.alertService.clear();
    this.isLoading = true;
    this.httpClient
      .get(`/_/internal/nseapi/v2/${this.equipmentType}/${this.equipment.id}/networkInterfaces/${type}?all`)
      .subscribe({
        next: (response: any) => {
          if (response) {
            this.switchPorts = response.networkInterfaces;
            this.closedPortStates = response.closedPortStates ?? null;
          }
          this.isLoading = false;
          this.showPortStatus = true;
        },
        error: (error: any) => {
          this.alertService.alert(
            {
              type: 'error',
              description: $localize`:@@bma_equipmentswitchportstatus_error:Something went wrong while processing network interface request.`,
            },
            true
          );
          this.isLoading = false;
          this.isError = true;
        },
      });
  }

  async showStatus(event, privateRackId: number) {
    const target = event.target || event.srcElement || event.currentTarget;

    if (target.classList.contains('shown')) {
      return;
    }

    target.innerText = 'Loading...';
    target.className = 'shown';

    await this.httpClient
      .get<any>(`/_/internal/dedicatedserverapi/v2/${this.equipmentType}/${privateRackId}`)
      .toPromise()
      .then(
        (privaterack: any) => {
          this.equipment = privaterack;
          this.getSwitchPortStatus(this.networkInterfaceType);
        },
        (error) => {
          target.innerText = 'error';
        }
      );
  }

  openCloseSwitchPortModal(action) {
    this.dynamicDialogRef = this.modalService.show(SwitchPortCloseModalComponent, {
      equipmentId: this.equipment.id,
      type: this.networkInterfaceType,
      action,
    });

    this.dynamicDialogRef.onClose.subscribe((event: any) => {
      if (event?.switchPortUpdated === true) {
        this.getSwitchPortStatus(this.networkInterfaceType);
      }
    });
  }

  openManagePortSpeedModal(networkInterface) {
    this.dynamicDialogRef = this.modalService.show(NetworkDeviceManageSpeedModalComponent, {
      networkDeviceName: networkInterface.switchName,
      port: networkInterface.switchInterface,
    });

    this.dynamicDialogRef.onClose.subscribe((data) => {
      if (data?.portSpeedUpdated === true) {
      }
    });
  }
}
