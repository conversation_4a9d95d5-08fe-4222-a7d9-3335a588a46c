:host ::ng-deep .p-accordion .p-accordion-content {
  padding: 0.5rem;
}
.grid-content {
  margin-bottom: 5px;
  padding-bottom: 4px;
  padding-left: 2px;
  font-size: 0.8rem !important;
}
.grid-content-border {
  border-bottom: 1px solid #ddd;
}
.show-data {
  cursor: pointer;
}
.link {
  background-color: transparent !important;
}
.header {
  margin-left: 2px !important;
}
.badge-status {
  width: 100px;
  display: inline-grid;
  align-items: center;
  height: 32px;
}
.network-interface-data a {
  font-size: larger !important;
}
.closed-port-data span,
.closed-port-data a,
.closed-port-data strong {
  font-size: small !important;
}
