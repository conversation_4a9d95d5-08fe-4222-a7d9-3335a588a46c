<div class="p-grid p-col-6 p-pl-0 p-py-0 p-pr-2">
    <div class="p-col-4 grid-content">
        <strong i18n="bma_common_switch">Switch</strong>
    </div>
    <div *ngIf="!showPortStatus" class="p-col-8 p-pl-0 grid-content">
        <ng-container *ngIf="!isError; else switchError">
            <ng-container *ngIf="equipment.networkInterfaces.public && equipment.networkInterfaces.public.ports.length > 0; else switchNotAvailable">
                <span (click)="showSwitchPortStatus()">
                    <strong class="text-primary show-data" i18n="@@bma_common_showstatus">Show Status</strong>
                </span>
            </ng-container>
            <ng-template #switchNotAvailable>
                <span class="text-muted" i18n="@@bma_common_notavailable">Not Available</span>
            </ng-template>
        </ng-container>
        <ng-template #switchError>
            <span class="badge badge-danger" i18n="@@bma_common_error">error</span>
        </ng-template>
    </div>
</div>

<app-loader *ngIf="isLoading"></app-loader>
<ng-container *ngIf="!isLoading && showPortStatus">
    <ng-container *ngIf="equipment.networkInterfaces[networkInterfaceType]|default:'' && equipment.networkInterfaces[networkInterfaceType].ports|default:'';else notAvailable">
        <ng-container *ngIf="!isError;else switchPortError">
            <div class="p-col-12 p-p-0 m-n2">
                <div class="table-responsive">
                    <table class="table table-responsive network-interface-table overflow-hidden">
                        <tbody class="d-block">
                            <tr class="d-block">
                                <td class="p-p-0 d-block" colspan="4">
                                    <div class="row justify-content-between">
                                        <div class="table-responsive p-col-12 p-d-flex p-flex-col p-pb-0 p-pl-3 p-mt-3 network-interface-data">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th class="text-left p-pl-0" width="30%" i18n="@@bma_common_networkdevice">Network Device</th>
                                                        <th class="text-left" i18n="@@bma_common_port">Port</th>
                                                        <th class="text-left" i18n="@@bma_common_linkspeed">Link Speed</th>
                                                        <th class="text-left" i18n="@@bma_common_type">Type</th>
                                                        <th class="text-left" i18n="@@bma_common_adminstatus">Admin Status</th>
                                                        <th class="text-left" i18n="@@bma_common_operationalstatus">Operational Status</th>
                                                        <th class="text-left p-pl-0" *ngIf="isEmployee" i18n="@@bma_common_actions">Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr *ngFor="let networkInterface of switchPorts">
                                                        <td class="text-left  p-pl-0">
                                                            <a pButton class="p-button-link p-pl-0" *ngIf="isEmployee" [routerLink]="['/networkDevices', networkInterface.switchName]"> {{networkInterface.switchName }}</a>
                                                            <span *ngIf="!isEmployee">
                                                                {{ networkInterface.switchName }}
                                                            </span>
                                                        </td>
                                                        <td class="text-left">
                                                            {{ networkInterface.switchInterface }}
                                                        </td>
                                                        <td class="text-left">
                                                            <span>{{ networkInterface.linkSpeed }}</span>
                                                        </td>
                                                        <td class="text-left">
                                                            <span>{{  networkInterface.type }}</span>
                                                        </td>
                                                        <td class="text-left">
                                                            <ng-container *ngIf="networkInterface['status']">
                                                                <ng-container *ngIf="networkInterface['status'] == 'OPEN';else adminStatusDown">
                                                                    <span i18n="@@bma_common_open" class="h5 badge badge-success">OPEN</span>
                                                                </ng-container>
                                                                <ng-template #adminStatusDown>
                                                                    <span i18n="@@bma_common_closed" class="h5 badge badge-warning"> CLOSED </span>
                                                                </ng-template>
                                                            </ng-container>
                                                        </td>
                                                        <td class="text-left">
                                                            <ng-container *ngIf="networkInterface['operStatus']">
                                                                <ng-container *ngIf="networkInterface['operStatus'] == 'OPEN';else statusDown">
                                                                    <span class="h5 badge badge-success">
                                                                        {{ networkInterface.linkSpeed }}
                                                                    </span>
                                                                </ng-container>
                                                                <ng-template #statusDown>
                                                                    <span class="h5 badge badge-danger" i18n="@@bma_common_down">DOWN</span>
                                                                </ng-template>
                                                            </ng-container>
                                                        </td>
                                                        <td class="text-left p-pl-0" *ngIf="isEmployee">
                                                            <a i18n="@@bma_common_capuncap" pButton href="javascript:void(0);" class="text-nowrap p-button-link p-pl-0" (click)="openManagePortSpeedModal(networkInterface['switchName'],networkInterface['switchInterface'])" [class.disabled]="equipment.isSharedEol"> Cap/Uncap </a>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <ng-container *ngIf="equipmentType == 'servers' && (networkInterfaceType != 'remoteManagement' || (isEmployee && currentUserService.hasPermission('networkdevice_ports')))">
                        <ng-container *ngIf="switchPorts && switchPorts[0] && switchPorts[0].status == 'OPEN'">
                            <a i18n="@@bma_common_closeport" *ngIf="equipment.networkInterfaces[networkInterfaceType].ports.length == 1" class="btn btn-sm btn-danger float-right" (click)="openCloseSwitchPortModal('close')" href="javascript:void(0);"> Close Port </a>
                            <a i18n="@@bma_common_closeports" *ngIf="equipment.networkInterfaces[networkInterfaceType].ports.length > 1" class="btn btn-sm btn-danger float-right" (click)="openCloseSwitchPortModal('close')" href="javascript:void(0);"> Close Ports </a>
                        </ng-container>
                        <ng-container *ngIf="switchPorts && switchPorts[0] && switchPorts[0].status == 'CLOSED'">
                            <a i18n="@@bma_common_openport" *ngIf="equipment.networkInterfaces[networkInterfaceType].ports.length == 1" class="btn btn-sm btn-primary float-right" (click)="openCloseSwitchPortModal('open')" href="javascript:void(0);"> Open Port </a>
                            <a i18n="@@bma_common_openports" *ngIf="equipment.networkInterfaces[networkInterfaceType].ports.length > 1" class="btn btn-sm btn-primary float-right" (click)="openCloseSwitchPortModal('open')" href="javascript:void(0);"> Open Ports </a>
                        </ng-container>
                    </ng-container>

                    <ng-container *ngIf="isEmployee && closedPortStates && switchPorts && switchPorts[0] && switchPorts[0].status == 'CLOSED'">
                        <table class="table table-responsive overflow-hidden closed-port-data d-table">
                            <tr>
                                <td colspan="2">&nbsp;</td>
                            </tr>
                            <tr>
                                <td colspan="2"><h5 i18n="@@bma_common_internalcomment">Internal Comment</h5></td>
                            </tr>
                            <tr *ngFor="let closedPortState of closedPortStates">
                                <td colspan="2">
                                    <strong>{{closedPortState.type}}</strong
                                    ><br />
                                    <span class="text-monospace" *ngIf="!closedPortState.comment" i18n="@@bma_common_nomessage">No Message</span>
                                    <span class="text-monospace" *ngIf="closedPortState.comment">{{closedPortState.comment}}</span>
                                    <a i18n-title="@@bma_common_viewalllog" *ngIf="closedPortState.correlationId" class="float-right" [routerLink]="['/audit-logs']" [queryParams]="{filter: closedPortState.correlationId}" title="View all related log messages...">
                                        <i class="fa fa-lg fa-tag"></i>
                                    </a>
                                </td>
                            </tr>
                        </table>
                    </ng-container>
                </div>
            </div>
        </ng-container>

        <ng-template #notAvailable>
            <table class="table table-striped mb-0">
                <tbody>
                    <tr>
                        <td>
                            <span class="text-muted" i18n="@@bma_common_networkinterfacenotavailable">Network Interface Not Available</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </ng-template>
    </ng-container>
</ng-container>
<ng-template #switchPortError>
    <div class="p-col-12 p-d-flex p-pl-6 p-flex-col p-pb-0">
        <div class="p-grid grid-row justify-content-center">
            <span i18n="@@bma_common_switchporterror" class="text-danger">There was an issue in loading interface details.</span>
        </div>
    </div>
</ng-template>
