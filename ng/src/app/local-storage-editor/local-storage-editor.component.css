#editor {
  position: relative;
}

#editor a {
  position: absolute;
  top: 0;
  right: 0;
}

ul {
  list-style: none;
  padding-left: 0;
  margin-bottom: 0;
}

#removeAll {
  float: right;
}

.inputfile {
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  position: absolute;
  z-index: -1;
}

.inputfile + label {
  font-size: 1rem;
  font-weight: 500;
  color: white;
  background: #5685c4;
  border: 1px solid #5685c4;
  display: inline-block;
  cursor: pointer;
  padding: 0.5rem 1rem;
  transition:
    background-color 0.2s,
    color 0.2s,
    border-color 0.2s,
    box-shadow 0.2s;
  border-radius: 3px;
}

.inputfile:focus + label {
  outline: 1px dotted #000;
  outline: -webkit-focus-ring-color auto 5px;
}

.inputfile:focus + label,
.inputfile + label:hover {
  background: #3f71b5;
  color: #fff;
  border-color: #5685c4;
}
