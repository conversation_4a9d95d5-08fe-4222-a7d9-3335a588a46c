import { Component, Input, OnInit, Output, OnChanges, EventEmitter, SimpleChanges } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { AlertService } from '../services/alert.service';
import { FileHandle } from './dragDrop.directive';

@Component({
  selector: 'app-local-storage-editor',
  templateUrl: './local-storage-editor.component.html',
  styleUrls: ['./local-storage-editor.component.css'],
})
export class LocalStorageEditorComponent implements OnInit, OnChanges {
  @Input() key: string;
  @Input() label = 'Content';
  @Input() rows = 10;
  @Input() content: string;
  @Input() clearButton = false;
  @Input() dragAndDrop = false;
  @Input() uploadButton = false;
  @Input() showSavedItems = true;
  @Input() uploadAllowedTypes: [];
  @Output() contentChange = new EventEmitter<string>();
  @Output() updatedSavedItemsEmitter: EventEmitter<object> = new EventEmitter<object>();

  savedItems: any = {};
  saveForm: UntypedFormGroup;
  isSaveFormOpen = false;
  fileInputId = '';

  constructor(
    private readonly formBuilder: UntypedFormBuilder,
    private alertService: AlertService
  ) {}

  get savedItemsKeys() {
    return Object.keys(this.savedItems);
  }

  ngOnInit() {
    this.retrieveSavedItems();

    this.saveForm = this.formBuilder.group({
      name: [null, [Validators.required, Validators.pattern('^[a-zA-Z0-9_-]+$')]],
    });

    this.fileInputId = `input${this.getRandomInt()}${this.getRandomInt()}`;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.hasOwnProperty('key')) {
      this.retrieveSavedItems();
    }
  }

  onContentChange(event) {
    this.contentChange.emit(event);
  }

  isStorageAvailable() {
    return typeof Storage !== 'undefined';
  }

  toggleSaveItemForm() {
    this.isSaveFormOpen = !this.isSaveFormOpen;
  }

  syncSavedItems() {
    localStorage.setItem(this.key, JSON.stringify(this.savedItems));
    this.updatedSavedItemsEmitter.emit(this.savedItems);
  }

  saveItem() {
    const name = this.saveForm.get('name').value;
    this.savedItems[name] = this.content;

    this.syncSavedItems();

    this.saveForm.reset();
    this.isSaveFormOpen = false;
  }

  removeAllSavedItems() {
    this.savedItems = {};
    this.syncSavedItems();
  }

  removeSavedItem(key) {
    delete this.savedItems[key];
    this.syncSavedItems();
  }

  useSavedItem(key) {
    this.content = this.savedItems[key];
    this.contentChange.emit(this.content);
  }

  retrieveSavedItems() {
    this.savedItems = {};

    if (!this.isStorageAvailable()) {
      return;
    }

    const localStorageItem = localStorage.getItem(this.key);

    this.savedItems = this.sanitizeItem(localStorageItem);
  }

  sanitizeItem(item) {
    let parsedItem;

    try {
      parsedItem = JSON.parse(item);
    } catch (e) {
      return {};
    }

    if (parsedItem === null || typeof parsedItem !== 'object') {
      return {};
    }

    for (const key in parsedItem) {
      if (typeof parsedItem[key] !== 'string') {
        return {};
      }
    }

    return parsedItem;
  }

  clear() {
    this.content = '';
  }

  onFileChange(fileList: FileList): void {
    // eslint-disable-next-line @typescript-eslint/prefer-for-of
    for (let index = 0; index < fileList.length; index++) {
      this.readFile(fileList[index]);
    }
  }

  filesDropped(files: FileHandle[]): void {
    files.forEach((file) => {
      this.readFile(file.file);
    });
  }

  readFile(file) {
    let typeIsAllowed = false;
    this.uploadAllowedTypes.forEach((type) => {
      typeIsAllowed = typeIsAllowed ? true : file.name.includes(type) || file.type === type;
    });
    if (!typeIsAllowed) {
      this.alertService.alert(
        {
          type: 'error',
          description: `${file.name}: ` + $localize`:@@bma_common_filetypenotallowed:File type not allowed`,
        },
        true
      );
      return;
    }

    const fileReader: FileReader = new FileReader();
    fileReader.onloadend = (x) => {
      this.content += '\n' + fileReader.result.toString();
      this.content = this.content.replace(/(^[ \t]*\n)/gm, '');
      this.onContentChange(this.content);
    };
    fileReader.readAsText(file);

    this.alertService.alert(
      {
        type: 'success',
        description: `${file.name}: ` + $localize`:@@bma_common_filereadsuccessfully:File read successfully`,
      },
      true
    );
  }

  getRandomInt() {
    return Math.floor(Math.random() * 1000);
  }
}
