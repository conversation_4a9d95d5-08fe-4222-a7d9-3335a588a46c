<div class="form-group">
    <label for="item">{{ label }}</label>
    <div id="editor">
        <a *ngIf="isStorageAvailable() && content" (click)="toggleSaveItemForm()" class="btn">
            <span aria-hidden="true" class="fa fa-save fa-lg"></span>
        </a>
        <textarea *ngIf="dragAndDrop" appDrag (files)="filesDropped($event)" id="item" (ngModelChange)="onContentChange($event)" [ngModel]="content" class="form-control" rows="{{ rows }}"></textarea>
        <textarea *ngIf="!dragAndDrop" id="item" (ngModelChange)="onContentChange($event)" [ngModel]="content" class="form-control" rows="{{ rows }}"></textarea>
    </div>
</div>

<form *ngIf="isSaveFormOpen && content" [formGroup]="saveForm" (ngSubmit)="saveItem()" class="row ml-0">
    <div class="form-group col-xs-4">
        <div class="input-group">
            <input type="text" formControlName="name" class="form-control" placeholder="Item name" />
            <span class="input-group-append">
                <button i18n-label="@@bma_common_save" pButton [disabled]="saveForm.invalid" class="btn btn-primary" icon="pi pi-check" iconPos="left" label="Save"></button>
            </span>
        </div>
        <small i18n-label="@@bma_localstorageeditor_save"><strong>Note!</strong> This data is saved in browser local storage and will not be available on another device.</small>
    </div>
</form>

<div *ngIf="savedItemsKeys.length > 0 && showSavedItems" class="alert alert-info" role="alert">
    <button i18n-label="@@bma_common_removeall" id="removeAll" pButton type="button" (click)="removeAllSavedItems()" icon="pi pi-times" class="p-button-sm p-button-text p-button-danger" label="Remove all"></button>

    <span i18n="@@bma_common_reuse">You can reuse one of the items saved before:</span>
    <ul>
        <li *ngFor="let key of savedItemsKeys">
            <button pButton type="button" (click)="removeSavedItem(key)" icon="pi pi-times" class="p-button-sm p-button-text p-button-danger"></button>
            <button pButton type="button" (click)="useSavedItem(key)" class="p-button-sm p-button-text" label="{{ key }} "></button>
        </li>
    </ul>
</div>

<div class="text-right">
    <button *ngIf="clearButton" pButton type="button" (click)="clear()" class="p-button-text mr-1" i18n-label="@@bma_common_clear" label="Clear"></button>
    <input *ngIf="uploadButton" type="file" [id]="fileInputId" multiple (change)="onFileChange($event.target.files)" class="inputfile" [accept]="uploadAllowedTypes.join(',')" />
    <label *ngIf="uploadButton" [for]="fileInputId" i18n="@@bma_common_chooseafile" class="m-0">Choose a file</label>
</div>
