import { ActivatedRoute, Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { Title } from '@angular/platform-browser';
import { AlertService } from 'src/app/services/alert.service';

@Component({
  selector: 'app-firmware-versions-list',
  templateUrl: './firmware-versions-list.component.html',
  styleUrls: ['./firmware-versions-list.component.css'],
})
export class FirmwareVersionsListComponent implements OnInit {
  hardwareScans: any;
  filters: UntypedFormGroup;
  isLoading: boolean;

  constructor(
    private activatedRoute: ActivatedRoute,
    private alertService: AlertService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private router: Router,
    private titleService: Title
  ) {}

  ngOnInit(): void {
    this.titleService.setTitle($localize`:@@bma_firmwareversionslist_title:Firmware versions overview` + ' | Leaseweb');
    this.filters = this.formBuilder.group({
      limit: [null],
      offset: [null],
      componentType: [null],
    });
    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    this.filters.reset(queryParams);
    const params = this.sanitise(this.filters.getRawValue());
    params.firmwareVersionCheck = 'blacklisted';

    this.hardwareScans = null;
    this.isLoading = true;
    this.alertService.clear();

    this.httpClient.get<any>('/_/internal/bmsdb/v2/hardwareScans', { params }).subscribe(
      (data: any) => {
        this.hardwareScans = data;
        this.isLoading = false;
      },
      (error: any) => {
        this.isLoading = false;
        this.alertService.alertApiError(error.error, true);
      }
    );
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  clearFilters(): void {
    this.filters.reset();
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
    });
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }

  formatBlacklistedFirmwaresList(firmwares: Array<any>): string {
    const firmwareTexts = [];

    firmwares
      .filter((firmware) => firmware.check === 'blacklisted')
      .forEach((firmware) => firmwareTexts.push(this.formatFirmwareType(firmware.type) + ' ' + firmware.version));

    return firmwareTexts.sort().join('\n');
  }

  formatFirmwareType(type: string): string {
    switch (type) {
      case 'ipmi': {
        return 'IPMI';
      }
      case 'bios': {
        return 'BIOS';
      }
      case 'disk': {
        return 'Disk';
      }
      case 'nic': {
        return 'NIC';
      }
      case 'raidController': {
        return 'Raid controller';
      }
    }
    return type;
  }
}
