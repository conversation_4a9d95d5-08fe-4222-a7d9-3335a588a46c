<div class="container">
    <div class="row">
        <h1 i18n="@@bma_firmwareversionslist_header" class="col-md-12">Servers with blacklisted firmware versions</h1>
    </div>
    <div class="row">
        <h4 i18n="@@bma_firmwareversionslist_subheader" class="col-md-12">This list contains hardware scans of servers which have firmware versions known to cause issues</h4>
    </div>

    <form [formGroup]="filters" (ngSubmit)="submitFilters()" role="form">
        <div class="row mb-3 align-items-center">
            <div class="col">
                <select class="form-control" formControlName="componentType" autofocus>
                    <option i18n="@@bma_common_type" value="null">Type</option>
                    <option i18n="@@bma_common_bios" value="bios">BIOS</option>
                    <option i18n="@@bma_common_ipmi" value="ipmi">IPMI</option>
                    <option i18n="@@bma_common_raidcontroller" value="raidController">Raid Controller</option>
                    <option i18n="@@bma_common_disk" value="disk">Disk</option>
                    <option i18n="@@bma_common_nic" value="nic">NIC</option>
                </select>
            </div>
            <div class="col">
                <button i18n-label="@@bma_common_reset" pButton type="button" icon="pi pi-refresh" label="Reset" class="p-button-secondary mr-2" (click)="clearFilters()"></button>
                <button i18n-label="@@bma_common_search" pButton [disabled]="isLoading" label="Search" icon="pi pi-search"></button>
            </div>
        </div>
    </form>
</div>

<app-loader *ngIf="isLoading"></app-loader>

<div *ngIf="!isLoading && hardwareScans">
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th i18n="@@bma_common_serverid">Server ID</th>
                    <th i18n="@@bma_common_scanfrom" class="text-center">Hardware scan from</th>
                    <th i18n="@@bma_common_blacklisted" class="text-center">Blacklisted</th>
                    <th i18n="@@bma_firmwareversionslist_goto" class="text-center">Go to</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngIf="hardwareScans.hardwareScans.length === 0">
                    <td i18n="@@bma_firmwareversionslist_noscans" colspan="999" class="text-center p-4">No hardware scans found</td>
                </tr>
                <tr *ngFor="let hardwareScan of hardwareScans.hardwareScans">
                    <td>
                        <span class="text-monospace">
                            <a [routerLink]="['/servers', hardwareScan.serverId]">{{ hardwareScan.serverId }}</a>
                        </span>
                    </td>
                    <td class="text-center">
                        <span class="text-monospace">
                            {{ hardwareScan.scannedAt | timeAgo }}
                            <br />
                            <small class="text-muted">{{ hardwareScan.scannedAt | date:'longTime' }}</small>
                        </span>
                    </td>
                    <td class="text-center">
                        <span class="text-monospace list">
                            {{ formatBlacklistedFirmwaresList(hardwareScan.firmwareVersions) }}
                        </span>
                    </td>
                    <td class="text-center">
                        <span class="text-monospace">
                            <a i18n="@@bma_firmwareversionslist_hardwarescanresults" [routerLink]="['/servers', hardwareScan.serverId, 'hardware']">Hardware Scan results</a>
                        </span>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<div class="row mb-3" *ngIf="!isLoading && hardwareScans">
    <div class="col-12">
        <app-pagination [totalCount]="hardwareScans._metadata.totalCount" [limit]="hardwareScans._metadata.limit" [offset]="hardwareScans._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>
