import { Component, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { CurrentUserService } from 'src/app/services/current-user.service';

@Component({
  selector: 'app-page-not-found',
  templateUrl: './page-not-found.component.html',
  styleUrls: ['./page-not-found.component.css'],
})
export class PageNotFoundComponent implements OnInit {
  constructor(
    private titleService: Title,
    private currentUserService: CurrentUserService
  ) {}

  ngOnInit(): void {
    this.titleService.setTitle('Page Not Found');
  }
}
