<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{width: '100%', 'max-height': '400px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_referenceeditmodal_title" class="modal-title">Edit Reference</h3>
    </ng-template>

    <form [formGroup]="form" (ngSubmit)="editReference()">
        <div class="modal-body">
            <div class="form-group">
                <label i18n="@@bma_referenceeditmodal_label" for="reference" class="col-form-label"> Reference </label>

                <input type="text" class="form-control" id="reference" formControlName="reference" maxlength="100" value="{{ equipment.contract.reference }}" autofocus />
                <small i18n="@@bma_referenceeditmodal_inputhelper" class="help-block"> Maximum 100 characters </small><br />

                <small *ngIf="form.get('reference').errors" class="text-danger">
                    <span i18n="@@bma_referenceeditmodal_inputrequired" *ngIf="form.get('reference').errors.required">{{ equipmentType }} reference is required</span>
                    <span i18n="@@bma_referenceeditmodal_inputmaxlength" *ngIf="form.get('reference').errors.maxLength">reference should not be longer than 100 characters</span>
                </small>
            </div>
        </div>
    </form>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_cancel" pButton type="button" icon="pi pi-times" iconPos="left" label="Cancel" class="p-button-secondary" (click)="closeModal()"></button>
            <button i18n-label="@@bma_common_ok" pButton (click)="editReference()" icon="pi pi-check" iconPos="left" *ngIf="!form.get('reference').errors" [loading]="isSubmitting" label="OK"></button>
        </div>
    </ng-template>
</p-dialog>
