import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { AlertService } from '../services/alert.service';

@Component({
  selector: 'app-reference-edit-modal',
  templateUrl: './reference-edit-modal.component.html',
  styleUrls: ['./reference-edit-modal.component.css'],
})
export class ReferenceEditModalComponent implements OnInit {
  equipment: any;
  equipmentType: any;
  form: UntypedFormGroup;
  isSubmitting = false;
  updateUrl = '';
  equipmentReference = '';
  showDialog = true;

  constructor(
    private alertService: AlertService,
    private dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient
  ) {}

  ngOnInit(): void {
    this.equipment = this.dynamicDialogConfig.data.equipment;
    this.equipmentType = this.dynamicDialogConfig.data.equipmentType;
    this.updateUrl = this.dynamicDialogConfig.data.updateUrl;
    this.form = this.formBuilder.group({
      reference: [
        this.updateUrl ? this.equipmentReference : this.equipment.contract.reference,
        [Validators.required, Validators.maxLength(100)],
      ],
    });
  }

  editReference(): void {
    this.alertService.clear();

    this.isSubmitting = true;

    if (this.form.invalid) {
      return;
    }

    if (!this.updateUrl) {
      this.updateUrl = `/_/internal/dedicatedserverapi/v2/${this.equipmentType}/${this.equipment.id}`;
    }

    this.httpClient
      .put<any>(this.updateUrl, {
        reference: this.form.get('reference').value,
      })
      .subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.alertService.alert(
            {
              type: 'success',
              description: $localize`:@@bma_referenceeditmodal_referenceupdate:Reference successfully updated` + '.',
            },
            true
          );

          this.closeModal(this.form.get('reference').value);
        },
        error: (error: any) => {
          this.isSubmitting = false;
          this.alertService.alertApiError(error.error, true);
          this.closeModal();
        },
      });
  }

  closeModal(reference: any = null) {
    this.dynamicDialogRef.close(reference);
  }
}
