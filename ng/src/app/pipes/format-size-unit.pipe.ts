import { Pipe, PipeTransform, Injectable } from '@angular/core';

@Pipe({
  name: 'formatSizeUnit',
})
@Injectable({
  providedIn: 'root',
})
export class FormatSizeUnitPipe implements PipeTransform {
  transform(value: any, isBits: boolean = false, unitType: string = 'bytes'): any {
    let bytes = value;
    if (isBits) {
      bytes = value / 8;
    }

    if (bytes >= 1000000000000) {
      bytes = (bytes / 1000000000000).toFixed(2) + ' T';
    } else if (bytes >= 1000000000) {
      bytes = (bytes / 1000000000).toFixed(2) + ' G';
    } else if (bytes >= 1000000) {
      bytes = (bytes / 1000000).toFixed(2) + ' M';
    } else if (bytes >= 1000) {
      bytes = (bytes / 1000).toFixed(2) + ' K';
    } else if (bytes > 1) {
      bytes = bytes.toFixed(2) + ' ';
    } else if (1 === bytes) {
      bytes = bytes + ' ';
    } else {
      bytes = '0 ';
    }

    if (unitType === 'bytes') {
      bytes = bytes + 'B';
    } else if (unitType === 'bytes/s') {
      bytes = bytes + 'bps';
    }

    return bytes;
  }
}
