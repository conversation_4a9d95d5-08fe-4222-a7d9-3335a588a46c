import { Pipe, PipeTransform, Injectable } from '@angular/core';

@Pipe({
  name: 'formatDiskPerformanceType',
})
@Injectable({
  providedIn: 'root',
})
export class FormatDiskPerformanceTypePipe implements PipeTransform {
  transform(value: string): string {
    switch (value) {
      case 'WRITE_INTENSIVE':
        value = $localize`:@@bma_pipes_writeintensive:Write Intensive`;
        break;
      case 'MIX_USE':
        value = $localize`:@@bma_pipes_mixuse:Mix Use`;
        break;
      default:
        break;
    }

    return value;
  }
}
