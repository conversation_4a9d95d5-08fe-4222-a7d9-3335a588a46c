import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'formatDate',
})
export class FormatDatePipe implements PipeTransform {
  transform(value: any, format: string): any {
    const dateValue = new Date(value);
    if (dateValue instanceof Date) {
      if (dateValue instanceof Date) {
        switch (format) {
          case 'short_time':
            return new Intl.DateTimeFormat('en', {
              hour: '2-digit',
              minute: '2-digit',
              timeZone: 'UTC',
              hour12: false,
            }).format(dateValue);
          case 'short_date':
            return new Intl.DateTimeFormat('en', {
              month: 'short',
              day: 'numeric',
              year: 'numeric',
              timeZone: 'UTC',
            }).format(dateValue);
          case 'short_datetime':
            return new Intl.DateTimeFormat('en', {
              month: 'short',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit',
              timeZone: 'UTC',
              hour12: false,
            }).format(dateValue);
        }
      }
    }
    return value;
  }
}
