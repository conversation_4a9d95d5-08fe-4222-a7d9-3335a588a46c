import { Pipe, PipeTransform, Injectable } from '@angular/core';

@Pipe({
  name: 'cidrToIp',
  pure: true,
})
@Injectable({
  providedIn: 'root',
})
export class CidrToIpPipe implements PipeTransform {
  transform(value: any, args?: any): any {
    if (value) {
      const parts = value.split('/');

      if (parts.length > 0) {
        return parts[0];
      }

      return '';
    }
    return value;
  }
}
