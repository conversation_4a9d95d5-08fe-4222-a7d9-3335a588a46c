import { Pipe, PipeTransform, Injectable } from '@angular/core';

@Pipe({
  name: 'formatDatabaseType',
})
@Injectable({
  providedIn: 'root',
})
export class FormatDatabaseTypePipe implements PipeTransform {
  transform(value: string): string {
    switch (value) {
      case 'MARIADB':
        value = 'MariaDB';
        break;
      case 'MONGODB':
        value = 'MongoDB';
        break;
      case 'POSTGRESQL':
        value = 'PostgreSQL';
        break;
      case 'REDIS':
        value = 'Redis';
        break;
      default:
        break;
    }

    return value;
  }
}
