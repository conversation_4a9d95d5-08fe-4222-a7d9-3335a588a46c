import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'timeAgo',
  pure: true,
})
export class TimeAgoPipe implements PipeTransform {
  transform(value: any, args?: any): any {
    if (value) {
      let seconds = 0;
      if (!isNaN(value)) {
        seconds = value;
      } else {
        seconds =
          value instanceof Date || (new Date(value) instanceof Date && !isNaN(new Date(value).valueOf()))
            ? Math.floor((+new Date() - +new Date(value)) / 1000)
            : value;
      }

      if (seconds < 29) {
        // less than 30 seconds ago will show as 'Just now'
        return $localize`:@@bma_pipes_justnow:Just now`;
      }
      const intervals = {
        year: 31536000,
        month: 2592000,
        week: 604800,
        day: 86400,
        hour: 3600,
        minute: 60,
        second: 1,
      };
      let counter;
      for (const i in intervals) {
        if (intervals.hasOwnProperty(i)) {
          counter = Math.floor(seconds / intervals[i]);
          if (counter > 0) {
            if (counter > 1) {
              return counter + ' ' + this.getTranslatedTime(i, true);
            } else {
              return counter + ' ' + this.getTranslatedTime(i);
            }
          }
        }
      }
    }
    return value;
  }

  getTranslatedTime(interval: string, plural: boolean = false): string {
    switch (interval) {
      case 'year':
        interval = plural ? $localize`:@@bma_pipes_years:years` : $localize`:@@bma_pipes_year:year`;
        break;
      case 'month':
        interval = plural ? $localize`:@@bma_pipes_months:months` : $localize`:@@bma_pipes_month:month`;
        break;
      case 'week':
        interval = plural ? $localize`:@@bma_pipes_weeks:weeks` : $localize`:@@bma_pipes_week:week`;
        break;
      case 'day':
        interval = plural ? $localize`:@@bma_pipes_days:days` : $localize`:@@bma_pipes_day:day`;
        break;
      case 'hour':
        interval = plural ? $localize`:@@bma_pipes_hours:hours` : $localize`:@@bma_pipes_hour:hour`;
        break;
      case 'minute':
        interval = plural ? $localize`:@@bma_pipes_minutes:minutes` : $localize`:@@bma_pipes_minute:minute`;
        break;
      case 'second':
        interval = plural ? $localize`:@@bma_pipes_seconds:seconds` : $localize`:@@bma_pipes_second:second`;
    }
    return interval;
  }
}
