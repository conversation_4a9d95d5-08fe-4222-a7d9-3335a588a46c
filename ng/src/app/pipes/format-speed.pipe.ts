import { Pipe, PipeTransform, Injectable } from '@angular/core';

@Pipe({
  name: 'formatSpeed',
})
@Injectable({
  providedIn: 'root',
})
export class FormatSpeedPipe implements PipeTransform {
  transform(value: number, ...args: unknown[]): string {
    if (+value === 10) {
      return '10 Mbps';
    } else if (+value === 100) {
      return '100 Mbps';
    } else if (+value === 1000) {
      return '1 Gbps';
    } else if (+value === 10000) {
      return '10 Gbps';
    } else if (+value === 25000) {
      return '25 Gbps';
    } else if (+value === 40000) {
      return '40 Gbps';
    } else if (+value === 100000) {
      return '100 Gbps';
    }

    return value ? value.toString() : '';
  }
}
