import { Pipe, PipeTransform } from '@angular/core';
import { CurrencyService } from 'src/app/services/currency.service';

@Pipe({
  name: 'formatCurrency',
})
export class FormatCurrencyPipe implements PipeTransform {
  constructor(private currencyService: CurrencyService) {}

  transform(value: any, salesOrgId: any, code?: any): any {
    const currency = code
      ? this.currencyService.salesOrgIdToCurrencyCode(salesOrgId)
      : this.currencyService.salesOrgIdToCurrencySymbol(salesOrgId);
    return `${currency}${value}`;
  }
}
