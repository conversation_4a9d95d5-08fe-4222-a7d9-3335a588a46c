import { PrivateNetwork } from './private-network.model';
import { Rack } from './racks.model';
import { FeatureAvailability } from './shared.model';

export interface AddToStorageApiError {
  errorMessage: string;
}
export interface AddStorageToPrivateNetworkRequest {
  bonding: number;
}

export interface DedicatedStorages {
  dedicatedStorages: DedicatedStorage[];
  _metadata: DedicatedStoragesMetadata;
}
interface DedicatedStoragesMetadata {
  limit: number;
  offset: number;
  totalCount: number;
}
export interface DedicatedStorage {
  contract: Contract;
  featureAvailability: FeatureAvailability;
  id: string;
  isRedundantPrivateNetworkCapable: boolean;
  location: Location;
  lswDataViewId: string;
  name: string;
  networkInterfaces: NetworkInterfaces;
  privateNetworks: PrivateNetwork[];
  rack: Rack;
  serialNumber: string;
}

interface Contract {
  customerId: string;
  deliveryStatus: string;
  id: string;
  privateNetworkPortSpeed: number;
  reference: string;
  salesOrgId: string;
}

interface Location {
  site: string;
  suite: string;
  rack: string;
  unit: string;
}
interface NetworkInterfaces {
  public: NetworkInterface;
  internal: NetworkInterface;
  remoteManagement: NetworkInterface;
}
interface NetworkInterface {
  mac: string;
  ip?: string;
  nullRouted?: boolean;
  gateway?: string;
  ports: Port[];
  locationId?: any;
}
interface Port {
  name: string;
  port: string;
}
