import { PrivateNetwork } from './private-network.model';
import { FeatureAvailability } from './shared.model';

export interface AddRackToPrivateNetworkRequest {
  linkSpeed: number;
  allServers: boolean;
}

interface Contract {
  customerId: string;
  deliveryStatus: string;
  id: string;
  privateNetworkPortSpeed: number;
  reference: string;
  salesOrgId: string;
}

interface Location {
  rack: string;
  site: string;
  suite: string;
}

interface RackMetadata {
  limit: number;
  offset: number;
  totalCount: number;
}

interface NetworkInterfaces {
  public: {
    gateway: string;
    ip: string;
    locationId: string;
    mac: string;
    nullRouted: boolean;
    ports: Port[];
  };
}

export interface PortsInterface {
  closedPortStates: [];
  networkInterfaces: PortInterface[];
}

export interface PortInterface {
  linkeSpeed: string;
  operStatus: string;
  status: string;
  switchInterface: string;
  switchName: string;
  type: string;
}

export interface PrivateRacks {
  privateRacks: PrivateRack[];
  _metadata: RackMetadata;
}

export interface PrivateRack {
  assetId: string;
  contract: Contract;
  featureAvailability: FeatureAvailability;
  id: string;
  isRedundantPrivateNetworkCapable: boolean;
  isPrivateNetworkEnabled: boolean;
  location: Location;
  networkInterfaces: NetworkInterfaces;
  privateNetworks: PrivateNetwork[];
  rack?: Rack;
}

interface Port {
  name: string;
  port: string;
}

export interface Rack {
  id: string;
  type: string;
  capacity: string;
}

export interface SpeedOption {
  [key: string]: {
    value: string;
    price: string | number;
  };
}

export interface PortSpeedUpdate {
  portSpeedUpdated: boolean;
}

export interface SwitchPortUpdate {
  switchPortUpdated: boolean;
}
