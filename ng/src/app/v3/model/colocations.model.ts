export interface DataUsedMetric {
  metrics: Metrics;
  _metadata: Metadata;
}
export interface DataUsedMetricV2 {
  metrics: MetricsV2;
  _metadata: Metadata;
}
export interface Colocation {
  contract: Contract;
  featureAvailability: FeatureAvailability;
  id: string;
  location: Location;
  lswDataViewId: string;
  networkInterfaces: NetworkInterfaces;
  privateNetworks: PrivateNetwork[];
  type: string;
}

interface Contract {
  aggregationPackId: string;
  billingCycle: number;
  billingFrequency: string;
  contractTerm: number;
  contractType: string;
  currency: string;
  customerId: string;
  deliveryStatus: string;
  endsAt: string;
  id: string;
  networkTraffic: NetworkTraffic;
  pricePerFrequency: string;
  privateNetworkPortSpeed: number;
  reference: string;
  remoteHands: RemoteHands;
  salesOrgId: string;
  sla: string;
  startsAt: string;
  status: string;
  subnets: any;
}

export interface Colocations {
  colocations: Colocation[];
  _metadata: ServersMetadata;
}

interface FeatureAvailability {
  powerCycle: boolean;
  privateNetwork: boolean;
}

interface Location {
  rack: string;
  site: string;
  suite: string;
}

interface Metadata {
  aggregation: string;
  from: string;
  granularity: any;
  networkType: string;
  to: string;
}
interface Metrics {
  PUBLIC: Public;
}
interface MetricsV2 {
  DOWN_PUBLIC: Public;
  UP_PUBLIC: Public;
}

interface NetworkInterfaces {
  public: NetworkInterface;
  internal: NetworkInterface;
  remoteManagement: NetworkInterface;
}
interface NetworkTraffic {
  connectivityType: string;
  datatrafficLimit: number;
  datatrafficUnit: string;
  trafficType: string;
  type: string;
}

interface NetworkInterface {
  mac: string;
  ip?: string;
  nullRouted?: boolean;
  gateway?: string;
  ports: Port[];
  locationId?: any;
}
interface Public {
  unit: string;
  values: Values[];
}
interface Port {
  name: string;
  port: string;
}

interface PrivateNetwork {
  createdAt: string;
  customerId: string;
  dhcp: string;
  equipmentCount: string;
  id: string;
  name: string;
  salesOrgId: string;
  serverCount: number;
  updatedAt: string;
  vlans: Vlans[];
  vlanId: string;
  linkSpeed: number;
  status: string;
  subnet: string;
  switch: Switch;
}

interface Vlans {
  vlanId: string;
  metro: string;
}

interface Switch {
  name: string;
  port: string;
}

interface ServersMetadata {
  limit: number;
  offset: number;
  totalCount: number;
}
interface RemoteHands {
  type: string;
}
interface Values {
  timestamp: string;
  value: number;
}
