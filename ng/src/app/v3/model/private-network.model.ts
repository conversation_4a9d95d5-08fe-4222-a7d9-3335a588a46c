export interface PrivateNetwork {
  createdAt: string;
  customerId: string;
  dhcp: string;
  equipmentCount: string;
  id: string;
  name: string;
  salesOrgId: string;
  serverCount: number;
  updatedAt: string;
  vlans: Vlans[];
  vlanId: string;
  linkSpeed: number;
  status: string;
  subnet: string;
  switch: Switch;
}

interface Switch {
  name: string;
  port: string;
}
export interface Vlans {
  vlanId: string;
  metro: string;
}
