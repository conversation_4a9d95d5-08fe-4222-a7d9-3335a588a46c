import { PrivateNetwork } from './private-network.model';
import { Rack } from './racks.model';

interface Contract {
  aggregationPackId: string;
  billingCycle: number;
  billingFrequency: string;
  contractTerm: number;
  contractType: string;
  currency: string;
  customerId: string;
  deliveryStatus: string;
  endsAt: string;
  id: string;
  ipv4Quantity: number;
  managedServices: any[];
  networkTraffic: NetworkTraffic;
  pricePerFrequency: string;
  privateNetworkPortSpeed: number;
  reference: string;
  salesOrgId: string;
  sla: string;
  softwareLicenses: SoftwareLicences[];
  startsAt: string;
  status: string;
  subnets: any;
  featureAvailability: FeatureAvailability;
  isPrivateNetworkCapable: boolean;
  isPrivateNetworkEnabled: boolean;
  isRedundantPrivateNetworkCapable: boolean;
  location: Location;
  networkInterfaces: NetworkInterfaces;
  powerPorts: PowerPort[];
  privateNetworks: PrivateNetwork[];
  rack: Rack;
  serialNumber: string;
  specs: Specs;
}

interface Cpu {
  type: string;
  quantity: number;
}
export interface DataUsedMetric {
  metrics: Metrics;
  _metadata: Metadata;
}

interface Hdd {
  id: string;
  size: number;
  unit: string;
  amount: number;
  type: string;
  performanceType: any;
}

interface FeatureAvailability {
  automation: boolean;
  ipmiReboot: boolean;
  powerCycle: boolean;
  privateNetwork: boolean;
  remoteManagement: boolean;
}

interface Metrics {
  DOWN_PUBLIC: Public;
  UP_PUBLIC: Public;
}

interface Metadata {
  aggregation: string;
  from: string;
  granularity: any;
  networkType: string;
  to: string;
}

interface NetworkInterfaces {
  internal: Internal;
  public: Internal;
  remoteManagement: RemoteManagement;
}

interface NetworkTraffic {
  connectivityType: string;
  datatrafficLimit: number;
  datatrafficUnit: string;
  trafficType: string;
  type: string;
}
export interface AddToPrivateNetworkServerParams {
  customerId?: string;
  salesOrgId?: string;
  privateNetworkEnabled?: string;
  privateNetworkCapable?: string;
  filter?: string;
  limit?: number;
  offset?: number;
  [key: string]: string | number | boolean | undefined;
}
interface Internal {
  gateway: string;
  ip: string;
  locationId: string;
  mac: string;
  nullRouted: string;
}

interface Location {
  rack: string;
  site: string;
  suite: string;
  unit: string;
}

interface Port {
  name: string;
  port: string;
}

interface PowerPort {
  name: string;
  port: string;
}

interface Public {
  unit: string;
  values: Values[];
}

interface RemoteManagement {
  gateway: string;
  ip: string;
  locationId: string;
  mac: string;
  ports: Port[];
}

export interface Servers {
  servers: Server[];
  _metadata: ServersMetadata;
}

export interface Server {
  assetId: string;
  contract: Contract;
  details: string[];
  featureAvailability: FeatureAvailability;
  id: string;
  isRedundantPrivateNetworkCapable: boolean;
  isSharedEol: boolean;
  location: Location;
  networkInterfaces: NetworkInterfaces;
  privateNetworks: PrivateNetwork[];
  rack: Rack;
  serialNumber: string;
  specs: Specs;
}

export interface ServerEquipment {
  assetId: string;
  contract: Contract;
}

interface SoftwareLicences {
  currency: string;
  name: string;
  price: string;
  type: string;
}

interface ServersMetadata {
  limit: number;
  offset: number;
  totalCount: number;
}

interface Specs {
  brand: string;
  chassis: string;
  hardwareRaidCapable: boolean;
  cpu: Cpu;
  ram: Ram;
  hdd: Hdd[];
  pciCards: any[];
}

interface Ram {
  size: number;
  unit: string;
}
interface Values {
  timestamp: string;
  value: number;
}
