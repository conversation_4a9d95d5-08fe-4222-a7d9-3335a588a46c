import { Colocation } from './colocations.model';
import { PrivateRack } from './racks.model';
import { Server } from './servers.model';
import { DedicatedStorage } from './storage.model';

export interface CloseModalEvent {
  bondingStatusUpdated?: boolean;
  rack?: PrivateRack;
  portSpeedUpdated?: boolean;
  linkSpeed?: number;
  switchPortUpdated?: boolean;
  dedicatedStorage?: DedicatedStorage;
  colocation?: Colocation;
  equipmentRemoved?: boolean;
  equipment?: PrivateRack;
  server?: Server;
}

export interface FilterParams {
  [key: string]: string | number;
}

export interface AddToApiError {
  errorMessage: string;
}

export interface FeatureAvailability {
  automation: boolean;
  powerCycle: boolean;
  ipmiReboot: boolean;
  privateNetwork: boolean;
  remoteManagement: boolean;
}
