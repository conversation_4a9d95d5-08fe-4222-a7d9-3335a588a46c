<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '600px'}">
    <ng-template pTemplate="header">
        <h3 class="m-0">Power Operation</h3>
    </ng-template>
    <span i18n="@@bma_poweroperationmodal_servertitle" class="p-text-secondary block mb-6" *ngIf="'servers' == equipmentType">Perform Power Operations on a Dedicated Server</span>
    <span i18n="@@bma_poweroperationmodal_networkequipmenttitle" class="p-text-secondary block mb-8" *ngIf="'networkEquipments' == equipmentType">Perform Power Operations on a Network Equipment</span>
    <form class="form-horizontal" [formGroup]="form" (ngSubmit)="performPowerOperation()">
        <div class="mb-3">
            <label for="selectElement" class="w-full block mb-1">Select an Operation<span class="text-red-500">*</span></label>
            <p-dropdown formControlName="action" autofocus inputId="selectElement" [options]="powerOperationOptions" formControlName="action" [showClear]="false" placeholder="Select an operation" class="w-full block" fluid appendTo="body" autoWidth="false" [style]="{'width':'100%'}"></p-dropdown>
        </div>
        <div *ngIf="'powerCycle'==form.get('action').value" id="action-powercycle-body-text">
            <p class="ml-1 text-xl">
                <span i18n="@@bma_poweroperationmodal_serverconfirmation" *ngIf="'servers' == equipmentType">Are you sure you want to power cycle dedicated server </span>
                <span i18n="@@bma_poweroperationmodal_networkequipmentconfirmation" *ngIf="'networkEquipments' == equipmentType">Are you sure you want to power cycle network equipment </span>
                <span class="text-monospace">{{ equipment.id }}</span
                >?
            </p>
            <div *ngIf="'servers' == equipmentType">
                <label i18n="@@bma_poweroperationmodal_schedulehardwarescanv3" for="power_operation_schedule_hw_scan" class="flex mb-3"> <p-checkbox formControlName="scheduleHwScan" [binary]="true" id="power_operation_schedule_hw_scan" value="0" class="mb-1 mb-1 mr-2" /> Schedule Hardware Scan </label>

                <lsw-message class="flex">
                    <p i18n="@@bma_poweroperationmodal_description1" class="ml-1 mt-0">By periodically performing a hardware scan in your dedicated server, you will be able to gather completely up-to-date information not just about inventory, but about the whole hardware customizations present in your dedicated server.</p>
                    <p i18n="@@bma_poweroperationmodal_description2" class="ml-1">A couple of examples about gathered information are: RAID controllers, and network interfaces currently present in your dedicated server, which will be displayed in the "Hardware Details" tab.</p>
                    <p i18n="@@bma_poweroperationmodal_description3" class="ml-1">
                        For more details about what information hardware scan will generate for you, please visit our Knowledge Base
                        <a target="_blank" href="https://kb.leaseweb.com/kb/dedicated-server/dedicated-server-viewing-dedicated-server-hardware-details/"> Dedicated Server Management: Hardware </a>
                        section.
                    </p>
                </lsw-message>
            </div>
        </div>
    </form>
    <ng-template pTemplate="footer">
        <div class="flex justify-start gap-2">
            <button i18n-label="@@bma_common_cancel" pButton class="p-button-secondary" label="Cancel" (click)="closeModal()" type="button"></button>
            <button i18n-label="@@bma_common_ok" pButton (click)="performPowerOperation()" [loading]="isSubmitting" label="Confirm"></button>
        </div>
    </ng-template>
</p-dialog>
