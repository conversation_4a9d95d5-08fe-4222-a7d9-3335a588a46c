import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { HttpClient } from '@angular/common/http';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-power-operation-modal-v3',
  templateUrl: './power-operation-modal-v3.component.html',
  styleUrls: ['./power-operation-modal-v3.component.css'],
})
export class PowerOperationModalV3Component implements OnInit {
  equipment: any;
  equipmentType: string;
  loading = true;
  form: UntypedFormGroup;
  isSubmitting = false;
  showDialog = true;
  powerOperationOptions = [
    { value: 'powerOn', label: 'Power On' },
    { value: 'powerOff', label: 'Power Off' },
    { value: 'powerCycle', label: 'Power Cycle' },
  ];
  city = false;

  constructor(
    private alertService: AlertService,
    private httpClient: HttpClient,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig,
    private readonly formBuilder: UntypedFormBuilder
  ) {}

  ngOnInit(): void {
    this.equipment = this.dynamicDialogConfig.data.equipment;
    this.equipmentType = this.dynamicDialogConfig.data.equipmentType;
    this.form = this.formBuilder.group({
      action: ['powerOn'],
      scheduleHwScan: null,
    });
  }

  performPowerOperation(): void {
    this.alertService.clear();
    this.isSubmitting = true;
    const body: any = {};
    body.equipmentId = this.equipment.id;
    const action = this.form.get('action').value;
    let url = `/_/internal/bmpapi/v2/${this.equipmentType}/${this.equipment.id}/` + action;
    if (action === 'powerCycle' && true === this.form.get('scheduleHwScan').value) {
      url = `/_/internal/bmpapi/v2/${this.equipmentType}/${this.equipment.id}/hardwareScan`;
      body.powerCycle = true;
    }
    const equipmentTypeAlert = this.equipmentType === 'servers' ? 'Server ' : 'Network Equipment ';
    this.httpClient.post<any>(url, body).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;
        this.alertService.alert(
          {
            type: 'success',
            description:
              equipmentTypeAlert + action + ` operation performed, please wait 10 minutes for it to complete`,
          },
          true
        );
        this.closeModal();
      },
      error: (error: any) => {
        this.isSubmitting = false;
        this.alertService.alert(
          {
            type: 'error',
            description:
              equipmentTypeAlert + action + ` operation cannot be performed. Please contact customer support`,
          },
          true
        );
        this.closeModal();
      },
    });
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }
}
