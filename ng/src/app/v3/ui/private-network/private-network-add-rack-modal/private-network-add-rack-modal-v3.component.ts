import { Component, inject, OnInit, signal } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Validators, FormGroup, FormBuilder } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { PrivateNetworkService } from 'src/app/services/private-network.service';
import { PrivateNetwork } from 'src/app/v3/model/private-network.model';
import { AddRackToPrivateNetworkRequest, PrivateRack, PrivateRacks, SpeedOption } from 'src/app/v3/model/racks.model';
import { AddToApiError, CloseModalEvent, FilterParams } from '../../../model/shared.model';

@Component({
  selector: 'app-private-network-add-rack-modal-v3',
  templateUrl: './private-network-add-rack-modal-v3.component.html',
  styleUrls: ['./private-network-add-rack-modal-v3.component.css'],
})
export class PrivateNetworkAddRackModalV3Component implements OnInit {
  error: AddToApiError;
  filters: FormGroup;
  form: FormGroup;
  options: SpeedOption;
  privateNetwork: PrivateNetwork;
  racks: PrivateRacks;
  rackToAdd: PrivateRack;

  showDialog = signal<boolean>(true);
  isLoadingRackLinkSpeedOptions = signal<boolean>(false);
  isLoading = signal<boolean>(false);
  isSubmitting = signal<boolean>(false);
  linkSpeed = signal<number>(0);

  public dynamicDialogRef = inject(DynamicDialogRef);
  private readonly alertService = inject(AlertService);
  private readonly currentUserService = inject(CurrentUserService);
  private readonly dynamicDialogConfig = inject(DynamicDialogConfig);
  private readonly formBuilder = inject(FormBuilder);
  private readonly httpClient = inject(HttpClient);
  private readonly privateNetworkService = inject(PrivateNetworkService);

  ngOnInit(): void {
    this.privateNetwork = this.dynamicDialogConfig.data.privateNetwork;
    this.form = this.formBuilder.group({
      rack: [null, Validators.required],
      linkSpeed: [null, Validators.required],
      allServers: [false],
    });
    this.filters = this.formBuilder.group({
      limit: [null],
      offset: [null],
      filter: [null],
    });
    this.loadPrivateRacks({});
    this.options = this.privateNetworkService.getPrivateNetworkPricesForPrivateRack(this.privateNetwork.salesOrgId);
  }

  loadPrivateRacks(params: { [key: string]: string | number | boolean }) {
    if (this.currentUserService.isEmployee()) {
      params.customerId = this.privateNetwork.customerId;
      params.salesOrgId = this.privateNetwork.salesOrgId;
    }
    params.privateNetworkEnabled = 'false';
    params.privateNetworkCapable = 'true';

    this.isLoading.set(true);
    this.racks = null;
    this.form.reset();
    this.httpClient.get<PrivateRacks>(`/_/internal/dedicatedserverapi/v2/privateRacks`, { params }).subscribe(
      (data: PrivateRacks) => {
        this.racks = data;
        this.isLoading.set(false);
      },
      (error: HttpErrorResponse) => {
        this.isLoading.set(false);
      }
    );
  }

  async onRackSelected() {
    if (this.currentUserService.isEmployee() && this.form.get('rack').value) {
      this.form.patchValue({ linkSpeed: this.form.get('rack').value.contract.privateNetworkPortSpeed });
    }
  }

  isRackAllowedToAdd(rackToAdd: PrivateRack) {
    if (rackToAdd.privateNetworks.length > 0 || rackToAdd.isRedundantPrivateNetworkCapable) {
      return false;
    }

    if (
      this.currentUserService.isEmployee() &&
      this.currentUserService.hasPermission('privatenetwork_manage') &&
      !rackToAdd.contract?.privateNetworkPortSpeed
    ) {
      return false;
    }

    if (this.currentUserService.isCustomer() && rackToAdd.contract?.privateNetworkPortSpeed) {
      return false;
    }

    return null;
  }

  add() {
    const rack = this.form.get('rack').value;
    const allServers = this.form.get('allServers').value;

    if (!rack) {
      return;
    }

    if (this.currentUserService.isCustomer()) {
      this.linkSpeed.set(this.form.get('linkSpeed').value);
    } else {
      this.linkSpeed.set(rack.contract.privateNetworkPortSpeed);
    }

    this.isSubmitting.set(true);
    this.httpClient
      .put<void>(
        `/_/internal/dedicatedserverapi/v2/privateRacks/${rack.id}/privateNetworks/${this.privateNetwork.id}`,
        {
          linkSpeed: this.linkSpeed(),
          allServers,
        } as AddRackToPrivateNetworkRequest
      )
      .subscribe(
        () => {
          this.isSubmitting.set(false);
          this.alertService.alert({
            type: 'success',
            description: $localize`:@@bma_privatenetworkaddprivaterackmodal_requestinprogress:Request to add dedicated rack ${rack.id} is in progress. Please refresh the page for current status after few minutes.`,
          });
          this.closeModal({ rack });
        },
        (error: HttpErrorResponse) => {
          this.isSubmitting.set(false);
          this.error = error.error as AddToApiError;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ rack });
        }
      );
  }

  onFiltersChange() {
    this.loadPrivateRacks(this.sanitise(this.filters.getRawValue()));
  }

  onPageChange(event) {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.onFiltersChange();
  }

  closeModal(event: CloseModalEvent | null = null) {
    this.dynamicDialogRef.close(event);
  }

  sanitise(obj: FilterParams) {
    return Object.entries(obj).reduce<FilterParams>((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
