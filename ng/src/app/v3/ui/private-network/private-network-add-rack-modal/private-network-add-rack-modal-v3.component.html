<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '700px'}">
    <ng-template pTemplate="header">
        <h3 class="m-0" i18n="@@bma_privatenetworkaddprivaterackmodal_title">Add New Dedicated Rack to Private Network {{ privateNetwork.id }}</h3>
    </ng-template>
    <div class="modal-body" [formGroup]="form">
        <form [formGroup]="filters" (ngSubmit)="onFiltersChange()" role="form" class="my-2">
            <p-iconField iconPosition="left">
                <p-inputIcon styleClass="pi pi-search" />
                <input type="text" pInputText placeholder="Filter for ID, IP address, MAC address, Contract ID, or reference" class="w-full" formControlName="filter" i18n-placeholder="@@bma_common_searchplaceholder" (change)="onFiltersChange()" [autofocus]="false" />
            </p-iconField>
        </form>
        <ng-container>
            <div class="spin-wrapper p-10 mt-5" *ngIf="isLoading()">
                <div class="spinner"></div>
            </div>
            <p-table [scrollable]="true" [value]="racks.privateRacks" [paginator]="true" [rows]="4" [autoLayout]="true" *ngIf="!isLoading() && racks">
                <ng-template pTemplate="header">
                    <tr>
                        <th scope="col" i18n="@@bma_common_id">ID</th>
                        <th scope="col" i18n="@@bma_common_reference">Reference</th>
                        <th scope="col" i18n="@@bma_common_location">Location</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="999" class="text-center p-4" i18n="@@bma_privatenetworkaddprivaterackmodal_nodedicatedracks">No dedicated racks eligible for private network..</td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-rack>
                    <tr [ngClass]="{'disabled': isRackAllowedToAdd(rack) === false}">
                        <td>
                            <div class="flex align-items-center gap-2">
                                <p-radioButton [inputId]="rack.id" [value]="rack" formControlName="rack" (onClick)="onRackSelected()" [disabled]="isRackAllowedToAdd(rack) === false" />
                                <label [for]="rack.id" [class.text-500]="isRackAllowedToAdd(rack) === false">{{ rack.id }}</label>
                                &nbsp;
                                <span *ngIf="rack.privateNetworks.length > 0" class="badge" [class.badge-danger-outline]="rack.privateNetworks[0].status === 'ERROR'" [class.badge-green-outline]="rack.privateNetworks[0].status === 'CONFIGURED'" [class.badge-warning-outline]="rack.privateNetworks[0].status === 'CONFIGURING' || rack.privateNetworks[0].status === 'REMOVING'">{{ rack.privateNetworks[0].status }}</span>
                            </div>
                        </td>
                        <td>
                            <strong>{{ rack.contract.reference|default:'-' }}</strong>
                        </td>
                        <td>
                            <app-equipment-location [location]="rack.location"></app-equipment-location>
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </ng-container>
        <div *ngIf="form.get('rack').value" class="form">
            <div class="spin-wrapper p-10 mt-5" *ngIf="isLoadingRackLinkSpeedOptions()">
                <div class="spinner"></div>
            </div>
            <div *ngIf="!isLoadingRackLinkSpeedOptions()">
                <p class="mt-3 mb-3 font-weight-bold" i18n="@@bma_privatenetworkaddprivaterackmodal_portspeed">The port speed in the contract for dedicated rack {{ form.get('rack').value.id }} is set to 2x: {{ form.get('rack').value.contract.privateNetworkPortSpeed|formatSpeed }}</p>
                <div class="mt-3">
                    <input class="form-check-input" type="checkbox" formControlName="allServers" id="allServers" />
                    <label class="form-check-label ml-3" for="allServers"> <b i18n="@@bma_privatenetworkaddprivaterackmodal_addallservers">Add all servers in the rack to the private network</b></label>
                </div>
                <div class="mt-3">
                    <span *ngIf="currentUserService.isCustomer() && form.valid && !error" class="align-middle mr-1"
                        ><span i18n="@@bma_common_totalprice">Total price: </span> <strong *ngIf="options">{{ options[form.get('linkSpeed').value].price|formatCurrency:privateNetwork.salesOrgId }}</strong
                        >. <span i18n="@@bma_common_nextinvoice">You will see the change in your next invoice.</span></span
                    >
                </div>
            </div>
        </div>
    </div>
    <ng-template pTemplate="footer">
        <div class="flex justify-start gap-2">
            <span class="align-middle" *ngIf="error" class="text-danger">{{ error.errorMessage }}</span>
            <button pButton type="button" icon="pi pi-times" iconPos="left" i18n-label="@@bma_common_close" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button pButton (click)="add()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting()" class="p-button-primary" [disabled]="!form.valid || error" i18n-label="@@bma_common_addtoprivatenetwork" label="Add to Private Network"></button>
        </div>
    </ng-template>
</p-dialog>
