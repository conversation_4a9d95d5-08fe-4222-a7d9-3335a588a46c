import { HttpClient } from '@angular/common/http';
import { Component, inject, OnInit, signal } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { AlertService } from 'src/app/services/alert.service';
import { CloseModalEvent } from 'src/app/v3/model/shared.model';

@Component({
  selector: 'app-private-network-add-colocation-modal-v3',
  templateUrl: './private-network-colocation-power-cycle-modal.component.html',
  styleUrls: ['./private-network-colocation-power-cycle-modal.component.css'],
})
export class PrivateNetworkColocationPowerCycleV3Component implements OnInit {
  colocationId: string;

  isSubmitting = signal<boolean>(false);
  showDialog = signal<boolean>(true);

  public dynamicDialogRef = inject(DynamicDialogRef);
  private readonly alertService = inject(AlertService);
  private readonly dynamicDialogConfig = inject(DynamicDialogConfig);
  private readonly httpClient = inject(HttpClient);

  ngOnInit() {
    this.colocationId = this.dynamicDialogConfig.data.colocationId;
  }

  powerCycleColocation(): void {
    this.isSubmitting.set(true);
    this.alertService.clear();
    if (this.colocationId) {
      this.httpClient.post<void>(`/_/internal/bmpapi/v2/colocations/${this.colocationId}/powerCycle`, {}).subscribe(
        () => {
          this.alertService.alert(
            {
              type: 'success',
              description: $localize`:@@bma_colocationpowercyclemodal_performed:Colocation power cycle performed. Please wait 10 minutes for your colocation to come up again.`,
            },
            true
          );
          this.isSubmitting.set(false);
          this.closeModal();
        },
        () => {
          this.alertService.alert(
            {
              type: 'error',
              description:
                $localize`:@@bma_colocationpowercyclemodal_errorpowercycling:Error while power cycling colocation` +
                ' ' +
                this.colocationId,
            },
            true
          );
          this.isSubmitting.set(false);
          this.closeModal();
        }
      );
    } else {
      this.closeModal();
    }
  }

  closeModal(event: CloseModalEvent | null = null) {
    this.dynamicDialogRef.close(event);
  }
}
