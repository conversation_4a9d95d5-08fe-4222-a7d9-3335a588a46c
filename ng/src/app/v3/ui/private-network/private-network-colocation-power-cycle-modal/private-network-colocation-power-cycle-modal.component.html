<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '700px'}">
    <ng-template pTemplate="header">
        <h3 class="m-0" i18n="@@bma_colocationpowercyclemodal_title">Power Cycle Colocation</h3>
    </ng-template>
    <div class="modal-body">
        <div class="mb-3">
            <span class="m-0" i18n="@@bma_colocationpowercyclemodal_confirmation">Are you sure you want to power cycle the colocation {{ colocationId }}?</span>
        </div>
    </div>
    <ng-template pTemplate="footer">
        <div class="flex justify-start gap-2">
            <button pButton type="button" icon="pi pi-times" iconPos="left" i18n-label="@@bma_common_close" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button pButton (click)="powerCycleColocation()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting()" class="p-button-primary" i18n-label="@@bma_common_ok" label="Ok"></button>
        </div>
    </ng-template>
</p-dialog>
