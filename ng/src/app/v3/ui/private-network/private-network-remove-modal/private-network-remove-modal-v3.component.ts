import { CommonModule } from '@angular/common';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Component, inject, OnInit } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { PrivateNetwork } from 'src/app/v3/model/private-network.model';
import { PrivateRack } from 'src/app/v3/model/racks.model';
import { AlertService } from 'src/app/services/alert.service';
import { CommerceService } from 'src/app/services/commerce.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { AddToApiError, CloseModalEvent } from '../../../model/shared.model';

@Component({
  selector: 'app-private-network-add-modal-v3',
  templateUrl: './private-network-remove-modal-v3.component.html',
  styleUrls: ['./private-network-remove-modal-v3.component.css'],
  standalone: true,
  imports: [ButtonModule, DialogModule, CommonModule],
})
export class PrivateNetworkRemoveModalV3Component implements OnInit {
  privateNetwork: PrivateNetwork;
  equipment: PrivateRack;
  equipmentType: string;
  equipmentName = 'dedicated server';
  isSubmitting = false;
  showDialog = true;
  error: AddToApiError;
  isEmployee: boolean;

  alertService = inject(AlertService);
  httpClient = inject(HttpClient);
  dynamicDialogRef = inject(DynamicDialogRef);
  dynamicDialogConfig = inject(DynamicDialogConfig);
  commerceService = inject(CommerceService);
  currentUserService = inject(CurrentUserService);

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.privateNetwork = this.dynamicDialogConfig.data.privateNetwork;
    this.equipment = this.dynamicDialogConfig.data.equipment;
    this.equipmentType = this.dynamicDialogConfig.data.equipmentType;
  }

  remove() {
    if (this.equipmentType === 'clouds') {
      this.removePrivateCloudWorkaround();
      return;
    }

    const equipmentTypesNeedsContractModification = ['servers'];

    if (!this.isEmployee) {
      if (equipmentTypesNeedsContractModification.includes(this.equipmentType)) {
        if (this.equipment.rack.type.includes('SHARED')) {
          window.open(
            this.commerceService.getCommerceConfigureProductUrl(
              this.equipment.contract.id,
              'DEDSER02_MOD_PRIVATE_NETWORK'
            )
          );
          return;
        }
      }
    }

    this.isSubmitting = true;
    this.httpClient
      .delete(
        `/_/internal/dedicatedserverapi/v2/${this.equipmentType}/${this.equipment.id}/privateNetworks/${this.privateNetwork.id}`,
        {}
      )
      .subscribe(
        () => {
          if (this.equipmentType === 'privateRacks') {
            this.equipmentName = 'dedicated rack';
          }

          this.isSubmitting = false;
          this.equipment.privateNetworks = [];
          this.alertService.alert({
            type: 'success',
            description: $localize`:@@bma_privatenetworkremovemodal_requestinprogress:Request to remove ${this.equipmentName} ${this.equipment.id} is in progress. Please refresh the page for current status after few minutes.`,
          });
          this.closeModal({ equipmentRemoved: true, equipment: this.equipment });
        },
        (error: HttpErrorResponse) => {
          this.isSubmitting = false;
          this.error = error.error as AddToApiError;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ equipmentRemoved: false });
        }
      );
  }

  removePrivateCloudWorkaround() {
    const body = {
      privateCloudId: this.equipment.id,
      privateNetworkId: this.privateNetwork.id,
    };
    this.isSubmitting = true;
    this.httpClient.post<any>(`/_legacy/privateNetwork/remove`, body).subscribe(
      () => {
        this.isSubmitting = false;
        this.equipment.privateNetworks = [];
        this.closeModal({ equipmentRemoved: true, equipment: this.equipment });
      },
      (error: HttpErrorResponse) => {
        this.isSubmitting = false;
        this.error = error.error as AddToApiError;
      }
    );
  }

  closeModal(event: CloseModalEvent | null = null) {
    this.dynamicDialogRef.close(event);
  }
}
