<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '700px'}">
    <ng-template pTemplate="header">
        <h3 class="modal-title pull-left" i18n="@@bma_privatenetworkremovemodal_title">Remove from Private Network</h3>
    </ng-template>
    <div class="modal-body">
        <div class="row">
            <div class="col-12">
                <p *ngIf="!equipment.isRedundantPrivateNetworkCapable; else noPrivateNetworkRedundancyRemove" i18n="@@bma_privatenetworkremovemodal_confirmation">Are you sure you want to remove {{ equipment.id }} from your private network {{ privateNetwork.id }}?</p>
                <ng-template #noPrivateNetworkRedundancyRemove>
                    <p *ngIf="isEmployee" i18n="@@bma_privatenetwork_redundancy_remove_notsupported" class="text-warning">Removing an equipment from a redundant Private Network is not yet supported.</p>
                    <p *ngIf="!isEmployee" i18n="@@bma_privatenetwork_redundancy_remove_notsupported_cp" class="text-warning">Removing an equipment from a redundant Private Network is not yet supported, please contact Sales.</p>
                </ng-template>
            </div>
        </div>
    </div>
    <ng-template pTemplate="footer">
        <div class="flex justify-start gap-2">
            <span class="align-middle" *ngIf="error" class="text-danger">{{ error.errorMessage }}</span>
            <button pButton type="button" icon="pi pi-times" iconPos="left" i18n-label="@@bma_common_close" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button *ngIf="!equipment.isRedundantPrivateNetworkCapable" pButton (click)="remove()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" i18n-label="@@bma_common_confirm" label="Confirm"></button>
        </div>
    </ng-template>
</p-dialog>
