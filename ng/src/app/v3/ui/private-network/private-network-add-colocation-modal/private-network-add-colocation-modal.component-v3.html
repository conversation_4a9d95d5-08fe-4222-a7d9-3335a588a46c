<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '700px'}">
    <ng-template pTemplate="header">
        <h3 class="m-0" i18n="@@bma_privatenetworkaddcolocationmodal_title">Add New Colocation to Private Network {{ privateNetwork?.id }}</h3>
    </ng-template>
    <div class="modal-body" [formGroup]="form">
        <form [formGroup]="filters" (ngSubmit)="onFiltersChange()" role="form" class="my-2">
            <p-iconField iconPosition="left">
                <p-inputIcon styleClass="pi pi-search" />
                <input type="text" pInputText i18n-placeholder="@@bma_common_searchplaceholder" placeholder="Filter for ID, IP address, MAC address, Contract ID, or reference" class="w-full" formControlName="filter" (change)="onFiltersChange()" [autofocus]="false" />
            </p-iconField>
        </form>

        <ng-container>
            <div class="spin-wrapper p-10 mt-5" *ngIf="isLoading()">
                <div class="spinner"></div>
            </div>
            <p-table [scrollable]="true" [value]="dedicatedStorages" [paginator]="true" [rows]="4" [autoLayout]="true" *ngIf="!isLoading() && colocations">
                <ng-template pTemplate="header">
                    <tr>
                        <th scope="col" i18n="@@bma_common_id">ID</th>
                        <th scope="col" i18n="@@bma_common_reference">Reference</th>
                        <th scope="col" i18n="@@bma_common_location">Location</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="999" class="text-center p-4" i18n="@@bma_privatenetworkaddcolocationmodal_nocolocations">No colocations eligible for private network.</td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-colocation>
                    <tr [ngClass]="{'disabled': isColocationAllowedToAdd(colocation) === false}">
                        <td>
                            <div class="flex align-items-center gap-2">
                                <label [for]="colocation.id" [class.text-500]="isColocationAllowedToAdd(colocation) === false">{{ colocation.id }}</label>
                                &nbsp;
                                <p-radioButton [inputId]="colocation.id" [value]="colocation" formControlName="colocation" (onClick)="onColocationSelected()" [disabled]="isColocationAllowedToAdd(colocation) === false" />
                                <span *ngIf="colocation.privateNetworks.length > 0" class="badge" [class.badge-danger-outline]="colocation.privateNetworks[0].status === 'ERROR'" [class.badge-green-outline]="colocation.privateNetworks[0].status === 'CONFIGURED'" [class.badge-warning-outline]="colocation.privateNetworks[0].status === 'CONFIGURING' || colocation.privateNetworks[0].status === 'REMOVING'">{{ colocation.privateNetworks[0].status }}</span>
                            </div>
                        </td>
                        <td>
                            <strong>{{ colocation.contract.reference|default:'-' }}</strong>
                        </td>
                        <td>
                            <app-equipment-location [location]="colocation.location"></app-equipment-location>
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </ng-container>
        <div *ngIf="form.get('colocation').value" class="form">
            <p class="mt-3 mb-3 font-weight-bold" i18n="@@bma_privatenetworkaddcolocationmodal_portspeed">The port speed in the contract for colocation rack {{ form.get('colocation').value.id }} is set to 2x: {{ form.get('colocation').value.contract.privateNetworkPortSpeed|formatSpeed }}</p>
        </div>
    </div>
    <ng-template pTemplate="footer">
        <div class="flex justify-start gap-2">
            <span class="align-middle" *ngIf="error" class="text-danger">{{ error.errorMessage }}</span>
            <button pButton type="button" icon="pi pi-times" iconPos="left" i18n-label="@@bma_common_close" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button pButton (click)="add()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting()" class="p-button-primary" [disabled]="!form.valid || error" i18n-label="@@bma_common_addtoprivatenetwork" label="Add to Private Network"></button>
        </div>
    </ng-template>
</p-dialog>
