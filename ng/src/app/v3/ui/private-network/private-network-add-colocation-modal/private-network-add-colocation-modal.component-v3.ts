import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Component, inject, OnInit, signal } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { PrivateNetworkService } from 'src/app/services/private-network.service';
import { Colocation, Colocations } from 'src/app/v3/model/colocations.model';
import { PrivateNetwork } from 'src/app/v3/model/private-network.model';
import { AddToApiError, CloseModalEvent } from 'src/app/v3/model/shared.model';

@Component({
  selector: 'app-private-network-add-colocation-modal-v3',
  templateUrl: './private-network-add-colocation-modal.component-v3.html',
  styleUrls: ['./private-network-add-colocation-modal.component-v3.css'],
})
export class PrivateNetworkAddColocationV3Component implements OnInit {
  colocations: Colocation[];
  error: AddToApiError;
  filters: FormGroup;
  form: FormGroup;
  privateNetwork: PrivateNetwork;

  isLoading = signal<boolean>(false);
  isSubmitting = signal<boolean>(false);
  showDialog = signal<boolean>(true);

  public dynamicDialogRef = inject(DynamicDialogRef);
  private readonly alertService = inject(AlertService);
  private readonly currentUserService = inject(CurrentUserService);
  private readonly dynamicDialogConfig = inject(DynamicDialogConfig);
  private readonly httpClient = inject(HttpClient);
  private readonly formBuilder = inject(FormBuilder);
  private readonly privateNetworkService = inject(PrivateNetworkService);

  ngOnInit() {
    this.form = this.formBuilder.group({
      colocation: [null, Validators.required],
      linkSpeed: [null, Validators.required],
    });
    this.filters = this.formBuilder.group({
      limit: [null],
      offset: [null],
      filter: [null],
    });
    this.loadColocations({});
  }

  add() {
    let linkSpeed = 0;

    const colocation = this.form.get('colocation').value;
    if (colocation) {
      linkSpeed = colocation.contract?.privateNetworkPortSpeed;
    }

    this.isSubmitting.set(true);
    this.httpClient
      .put<void>(
        `/_/internal/dedicatedserverapi/v2/colocations/${colocation.id}/privateNetworks/${this.privateNetwork?.id}`,
        {
          linkSpeed,
        }
      )
      .subscribe(
        () => {
          this.isSubmitting.set(false);
          this.alertService.alert({
            type: 'success',
            description: $localize`:@@bma_privatenetworkaddcolocationmodal_requestinprogress:Request to add colocation ${colocation.id} is in progress. Please refresh the page for current status after few minutes.`,
          });
          this.closeModal({ colocation });
        },
        (error: HttpErrorResponse) => {
          this.isSubmitting.set(false);
          this.error = error.error as AddToApiError;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ colocation });
        }
      );
  }

  closeModal(event: CloseModalEvent | null = null) {
    this.dynamicDialogRef.close(event);
  }

  isColocationAllowedToAdd(colocationToAdd: Colocation) {
    if (this.currentUserService.isCustomer()) {
      return false;
    }

    if (colocationToAdd.privateNetworks.length > 0) {
      return false;
    }

    if (
      this.currentUserService.isEmployee() &&
      this.currentUserService.hasPermission('privatenetwork_manage') &&
      !colocationToAdd.contract.privateNetworkPortSpeed
    ) {
      return false;
    }

    return null;
  }

  loadColocations(params: { [key: string]: string | number | boolean }) {
    if (this.currentUserService.isEmployee()) {
      params.customerId = this.privateNetwork?.customerId;
      params.salesOrgId = this.privateNetwork?.salesOrgId;
    }
    params.privateNetworkEnabled = 'false';
    params.privateNetworkCapable = 'true';

    this.isLoading.set(true);
    this.colocations = null;
    this.form.reset();
    this.httpClient.get<Colocations>(`/_/internal/dedicatedserverapi/v2/colocations`, { params }).subscribe(
      (data: Colocations) => {
        this.colocations = data.colocations;
        this.isLoading.set(false);
      },
      () => {
        this.isLoading.set(false);
      }
    );
  }

  onColocationSelected() {
    this.form.patchValue({ linkSpeed: null });
    if (this.currentUserService.isEmployee() && this.form.get('colocation').value) {
      this.form.patchValue({ linkSpeed: this.form.get('colocation').value.contract.privateNetworkPortSpeed });
    }
  }

  onFiltersChange() {
    this.loadColocations(this.sanitise(this.filters.getRawValue()));
  }

  onPageChange(event) {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.onFiltersChange();
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
