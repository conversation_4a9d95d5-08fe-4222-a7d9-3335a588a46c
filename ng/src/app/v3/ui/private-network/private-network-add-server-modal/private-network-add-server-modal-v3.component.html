<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '700px'}">
    <ng-template pTemplate="header">
        <h3 class="m-0" i18n="@@bma_privatenetworkaddservermodal_title">Add new server to Private Network {{ privateNetwork.id }}</h3>
    </ng-template>
    <div class="modal-body" [formGroup]="form">
        <form [formGroup]="filters" (ngSubmit)="onFiltersChange()" role="form" class="my-2">
            <p-iconField iconPosition="left">
                <p-inputIcon styleClass="pi pi-search" />
                <input type="text" pInputText placeholder="Filter for ID, IP address, MAC address, Contract ID, or reference" class="w-full" formControlName="filter" i18n-placeholder="@@bma_common_searchplaceholder" (change)="onFiltersChange()" [autofocus]="false" />
            </p-iconField>
        </form>

        <ng-container>
            <div class="spin-wrapper p-10 mt-5" *ngIf="isLoading">
                <div class="spinner"></div>
            </div>

            <p-table [scrollable]="true" [value]="servers.servers" [paginator]="true" [rows]="4" [autoLayout]="true" *ngIf="!isLoading && servers">
                <ng-template pTemplate="header">
                    <tr>
                        <th scope="col" i18n="@@bma_common_serverid">Server ID</th>
                        <th scope="col" i18n="@@bma_common_reference">Reference</th>
                        <th scope="col" i18n="@@bma_common_location">Location</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="999" class="text-center p-4" i18n="@@bma_privatenetworkaddservermodal_noservers">No dedicated servers eligible for private network.</td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-server>
                    <tr [ngClass]="{'disabled': isServerAllowedToAdd(server) === false}">
                        <td>
                            <div class="flex align-items-center gap-2">
                                <p-radioButton [inputId]="server.id" [value]="server" formControlName="server" (onClick)="onServerSelected(server)" [disabled]="isServerAllowedToAdd(server) === false" />
                                <label [for]="server.id" [class.text-500]="isServerAllowedToAdd(server) === false">{{ server.id }}</label>
                                &nbsp;

                                <p-tag *ngIf="server.privateNetworks.length > 0" [severity]="getServerPrivateNetworkTagSeverity(server.privateNetworks[0].status)" [value]="server.privateNetworks[0].status" />
                            </div>
                        </td>
                        <td>
                            <strong>{{ server.contract.reference|default:'-' }}</strong>
                        </td>
                        <td>
                            <app-equipment-location [location]="server.location"></app-equipment-location>
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </ng-container>

        <div *ngIf="form.get('server').value" class="form">
            <div class="spin-wrapper p-10 mt-5" *ngIf="isLoadingServerLinkSpeedOptions()">
                <div class="spinner"></div>
            </div>
            <div *ngIf="!isLoadingServerLinkSpeedOptions()">
                <p class="mt-3 mb-3 font-weight-bold" i18n="@@bma_privatenetworkaddservermodal_selectportspeed" [ngClass]="{'text-muted' : (serverToAdd && serverToAdd.rack.type != 'DEDICATED' && currentUserService.isEmployee()) }">Select port speed for server {{ form.get('server').value.id }} :</p>

                <div *ngFor="let option of options">
                    <p-radioButton [inputId]="option" [value]="+option" formControlName="linkSpeed" [checked]="form.get('server').value.contract.privateNetworkPortSpeed == option" [disabled]="isLinkSpeedSelectable(option)" />
                    <label [for]="option" [class.text-muted]="isLinkSpeedSelectable(option) === false" class="ml-1">{{ option|formatSpeed }} </label>
                </div>
            </div>
        </div>
    </div>
    <ng-template pTemplate="footer">
        <div class="flex justify-start gap-2">
            <span class="align-middle" *ngIf="error" class="text-danger">{{ error.errorMessage }}</span>
            <button pButton type="button" icon="pi pi-times" iconPos="left" i18n-label="@@bma_common_close" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button pButton (click)="add()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" class="p-button-primary" [disabled]="!form.valid || error" i18n-label="@@bma_common_addtoprivatenetwork" label="Add to Private Network"></button>
        </div>
    </ng-template>
</p-dialog>
