import { Component, OnInit, signal } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Validators, FormGroup, FormBuilder } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { CommerceService } from 'src/app/services/commerce.service';
import { PrivateNetworkService } from 'src/app/services/private-network.service';
import { PrivateNetwork } from 'src/app/v3/model/private-network.model';
import { AddToPrivateNetworkServerParams, Server, Servers } from 'src/app/v3/model/servers.model';
import { AddToApiError, CloseModalEvent } from 'src/app/v3/model/shared.model';
import { PrivateRack } from 'src/app/v3/model/racks.model';

@Component({
  selector: 'app-private-network-add-server-modal-v3',
  templateUrl: './private-network-add-server-modal-v3.component.html',
  styleUrls: ['./private-network-add-server-modal-v3.component.css'],
})
export class PrivateNetworkAddServerModalV3Component implements OnInit {
  privateNetwork: PrivateNetwork;
  options: Array<string>;
  filters: FormGroup;
  servers: Servers;
  error: AddToApiError;
  isLoading = false;
  isSubmitting = false;
  showDialog = true;
  form: FormGroup;
  serverToAdd: Server;
  isLoadingServerLinkSpeedOptions = signal<boolean>(false);

  constructor(
    private alertService: AlertService,
    private formBuilder: FormBuilder,
    private httpClient: HttpClient,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig,
    private currentUserService: CurrentUserService,
    private commerceService: CommerceService,
    private privateNetworkService: PrivateNetworkService
  ) {}

  ngOnInit() {
    this.privateNetwork = this.dynamicDialogConfig.data.privateNetwork;
    const initialServerId = this.dynamicDialogConfig.data.serverId;

    this.form = this.formBuilder.group({
      server: [null, Validators.required],
      linkSpeed: [null, Validators.required],
    });
    this.filters = this.formBuilder.group({
      limit: [null],
      offset: [null],
      filter: [initialServerId || null],
    });

    if (initialServerId) {
      this.filters.get('filter')?.disable();
    }

    this.loadServers({});
    this.options = this.privateNetworkService.getServerPortSpeeds();
  }

  loadServers(params: Partial<AddToPrivateNetworkServerParams>) {
    if (this.currentUserService.isEmployee()) {
      params.customerId = this.privateNetwork.customerId;
      params.salesOrgId = this.privateNetwork.salesOrgId;
    }
    const filterValue = this.filters.getRawValue().filter;
    if (filterValue) {
      params.filter = filterValue;
    }
    params.privateNetworkEnabled = 'false';
    params.privateNetworkCapable = 'true';

    this.isLoading = true;
    this.servers = null;
    this.form.reset();
    this.httpClient.get<Servers>(`/_/internal/dedicatedserverapi/v2/servers`, { params }).subscribe(
      (data: Servers) => {
        this.servers = data;
        this.isLoading = false;
      },
      () => {
        this.isLoading = false;
      }
    );
  }

  async onServerSelected(server: Server) {
    this.isLoadingServerLinkSpeedOptions.set(true);
    if (!this.currentUserService.isEmployee()) {
      if (server.rack.type === 'DEDICATED' && server.rack.id) {
        await this.httpClient
          .get<PrivateRack>(`/_/internal/dedicatedserverapi/v2/privateRacks/${server.rack.id}`)
          .toPromise()
          .then(
            (rackData: PrivateRack) => {
              if (!rackData.isPrivateNetworkEnabled) {
                this.alertService.alert({
                  type: 'error',
                  description: $localize`:@@bma_common_contractmodificationnotallowed:Contract Modifications are not allowed`,
                });
                this.isLoadingServerLinkSpeedOptions.set(false);
                this.closeModal();
              }
            },
            (error) => {
              this.alertService.alertApiError($localize`:@@bma_common_error:error`);
            }
          );
      } else if (server.rack.type.includes('SHARED') && !server.contract.privateNetworkPortSpeed) {
        window.open(
          this.commerceService.getCommerceConfigureProductUrl(server.contract.id, 'DEDSER02_MOD_PRIVATE_NETWORK')
        );
        this.closeModal({ server });
        return;
      }
    }

    this.serverToAdd = server;

    if (this.serverToAdd.rack.type !== 'DEDICATED' && (!server.rack || !server.rack.capacity)) {
      // Workaround because rack info is not available in the collection responses
      this.httpClient
        .get<Server>(`/_/internal/dedicatedserverapi/v2/servers/${server.id}`)
        .subscribe((data: Server) => {
          server.rack = data.rack;
        });
    }

    this.form.patchValue({ linkSpeed: null });
    if (
      this.currentUserService.isEmployee() &&
      this.serverToAdd.rack.type.includes('SHARED') &&
      this.form.get('server').value
    ) {
      this.form.patchValue({ linkSpeed: this.form.get('server').value.contract.privateNetworkPortSpeed });
    }
    this.isLoadingServerLinkSpeedOptions.set(false);
  }

  isLinkSpeedSelectable(linkSpeed: string) {
    // See: https://github.com/angular/angular/issues/11763
    // Workaround for `[attr.disabled]="expression that evaluates to null or anything other than null"`
    if (this.serverToAdd.rack.type.includes('SHARED') && this.currentUserService.isEmployee()) {
      return false;
    }

    const server = this.form.get('server').value;
    if (!server) {
      return false;
    }
    if (+linkSpeed === 25000 && server.rack?.type !== 'SHARED_200GE') {
      return false;
    }
    if (+linkSpeed === 10000 && server.rack?.capacity !== '10G' && this.serverToAdd.rack.type.includes('SHARED')) {
      return false;
    }

    return null;
  }

  isServerAllowedToAdd(serverToAdd: Server) {
    // See: https://github.com/angular/angular/issues/11763
    // Workaround for `[attr.disabled]="expression that evaluates to null or anything other than null"`

    if (serverToAdd.privateNetworks.length > 0) {
      return false;
    }

    if (
      this.currentUserService.isEmployee() &&
      this.currentUserService.hasPermission('privatenetwork_manage') &&
      serverToAdd.rack.type.includes('SHARED') &&
      !serverToAdd.contract?.privateNetworkPortSpeed
    ) {
      return false;
    }

    if (
      this.currentUserService.isCustomer() &&
      serverToAdd.rack.type.includes('SHARED') &&
      serverToAdd.contract?.privateNetworkPortSpeed
    ) {
      return false;
    }

    if (serverToAdd.isRedundantPrivateNetworkCapable) {
      return false;
    }

    return null;
  }

  add() {
    let linkSpeed = 0;

    const server = this.form.get('server').value;
    if (
      this.currentUserService.isCustomer() ||
      (this.currentUserService.isEmployee() && this.serverToAdd.rack.type.includes('DEDICATED'))
    ) {
      linkSpeed = this.form.get('linkSpeed').value;
    } else {
      linkSpeed = server.contract.privateNetworkPortSpeed;
    }

    this.isSubmitting = true;
    this.httpClient
      .put<PrivateNetwork>(
        `/_/internal/dedicatedserverapi/v2/servers/${server.id}/privateNetworks/${this.privateNetwork.id}`,
        {
          linkSpeed,
        }
      )
      .subscribe(
        () => {
          this.isSubmitting = false;
          this.alertService.alert({
            type: 'success',
            description: $localize`:@@bma_privatenetworkaddservermodal_requestinprogress:Request to add  dedicated server ${server.id} is in progress. Please refresh the page for current status after few minutes.`,
          });
          this.closeModal({ server });
        },
        (error: HttpErrorResponse) => {
          this.isSubmitting = false;
          this.error = error.error;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ server });
        }
      );
  }

  onFiltersChange() {
    this.loadServers(this.sanitise(this.filters.getRawValue()));
  }

  onPageChange(event) {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.onFiltersChange();
  }

  closeModal(event: CloseModalEvent | null = null) {
    this.dynamicDialogRef.close(event);
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }

  getServerPrivateNetworkTagSeverity(privateNetworkStatus) {
    switch (privateNetworkStatus) {
      case 'ERROR': {
        return 'danger';
      }
      case 'CONFIGURED': {
        return 'success';
      }
      case 'CONFIGURING': {
        return 'warn';
      }
      case 'REMOVING': {
        return 'warn';
      }
      default: {
        return 'primary';
      }
    }
  }
}
