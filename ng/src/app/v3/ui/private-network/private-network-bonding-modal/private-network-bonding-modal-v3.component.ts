import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Component, inject, OnInit, signal } from '@angular/core';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { AlertService } from 'src/app/services/alert.service';
import { CloseModalEvent } from 'src/app/v3/model/shared.model';
import { DedicatedStorage } from 'src/app/v3/model/storage.model';

@Component({
  selector: 'app-private-network-bonding-modal-v3',
  templateUrl: './private-network-bonding-modal-v3.component.html',
  styleUrls: ['./private-network-bonding-modal-v3.component.css'],
})
export class PrivateNetworkBondingModalV3Component implements OnInit {
  action: string;
  buttonLabel: string;
  equipmentId: string;
  privateNetworkId: string;
  type: string;

  isSubmitting = signal<boolean>(false);
  showDialog = signal<boolean>(true);

  dynamicDialogRef = inject(DynamicDialogRef);
  private readonly alertService = inject(AlertService);
  private readonly dynamicDialogConfig = inject(DynamicDialogConfig);
  private readonly httpClient = inject(HttpClient);

  ngOnInit() {
    this.action = this.dynamicDialogConfig.data.action;
    this.buttonLabel = this.action + ' ' + $localize`:@@bma_bonding_title:Bonding`;
    this.equipmentId = this.dynamicDialogConfig.data.equipmentId;
    this.privateNetworkId = this.dynamicDialogConfig.data.privateNetworkId;
  }

  closeModal(event: CloseModalEvent | null = null) {
    this.dynamicDialogRef.close(event);
  }

  doAction() {
    this.isSubmitting.set(true);
    this.alertService.clear();
    const params: { bonding?: boolean } = {};
    params.bonding = this.action === 'enable' ? true : false;

    this.httpClient
      .put<DedicatedStorage>(
        `/_/internal/dedicatedserverapi/v2/dedicatedStorages/${this.equipmentId}/privateNetworks/${this.privateNetworkId}`,
        params
      )
      .subscribe({
        next: () => {
          this.isSubmitting.set(false);
          this.alertService.alert({
            type: 'success',
            description: $localize`:@@bma_privatenetworkatogglebondingnmodal_requestinprogress:Request to change bonding status for Dedicated Storage ${this.equipmentId} is in progress. Please refresh the page for current status after few minutes.`,
          });
          this.closeModal({ bondingStatusUpdated: true });
        },
        error: (error: HttpErrorResponse) => {
          this.isSubmitting.set(false);
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ bondingStatusUpdated: false });
        },
      });
  }
}
