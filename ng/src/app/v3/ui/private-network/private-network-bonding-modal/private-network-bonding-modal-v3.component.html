<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '700px'}">
    <ng-template pTemplate="header">
        <h3 class="m-0" i18n="@@bma_bonding_changestatus">Change bonding status</h3>
    </ng-template>
    <form (ngSubmit)="doAction()">
        <div class="modal-body">
            <div class="mb-3">
                <span class="m-0" i18n="@@bma_bonding_downtime"
                    >This action requires downtime. Are you sure you want to {{ action }} bonding for dedicated storage <span class="m-0">{{ equipmentId }}</span
                    >?</span
                >
            </div>
        </div>
    </form>

    <ng-template pTemplate="footer">
        <div class="flex justify-start gap-2">
            <button pButton type="button" icon="pi pi-times" iconPos="left" i18n-label="@@bma_common_close" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button pButton (click)="doAction()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting()" class="p-button-primary" label="{{ buttonLabel|titlecase }}"></button>
        </div>
    </ng-template>
</p-dialog>
