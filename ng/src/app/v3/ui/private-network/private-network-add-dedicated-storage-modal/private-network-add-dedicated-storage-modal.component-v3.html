<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '700px'}">
    <ng-template pTemplate="header">
        <h3 class="m-0" i18n="@@bma_privatenetworkadddedicatedstoragesmodal_title">Add New Dedicated Storage to Private Network {{ privateNetwork.id }}</h3>
    </ng-template>
    <div class="modal-body" [formGroup]="form">
        <form [formGroup]="filters" (ngSubmit)="onFiltersChange()" role="form" class="my-2">
            <p-iconField iconPosition="left">
                <p-inputIcon styleClass="pi pi-search" />
                <input type="text" pInputText placeholder="Filter for ID, IP address, MAC address, Contract ID, or reference" class="w-full" formControlName="filter" i18n-placeholder="@@bma_common_searchplaceholder" (change)="onFiltersChange()" [autofocus]="false" />
            </p-iconField>
        </form>
        <ng-container>
            <div class="spin-wrapper p-10 mt-5" *ngIf="isLoading()">
                <div class="spinner"></div>
            </div>
            <p-table [scrollable]="true" [value]="dedicatedStorages" [paginator]="true" [rows]="4" [autoLayout]="true" *ngIf="!isLoading() && dedicatedStorages">
                <ng-template pTemplate="header">
                    <tr>
                        <th scope="col" i18n="@@bma_common_id">ID</th>
                        <th scope="col" i18n="@@bma_common_reference">Reference</th>
                        <th scope="col" i18n="@@bma_common_location">Location</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="999" class="text-center p-4" i18n="@@bma_privatenetworkaddstoragemodal_nonestorages">No Dedicated Storages eligible for private network.</td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-storage>
                    <tr [ngClass]="{'disabled': isDedicatedStorageAllowedToAdd(storage) === false}">
                        <td>
                            <div class="flex align-items-center gap-2">
                                <p-radioButton [inputId]="storage.id" [value]="storage" formControlName="dedicatedStorage" (onClick)="onDedicatedStorageSelected()" [disabled]="isDedicatedStorageAllowedToAdd(storage) === false" />
                                <label [for]="storage.id" [class.text-500]="isDedicatedStorageAllowedToAdd(storage) === false">{{ storage.id }}</label>
                                &nbsp;
                                <span *ngIf="storage.privateNetworks.length > 0" class="badge" [class.badge-danger-outline]="storage.privateNetworks[0].status === 'ERROR'" [class.badge-green-outline]="storage.privateNetworks[0].status === 'CONFIGURED'" [class.badge-warning-outline]="storage.privateNetworks[0].status === 'CONFIGURING' || storage.privateNetworks[0].status === 'REMOVING'">{{ storage.privateNetworks[0].status }}</span>
                            </div>
                        </td>
                        <td>
                            <strong>{{ storage.contract.reference|default:'-' }}</strong>
                        </td>
                        <td>
                            <app-equipment-location [location]="storage.location"></app-equipment-location>
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </ng-container>
        <div *ngIf="form.get('dedicatedStorage').value" class="form">
            <p class="mt-3 mb-3 font-weight-bold" i18n="@@bma_privatenetworkadddedicatedstoragemodal_selectbonding">Select Bonding for dedicated storage {{ form.get('dedicatedStorage').value.id }} :</p>
            <div class="mt-3">
                <input class="form-check-input" type="checkbox" formControlName="bonding" id="yes" [value]="1" />
                <label for="yes" class="ml-1" i18n="@@bma_common_yes">Yes</label>
            </div>
            <div class="mt-3">
                <input class="form-check-input" type="checkbox" formControlName="bonding" id="no" [value]="0" />
                <label for="no" class="ml-1" i18n="@@bma_common_no">No</label>
            </div>
        </div>
    </div>
    <ng-template pTemplate="footer">
        <div class="flex justify-start gap-2">
            <span class="align-middle" *ngIf="error" class="text-danger">{{ error.errorMessage }}</span>
            <button pButton type="button" icon="pi pi-times" iconPos="left" i18n-label="@@bma_common_close" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button pButton (click)="add()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting()" class="p-button-primary" [disabled]="!form.valid || error" i18n-label="@@bma_common_addtoprivatenetwork" label="Add to Private Network"></button>
        </div>
    </ng-template>
</p-dialog>
