import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Component, inject, OnInit, signal } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { PrivateNetworkService } from 'src/app/services/private-network.service';
import { PrivateNetwork } from 'src/app/v3/model/private-network.model';
import { AddToApiError, CloseModalEvent, FilterParams } from 'src/app/v3/model/shared.model';
import { AddStorageToPrivateNetworkRequest, DedicatedStorage, DedicatedStorages } from 'src/app/v3/model/storage.model';

@Component({
  selector: 'app-private-network-add-dedicated-storage-modal-v3',
  templateUrl: './private-network-add-dedicated-storage-modal.component-v3.html',
  styleUrls: ['./private-network-add-dedicated-storage-modal.component-v3.css'],
})
export class PrivateNetworkAddDedicatedStorageV3Component implements OnInit {
  dedicatedStorages: DedicatedStorage[];
  error: AddToApiError;
  filters: FormGroup;
  form: FormGroup;
  privateNetwork: PrivateNetwork;

  bonding = signal<number>(0);
  showDialog = signal<boolean>(true);
  isSubmitting = signal<boolean>(false);
  isLoading = signal<boolean>(false);

  public dynamicDialogRef = inject(DynamicDialogRef);
  private readonly alertService = inject(AlertService);
  private readonly currentUserService = inject(CurrentUserService);
  private readonly dynamicDialogConfig = inject(DynamicDialogConfig);
  private readonly httpClient = inject(HttpClient);
  private readonly formBuilder = inject(FormBuilder);
  private readonly privateNetworkService = inject(PrivateNetworkService);

  ngOnInit() {
    this.privateNetwork = this.dynamicDialogConfig.data.privateNetwork;
    this.form = this.formBuilder.group({
      dedicatedStorage: [null, Validators.required],
      bonding: ['null', Validators.required],
    });

    this.filters = this.formBuilder.group({
      limit: [null],
      offset: [null],
      filter: [null],
    });
    this.loadDedicatedStorage({});
  }

  loadDedicatedStorage(params: { [key: string]: string | number | boolean }) {
    if (this.currentUserService.isEmployee()) {
      params.customerId = this.privateNetwork.customerId;
      params.salesOrgId = this.privateNetwork.salesOrgId;
    }
    params.privateNetworkEnabled = 'false';
    params.privateNetworkCapable = 'true';

    this.isLoading.set(true);
    this.dedicatedStorages = null;
    this.form.reset();
    this.httpClient.get<DedicatedStorages>(`/_/internal/dedicatedserverapi/v2/dedicatedStorages`, { params }).subscribe(
      (data: DedicatedStorages) => {
        this.dedicatedStorages = data.dedicatedStorages;
        this.isLoading.set(false);
      },
      (error: HttpErrorResponse) => {
        this.isLoading.set(false);
      }
    );
  }

  isDedicatedStorageAllowedToAdd(dedicatedStorageToAdd: DedicatedStorage) {
    if (dedicatedStorageToAdd.privateNetworks.length > 0) {
      return false;
    }

    if (
      this.currentUserService.isEmployee() &&
      this.currentUserService.hasPermission('privatenetwork_manage') &&
      dedicatedStorageToAdd.rack.type &&
      dedicatedStorageToAdd.rack.type.includes('SHARED') &&
      !dedicatedStorageToAdd.contract?.privateNetworkPortSpeed
    ) {
      return false;
    }

    if (
      this.currentUserService.isCustomer() &&
      dedicatedStorageToAdd.rack.type &&
      dedicatedStorageToAdd.rack.type.includes('SHARED') &&
      dedicatedStorageToAdd.contract?.privateNetworkPortSpeed
    ) {
      return false;
    }

    if (dedicatedStorageToAdd.isRedundantPrivateNetworkCapable) {
      return false;
    }

    return null;
  }

  add() {
    const dedicatedStorage = this.form.get('dedicatedStorage').value;
    const bondingValue = this.form.get('bonding').value;
    this.bonding.set(bondingValue);

    this.isSubmitting.set(true);
    this.httpClient
      .put<void>(
        `/_/internal/dedicatedserverapi/v2/dedicatedStorages/${dedicatedStorage.id}/privateNetworks/${this.privateNetwork.id}`,
        {
          bonding: this.bonding(),
        } as AddStorageToPrivateNetworkRequest
      )
      .subscribe(
        () => {
          this.isSubmitting.set(false);
          this.alertService.alert({
            type: 'success',
            description: $localize`:@@bma_privatenetworkadddedicatedstoragessmodal_requestinprogress:Request to add Dedicated Storage ${dedicatedStorage.id} is in progress. Please refresh the page for current status after few minutes.`,
          });
          this.closeModal({ dedicatedStorage });
        },
        (error: HttpErrorResponse) => {
          this.isSubmitting.set(false);
          this.error = error.error as AddToApiError;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ dedicatedStorage });
        }
      );
  }

  onFiltersChange() {
    this.loadDedicatedStorage(this.sanitise(this.filters.getRawValue()));
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.onFiltersChange();
  }

  closeModal(event: CloseModalEvent | null = null) {
    this.dynamicDialogRef.close(event);
  }

  sanitise(obj: FilterParams) {
    return Object.entries(obj).reduce<FilterParams>((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
