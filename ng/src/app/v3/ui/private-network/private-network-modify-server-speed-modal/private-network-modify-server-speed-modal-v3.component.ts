import { Component, inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { PrivateNetworkService } from 'src/app/services/private-network.service';
import { AddToApiError, CloseModalEvent } from 'src/app/v3/model/shared.model';
import { Server } from '../../../model/servers.model';
import { PrivateNetwork } from '../../../model/private-network.model';

@Component({
  selector: 'app-private-network-modify-server-speed-modal-v3-modal',
  templateUrl: './private-network-modify-server-speed-modal-v3.component.html',
  styleUrls: ['./private-network-modify-server-speed-modal-v3.component.css'],
})
export class PrivateNetworkModifyServerSpeedModalV3Component implements OnInit {
  error: AddToApiError;
  isLoadingRack = false;
  isLoadingOptions = false;
  isSubmitting = false;
  options: Array<string>;
  privateNetwork: PrivateNetwork;
  server: Server;
  showDialog = true;
  form: FormGroup;

  currentUserService = inject(CurrentUserService);
  dynamicDialogRef = inject(DynamicDialogRef);
  private readonly formBuilder = inject(FormBuilder);
  private readonly httpClient = inject(HttpClient);
  private readonly dynamicDialogConfig = inject(DynamicDialogConfig);
  private readonly privateNetworkService = inject(PrivateNetworkService);

  ngOnInit(): void {
    this.form = this.formBuilder.group({
      linkSpeed: [this.server?.privateNetworks[0]?.linkSpeed, Validators.required],
    });
    this.privateNetwork = this.dynamicDialogConfig.data.privateNetwork;
    this.server = this.dynamicDialogConfig.data.server;

    if (!this.server.rack || !this.server.rack.capacity) {
      this.isLoadingRack = true;
      this.httpClient.get<Server>(`/_/internal/dedicatedserverapi/v2/servers/${this.server.id}`).subscribe(
        (data: Server) => {
          this.isLoadingRack = false;
          this.server.rack = data.rack;
        },
        (error: AddToApiError) => {
          this.isLoadingRack = false;
        }
      );
    }
    this.options = this.privateNetworkService.getServerPortSpeeds();
  }

  closeModal(event: CloseModalEvent | null = null) {
    this.dynamicDialogRef.close(event);
  }

  isLinkSpeedSelectable(linkSpeed: number) {
    if (+linkSpeed === 25000 && this.server.rack?.type !== 'SHARED_200GE') {
      return false;
    }
    if (+linkSpeed === 10000 && this.server.rack?.capacity !== '10G') {
      return false;
    }
    if (+linkSpeed === this.server.privateNetworks[0].linkSpeed) {
      return false;
    }
    return null;
  }

  modify() {
    const body = {
      linkSpeed: this.form.get('linkSpeed').value,
    };

    this.isSubmitting = true;
    this.httpClient
      .put(
        `/_/internal/dedicatedserverapi/v2/servers/${this.server.id}/privateNetworks/${this.privateNetwork.id}`,
        body
      )
      .subscribe(
        () => {
          this.isSubmitting = false;
          this.server.privateNetworks[0].status = 'CONFIGURING';
          this.server.privateNetworks[0].linkSpeed = body.linkSpeed;
          this.closeModal({ server: this.server });
        },
        (error: HttpErrorResponse) => {
          this.error = error.error;
          this.isSubmitting = false;
        }
      );
  }
}
