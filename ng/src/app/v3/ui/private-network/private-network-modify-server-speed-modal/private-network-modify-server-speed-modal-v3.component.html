<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '700px'}">
    <ng-template pTemplate="header">
        <h3 class="m-0" i18n="@@bma_privatenetworkmodifyservermodal_title">Upgrade / Downgrade Port Speed of {{ server.id }}</h3>
    </ng-template>

    <form [formGroup]="form">
        <div class="modal-body">
            <div class="mb-3">
                <span class="m-0" [ngClass]="{'text-muted' : (server.rack.type != 'DEDICATED' && currentUserService.isEmployee())}" i18n="@@bma_privatenetworkmodifyservermodal_selectportspeed">Select Port Speed for server {{ server.id }}:</span>
            </div>
        </div>

        <ng-container>
            <div class="mb-3">
                <div *ngFor="let option of options">
                    <p-radioButton [inputId]="option" [value]="+option" formControlName="linkSpeed" [checked]="form.get('server')?.value.contract.privateNetworkPortSpeed == option" [disabled]="isLinkSpeedSelectable(option)" />
                    <label [for]="option" [class.text-muted]="isLinkSpeedSelectable(option) === false" class="ml-1">{{ option|formatSpeed }} </label>
                </div>
            </div>
        </ng-container>
    </form>

    <ng-template pTemplate="footer">
        <div class="flex justify-start gap-2">
            <span class="align-middle" *ngIf="error" class="text-danger">{{ error.errorMessage }}</span>
            <button pButton type="button" icon="pi pi-times" iconPos="left" i18n-label="@@bma_common_close" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button pButton (click)="modify()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" [disabled]="!form.valid || form.get('linkSpeed').value === server.privateNetworks[0].linkSpeed || error" class="p-button-primary" i18n-label="@@bma_privatenetworkmodifyservermodal_submit" label="{{ form.get('linkSpeed').value < server.privateNetworks[0].linkSpeed ? 'Downgrade' : 'Upgrade' }} Port Speed"></button>
        </div>
    </ng-template>
</p-dialog>
