<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '700px'}">
    <ng-template pTemplate="header">
        <h3 class="m-0" i18n="@@bma_common_switchportcapuncapoperation">Switch Port Cap/Uncap Operation</h3>
    </ng-template>
    <form [formGroup]="form" (ngSubmit)="onChangeSpeed()">
        <div class="modal-body">
            <div class="mb-3">
                <span class="m-0" i18n="@@bma_common_selectlinkspeed">Select the desired link speed for {{ port }} on device {{ networkDeviceName }}?</span>
            </div>
            <p-dropdown formControlName="linkSpeed" autofocus optionLabel="name" optionValue="value" [options]="linkSpeeds" [showClear]="false" class="w-full block" [autoDisplayFirst]="true" fluid appendTo="body" autoWidth="false" [style]="{'width':'100%'}"> </p-dropdown>
        </div>
    </form>

    <ng-template pTemplate="footer">
        <div class="flex justify-start gap-2">
            <span class="align-middle" *ngIf="error" class="text-danger">{{ error.errorMessage }}</span>
            <button pButton type="button" icon="pi pi-times" iconPos="left" i18n="@@bma_common_cancel" label="Cancel" class="p-button-secondary" (click)="closeModal()"></button>
            <button pButton (click)="onChangeSpeed()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting()" class="p-button-primary" i18n-label="@@bma_common_execute" label="Execute"></button>
        </div>
    </ng-template>
</p-dialog>
