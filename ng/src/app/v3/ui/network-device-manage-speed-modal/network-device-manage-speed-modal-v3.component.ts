import { CommonModule } from '@angular/common';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Component, inject, OnInit, signal } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { AlertService } from 'src/app/services/alert.service';
import { CloseModalEvent } from '../../model/shared.model';

@Component({
  selector: 'app-network-device-manage-speed-v3-modal',
  templateUrl: './network-device-manage-speed-modal-v3.component.html',
  styleUrls: ['./network-device-manage-speed-modal-v3.component.css'],
  standalone: true,
  imports: [CommonModule, DialogModule, ButtonModule, ReactiveFormsModule, DropdownModule],
})
export class NetworkDeviceManageSpeedModalV3Component implements OnInit {
  form: FormGroup;
  isSubmitting = signal<boolean>(false);
  networkDeviceName = '';
  port = '';
  showDialog = true;

  linkSpeeds = [
    { value: 'Remove port cap', name: $localize`:@@bma_common_removeportcap:Remove port cap` },
    { value: '10', name: '10 Mbps' },
    { value: '100', name: '100 Mbps' },
    { value: '1000', name: '1000 Mbps' },
    { value: '1000', name: '1000 Mbps' },
    { value: '10000', name: '10000 Mbps' },
    { value: '25000', name: '25000 Mbps' },
  ];

  private readonly httpClient = inject(HttpClient);
  private readonly formBuilder = inject(FormBuilder);
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly alertService = inject(AlertService);
  private readonly dynamicDialogRef = inject(DynamicDialogRef);
  private readonly dynamicDialogConfig = inject(DynamicDialogConfig);

  ngOnInit() {
    this.form = this.formBuilder.group({
      linkSpeed: [this.linkSpeeds[0].value, Validators.required],
    });
    this.networkDeviceName = this.dynamicDialogConfig.data.networkDeviceName;
    this.port = this.dynamicDialogConfig.data.port;
  }

  onChangeSpeed() {
    this.alertService.clear();

    if (this.form.valid) {
      const body: any = {};
      const linkSpeed = this.form.get('linkSpeed').value;
      body.linkSpeed = linkSpeed;

      this.activatedRoute.queryParams.subscribe((queryParams) => {
        body.serverId = queryParams.serverId;
      });

      let postUrl = '';

      if (body.linkSpeed === 'remove') {
        postUrl = `/_/internal/nseapi/v2/networkDevices/${this.networkDeviceName}/ports/${this.port}/uncap`;
      } else {
        postUrl = `/_/internal/nseapi/v2/networkDevices/${this.networkDeviceName}/ports/${this.port}/cap`;
      }
      this.isSubmitting.set(true);

      this.httpClient.post(postUrl, body).subscribe({
        next: () => {
          this.isSubmitting.set(false);
          this.closeModal({ portSpeedUpdated: true, linkSpeed: body.linkSpeed });
          this.alertService.alert(
            {
              type: 'success',
              description:
                body.linkSpeed === 'remove'
                  ? $localize`:@@bma_networkdevice_removecapsuccess:Successfully removed any existing cap from port '${this.port}' for network device '${this.networkDeviceName}'`
                  : $localize`:@@bma_networkdevice_linkspeedchanged:Successfully changed the link speed to ${body.linkSpeed} mbps for port '${this.port}' for network device '${this.networkDeviceName}'`,
            },
            true
          );
        },
        error: (error: HttpErrorResponse) => {
          this.isSubmitting.set(false);
          if (error.error.errorDetails) {
            Object.entries(error.error.errorDetails).forEach(([key, value]) => {
              this.form.get(key).setErrors({ server: value[0] });
            });
          }
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ portSpeedUpdated: false });
        },
      });
    } else {
      this.alertService.alert(
        {
          type: 'error',
          description: $localize`:@@bma_networkdevice_changespeedfailed:Failed to change speed`,
        },
        true
      );
    }
  }

  closeModal(event: CloseModalEvent | null = null) {
    this.dynamicDialogRef.close(event);
  }
}
