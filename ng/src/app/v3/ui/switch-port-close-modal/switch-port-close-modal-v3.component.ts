import { CommonModule } from '@angular/common';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Component, inject, OnInit, signal } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { CloseModalEvent } from '../../model/shared.model';

@Component({
  selector: 'app-switch-port-close-v3-modal',
  templateUrl: './switch-port-close-modal-v3.component.html',
  styleUrls: ['./switch-port-close-modal-v3.component.css'],
  standalone: true,
  imports: [CommonModule, DialogModule, ButtonModule, ReactiveFormsModule, InputTextareaModule],
})
export class SwitchPortCloseModalV3Component implements OnInit {
  showDialog = true;
  equipmentId: string;
  type: string;
  action: string;
  form: FormGroup;
  isSubmitting = signal(false);
  isEmployee = false;

  private readonly httpClient = inject(HttpClient);
  private readonly formBuilder = inject(FormBuilder);
  private readonly alertService = inject(AlertService);
  private readonly currentUserService = inject(CurrentUserService);
  private readonly dynamicDialogRef = inject(DynamicDialogRef);
  private readonly dynamicDialogConfig = inject(DynamicDialogConfig);

  ngOnInit() {
    this.isEmployee = this.currentUserService.isEmployee();
    this.equipmentId = this.dynamicDialogConfig.data.equipmentId;
    this.type = this.dynamicDialogConfig.data.type;
    this.action = this.dynamicDialogConfig.data.action;
    this.form = this.formBuilder.group({
      comment: [null],
    });
  }

  doAction() {
    this.isSubmitting.set(true);
    this.alertService.clear();
    const params: any = {};
    params.bareMetalId = this.equipmentId;

    if (this.isEmployee && this.currentUserService.hasPermission('networkdevice_ports')) {
      params.comment = this.form.get('comment').value;
    }

    this.httpClient
      .post<any>(
        `/_/internal/nseapi/v2/servers/${this.equipmentId}/networkInterfaces/${this.type}/${this.action}`,
        params
      )
      .subscribe({
        next: () => {
          this.isSubmitting.set(false);
          this.form.reset();
          this.alertService.alert({
            type: 'success',
            description: `Success! ${this.action} ${this.type} network interface for server ${this.equipmentId}`,
          });
          this.closeModal({ switchPortUpdated: true });
        },
        error: (error: HttpErrorResponse) => {
          this.isSubmitting.set(false);
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ switchPortUpdated: false });
        },
      });
  }

  closeModal(event: CloseModalEvent | null = null) {
    this.dynamicDialogRef.close(event);
  }
}
