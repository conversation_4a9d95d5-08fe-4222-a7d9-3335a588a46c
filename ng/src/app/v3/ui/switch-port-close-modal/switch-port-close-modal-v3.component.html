<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '700px'}">
    <ng-template pTemplate="header">
        <h3 class="m-0" i18n="@@bma_switchport_close_modal_title">Toggle {{ type }} network interface</h3>
    </ng-template>

    <form [formGroup]="form">
        <div class="modal-body">
            <div class="mb-3">
                <span class="m-0" i18n="@@bma_switchport_close_modal_confirmation_text">Are you sure you want to {{ action }} the {{ type }} network interface for dedicated server {{ equipmentId }} ?</span>
            </div>
        </div>
        <ng-container *ngIf="isEmployee && action == 'close'">
            <div class="mb-3">
                <span class="m-0" i18n="@@bma_switchport_close_modal_message">Message</span>
            </div>
            <textarea rows="5" cols="60" pInputTextarea formControlName="comment"></textarea>
        </ng-container>
    </form>

    <ng-template pTemplate="footer">
        <div class="flex justify-start gap-2">
            <button pButton type="button" icon="pi pi-times" iconPos="left" i18n-label="@@bma_common_close" label="Close" (click)="closeModal()"></button>
            <button pButton (click)="doAction()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting()" class="p-button-primary" i18n-label="@@bma_common_execute" label="Execute"></button>
        </div>
    </ng-template>
</p-dialog>
