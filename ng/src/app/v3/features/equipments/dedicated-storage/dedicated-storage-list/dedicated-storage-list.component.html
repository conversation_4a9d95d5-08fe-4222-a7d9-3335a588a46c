<lsw-page-header primaryTitle="Dedicated Storage">
    <div action class="flex flex-row gap-3">
        <p-button *ngIf="isEmployee" i18n-label="@@bma_common_addtoprivatenetwork" type="button" label="Add to Private Network" (click)="openAddToPrivateNetworkModal()" styleClass="p-button-secondary" icon="pi pi-plus"></p-button>
    </div>
</lsw-page-header>

<app-customer-aware-header [customerId]="customerId" [salesOrgId]="salesOrgId" [isEmployee]="isEmployee"></app-customer-aware-header>

<lsw-data-view [items]="dedicatedStorages()" [updatedItems]="dedicatedStorages()" [totalItemCount]="totalCount()" [accordionContentTemplate]="accordionContentTemplate" [accordionContentHiddenTemplate]="accordionContentHiddenTemplate" [accordionRequired]="true" [enableCardListTopbarView]="true" [enableTopbarViewFilters]="true" [errorLoadingDynamicItems]="false" [filtersDialogTemplate]="filterDialogTemplate" [isFilterApplied]="isFilterApplied()" [isNewFilter]="isNewFilter()" [filterForm]="filters" [isLoading]="isLoading()" [desktopPaginationOffset]="getOffset()" [desktopPaginationLimit]="getLimit()" (loadDesktopItemsFromParent)="loadDesktopItems($event)" (loadItemsFromParent)="loadItems($event)" (clearFiltersEvent)="clearFilters()" (dialogFiltersEvent)="dialogFiltersEvent()" />

<ng-template #accordionContentTemplate let-item let-triggerAction="triggerAction">
    <div class="grid nested-grid m-0">
        <div class="col-12 pb-0">
            <div class="grid">
                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_id">ID</span>
                    <a [href]="locationService.getAbsoluteUrlWithBase('/dedicatedStorages/' + item.id)">{{ item.id ? item.id : '-' }}</a>
                </div>
                <div class="col-12 lg:col-6 pl-3 py-1">
                    <div class="flex flex-row gap-3">
                        <span class="font-bold" i18n="@@bma_common_actions">Actions</span>
                        <div class="flex flex-wrap gap-3">
                            <p-button i18n-label="@@bma_common_credentials" label="Credentials" size="small" icon="pi pi-lock" styleClass="p-button-tertiary py-0 px-1" (click)="locationService.navigateToWithBase('/dedicatedStorages/' + item.id + '/credentials')"></p-button>
                            <p-button i18n-label="@@bma_common_ips" label="IPs" size="small" icon="pi pi-globe" styleClass="p-button-tertiary py-0 px-1" (click)="locationService.navigateToWithBase('/dedicatedStorages/' + item.id + '/ips')"></p-button>
                            <p-button i18n-label="@@bma_common_remove_from_pn" label="Remove from Private Network" size="small" icon="pi pi-minus" styleClass="p-button-tertiary py-0 px-1" *ngIf="isEmployee && item.privateNetworks.length > 0 && item.privateNetworks[0].status === 'CONFIGURED'" (click)="openRemoveFromPrivateNetworkModal(item); false" class="p-button-link"></p-button>
                        </div>
                    </div>
                </div>
                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_location">Location</span>
                    <app-equipment-location [location]="item.location"></app-equipment-location>
                </div>

                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_privatenetwork">Private Network</span>
                    <p-badge
                        *ngIf="item.privateNetworks?.length > 0"
                        [value]="item?.isRedundantPrivateNetworkCapable && item.privateNetworks?.length !== 0 ? 'REDUNDANT' : 'ENABLED'"
                        [styleClass]="
                    item?.isRedundantPrivateNetworkCapable && item.privateNetworks?.length !== 0 ? 'badge-redundant-color' : 'badge-enabled-color'"></p-badge>
                    <span *ngIf="item.privateNetworks?.length === 0" i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
                </div>

                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_serverid">IP Address</span>
                    <ng-container *ngIf="(item?.networkInterfaces?.public && item?.networkInterfaces?.public?.ip)">
                        <span class="selectable">{{ item.networkInterfaces.public.ip|cidrToIp }}</span>
                    </ng-container>
                    <span *ngIf="!item?.networkInterfaces?.internal" class="text-muted">-</span>
                </div>

                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_status">Private Network Status</span>
                    <p-badge *ngIf="item.privateNetworks.length > 0" [value]="item.privateNetworks[0].status.toUpperCase()" [styleClass]="dedicatedStorageListService.getBadgeSeverity(item)"> </p-badge>
                    <span *ngIf="item.privateNetworks.length === 0" class="text-muted" i18n="@@bma_common_not_configured">-</span>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #accordionContentHiddenTemplate let-item let-triggerAction="triggerAction">
    <div class="grid nested-grid m-0">
        <div class="col-12">
            <div class="grid">
                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_name">Name</span>
                    {{ item.name|default:'-' }}
                </div>
                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_subnet">Subnet</span>
                    <span *ngIf="item?.privateNetworks.length > 0 && item?.privateNetworks[0].dhcp === 'ENABLED'">{{ item?.privateNetworks[0]?.subnet }}</span>
                    <span *ngIf="item?.privateNetworks.length > 0 && item?.privateNetworks[0].dhcp === 'DISABLED'" i18n="@@bma_common_dhcpdisabled" class="text-muted">DHCP disabled</span>
                    <span *ngIf="item?.privateNetworks.length === 0" class="text-muted">-</span>
                </div>

                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_bonding_title">Bonding</span>
                    <ng-container *ngIf="item?.privateNetworks[0]?.bonding === true; else bondingDisabled">
                        <span i18n="@@bma_bonding_enabled">Enabled</span>
                    </ng-container>
                    <ng-template #bondingDisabled>
                        <span i18n="@@bma_bonding_disabled">Disabled</span>
                    </ng-template>
                    <ng-container *ngIf="item?.privateNetworks[0]?.bonding === true">
                        <a *ngIf="item?.networkInterfaces['internal']?.ports?.length == 4" i18n="@@bma_bonding_disable" (click)="openToggleBondingModal(item, 'disable')" href="javascript:void(0);"> Disable Bonding </a>
                    </ng-container>
                    <ng-container *ngIf="item?.privateNetworks[0]?.bonding === false">
                        <a *ngIf="item?.networkInterfaces['internal']?.ports?.length == 4" i18n="@@bma_bonding_enable" (click)="openToggleBondingModal(item, 'enable')" href="javascript:void(0);"> Enable Bonding </a>
                    </ng-container>
                </div>

                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_portspeed">Port Speed</span>
                    <span *ngIf="item.privateNetworks.length > 0" class="port-speed">{{ item?.privateNetworks[0].linkSpeed|formatSpeed }}</span>
                    <span *ngIf="item.privateNetworks.length === 0" class="text-muted">-</span>
                </div>

                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_reference">Reference</span>
                    <span class="font-bold mr-3">{{ item.contract.reference ? item.contract.reference : '-' }}</span>
                </div>

                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_vlanid">Vlan ID</span>
                    {{ item?.privateNetworks[0]?.vlanId|default:'-' }}
                </div>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #filterDialogTemplate>
    <form [formGroup]="filters">
        <div class="flex flex-column field p-4">
            <label for="reference" i18n="@@bma_common_search">Search</label>
            <div class="flex gap-1 md:gap-2 mb-7">
                <p-iconField iconPosition="right" class="flex-grow-1">
                    <input pInputText id="filter" class="w-full" formControlName="filter" i18n-placeholder="@@bma_common_filter" placeholder="Filter..." />
                </p-iconField>
            </div>
        </div>
    </form>
</ng-template>
