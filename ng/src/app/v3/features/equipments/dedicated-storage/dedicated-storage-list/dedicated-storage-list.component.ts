import { Component, computed, inject, OnInit, signal } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { AlertService } from 'src/app/services/alert.service';
import { FormBuilder, FormGroup } from '@angular/forms';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { SiteService } from 'src/app/services/site.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { PrivateNetworkAddDedicatedStorageV3Component } from 'src/app/v3/ui/private-network/private-network-add-dedicated-storage-modal/private-network-add-dedicated-storage-modal.component-v3';
import { PrivateNetworkRemoveModalV3Component } from 'src/app/v3/ui/private-network/private-network-remove-modal/private-network-remove-modal-v3.component';
import { LocationService } from 'src/app/services/location.service';
import { DedicatedStorage } from 'src/app/v3/model/storage.model';
import { PrivateNetworkService } from 'src/app/services/private-network.service';
import { PrivateNetwork } from 'src/app/v3/model/private-network.model';
import { PrivateNetworkBondingModalV3Component } from 'src/app/v3/ui/private-network/private-network-bonding-modal/private-network-bonding-modal-v3.component';
import { DedicatedStorageListService } from './dedicated-storage-list.service';

@Component({
  selector: 'app-dedicated-storage-list',
  templateUrl: './dedicated-storage-list.component.html',
  styleUrls: ['./dedicated-storage-list.component.css'],
})
export class DedicatedStorageListComponent implements OnInit {
  customerId: string;
  equipmentType = 'dedicatedStorages';
  dynamicDialogRef: DynamicDialogRef;
  filters: FormGroup;
  headerTitle: string;
  privateNetwork = signal<PrivateNetwork[]>([]);
  isEmployee: boolean;
  salesOrgId: string;

  readonly locationService = inject(LocationService);

  isFilterApplied = signal<boolean>(false);
  isNewFilter = signal<boolean>(false);

  dedicatedStorages = computed(() => this.dedicatedStorageListService.dedicatedStorages());
  isLoading = computed(() => this.dedicatedStorageListService.isLoading());
  totalCount = computed(() => this.dedicatedStorageListService.totalCount());

  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly alertService = inject(AlertService);
  private readonly currentUserService = inject(CurrentUserService);
  private readonly dedicatedStorageListService = inject(DedicatedStorageListService);
  private readonly formBuilder = inject(FormBuilder);
  private readonly modalService = inject(ModalService);
  private readonly privateNetworkService = inject(PrivateNetworkService);
  private readonly router = inject(Router);
  private readonly siteService = inject(SiteService);
  private readonly titleService = inject(Title);

  ngOnInit() {
    this.headerTitle = 'Dedicated Storage';
    this.isEmployee = this.dedicatedStorageListService.isUserEmployee();

    this.filters = this.formBuilder.group({
      offset: [0],
      limit: [10],
      filter: [null],
      customerId: [null],
      salesOrgId: [null],
    });

    this.alertService.clear();
    if (!this.isEmployee) {
      this.customerId = this.currentUserService.getCustomerId();
      this.salesOrgId = this.currentUserService.getSalesOrgId();
    }

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));

    this.privateNetworkService.getPrivateNetwork(this.customerId, this.salesOrgId).then((privateNetwork) => {
      this.privateNetwork.set(privateNetwork);
    });

    /*eslint-disable */
    const event = new Event('update_product_navbar');
    event['customerId'] = this.customerId;
    event['salesOrgId'] = this.salesOrgId;
    event['country'] = this.siteService.getCountry(this.salesOrgId);
    window.dispatchEvent(event);
    /*eslint-enable */

    if (this.customerId) {
      this.headerTitle = this.headerTitle + ' | Customer ' + this.customerId + ' | Leaseweb';
    }

    this.titleService.setTitle(this.headerTitle);
  }

  clearFilters() {
    let queryParams = null;
    this.filters.reset();

    if (this.isEmployee) {
      queryParams = { salesOrgId: this.salesOrgId, customerId: this.customerId };
      this.filters.reset(queryParams);
    }

    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams,
    });
  }

  changeRouterParams() {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.dedicatedStorageListService.sanitise(this.filters.getRawValue()),
    });
  }

  dialogFiltersEvent() {
    this.submitFilters();
  }

  getLimit() {
    return +this.filters.get('limit').value;
  }

  getOffset() {
    return +this.filters.get('offset').value;
  }

  loadDesktopItems(event?: any) {
    this.loadItems(event);
  }

  loadItems(event?: any) {
    this.filters.patchValue(event);
    this.changeRouterParams();
  }

  onPageChange(event) {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  onQueryParamsChanged(queryParams: Params) {
    if (!this.dedicatedStorageListService.isEmpty(queryParams)) {
      this.filters.patchValue(queryParams);
    }

    const params = this.dedicatedStorageListService.sanitise(this.filters.getRawValue());

    if (queryParams.customerId) {
      this.customerId = queryParams.customerId;
    }

    if (queryParams.salesOrgId) {
      this.salesOrgId = queryParams.salesOrgId;
    }

    if (queryParams.filter) {
      const filterType = this.dedicatedStorageListService.getFilterType(params.filter);
      params[filterType] = params.filter;
    }

    const isEmpty = this.dedicatedStorageListService.isEmpty(params);

    this.isFilterApplied.set(isEmpty);

    this.dedicatedStorageListService.getDedicatedStorages(params);
  }

  openToggleBondingModal(equipment: DedicatedStorage, action) {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkBondingModalV3Component, {
      equipmentId: equipment.id,
      privateNetworkId: equipment.privateNetworks[0]?.id,
      action,
    });
  }

  openAddToPrivateNetworkModal() {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkAddDedicatedStorageV3Component, {
      privateNetwork: this.privateNetwork(),
    });
  }

  openRemoveFromPrivateNetworkModal(equipment: DedicatedStorage) {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkRemoveModalV3Component, {
      privateNetwork: this.privateNetwork(),
      equipment,
      equipmentType: this.equipmentType,
    });
  }

  submitFilters() {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }
}
