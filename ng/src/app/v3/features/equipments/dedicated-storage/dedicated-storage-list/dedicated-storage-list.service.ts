import { HttpClient } from '@angular/common/http';
import { inject, Injectable, signal } from '@angular/core';
import { Params } from '@angular/router';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { Server } from 'src/app/v3/model/servers.model';
import { DedicatedStorage, DedicatedStorages } from 'src/app/v3/model/storage.model';
import { CustomValidator } from 'src/app/validators/custom.validator';

@Injectable({ providedIn: 'root' })
export class DedicatedStorageListService {
  dedicatedStorages = signal<DedicatedStorage[]>([]);
  isEmployee = signal<boolean>(false);
  isLoading = signal<boolean>(false);
  totalCount = signal<number>(0);

  private readonly currentUserService = inject(CurrentUserService);
  private readonly customValidator = inject(CustomValidator);
  private readonly httpClient = inject(HttpClient);

  getFilterType(value: string) {
    const contractItemId = /^[0-9]{14}$/;
    const serverId = /^[0-9]{1,8}$/;

    if (this.customValidator.isValidIp(value)) {
      return 'ip';
    }
    if (this.customValidator.isValidMacAddress(value)) {
      return 'macAddress';
    }
    if (contractItemId.test(value)) {
      return 'contractItemId';
    }

    if (serverId.test(value)) {
      return 'serverId';
    }

    return 'reference';
  }

  getDedicatedStorages(params: Params) {
    this.isLoading.set(true);

    const sanitizedParams = Object.entries(params).reduce(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = value;
        }
        return acc;
      },
      {} as { [key: string]: string | number | boolean }
    );

    this.httpClient
      .get<DedicatedStorages>(`/_/internal/dedicatedserverapi/v2/dedicatedStorages`, { params: sanitizedParams })
      .subscribe(
        (data: DedicatedStorages) => {
          this.isLoading.set(false);
          this.dedicatedStorages.set(data.dedicatedStorages);
          this.totalCount.set(data._metadata.totalCount);
        },
        () => {
          this.isLoading.set(false);
          this.dedicatedStorages.set([]);
          this.totalCount.set(0);
        }
      );
  }

  getBadgeSeverity(item: Server) {
    if (!item.privateNetworks || item.privateNetworks.length === 0) {
      return 'info';
    }

    const status = item.privateNetworks[0].status;

    switch (status) {
      case 'CONFIGURED':
        return 'badge-success-color';
      case 'CONFIGURING':
        return 'badge-configuring-color';
      case 'REMOVING':
        return 'badge-error-color';
      case 'ERROR':
        return 'badge-error-color';
      default:
        return 'info';
    }
  }

  isUserEmployee() {
    this.isEmployee.set(this.currentUserService.isEmployee());
    return this.isEmployee();
  }

  isEmpty(obj: object) {
    for (const prop in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, prop)) {
        return false;
      }
    }

    return true;
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
