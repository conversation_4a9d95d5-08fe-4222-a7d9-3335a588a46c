import { Component, computed, inject, OnInit, signal } from '@angular/core';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { AlertService } from 'src/app/services/alert.service';
import { FormGroup, FormBuilder } from '@angular/forms';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { ModalService } from 'src/app/services/modal.service';
import { ServerRemoteManagementModalComponent } from 'src/app/server/server-remote-management-modal/server-remote-management-modal.component';
import { PowerOperationModalV3Component } from 'src/app/v3/ui/power-operation-modal/power-operation-modal-v3.component';
import { SiteService } from 'src/app/services/site.service';
import { LocationService } from 'src/app/services/location.service';
import { PrivateNetworkAddServerModalV3Component } from 'src/app/v3/ui/private-network/private-network-add-server-modal/private-network-add-server-modal-v3.component';
import { PrivateNetworkService } from 'src/app/services/private-network.service';
import { PrivateNetworkRemoveModalV3Component } from 'src/app/v3/ui/private-network/private-network-remove-modal/private-network-remove-modal-v3.component';
import { PrivateNetwork } from 'src/app/v3/model/private-network.model';
import { Server } from 'src/app/v3/model/servers.model';
import { PrivateNetworkModifyServerSpeedModalV3Component } from 'src/app/v3/ui/private-network/private-network-modify-server-speed-modal/private-network-modify-server-speed-modal-v3.component';
import { ServerListService } from './server-list.service';
@Component({
  selector: 'app-server-list',
  templateUrl: './server-list.component.html',
  styleUrls: ['./server-list.component.css'],
})
export class ServerListComponent implements OnInit {
  customerId: string;
  equipmentType = 'servers';
  dynamicDialogRef: DynamicDialogRef;
  headerTitle: string;
  isFilterApplied = signal<boolean>(false);
  isNewFilter = signal<boolean>(false);
  isEmployee: boolean;
  filters: FormGroup;
  privateNetwork = signal<PrivateNetwork[]>([]);
  privateNetworkEnabledOptions = [true, false];
  salesOrgId: string;

  isLoading = computed(() => this.serverListService.isLoading());
  privateNetworkStatus = computed(() => this.serverListService.privateNetworkStatus());
  servers = computed(() => this.serverListService.servers());
  totalCount = computed(() => this.serverListService.totalCount());
  private serverModificationCache = new Map<string, boolean>();

  private readonly alertService = inject(AlertService);
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly currentUserService = inject(CurrentUserService);
  private readonly modalService = inject(ModalService);
  private readonly formBuilder = inject(FormBuilder);
  private readonly locationService = inject(LocationService);
  private readonly titleService = inject(Title);
  private readonly privateNetworkService = inject(PrivateNetworkService);
  private readonly router = inject(Router);
  private readonly serverListService = inject(ServerListService);
  private readonly siteService = inject(SiteService);

  ngOnInit() {
    this.headerTitle = 'Dedicated Servers';
    this.isEmployee = this.serverListService.isUserEmployee();
    this.filters = this.formBuilder.group({
      offset: [0],
      limit: [10],
      filter: [null],
      customerId: [null],
      salesOrgId: [null],
      privateNetworkEnabled: [null],
    });

    this.alertService.clear();
    if (!this.isEmployee) {
      this.customerId = this.currentUserService.getCustomerId();
      this.salesOrgId = this.currentUserService.getSalesOrgId();
    }

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));

    this.privateNetworkService.getPrivateNetwork(this.customerId, this.salesOrgId).then((privateNetwork) => {
      this.privateNetwork.set(privateNetwork);
    });
    /*eslint-disable */
    const event = new Event('update_product_navbar');
    event['customerId'] = this.customerId;
    event['salesOrgId'] = this.salesOrgId;
    event['country'] = this.siteService.getCountry(this.salesOrgId);
    window.dispatchEvent(event);
    /*eslint-enable */

    if (this.customerId) {
      this.headerTitle = this.headerTitle + ' | Customer ' + this.customerId + ' | Leaseweb';
    }

    this.titleService.setTitle(this.headerTitle);
  }

  changeRouterParams() {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.serverListService.sanitise(this.filters.getRawValue()),
    });
  }

  clearFilters() {
    const queryParams: Params = {
      offset: 0,
      limit: this.filters.get('limit').value,
    };

    if (this.isEmployee) {
      queryParams.salesOrgId = this.salesOrgId;
      queryParams.customerId = this.customerId;
    }

    this.filters.reset(queryParams);
    return this.changeRouterParams();
  }

  dialogFiltersEvent() {
    this.submitFilters();
  }

  getLimit() {
    return +this.filters.get('limit').value;
  }

  getOffset() {
    return +this.filters.get('offset').value;
  }

  getCommerceCreateContractUrl() {
    return this.serverListService.getCommerceContractUrl();
  }

  loadDesktopItems(event?: any) {
    this.loadItems(event);
  }

  loadItems(event?: any) {
    this.filters.patchValue(event);
    this.changeRouterParams();
  }

  onQueryParamsChanged(queryParams) {
    if (!this.serverListService.isEmpty(queryParams)) {
      this.filters.patchValue(queryParams);
    }

    const params = this.serverListService.sanitise(this.filters.getRawValue());

    if (queryParams.customerId) {
      this.customerId = queryParams.customerId;
    }

    if (queryParams.salesOrgId) {
      this.salesOrgId = queryParams.salesOrgId;
    }

    if (queryParams.filter) {
      const filterType = this.serverListService.getFilterType(params.filter);
      params[filterType] = params.filter;
    }

    this.isFilterApplied.set(this.serverListService.isEmpty(params));

    this.serverListService.getServers(params);
  }

  openRemoteManagementModal(serverId: string) {
    this.modalService.show(ServerRemoteManagementModalComponent, { serverId });
  }

  openPowerOperationModal(equipment: string) {
    this.modalService.show(PowerOperationModalV3Component, {
      equipment,
      equipmentType: 'servers',
    });
  }

  openAddToPrivateNetworkModal() {
    console.log('this.privateNetwork()', this.privateNetwork())
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkAddServerModalV3Component, {
      privateNetwork: this.privateNetwork(),
      serverId: '9110'
    });
  }

  openRemoveFromPrivateNetworkModal(equipment: string) {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkRemoveModalV3Component, {
      privateNetwork: this.privateNetwork(),
      equipment,
      equipmentType: this.equipmentType,
    });
  }

  openPrivateNetworkModifyServerModal(server: Server) {
    if (server?.rack?.type.includes('SHARED')) {
      window.open(
        this.serverListService.getCommerceConfigureProductUrl(server.contract.id, 'DEDSER02_MOD_PRIVATE_NETWORK')
      );
      return;
    }

    this.dynamicDialogRef = this.modalService.show(PrivateNetworkModifyServerSpeedModalV3Component, {
      privateNetwork: this.privateNetwork(),
      server,
    });
  }

  submitFilters() {
    this.changeRouterParams();
  }

  showUsage(event, serverId: number) {
    this.serverListService.showUsage(event, serverId);
  }
}
