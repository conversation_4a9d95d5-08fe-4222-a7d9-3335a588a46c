import { formatDate } from '@angular/common';
import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable, signal } from '@angular/core';
import { Params } from '@angular/router';
import { DataUsedMetric, Server, Servers } from 'src/app/v3/model/servers.model';
import { FormatSizeUnitPipe } from 'src/app/pipes/format-size-unit.pipe';
import { CommerceService } from 'src/app/services/commerce.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { CustomValidator } from 'src/app/validators/custom.validator';

@Injectable({ providedIn: 'root' })
export class ServerListService {
  isLoading = signal<boolean>(false);
  isEmployee = signal<boolean>(false);
  totalCount = signal<number>(0);
  servers = signal<Server[]>([]);
  privateNetworkStatus = signal<string>('');

  private readonly commerceService = inject(CommerceService);
  private readonly customValidator = inject(CustomValidator);
  private readonly currentUserService = inject(CurrentUserService);
  private readonly httpClient = inject(HttpClient);
  private readonly formatSizeUnitPipe = inject(FormatSizeUnitPipe);

  getCommerceContractUrl() {
    return this.commerceService.getCommerceCreateContractUrl('dedicated-server');
  }

  getServers(params: Params) {
    this.isLoading.set(true);

    this.httpClient.get<Servers>(`/_/internal/dedicatedserverapi/v2/servers`, { params }).subscribe({
      next: (data: Servers) => {
        this.isLoading.set(false);
        this.servers.set(
          data.servers.map((s) => {
            s.isSharedEol = this.isServerSharedEol(s);
            s.details = this.getServerDetails(s);
            return s;
          })
        );
        this.totalCount.set(data._metadata.totalCount);
      },
      error: () => {
        this.isLoading.set(false);
        this.servers.set([]);
        this.totalCount.set(0);
      },
    });
  }

  getBadgeSeverity(item: Server) {
    if (!item.privateNetworks || item.privateNetworks.length === 0) {
      return 'info';
    }

    const status = item.privateNetworks[0].status;

    switch (status) {
      case 'CONFIGURED':
        return 'badge-success-color';
      case 'CONFIGURING':
        return 'badge-configuring-color';
      case 'REMOVING':
        return 'badge-error-color';
      case 'ERROR':
        return 'badge-error-color';
      default:
        return 'info';
    }
  }

  getFilterType(value: string) {
    const contractItemId = /^[0-9]{14}$/;
    const serverId = /^[0-9]{1,8}$/;

    if (this.customValidator.isValidIp(value)) {
      return 'ip';
    }
    if (this.customValidator.isValidMacAddress(value)) {
      return 'macAddress';
    }
    if (contractItemId.test(value)) {
      return 'contractItemId';
    }

    if (serverId.test(value)) {
      return 'serverId';
    }

    return 'reference';
  }

  getServerDetails(server: Server) {
    const details = [];
    if (server.specs.chassis) {
      details.push(server.specs.chassis);
    }

    const cpuRamDetails = [];
    if (server.specs.cpu.quantity > 0 && server.specs.cpu.type) {
      cpuRamDetails.push(server.specs.cpu.quantity + 'x ' + server.specs.cpu.type);
    }
    if (server.specs.ram.size && server.specs.ram.unit) {
      cpuRamDetails.push(server.specs.ram.size + server.specs.ram.unit);
    }
    if (cpuRamDetails.length > 0) {
      details.push(cpuRamDetails.join(' - '));
    }

    if (server.specs.hdd.length > 0) {
      const hddDetails = [];
      server.specs.hdd.forEach((hdd) => hddDetails.push(hdd.amount + 'x' + hdd.size + hdd.unit + ' ' + hdd.type));
      details.push(hddDetails.join(' - '));
    }

    return details;
  }

  getCommerceConfigureProductUrl(contractId: string, mod: string) {
    return this.commerceService.getCommerceConfigureProductUrl(contractId, mod);
  }

  getCookie(name: string) {
    const match = document.cookie
      .split('; ')
      .find((row) => row.startsWith(name + '='));
      console.log('match', match);
    return match ? decodeURIComponent(match.split('=')[1]) : null;
  }

  isServerSharedEol(server: Server) {
    if (!server.contract) {
      return false;
    }

    return server.contract.salesOrgId === '1700' && server.rack.type === 'SHARED_EOL';
  }

  isUserEmployee() {
    this.isEmployee.set(this.currentUserService.isEmployee());
    return this.isEmployee();
  }

  isEmpty(obj: object) {
    for (const prop in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, prop)) {
        return false;
      }
    }

    return true;
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (!isNaN(+val) && val !== null) {
        acc[key] = val;
        return acc;
      }

      if (typeof val === 'boolean') {
        acc[key] = val;
        return acc;
      }

      if (val && val !== 'null') {
        acc[key] = val;
      }

      return acc;
    }, {});
  }

  serverCanBeModified(server: Server) {
    if (server?.privateNetworks?.length > 0 && server?.privateNetworks[0]?.status !== 'CONFIGURED') {
      return false;
    }
    if (server?.rack.type === 'DEDICATED') {
      return true;
    }
    if (server?.privateNetworks?.length === 0) {
      if (this.isEmployee()) {
        return true;
      }
      return false;
    }
    if (this.isEmployee()) {
      return true;
    }
    return true;
  }

  async showUsage(event, serverId: number) {
    let equipment: Server;
    const target = event.target || event.srcElement || event.currentTarget;
    if (target.classList.contains('shown')) {
      return;
    }

    target.innerText = $localize`:@@bma_common_loading:Loading...`;
    target.className = 'shown';

    await this.httpClient
      .get<Server>(`/_/internal/dedicatedserverapi/v2/servers/${serverId}`)
      .toPromise()
      .then(
        (server: Server) => {
          equipment = server;
        },
        () => {
          target.parentElement.innerText = $localize`:@@bma_common_error:error`;
        }
      );

    const date = new Date();
    const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
    const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    const from = formatDate(firstDay, 'yyyy-MM-dd', 'en-US');
    const to = formatDate(lastDay, 'yyyy-MM-dd', 'en-US');

    let params = new HttpParams();

    params = params.set('from', from);
    params = params.set('to', to);

    switch (equipment.contract.networkTraffic.type) {
      case '95TH':
      case 'FLATFEE':
        params = params.set('aggregation', '95TH');
        this.httpClient
          .get<Servers>(`/_/bareMetals/v2/servers/${equipment.id}/metrics/bandwidth`, { params })
          .subscribe(
            (usage: any) => {
              if (usage) {
                target.innerText =
                  this.formatSizeUnitPipe.transform(usage.metrics.PUBLIC.values[0].value, false, 'bytes/s') + ' 95th';
              }
            },
            () => {
              target.innerText = $localize`:@@bma_common_error:error`;
            }
          );
        break;
      case 'DATATRAFFIC':
        params = params.set('aggregation', 'SUM');
        this.httpClient
          .get<DataUsedMetric>(`/_/bareMetals/v2/servers/${equipment.id}/metrics/datatraffic`, { params })
          .subscribe(
            (usage: DataUsedMetric) => {
              if (usage) {
                target.innerText = this.formatSizeUnitPipe.transform(
                  usage.metrics.DOWN_PUBLIC.values[0].value + usage.metrics.UP_PUBLIC.values[0].value,
                  false,
                  'bytes'
                );
              }
            },
            () => {
              target.innerText = $localize`:@@bma_common_error:error`;
            }
          );
        break;

      default:
        target.innerText = $localize`:@@bma_common_notavailable:Not Available`;
    }
  }

  setCookie(name: string, value: string, days: number = 1) {
    const expires = new Date(Date.now() + days * 86400000).toUTCString();
    document.cookie =
      name + '=' + encodeURIComponent(value) + '; expires=' + expires + '; path=/';
  }
  
    /*setCookie(name: string, value: string, days: number) {
    // 60000 milliseconds = 1 minute
    const expires = new Date(Date.now() + 300000).toUTCString();
    document.cookie =
      name + '=' + encodeURIComponent(value) + '; expires=' + expires + '; path=/';
    }*/
}
