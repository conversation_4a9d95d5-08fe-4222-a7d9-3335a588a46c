.banner-container {
  border: 1px solid #5685c4;
  display: flex;
  justify-content: space-between;
  background-color: white;
  padding: 20px;
  width: 100%;
}

:host ::ng-deep .p-accordion .p-accordion-content {
  padding: 0.5rem;
}
.grid-content {
  margin-bottom: 5px;
  padding-bottom: 4px;
  padding-left: 2px;
  font-size: 0.8rem !important;
}
.grid-content-border {
  border-bottom: 1px solid #ddd;
}
.grid-row {
  padding-right: 10px;
}
.show-data {
  cursor: pointer;
}

.quick-actions a,
.quick-actions button {
  padding-top: 4px;
  padding-bottom: 4px;
  font-size: 0.9rem;
  text-align: left;
}
.disabled {
  color: #aaa !important;
  pointer-events: none;
  cursor: not-allowed;
  opacity: 0.6;
}
.disabled * {
  color: #aaa !important;
  pointer-events: none;
  cursor: not-allowed;
}
.server-details {
  font-size: 0.75em;
  font-weight: 500;
}
::ng-deep .p-badge.badge-redundant-color {
  background-color: #feca57;
  color: #ffffff;
}
::ng-deep .p-badge.badge-enabled-color {
  background-color: #5685c4;
  color: #ffffff;
}
::ng-deep .p-badge.badge-success-color {
  background-color: #83b91e;
  color: #ffffff;
}
::ng-deep .p-badge.badge-configuring-color {
  background-color: #f28702;
  color: #ffffff;
}
::ng-deep .p-badge.badge-error-color {
  background-color: #c4153d;
  color: #ffffff;
}
