<lsw-page-header primaryTitle="Dedicated Server" i18n-secondaryTitle="@@bma_serverlist_manage_dedicated_server" secondaryTitle="Manage your Dedicated Server">
    <div action class="flex flex-row gap-3">
        <p-button i18n-label="@@bma_common_addtoprivatenetwork" type="button" label="Add to Private Network" (click)="openAddToPrivateNetworkModal()" styleClass="p-button-secondary" icon="pi pi-plus"></p-button>
        <a pButton *ngIf="!isEmployee" i18n-label="@@bma_serverlist_adddedicatedserver" label="Add Dedicated Server" target="_blank" [href]="getCommerceCreateContractUrl()" icon="pi pi-shopping-cart"></a>
    </div>
</lsw-page-header>

<!-- Banner with no close button -->
<!--<lsw-message *ngIf="showBanner" class="flex">
    <p i18n="@@bma_poweroperationmodal_description1" class="ml-1 mt-0">By periodically performing a hardware scan in your dedicated server, you will be able to gather completely up-to-date information not just about inventory, but about the whole hardware customizations present in your dedicated server.</p>
</lsw-message>-->

<!-- Banner with close button -->
<!--<lsw-message *ngIf="showBanner">
    <div class="banner-container">
        <p i18n="@@bma_poweroperationmodal_description1" class="ml-1 mt-0">By periodically performing a hardware scan in your dedicated server, you will be able to gather completely up-to-date information not just about inventory, but about the whole hardware customizations present in your dedicated server.</p>
        <i class="pi pi-times banner-close-button" (click)="closeBanner()"></i>  
    </div>
</lsw-message>-->

<br>

<lsw-data-view [items]="servers()" [updatedItems]="servers()" [totalItemCount]="totalCount()" [accordionContentTemplate]="accordionContentTemplate" [accordionContentHiddenTemplate]="accordionContentHiddenTemplate" [accordionRequired]="true" [enableCardListTopbarView]="true" [enableTopbarViewFilters]="true" [errorLoadingDynamicItems]="false" [filtersDialogTemplate]="filterDialogTemplate" [isFilterApplied]="isFilterApplied()" [isNewFilter]="isNewFilter()" [filterForm]="filters" [isLoading]="isLoading()" [desktopPaginationOffset]="getOffset()" [desktopPaginationLimit]="getLimit()" (loadDesktopItemsFromParent)="loadDesktopItems($event)" (loadItemsFromParent)="loadItems($event)" (clearFiltersEvent)="clearFilters()" (dialogFiltersEvent)="dialogFiltersEvent()" />

<ng-template #accordionContentTemplate let-item let-triggerAction="triggerAction">
    <div class="grid nested-grid m-0">
        <div class="col-12 pb-0">
            <div class="grid">
                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_serverid">Server ID</span>
                    <a [href]="locationService.getAbsoluteUrlWithBase('/servers/' + item.id)">{{ item.id ? item.id : '-' }}</a>
                </div>
                <div class="col-12 lg:col-6 pl-3 py-1">
                    <div class="flex flex-row gap-3">
                        <span class="font-bold" i18n="@@bma_common_actions">Actions</span>
                        <div class="flex flex-wrap gap-3">
                            <p-button i18n-label="@@bma_common_poweroperation" label="Power Operation" size="small" icon="pi pi-power-off" styleClass="p-button-tertiary py-0 px-1" [class.disabled]="(!item.featureAvailability.powerCycle && !item.featureAvailability.ipmiReboot)" (click)="openPowerOperationModal(item)"></p-button>
                            <p-button i18n-label="@@bma_common_datagraphs" label="Datagraphs" size="small" icon="fa fa-graph-bar" styleClass="p-button-tertiary py-0 px-1" *ngIf="(item?.networkInterfaces?.public|default:'') && (item?.networkInterfaces?.public?.ports|default:'')" label="Datagraphs" class="p-button-link" [class.disabled]="item.isSharedEol" (click)="locationService.navigateToWithBase('/servers/' + item.id + '/graphs')"></p-button>
                            <p-button i18n-label="@@bma_common_credentials" label="Credentials" size="small" icon="pi pi-lock" styleClass="p-button-tertiary py-0 px-1" (click)="locationService.navigateToWithBase('/servers/' + item.id + '/credentials')"></p-button>
                            <p-button i18n-label="@@bma_common_createticket" label="Create Ticket" size="small" icon="pi pi-ticket" styleClass="p-button-tertiary py-0 px-1" *ngIf="!isEmployee" (click)="locationService.navigateTo('/tickets/new?equipmentId=' + item.id + '&category=technical-assistance')"></p-button>
                            <p-button i18n-label="@@bma_common_acronisbackup" label="Acronis Backup" size="small" icon="pi pi-wave-pulse" styleClass="p-button-tertiary py-0 px-1" *ngIf="!isEmployee" (click)="locationService.navigateTo('/backup/#/order/servers')"></p-button>
                        </div>
                    </div>
                </div>

                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_location">Location</span>
                    <app-equipment-location [location]="item.location"></app-equipment-location>
                </div>

                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_privatenetwork">Private Network</span>
                    <p-badge
                        *ngIf="item.privateNetworks?.length > 0"
                        [value]="item?.isRedundantPrivateNetworkCapable && item.privateNetworks?.length !== 0 ? 'REDUNDANT' : 'ENABLED'"
                        [styleClass]="
                    item?.isRedundantPrivateNetworkCapable && item.privateNetworks?.length !== 0 ? 'badge-redundant-color' : 'badge-enabled-color'"></p-badge>
                    <span *ngIf="item.privateNetworks?.length === 0" i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
                </div>

                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_serverid">IP Address</span>
                    <ng-container *ngIf="(item?.networkInterfaces?.public && item?.networkInterfaces?.public?.ip)">
                        <span class="selectable">{{ item.networkInterfaces.public.ip|cidrToIp }}</span>
                    </ng-container>
                    <span *ngIf="!item?.networkInterfaces?.internal" class="text-muted">-</span>
                </div>

                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_status">Private Network Status</span>
                    <p-badge *ngIf="item.privateNetworks.length > 0" [value]="item.privateNetworks[0].status.toUpperCase()" [styleClass]="serverListService.getBadgeSeverity(item)"> </p-badge>
                    <span *ngIf="item.privateNetworks.length === 0" class="text-muted" i18n="@@bma_common_not_configured">-</span>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #accordionContentHiddenTemplate let-item let-triggerAction="triggerAction">
    <div class="grid nested-grid m-0">
        <div class="col-12">
            <div class="grid">
                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_dataused">Data Used</span>
                    <span class="show-data" (click)="showUsage($event, item.id)" *ngIf="(item.networkInterfaces.public|default:'') && (item.networkInterfaces.public.ports|default:''); else notAvailable" [class.disabled]="item.isSharedEol">
                        <strong class="text-primary show-data" i18n="@@bma_common_show">Show</strong>
                    </span>
                </div>
                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_subnet">Subnet</span>
                    <span *ngIf="item?.privateNetworks.length > 0 && item?.privateNetworks[0].dhcp === 'ENABLED'">{{ item?.privateNetworks[0]?.subnet }}</span>
                    <span *ngIf="item?.privateNetworks.length > 0 && item?.privateNetworks[0].dhcp === 'DISABLED'" i18n="@@bma_common_dhcpdisabled" class="text-muted">DHCP disabled</span>
                    <span *ngIf="item?.privateNetworks.length === 0" class="text-muted">-</span>
                </div>
                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_serial">Serial</span>
                    <ng-container *ngIf="item?.serialNumber|default">
                        {{ item.serialNumber|default:'-' }}
                    </ng-container>
                </div>
                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_portspeed">Port Speed</span>
                    <span>{{ item?.privateNetworks.length > 0 ? (item?.privateNetworks[0]?.linkSpeed | formatSpeed) : '-' }}</span>
                    &nbsp;&nbsp;
                    <span *ngIf="serverListService.serverCanBeModified(item) && item?.privateNetworks.length > 0" (click)="openPrivateNetworkModifyServerModal(item)">
                        <strong class="text-primary show-data" i18n="@@bma_common_modify">Modify</strong>
                    </span>
                </div>
                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_model">Model</span>
                    {{ item?.specs?.chassis|default:'-' }}
                </div>
                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_vlanid">Vlan ID</span>
                    {{ item?.privateNetworks[0]?.vlanId|default:'-' }}
                </div>

                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_reference">Reference</span>
                    {{ item?.contract?.reference ? item?.contract?.reference : '-' }}
                </div>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #filterDialogTemplate>
    <form [formGroup]="filters">
        <div class="flex flex-column field p-4">
            <label for="reference" i18n="@@bma_common_search">Search</label>
            <div class="flex gap-1 md:gap-2 mb-7">
                <p-iconField iconPosition="right" class="flex-grow-1">
                    <p-inputIcon styleClass="pi pi-search" />
                    <input pInputText id="filter" class="w-full" formControlName="filter" placeholder="Search for Reference, Server ID, IP, MAC Address or Contract ID" i18n-placeholder="@@bma_serverlist_searchbyparams" />
                </p-iconField>
            </div>
            <label for="privateNetworkEnabled">Private Network</label>
            <div class="flex md:gap-2 mb-7 align-items-center gap-2 w-max">
                <p-dropdown id="privateNetworkEnabled" formControlName="privateNetworkEnabled" [options]="privateNetworkEnabledOptions" i18n-placeholder="@@bma_serverlist_filterprivatenetwork" placeholder="Is part of a Private Network?" [showClear]="true">
                    <ng-template pTemplate="selectedItem">
                        <div class="flex align-items-center gap-2">
                            <div>{{ filters.get('privateNetworkEnabled').value.toString() === "true" ? "Yes" : "No"  }}</div>
                        </div>
                    </ng-template>
                    <ng-template let-option pTemplate="item">
                        <div class="flex align-items-center gap-2">
                            <div>{{ option.toString() === "true" ? "Yes" : "No"  }}</div>
                        </div>
                    </ng-template>
                </p-dropdown>
            </div>
        </div>
    </form>
</ng-template>
