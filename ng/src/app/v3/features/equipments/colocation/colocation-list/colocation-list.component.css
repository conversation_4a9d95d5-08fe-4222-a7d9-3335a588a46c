.show-data {
  cursor: pointer;
}
.disabled {
  color: #aaa !important;
  pointer-events: none;
  cursor: not-allowed;
  opacity: 0.6;
}
.disabled * {
  color: #aaa !important;
  pointer-events: none;
  cursor: not-allowed;
}
::ng-deep .p-badge.badge-redundant-color {
  background-color: #feca57;
  color: #ffffff;
}
::ng-deep .p-badge.badge-enabled-color {
  background-color: #5685c4;
  color: #ffffff;
}
::ng-deep .p-badge.badge-success-color {
  background-color: #83b91e;
  color: #ffffff;
}
::ng-deep .p-badge.badge-configuring-color {
  background-color: #f28702;
  color: #ffffff;
}
::ng-deep .p-badge.badge-error-color {
  background-color: #c4153d;
  color: #ffffff;
}
