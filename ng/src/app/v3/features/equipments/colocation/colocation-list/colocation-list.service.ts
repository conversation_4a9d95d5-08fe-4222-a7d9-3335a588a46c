import { formatDate } from '@angular/common';
import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable, signal } from '@angular/core';
import { Params } from '@angular/router';
import { FormatSizeUnitPipe } from 'src/app/pipes/format-size-unit.pipe';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { Colocation, Colocations, DataUsedMetric, DataUsedMetricV2 } from 'src/app/v3/model/colocations.model';
import { Server } from 'src/app/v3/model/servers.model';
import { CustomValidator } from 'src/app/validators/custom.validator';

@Injectable({ providedIn: 'root' })
export class ColocationListService {
  customerId: string;
  colocations = signal<Colocation[]>([]);
  isLoading = signal<boolean>(false);
  isEmployee = signal<boolean>(false);
  totalCount = signal<number>(0);
  remoteHandsType = signal<string>('');
  handsType = 'NA';
  remoteHands: any;
  salesOrgId: string;

  private readonly customValidator = inject(CustomValidator);
  private readonly currentUserService = inject(CurrentUserService);
  private readonly formatSizeUnitPipe = inject(FormatSizeUnitPipe);
  private readonly httpClient = inject(HttpClient);

  getFilterType(value: string) {
    if (this.customValidator.isValidIp(value)) {
      return 'ip';
    }
    if (this.customValidator.isValidMacAddress(value)) {
      return 'macAddress';
    }

    return 'reference';
  }

  getColocations(params: Params) {
    this.isLoading.set(true);

    this.httpClient.get<Colocations>(`/_/internal/dedicatedserverapi/v2/colocations`, { params }).subscribe(
      (data: Colocations) => {
        if (data) {
          this.getRemoteHandsInformation();
          data.colocations.forEach((colocation) => this.getDetails(colocation.id));
        }
        this.isLoading.set(false);
        this.colocations.set(data.colocations);
        this.totalCount.set(data._metadata.totalCount);
      },
      () => {
        this.isLoading.set(false);
        this.colocations.set([]);
        this.totalCount.set(0);
      }
    );
  }

  getRemoteHandsInformation() {
    let params = new HttpParams();
    if (this.isEmployee) {
      params = params.set('customerId', this.customerId);
      params = params.set('salesOrgId', this.salesOrgId);
    }
    this.httpClient.get('/_legacy/remotehands/options', { params }).subscribe((data: any) => {
      if (data && Object.keys(data.remoteHandsInformation).length > 0) {
        this.remoteHands = data.remoteHandsInformation;
        if (this.remoteHands.configuration.remoteHands.type) {
          this.handsType = this.remoteHands.configuration.remoteHands.type.toUpperCase();
        }
      }
    });
  }

  isUserEmployee() {
    this.isEmployee.set(this.currentUserService.isEmployee());
    return this.isEmployee();
  }

  isEmpty(obj: object) {
    for (const prop in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, prop)) {
        return false;
      }
    }

    return true;
  }

  getDetails(colocationId: string) {
    this.httpClient
      .get<Colocation>(`/_/internal/dedicatedserverapi/v2/colocations/${colocationId}`)
      .toPromise()
      .then(
        (colocation: Colocation) => {
          if (colocation?.contract?.remoteHands.type && colocation.contract.remoteHands.type.length > 0) {
            this.remoteHandsType.set(colocation.contract.remoteHands.type);
          } else {
            this.remoteHandsType.set($localize`:@@bma_common_error:error`);
          }
        },
        () => {
          this.remoteHandsType.set($localize`:@@bma_common_error:error`);
        }
      );
  }

  getBadgeSeverity(item: Server) {
    if (!item.privateNetworks || item.privateNetworks.length === 0) {
      return 'info';
    }

    const status = item.privateNetworks[0].status;

    switch (status) {
      case 'CONFIGURED':
        return 'badge-success-color';
      case 'CONFIGURING':
        return 'badge-configuring-color';
      case 'REMOVING':
        return 'badge-error-color';
      case 'ERROR':
        return 'badge-error-color';
      default:
        return 'info';
    }
  }

  async showUsage(event, colocationId: number) {
    let equipment: Colocation;
    const target = event.target || event.srcElement || event.currentTarget;

    if (target.classList.contains('shown')) {
      return;
    }

    target.innerText = $localize`:@@bma_common_loading:Loading...`;
    target.className = 'shown';

    await this.httpClient
      .get<Colocation>(`/_/internal/dedicatedserverapi/v2/colocations/${colocationId}`)
      .toPromise()
      .then(
        (colocation: Colocation) => {
          equipment = colocation;
        },
        () => {
          target.parentElement.innerText = $localize`:@@bma_common_error:error`;
        }
      );

    const date = new Date();
    const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
    const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    const from = formatDate(firstDay, 'yyyy-MM-dd', 'en-US');
    const to = formatDate(lastDay, 'yyyy-MM-dd', 'en-US');

    let params = new HttpParams();

    params = params.set('from', from);
    params = params.set('to', to);

    if (equipment.contract) {
      switch (equipment.contract.networkTraffic.type) {
        case '95TH':
        case 'FLATFEE':
        case 'BANDWIDTH_UNMETERED':
          params = params.set('aggregation', '95TH');

          this.httpClient
            .get<DataUsedMetric>(`/_/bareMetals/v2/colocations/${equipment.id}/metrics/bandwidth`, { params })
            .subscribe(
              (usage: DataUsedMetric) => {
                if (usage) {
                  target.parentElement.innerText =
                    this.formatSizeUnitPipe.transform(usage.metrics.PUBLIC.values[0].value, false, 'bytes/s') + ' 95th';
                }
              },
              () => {
                target.parentElement.innerText = $localize`:@@bma_common_error:error`;
              }
            );
          break;
        case 'DATATRAFFIC':
          params = params.set('aggregation', 'SUM');
          this.httpClient
            .get<DataUsedMetricV2>(`/_/bareMetals/v2/colocations/${equipment.id}/metrics/datatraffic`, { params })
            .subscribe(
              (usage: DataUsedMetricV2) => {
                if (usage) {
                  target.parentElement.innerText = this.formatSizeUnitPipe.transform(
                    usage.metrics.DOWN_PUBLIC.values[0].value + usage.metrics.UP_PUBLIC.values[0].value,
                    false,
                    'bytes'
                  );
                }
              },
              () => {
                target.parentElement.innerText = 'error';
              }
            );
          break;

        default:
          target.parentElement.innerText = $localize`:@@bma_common_notavailable:Not Available`;
      }
    }
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (!isNaN(+val) && val !== null) {
        acc[key] = val;
        return acc;
      }

      if (typeof val === 'boolean') {
        acc[key] = val;
        return acc;
      }

      if (val && val !== 'null') {
        acc[key] = val;
      }

      return acc;
    }, {});
  }
}
