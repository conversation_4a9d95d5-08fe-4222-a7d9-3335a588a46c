import { Component, computed, inject, OnInit, signal } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { PrivateNetwork } from 'src/app/v3/model/private-network.model';
import { Title } from '@angular/platform-browser';
import { FormBuilder, FormGroup } from '@angular/forms';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { AlertService } from 'src/app/services/alert.service';
import { PrivateNetworkService } from 'src/app/services/private-network.service';
import { SiteService } from 'src/app/services/site.service';
import { LocationService } from 'src/app/services/location.service';
import { Colocation } from 'src/app/v3/model/colocations.model';
import { PrivateNetworkRemoveModalV3Component } from 'src/app/v3/ui/private-network/private-network-remove-modal/private-network-remove-modal-v3.component';
import { PrivateNetworkAddColocationV3Component } from 'src/app/v3/ui/private-network/private-network-add-colocation-modal/private-network-add-colocation-modal.component-v3';
import { PrivateNetworkColocationPowerCycleV3Component } from 'src/app/v3/ui/private-network/private-network-colocation-power-cycle-modal/private-network-colocation-power-cycle-modal.component';
import { ColocationListService } from './colocation-list.service';

@Component({
  selector: 'app-colocations-list',
  templateUrl: './colocation-list.component.html',
  styleUrls: ['./colocation-list.component.css'],
})
export class ColocationListComponent implements OnInit {
  customerId: string;
  dynamicDialogRef: DynamicDialogRef;
  equipmentType = 'colocations';
  filters: FormGroup;
  headerTitle: string;
  privateNetwork = signal<PrivateNetwork[]>([]);
  isFilterApplied = signal<boolean>(false);
  isNewFilter = signal<boolean>(false);
  isEmployee: boolean;
  salesOrgId: string;

  colocations = computed(() => this.colocationListService.colocations());
  isLoading = computed(() => this.colocationListService.isLoading());
  totalCount = computed(() => this.colocationListService.totalCount());
  remoteHandsType = computed(() => this.colocationListService.remoteHandsType());

  private readonly alertService = inject(AlertService);
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly colocationListService = inject(ColocationListService);
  private readonly currentUserService = inject(CurrentUserService);
  private readonly locationService = inject(LocationService);
  private readonly modalService = inject(ModalService);
  private readonly formBuilder = inject(FormBuilder);
  private readonly titleService = inject(Title);
  private readonly privateNetworkService = inject(PrivateNetworkService);
  private readonly router = inject(Router);
  private readonly siteService = inject(SiteService);

  ngOnInit() {
    this.headerTitle = 'Colocations';
    this.isEmployee = this.colocationListService.isUserEmployee();
    this.filters = this.formBuilder.group({
      offset: [0],
      limit: [10],
      filter: [null],
      customerId: [null],
      salesOrgId: [null],
    });

    this.alertService.clear();
    if (!this.isEmployee) {
      this.customerId = this.currentUserService.getCustomerId();
      this.salesOrgId = this.currentUserService.getSalesOrgId();
    }

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));

    this.privateNetworkService.getPrivateNetwork(this.customerId, this.salesOrgId).then((privateNetwork) => {
      this.privateNetwork = privateNetwork;
    });
    /*eslint-disable */
    const event = new Event('update_product_navbar');
    event['customerId'] = this.customerId;
    event['salesOrgId'] = this.salesOrgId;
    event['country'] = this.siteService.getCountry(this.salesOrgId);
    window.dispatchEvent(event);
    /*eslint-enable */

    if (this.customerId) {
      this.headerTitle = this.headerTitle + ' | Customer ' + this.customerId + ' | Leaseweb';
    }

    this.titleService.setTitle(this.headerTitle);
  }

  clearFilters() {
    const queryParams: Params = {
      offset: 0,
      limit: this.filters.get('limit').value,
    };

    if (this.isEmployee) {
      queryParams.salesOrgId = this.salesOrgId;
      queryParams.customerId = this.customerId;
    }

    this.filters.reset(queryParams);
    return this.changeRouterParams();
  }

  changeRouterParams() {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.colocationListService.sanitise(this.filters.getRawValue()),
    });
  }

  dialogFiltersEvent() {
    this.submitFilters();
  }

  getLimit() {
    return +this.filters.get('limit').value;
  }

  getOffset() {
    return +this.filters.get('offset').value;
  }

  loadDesktopItems(event?: any) {
    this.loadItems(event);
  }

  loadItems(event?: any) {
    this.filters.patchValue(event);
    this.changeRouterParams();
  }

  manageRemoteHandsPackages() {
    let params = {};
    if (this.isEmployee) {
      params = {
        customerId: this.customerId,
        salesOrgId: this.salesOrgId,
      };
    }
    this.router.navigate(['remoteHands'], {
      queryParams: params,
    });
  }

  onQueryParamsChanged(queryParams) {
    if (!this.colocationListService.isEmpty(queryParams)) {
      this.filters.patchValue(queryParams);
    }

    const params = this.colocationListService.sanitise(this.filters.getRawValue());

    if (queryParams.customerId) {
      this.customerId = queryParams.customerId;
    }

    if (queryParams.salesOrgId) {
      this.salesOrgId = queryParams.salesOrgId;
    }

    if (queryParams.filter) {
      const filterType = this.colocationListService.getFilterType(params.filter);
      params[filterType] = params.filter;
    }

    this.isFilterApplied.set(this.colocationListService.isEmpty(params));

    this.colocationListService.getColocations(params);
  }

  openAddToPrivateNetworkModal() {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkAddColocationV3Component, {
      privateNetwork: this.privateNetwork,
    });
  }

  openPowerCycleModal(colocationId: string) {
    this.modalService.show(PrivateNetworkColocationPowerCycleV3Component, {
      colocationId,
    });
  }

  openRemoveFromPrivateNetworkModal(equipment: Colocation) {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkRemoveModalV3Component, {
      privateNetwork: this.privateNetwork,
      equipment,
      equipmentType: this.equipmentType,
    });
  }

  submitFilters() {
    this.changeRouterParams();
  }

  showUsage(event, serverId: number) {
    this.colocationListService.showUsage(event, serverId);
  }
}
