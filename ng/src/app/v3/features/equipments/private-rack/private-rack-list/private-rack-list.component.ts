import { Component, computed, inject, OnInit, signal } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { AlertService } from 'src/app/services/alert.service';
import { UntypedFormBuilder, FormGroup } from '@angular/forms';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { ModalService } from 'src/app/services/modal.service';
import { SiteService } from 'src/app/services/site.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { PrivateNetworkService } from 'src/app/services/private-network.service';
import { LocationService } from 'src/app/services/location.service';
import { PrivateNetworkRemoveModalV3Component } from 'src/app/v3/ui/private-network/private-network-remove-modal/private-network-remove-modal-v3.component';
import { PrivateNetworkAddRackModalV3Component } from 'src/app/v3/ui/private-network/private-network-add-rack-modal/private-network-add-rack-modal-v3.component';
import { PrivateNetwork } from 'src/app/v3/model/private-network.model';
import { PrivateRackListService } from './private-rack-list.service';

@Component({
  selector: 'app-private-rack-list',
  templateUrl: './private-rack-list.component.html',
  styleUrls: ['./private-rack-list.component.css'],
})
export class PrivateRackListComponent implements OnInit {
  customerId: string;
  dynamicDialogRef: DynamicDialogRef;
  filters: FormGroup;
  equipmentType = 'privateRacks';
  headerTitle: string;
  isEmployee: boolean;
  isFilterApplied = signal<boolean>(false);
  isNewFilter = signal<boolean>(false);
  privateNetwork: PrivateNetwork;
  salesOrgId: string;

  isLoading = computed(() => this.privateRackListService.isLoading());
  privateRacks = computed(() => this.privateRackListService.racks());
  totalCount = computed(() => this.privateRackListService.totalCount());

  readonly locationService = inject(LocationService);
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly alertService = inject(AlertService);
  private readonly currentUserService = inject(CurrentUserService);
  private readonly formBuilder = inject(UntypedFormBuilder);
  private readonly privateNetworkService = inject(PrivateNetworkService);
  private readonly privateRackListService = inject(PrivateRackListService);
  private readonly modalService = inject(ModalService);
  private readonly router = inject(Router);
  private readonly titleService = inject(Title);
  private readonly siteService = inject(SiteService);

  ngOnInit() {
    this.headerTitle = 'Dedicated Racks';
    this.isEmployee = this.privateRackListService.isUserEmployee();
    this.filters = this.formBuilder.group({
      offset: [0],
      limit: [10],
      filter: [null],
      customerId: [null],
      salesOrgId: [null],
      privateNetworkEnabled: [null],
    });

    this.alertService.clear();
    if (!this.isEmployee) {
      this.customerId = this.currentUserService.getCustomerId();
      this.salesOrgId = this.currentUserService.getSalesOrgId();
    }

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));

    this.privateNetworkService.getPrivateNetwork(this.customerId, this.salesOrgId).then((privateNetwork) => {
      this.privateNetwork = privateNetwork;
    });
    /*eslint-disable */
    const event = new Event('update_product_navbar');
    event['customerId'] = this.customerId;
    event['salesOrgId'] = this.salesOrgId;
    event['country'] = this.siteService.getCountry(this.salesOrgId);
    window.dispatchEvent(event);
    /*eslint-enable */

    if (this.customerId) {
      this.headerTitle = `${this.headerTitle} | Customer ${this.customerId}`;
    }

    this.titleService.setTitle(`${this.headerTitle} | Leaseweb`);
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.privateRackListService.sanitise(this.filters.getRawValue()),
    });
  }

  clearFilters() {
    const queryParams: Params = {
      offset: 0,
      limit: this.filters.get('limit').value,
    };

    if (this.isEmployee) {
      queryParams.salesOrgId = this.salesOrgId;
      queryParams.customerId = this.customerId;
    }

    this.filters.reset(queryParams);
    return this.changeRouterParams();
  }

  dialogFiltersEvent() {
    this.submitFilters();
  }

  getLimit() {
    return +this.filters.get('limit').value;
  }

  getOffset() {
    return +this.filters.get('offset').value;
  }

  loadDesktopItems(event?: any) {
    this.loadItems(event);
  }

  loadItems(event?: any) {
    this.filters.patchValue(event);
    this.changeRouterParams();
  }

  onQueryParamsChanged(queryParams): void {
    if (!this.privateRackListService.isEmpty(queryParams)) {
      this.filters.patchValue(queryParams);
    }

    const params = this.privateRackListService.sanitise(this.filters.getRawValue());

    if (queryParams.customerId) {
      this.customerId = queryParams.customerId;
    }

    if (queryParams.salesOrgId) {
      this.salesOrgId = queryParams.salesOrgId;
    }

    if (params.filter) {
      const filterType = this.privateRackListService.getFilterType(params.filter);
      params[filterType] = params.filter;
    }

    this.isFilterApplied.set(this.privateRackListService.isEmpty(params));

    this.privateRackListService.getRacks(params);
  }

  openAddToPrivateNetworkModal() {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkAddRackModalV3Component, {
      privateNetwork: this.privateNetwork,
    });
  }

  openRemoveFromPrivateNetworkModal(equipment: string) {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkRemoveModalV3Component, {
      privateNetwork: this.privateNetwork,
      equipment,
      equipmentType: this.equipmentType,
    });
  }

  showUsage(event, serverId: number) {
    this.privateRackListService.showUsage(event, serverId);
  }

  submitFilters(): void {
    this.changeRouterParams();
  }
}
