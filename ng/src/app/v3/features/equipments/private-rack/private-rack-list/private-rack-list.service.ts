import { formatDate } from '@angular/common';
import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable, signal } from '@angular/core';
import { Params } from '@angular/router';
import { FormatSizeUnitPipe } from 'src/app/pipes/format-size-unit.pipe';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { PrivateRack, PrivateRacks } from 'src/app/v3/model/racks.model';

@Injectable({ providedIn: 'root' })
export class PrivateRackListService {
  isLoading = signal<boolean>(false);
  isEmployee = signal<boolean>(false);
  totalCount = signal<number>(0);
  racks = signal<PrivateRack[]>([]);

  private readonly currentUserService = inject(CurrentUserService);
  private readonly formatSizeUnitPipe = inject(FormatSizeUnitPipe);
  private readonly httpClient = inject(HttpClient);

  getRacks(params: Params) {
    this.isLoading.set(true);

    this.httpClient.get<any>(`/_/internal/dedicatedserverapi/v2/privateRacks`, { params }).subscribe(
      (data: PrivateRacks) => {
        this.racks.set(data.privateRacks);
        this.totalCount.set(data._metadata.totalCount);
        this.isLoading.set(false);
      },
      () => {
        this.isLoading.set(false);
        this.racks.set([]);
        this.totalCount.set(0);
      }
    );
  }

  getFilterType(value: string) {
    const site = /^[A-Z]{3}-[0-9]{2}$/;
    const privateRackId = /^[0-9]+$/;

    if (site.test(value)) {
      return 'site';
    }

    if (privateRackId.test(value)) {
      return 'privateRackId';
    }

    return 'reference';
  }

  getBadgeSeverity(item: PrivateRack) {
    if (!item.privateNetworks || item.privateNetworks.length === 0) {
      return 'info';
    }

    const status = item.privateNetworks[0].status;

    switch (status) {
      case 'CONFIGURED':
        return 'badge-success-color';
      case 'CONFIGURING':
        return 'badge-configuring-color';
      case 'REMOVING':
        return 'badge-error-color';
      case 'ERROR':
        return 'badge-error-color';
      default:
        return 'info';
    }
  }

  isUserEmployee() {
    this.isEmployee.set(this.currentUserService.isEmployee());
    return this.isEmployee();
  }

  isEmpty(obj: object) {
    for (const prop in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, prop)) {
        return false;
      }
    }

    return true;
  }

  async showUsage(event, privateRackId: number) {
    let equipment: any;
    const target = event.target || event.srcElement || event.currentTarget;

    if (target.classList.contains('shown')) {
      return;
    }

    target.innerText = $localize`:@@bma_common_loading:Loading...`;
    target.className = 'shown';

    await this.httpClient
      .get<any>(`/_/internal/dedicatedserverapi/v2/privateRacks/${privateRackId}`)
      .toPromise()
      .then(
        (privaterack: any) => {
          equipment = privaterack;
        },
        () => {
          target.parentElement.innerText = $localize`:@@bma_common_error:error`;
        }
      );

    const date = new Date();
    const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
    const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    const from = formatDate(firstDay, 'yyyy-MM-dd', 'en-US');
    const to = formatDate(lastDay, 'yyyy-MM-dd', 'en-US');

    let params = new HttpParams();

    params = params.set('from', from);
    params = params.set('to', to);

    switch (equipment.contract.networkTraffic.type) {
      case '95TH':
      case 'FLATFEE':
        params = params.set('aggregation', '95TH');
        this.httpClient
          .get<any>(`/_/bareMetals/v2/privateRacks/${equipment.id}/metrics/bandwidth`, { params })
          .subscribe(
            (usage: any) => {
              if (usage) {
                target.parentElement.innerText =
                  this.formatSizeUnitPipe.transform(usage.metrics.PUBLIC.values[0].value, false, 'bytes/s') + ' 95th';
              }
            },
            () => {
              target.parentElement.innerText = $localize`:@@bma_common_error:error`;
            }
          );
        break;
      case 'DATATRAFFIC':
        params = params.set('aggregation', 'SUM');
        this.httpClient
          .get<any>(`/_/bareMetals/v2/privateRacks/${equipment.id}/metrics/datatraffic`, { params })
          .subscribe(
            (usage: any) => {
              if (usage) {
                target.parentElement.innerText = this.formatSizeUnitPipe.transform(
                  usage.metrics.DOWN_PUBLIC.values[0].value + usage.metrics.UP_PUBLIC.values[0].value,
                  false,
                  'bytes'
                );
              }
            },
            () => {
              target.parentElement.innerText = $localize`:@@bma_common_error:error`;
            }
          );
        break;

      default:
        target.innerText = $localize`:@@bma_common_notavailable:Not Available`;
    }
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
