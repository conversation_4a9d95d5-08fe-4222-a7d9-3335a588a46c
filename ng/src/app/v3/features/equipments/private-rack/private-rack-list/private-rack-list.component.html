<lsw-page-header primaryTitle="Dedicated Racks" i18n-secondaryTitle="@@bma_privateracklist_manage_dedicated_rack" secondaryTitle="Manage your Dedicated Rack">
    <div action class="flex flex-row gap-3">
        <p-button *ngIf="isEmployee" i18n-label="@@bma_common_addtoprivatenetwork" type="button" label="Add to Private Network" (click)="openAddToPrivateNetworkModal()" styleClass="p-button-secondary" icon="pi pi-plus"></p-button>
        <a pButton *ngIf="!isEmployee" i18n-label="@@bma_privateracklist_adddedicatedrack" label="Add Dedicated Rack" href="https://www.leaseweb.com/colocation/private-rack" target="_blank" icon="pi pi-shopping-cart" class="p-button-success"></a>
    </div>
</lsw-page-header>

<app-customer-aware-header [customerId]="customerId" [salesOrgId]="salesOrgId" [isEmployee]="isEmployee"></app-customer-aware-header>

<lsw-data-view [items]="privateRacks()" [updatedItems]="privateRacks()" [totalItemCount]="totalCount()" [accordionContentTemplate]="accordionContentTemplate" [accordionContentHiddenTemplate]="accordionContentHiddenTemplate" [accordionRequired]="true" [enableCardListTopbarView]="true" [enableTopbarViewFilters]="true" [errorLoadingDynamicItems]="false" [filtersDialogTemplate]="filterDialogTemplate" [isFilterApplied]="isFilterApplied()" [isNewFilter]="isNewFilter()" [filterForm]="filters" [isLoading]="isLoading()" [desktopPaginationOffset]="getOffset()" [desktopPaginationLimit]="getLimit()" (loadDesktopItemsFromParent)="loadDesktopItems($event)" (loadItemsFromParent)="loadItems($event)" (clearFiltersEvent)="clearFilters()" (dialogFiltersEvent)="dialogFiltersEvent()" />

<ng-template #accordionContentTemplate let-item let-triggerAction="triggerAction">
    <div class="grid nested-grid m-0">
        <div class="col-12 pb-0">
            <div class="grid">
                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_rackid">Rack ID</span>
                    <a [href]="locationService.getAbsoluteUrlWithBase('/racks/' + item.id)">{{ item.id ? item.id : '-' }}</a>
                </div>
                <div class="col-12 lg:col-6 pl-3 py-1">
                    <div class="flex flex-row gap-3">
                        <span class="font-bold" i18n="@@bma_common_actions">Actions</span>
                        <div class="flex flex-wrap gap-3">
                            <p-button i18n-label="@@bma_common_credentials" label="Credentials" size="small" icon="pi pi-lock" styleClass="p-button-tertiary py-0 px-1" (click)="locationService.navigateToWithBase('/racks/' + item.id + '/credentials')"></p-button>
                            <p-button i18n-label="@@bma_common_datagraphs" label="Datagraphs" size="small" icon="fa fa-graph-bar" styleClass="p-button-tertiary py-0 px-1" (click)="locationService.navigateToWithBase('/racks/' + item.id + '/graphs')" *ngIf="(item?.networkInterfaces?.public|default:'') && (item?.networkInterfaces?.public?.ports|default:'')" label="Datagraphs" class="p-button-link"></p-button>
                            <p-button i18n-label="@@bma_common_ipaddresses" label="IP Addresses" size="small" icon="pi pi-globe" styleClass="p-button-tertiary py-0 px-1" (click)="locationService.navigateToWithBase('/racks/' + item.id + '/ips')"></p-button>
                            <p-button i18n-label="@@bma_common_notificationsettings" label="Notification Settings" size="small" icon="pi pi-bell" styleClass="p-button-tertiary py-0 px-1" (click)="locationService.navigateToWithBase('/racks/' + item.id + '/notifications')"></p-button>
                            <p-button i18n-label="@@bma_common_remove_from_pn" label="Remove from Private Network" size="small" icon="pi pi-minus" styleClass="p-button-tertiary py-0 px-1" *ngIf="isEmployee && item.privateNetworks.length > 0 && item.privateNetworks[0].status === 'CONFIGURED'" (click)="openRemoveFromPrivateNetworkModal(item); false" class="p-button-link"></p-button>
                            <p-button i18n-label="@@bma_common_createticket" label="Create Ticket" size="small" icon="pi pi-ticket" styleClass="p-button-tertiary py-0 px-1" *ngIf="!isEmployee" (click)="locationService.navigateTo('/tickets/new?equipmentId=' + item.id + '&category=technical-assistance')" class="p-button-link"></p-button>
                        </div>
                    </div>
                </div>
                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_location">Location</span>
                    <app-equipment-location [location]="item.location"></app-equipment-location>
                </div>
                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_privatenetwork">Private Network</span>
                    <p-badge
                        *ngIf="item.privateNetworks?.length > 0"
                        [value]="item?.isRedundantPrivateNetworkCapable && item.privateNetworks?.length !== 0 ? 'REDUNDANT' : 'ENABLED'"
                        [styleClass]="
                    item?.isRedundantPrivateNetworkCapable && item.privateNetworks?.length !== 0 ? 'badge-redundant-color' : 'badge-enabled-color'"></p-badge>
                    <span *ngIf="item.privateNetworks?.length === 0" i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
                </div>

                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_serverid">IP Address</span>
                    <ng-container *ngIf="(item?.networkInterfaces?.public && item?.networkInterfaces?.public?.ip)">
                        <span class="selectable">{{ item.networkInterfaces.public.ip|cidrToIp }}</span>
                    </ng-container>
                    <span *ngIf="!item?.networkInterfaces?.internal" class="text-muted">-</span>
                </div>

                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_status">Private Network Status</span>
                    <p-badge *ngIf="item.privateNetworks.length > 0" [value]="item.privateNetworks[0].status.toUpperCase()" [styleClass]="privateRackListService.getBadgeSeverity(item)"> </p-badge>
                    <span *ngIf="item.privateNetworks.length === 0" class="text-muted">-</span>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #accordionContentHiddenTemplate let-item let-triggerAction="triggerAction">
    <div class="grid nested-grid m-0">
        <div class="col-12">
            <div class="grid">
                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_dataused">Data Used</span>
                    <span class="show-data" (click)="showUsage($event, item.id)" *ngIf="(item.networkInterfaces.public|default:'') && (item.networkInterfaces.public.ports|default:''); else notAvailable" [class.disabled]="item.isSharedEol">
                        <strong class="text-primary show-data" i18n="@@bma_common_show">Show</strong>
                    </span>
                </div>

                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_subnet">Subnet</span>
                    <span *ngIf="item?.privateNetworks.length > 0 && item?.privateNetworks[0].dhcp === 'ENABLED'">{{ item?.privateNetworks[0]?.subnet }}</span>
                    <span *ngIf="item?.privateNetworks.length > 0 && item?.privateNetworks[0].dhcp === 'DISABLED'" i18n="@@bma_common_dhcpdisabled" class="text-muted">DHCP disabled</span>
                    <span *ngIf="item?.privateNetworks.length === 0" class="text-muted"> - </span>
                </div>

                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_vlanid">Vlan ID</span>
                    {{ item?.privateNetworks[0]?.vlanId|default:'-' }}
                </div>

                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_portspeed">Port Speed</span>
                    <span *ngIf="item.privateNetworks.length > 0" class="port-speed">2x{{ item?.privateNetworks[0].linkSpeed|formatSpeed }}</span>
                    <span *ngIf="item.privateNetworks.length === 0" class="text-muted">-</span>
                </div>

                <div class="col-12 lg:col-6 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_reference">Reference</span>
                    {{ item?.contract?.reference ? item?.contract?.reference : '-' }}
                </div>

                <div class="col-12 lg:col-12 pl-3 py-1">
                    <app-equipment-show-status-v3 [equipment]="item" [equipmentType]="equipmentType" networkInterfaceType="public"></app-equipment-show-status-v3>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #filterDialogTemplate>
    <form [formGroup]="filters">
        <div class="flex flex-column field p-4">
            <label for="reference" i18n="@@bma_common_search">Search</label>
            <div class="flex gap-1 md:gap-2 mb-7">
                <p-iconField iconPosition="right" class="flex-grow-1">
                    <input pInputText id="filter" class="w-full" formControlName="filter" i18n-placeholder="@@bma_privateracklist_searchbyrackid" placeholder="Search by rack ID" />
                </p-iconField>
            </div>
        </div>
    </form>
</ng-template>
