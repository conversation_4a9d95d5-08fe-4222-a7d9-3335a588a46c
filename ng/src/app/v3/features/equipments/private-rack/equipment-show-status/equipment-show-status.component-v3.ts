import { HttpClient } from '@angular/common/http';
import { Component, inject, Input, OnInit, signal } from '@angular/core';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { LocationService } from 'src/app/services/location.service';
import { ModalService } from 'src/app/services/modal.service';
import {
  PortInterface,
  PortsInterface,
  PortSpeedUpdate,
  PrivateRack,
  SwitchPortUpdate,
} from 'src/app/v3/model/racks.model';
import { NetworkDeviceManageSpeedModalV3Component } from 'src/app/v3/ui/network-device-manage-speed-modal/network-device-manage-speed-modal-v3.component';
import { SwitchPortCloseModalV3Component } from 'src/app/v3/ui/switch-port-close-modal/switch-port-close-modal-v3.component';

@Component({
  selector: 'app-equipment-show-status-v3',
  templateUrl: './equipment-show-status.component-v3.html',
  styleUrls: ['./equipment-show-status.component-v3.css'],
})
export class EquipmentShowStatusV3Component implements OnInit {
  @Input() equipment: PrivateRack;
  @Input() equipmentType: string;
  @Input() networkInterfaceType: string;

  locationService = inject(LocationService);
  dynamicDialogRef: DynamicDialogRef;
  switchPorts: PortInterface[];
  closedPortStates: [];
  isLoading = signal<boolean>(false);
  error = signal<boolean>(false);
  isEmployee = signal<boolean>(false);
  showPortStatus = signal<boolean>(false);

  private readonly alertService = inject(AlertService);
  private readonly currentUserService = inject(CurrentUserService);
  private readonly httpClient = inject(HttpClient);
  private readonly modalService = inject(ModalService);

  ngOnInit() {
    this.isEmployee.set(this.currentUserService.isEmployee());
  }

  showSwitchPortStatus() {
    this.getSwitchPortStatus(this.networkInterfaceType);
  }

  getSwitchPortStatus(type: string) {
    this.alertService.clear();
    this.isLoading.set(true);

    this.httpClient
      .get(`/_/internal/nseapi/v2/${this.equipmentType}/${this.equipment.id}/networkInterfaces/${type}?all`)
      .subscribe({
        next: (response: PortsInterface) => {
          if (response) {
            this.switchPorts = response.networkInterfaces;
            this.closedPortStates = response.closedPortStates ?? null;
          }
          this.isLoading.set(false);
          this.showPortStatus.set(true);
        },
        error: () => {
          this.alertService.alert(
            {
              type: 'error',
              description: $localize`:@@bma_equipmentswitchportstatus_error:Something went wrong while processing network interface request.`,
            },
            true
          );
          this.isLoading.set(false);
          this.error.set(true);
        },
      });
  }

  openCloseSwitchPortModal(action: string) {
    this.dynamicDialogRef = this.modalService.show(SwitchPortCloseModalV3Component, {
      equipmentId: this.equipment.id,
      type: this.networkInterfaceType,
      action,
    });

    this.dynamicDialogRef.onClose.subscribe((event: SwitchPortUpdate) => {
      if (event?.switchPortUpdated === true) {
        this.getSwitchPortStatus(this.networkInterfaceType);
      }
    });
  }

  openManagePortSpeedModal(port: PortInterface) {
    this.dynamicDialogRef = this.modalService.show(NetworkDeviceManageSpeedModalV3Component, {
      networkDeviceName: port.switchName,
      port: port.switchInterface,
    });

    this.dynamicDialogRef.onClose.subscribe((data: PortSpeedUpdate) => {
      if (data?.portSpeedUpdated === true) {
      }
    });
  }
}
