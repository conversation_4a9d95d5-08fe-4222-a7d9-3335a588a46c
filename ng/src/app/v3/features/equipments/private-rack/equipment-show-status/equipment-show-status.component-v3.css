:host ::ng-deep .table-container {
  width: 100%;
  overflow-x: auto;
  background-color: #f9f9f9;
}

:host ::ng-deep .p-datatable {
  width: 90%;
  font-size: 0.875rem;
  padding: 0;
}

:host ::ng-deep .p-datatable table {
  width: 100%;
  table-layout: fixed;
}

:host ::ng-deep .p-datatable .p-datatable-thead > tr > th {
  padding: 0.5rem;
}

:host ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
  padding: 0.5rem;
}

:host ::ng-deep .p-button.p-button-link {
  font-size: 0.875rem;
}

.show-data {
  cursor: pointer;
}

.action-buttons {
  width: 91%;
}
