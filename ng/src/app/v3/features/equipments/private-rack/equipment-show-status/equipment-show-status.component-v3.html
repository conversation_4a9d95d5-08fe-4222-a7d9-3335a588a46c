<span class="font-bold mr-3" i18n="bma_common_switch">Switch</span>
<ng-container *ngIf="!error(); else switchPortError">
    <ng-container *ngIf="equipment.networkInterfaces.public && equipment.networkInterfaces.public.ports.length > 0; else switchNotAvailable">
        <span (click)="showSwitchPortStatus()">
            <strong class="text-primary show-data" i18n="@@bma_common_showstatus">Show Status</strong>
        </span>
        <ng-template #switchNotAvailable>
            <span class="text-muted" i18n="@@bma_common_notavailable">Not Available</span>
        </ng-template>
        <ng-template #switchError>
            <span class="badge badge-danger" i18n="@@bma_common_error">error</span>
        </ng-template>
    </ng-container>

    <div class="spin-wrapper p-10 mt-5" *ngIf="isLoading()">
        <div class="spinner"></div>
    </div>

    <ng-container *ngIf="!isLoading() && showPortStatus()">
        <div class="table-container mt-4">
            <p-table [value]="switchPorts" [resizableColumns]="true" styleClass="p-datatable-sm" [tableStyle]="{'table-layout': 'fixed'}">
                <ng-template pTemplate="header">
                    <tr>
                        <th i18n="@@bma_common_networkdevice">Network Device</th>
                        <th i18n="@@bma_common_port">Port</th>
                        <th i18n="@@bma_common_linkspeed">Link Speed</th>
                        <th i18n="@@bma_common_type">Type</th>
                        <th i18n="@@bma_common_adminstatus">Admin Status</th>
                        <th i18n="@@bma_common_operationalstatus">Operational Status</th>
                        <th *ngIf="isEmployee()" i18n="@@bma_common_actions">Actions</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-port>
                    <tr>
                        <td>
                            <a pButton class="p-button-link p-pl-0" *ngIf="isEmployee()" [href]="locationService.getAbsoluteUrlWithBase('/networkDevices/' + port.switchName)"> {{port.switchName}} </a>
                            <span *ngIf="!isEmployee()">{{port.switchName}}</span>
                        </td>
                        <td>
                            <span>{{ port.switchInterface }}</span>
                        </td>
                        <td>
                            <span>{{ port.linkSpeed }}</span>
                        </td>
                        <td>
                            <span>{{ port.type }}</span>
                        </td>
                        <td>
                            <ng-container *ngIf="port.status">
                                <ng-container *ngIf="port.status == 'OPEN'; else adminStatusDown">
                                    <p-badge i18n="@@bma_common_open" value="OPEN" severity="success"></p-badge>
                                </ng-container>
                                <ng-template #adminStatusDown>
                                    <p-badge i18n="@@bma_common_closed" value="CLOSED" severity="danger"></p-badge>
                                </ng-template>
                            </ng-container>
                        </td>
                        <td>
                            <ng-container *ngIf="port.operStatus">
                                <ng-container *ngIf="port.operStatus == 'OPEN'; else statusDown">
                                    <p-badge i18n="@@bma_common_open" [value]="port.linkSpeed" severity="success"></p-badge>
                                </ng-container>
                                <ng-template #statusDown>
                                    <p-badge i18n="@@bma_common_down" value="DOWN" severity="danger"></p-badge>
                                </ng-template>
                            </ng-container>
                        </td>
                        <td *ngIf="isEmployee()">
                            <a i18n="@@bma_common_capuncap" pButton href="javascript:void(0);" (click)="openManagePortSpeedModal(port)" [class.disabled]="equipment.isSharedEol"> Cap/Uncap </a>
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </div>

        <div class="row mb-3 mt-3 action-buttons" *ngIf="equipmentType == 'servers' && (networkInterfaceType != 'remoteManagement' || (isEmployee && currentUserService.hasPermission('networkdevice_ports')))">
            <div class="col text-right" *ngIf="switchPorts && switchPorts[0] && switchPorts[0].status == 'OPEN'">
                <button pButton type="button" *ngIf="equipment.networkInterfaces[networkInterfaceType].ports.length == 1" i18n-label="@@bma_common_closeport" label="Close Port" class="p-button-danger" (click)="openCloseSwitchPortModal('close')"></button>
                <button pButton type="button" *ngIf="equipment.networkInterfaces[networkInterfaceType].ports.length > 1" i18n-label="@@bma_common_closeports" label="Close Ports" class="p-button-danger" (click)="openCloseSwitchPortModal('close')"></button>
            </div>
            <div class="col text-right" *ngIf="switchPorts && switchPorts[0] && switchPorts[0].status == 'CLOSED'">
                <button pButton *ngIf="equipment.networkInterfaces[networkInterfaceType].ports.length == 1" type="button" i18n="@@bma_common_openport" label="Open Port" class="p-button-prinary" (click)="openCloseSwitchPortModal('open')"></button>
                <button pButton *ngIf="equipment.networkInterfaces[networkInterfaceType].ports.length > 1" type="button" i18n="@@bma_common_openports" label="Open Ports" class="p-button-prinary" (click)="openCloseSwitchPortModal('open')"></button>
            </div>
        </div>
    </ng-container>

    <ng-container *ngIf="isEmployee() && closedPortStates && switchPorts && switchPorts[0] && switchPorts[0].status == 'CLOSED'">
        <div class="table-container mt-4">
            <p-table [value]="closedPortStates" [resizableColumns]="true" styleClass="p-datatable-sm" [tableStyle]="{'table-layout': 'fixed'}">
                <ng-template pTemplate="header">
                    <tr>
                        <th i18n="@@bma_common_internalcomment">Internal Comment</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-port>
                    <td>
                        <strong>{{port.type}}</strong>
                        <br />
                        <span class="font-bold" *ngIf="!port.comment" i18n="@@bma_common_nomessage">No Message</span>
                        <span class="font-bold" *ngIf="port.comment">{{port.comment}}</span>
                        <a i18n-title="@@bma_common_viewalllog" *ngIf="port.correlationId" class="text-right" [href]="locationService.getAbsoluteUrlWithBase('/audit-logs?filter' + port.correlationId)" title="View all related log messages..."><i class="fa fa-log"></i></a>
                    </td>
                </ng-template>
            </p-table>
        </div>
    </ng-container>
</ng-container>

<ng-template #notAvailable>
    <span i18n="@@bma_common_notavailable">Not available</span>
</ng-template>

<ng-template #switchPortError>
    <span i18n="@@bma_common_switchporterror">There was an issue in loading interface details.</span>
</ng-template>
