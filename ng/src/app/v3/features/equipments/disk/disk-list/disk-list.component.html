<lsw-page-header primaryTitle="Disks" secondaryTitle="Search for disks by serial number" i18n-secondaryTitle="@@bma_disklist_caption" />

<lsw-data-view [isLoading]="isLoading()" [items]="disks()" [updatedItems]="updatedDisks()" [totalItemCount]="totalCount()" [desktopPaginationOffset]="offset()" [desktopPaginationLimit]="limit()" (loadDesktopItemsFromParent)="loadDesktopItems($event)" [itemTemplate]="itemTemplate" [errorLoadingDynamicItems]="false" (loadItemsFromParent)="loadItems($event)" [enableCardListTopbarView]="true" [enableTopbarViewFilters]="true" [filtersDialogTemplate]="filterDialogTemplate" [filterForm]="filters" [isNewFilter]="isNewFilter()" [isFilterApplied]="isFilterApplied()" (clearFiltersEvent)="clearFilters()" (dialogFiltersEvent)="dialogFiltersEvent()" />

<ng-template #filterDialogTemplate>
    <form [formGroup]="filters">
        <div class="flex flex-column field p-4">
            <label for="reference" i18n="@@bma_common_search">Search</label>
            <div class="flex gap-1 md:gap-2 mb-7">
                <p-iconField iconPosition="right" class="flex-grow-1">
                    <input pInputText id="serial" class="w-full" formControlName="serial" i18n-placeholder="@@bma_disklist_searchbyserial" minlength="3" placeholder="Search by disk serial" />
                </p-iconField>
            </div>
        </div>
    </form>
</ng-template>

<ng-template #itemTemplate let-disk let-triggerAction="triggerAction">
    <div class="grid nested-grid m-0">
        <div class="col-12 pb-0">
            <div class="grid">
                <div class="col-12 lg:col-5 pl-3 py-1">
                    <span class="font-bold mr-3" i18n="@@bma_common_disk-serial">Disk Serial</span><a [href]="'/emp/disks/' + disk.serial">{{ disk.serial }}</a>
                </div>
                <div class="col-12 lg:col-7 pl-3 py-1"><span class="font-bold mr-3" i18n="@@bma_common_model">Model</span>{{ disk.device_model ?? '-' }}</div>
                <div class="col-12 lg:col-5 pl-3 py-1"><span class="font-bold mr-3" i18n="@@bma_common_type">Type</span> {{ disk.type ?? '-' }}</div>
                <div class="col-12 lg:col-7 pl-3 py-1"><span class="font-bold mr-3" i18n="@@bma_common_size">Size</span> {{ disk.size ?? '-' }}</div>
            </div>
        </div>
    </div>
</ng-template>
