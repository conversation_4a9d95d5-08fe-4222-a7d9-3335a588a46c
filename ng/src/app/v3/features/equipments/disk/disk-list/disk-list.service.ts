import { HttpClient } from '@angular/common/http';
import { inject, Injectable, signal } from '@angular/core';
import { Params } from '@angular/router';
import { Disk, Disks } from 'src/app/v3/model/disks.model';

@Injectable({ providedIn: 'root' })
export class DiskListService {
  disks = signal<Disk[]>([]);
  isLoading = signal<boolean>(false);
  totalCount = signal<number>(0);
  offset = signal<number>(0);
  limit = signal<number>(10);

  private readonly httpClient = inject(HttpClient);

  getDisks(params: Params) {
    this.isLoading.set(true);

    this.httpClient.get<Disks>(`/_/internal/bmsdb/v2/hardDisks`, { params }).subscribe({
      next: (data: Disks) => {
        this.isLoading.set(false);
        this.disks.set(data.hardDisks);
        this.totalCount.set(data._metadata.totalCount);
        this.offset.set(data._metadata.offset);
        this.limit.set(data._metadata.limit);
      },
      error: () => {
        this.isLoading.set(false);
        this.disks.set([]);
        this.totalCount.set(0);
      },
    });
  }

  isEmpty(obj: object) {
    for (const prop in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, prop)) {
        return false;
      }
    }

    return true;
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (!isNaN(+val) && val !== null) {
        acc[key] = val;
        return acc;
      }

      if (typeof val === 'boolean') {
        acc[key] = val;
        return acc;
      }

      if (val && val !== 'null') {
        acc[key] = val;
      }

      return acc;
    }, {});
  }
}
