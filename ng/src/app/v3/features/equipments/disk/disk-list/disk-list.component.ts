import { Component, computed, inject, OnInit, signal } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { FormBuilder, FormGroup } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { Disk } from 'src/app/v3/model/disks.model';
import { DiskListService } from './disk-list.service';

@Component({
  selector: 'app-disks-list',
  templateUrl: './disk-list.component.html',
  styleUrls: ['./disk-list.component.css'],
})
export class DiskListComponent implements OnInit {
  customerId: string;
  dynamicDialogRef: DynamicDialogRef;
  equipmentType = 'disks';
  filters: FormGroup;
  headerTitle: string;
  isFilterApplied = signal<boolean>(false);
  isNewFilter = signal<boolean>(false);
  isEmployee: boolean;
  salesOrgId: string;
  updatedDisks = signal<Disk[]>([]);
  temporaryFilters = signal({});

  disks = computed(() => this.diskListService.disks());
  isLoading = computed(() => this.diskListService.isLoading());
  totalCount = computed(() => this.diskListService.totalCount());
  offset = computed(() => this.diskListService.offset());
  limit = computed(() => this.diskListService.limit());

  private readonly alertService = inject(AlertService);
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly diskListService = inject(DiskListService);
  private readonly formBuilder = inject(FormBuilder);
  private readonly router = inject(Router);

  ngOnInit() {
    this.headerTitle = 'Disks';
    this.filters = this.formBuilder.group({
      offset: [0],
      limit: [10],
      serial: [null],
    });

    this.alertService.clear();

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  clearFilters() {
    const queryParams: Params = {
      offset: 0,
      limit: this.filters.get('limit').value,
    };

    if (this.isEmployee) {
      queryParams.salesOrgId = this.salesOrgId;
      queryParams.customerId = this.customerId;
    }

    this.filters.reset(queryParams);
    return this.changeRouterParams();
  }

  changeRouterParams() {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.diskListService.sanitise(this.filters.getRawValue()),
    });
  }

  dialogFiltersEvent() {
    this.submitFilters();
  }

  getLimit() {
    return +this.filters.get('limit').value;
  }

  getOffset() {
    return +this.filters.get('offset').value;
  }

  loadDesktopItems(event?: any) {
    this.loadItems(event);
  }

  loadItems(event?: any) {
    this.filters.patchValue(event);
    this.changeRouterParams();
  }

  onQueryParamsChanged(queryParams) {
    if (!this.diskListService.isEmpty(queryParams)) {
      this.filters.patchValue(queryParams);
    }

    const params = this.diskListService.sanitise(this.filters.getRawValue());

    this.isFilterApplied.set(this.diskListService.isEmpty(params));

    this.diskListService.getDisks(params);
  }

  submitFilters() {
    this.changeRouterParams();
  }
}
