import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-community-string',
  templateUrl: './community-string.component.html',
  styleUrls: ['./community-string.component.css'],
})
export class CommunityStringComponent {
  @Input() communityString: any;
  displayCommunityString = false;
  hide = $localize`:@@bma_communitystring_hide:hide`;
  show = $localize`:@@bma_communitystring_show:show`;

  toggleDisplayCommunityString() {
    return (this.displayCommunityString = !this.displayCommunityString);
  }
}
