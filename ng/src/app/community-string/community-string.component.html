<span class="text-monospace selectable" *ngIf="displayCommunityString else redacted">
    {{ communityString }}
</span>
<ng-template #redacted>
    <span i18n="@@bma_communitystring_redacted" class="text-muted text-monospace"> [redacted] </span>
</ng-template>

<a href="javascript:void(0);" (click)="toggleDisplayCommunityString()" class="pull-right">
    {{ displayCommunityString ? hide : show }}
    <span class="fa" [ngClass]="{'fa-show' : !displayCommunityString, 'fa-hide' : displayCommunityString}"></span>
</a>
