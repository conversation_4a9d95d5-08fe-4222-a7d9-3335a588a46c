import { ActivatedRoute } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-site-configuration',
  templateUrl: './site-configuration.component.html',
  styleUrls: ['./site-configuration.component.css'],
})
export class SiteConfigurationComponent implements OnInit {
  configuration: any;
  currentSite: string;
  sites: Array<string>;
  isLoading = false;

  constructor(
    private activatedRoute: ActivatedRoute,
    private httpClient: HttpClient,
    private titleService: Title
  ) {}

  ngOnInit(): void {
    this.isLoading = true;
    this.httpClient.get<any>('/_/internal/nseapi/v2/configuration').subscribe(
      (data: any) => {
        this.isLoading = false;
        this.sites = Object.keys(data);
        this.configuration = data;
      },
      (error: any) => {
        this.isLoading = false;
      }
    );

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    this.currentSite = queryParams.site;

    if (this.currentSite) {
      this.titleService.setTitle(
        $localize`:@@bma_siteconfiguration_sitetitle:${this.currentSite} Site Configuration` + ' | Leaseweb'
      );
    } else {
      this.titleService.setTitle($localize`:@@bma_siteconfiguration_title:Site Configuration` + ' | Leaseweb');
    }
  }
}
