<div class="mb-4">
    <h1 i18n="@@bma_siteconfiguration_header">{{ currentSite }} Site Configuration</h1>
</div>

<app-loader *ngIf="isLoading"></app-loader>

<div class="row" *ngIf="!isLoading">
    <div class="col-2" *ngIf="sites; else errorFetchingData">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a [routerLink]="['.']" routerLinkActive="active" class="nav-link"><span class="text-muted fa fa-cloud"></span><span i18n="@@bma_common_overview">&nbsp;Overview</span></a>
            </li>
            <li *ngFor="let site of sites" class="nav-item">
                <a [routerLink]="['.']" [queryParams]="{site: site}" routerLinkActive="active" class="nav-link"><span class="text-muted fa fa-cloud"></span>&nbsp;{{ site }}</a>
            </li>
        </ul>
    </div>

    <div class="col-10" *ngIf="sites">
        <div *ngIf="currentSite && configuration" class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th i18n="@@bma_common_services" scope="col">Services</th>
                        <th i18n="@@bma_common_publicnetwork" scope="col" class="text-center">Public Network</th>
                        <th i18n="@@bma_common_internalnetwork" scope="col" class="text-center">Internal Network</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td i18n="@@bma_siteconfiguration_ntp">NTP</td>
                        <td class="text-center">
                            <span class="selectable">{{ configuration[currentSite].ntp.ip.public }}</span>
                        </td>
                        <td class="text-center">
                            <span class="selectable">{{ configuration[currentSite].ntp.ip.internal }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_siteconfiguration_tacacs">TACACS</td>
                        <td class="text-center">
                            <span class="selectable">{{ configuration[currentSite].tacacs.ip.public }}</span>
                        </td>
                        <td class="text-center">
                            <span class="selectable">{{ configuration[currentSite].tacacs.ip.internal }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_siteconfiguration_syslog">SYSLOG</td>
                        <td class="text-center">
                            <span class="selectable">{{ configuration[currentSite].syslog.ip.public }}</span>
                        </td>
                        <td class="text-center">
                            <span class="selectable">{{ configuration[currentSite].syslog.ip.internal }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_siteconfiguration_dhcp">DHCP</td>
                        <td class="text-center">
                            <span class="selectable">{{ configuration[currentSite].dhcpapi.ip.public }}</span>
                        </td>
                        <td class="text-center">
                            <span class="selectable">{{ configuration[currentSite].dhcpapi.ip.internal }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_siteconfiguration_tftp">TFTP</td>
                        <td class="text-center">
                            <span class="selectable">{{ configuration[currentSite].tftp.ip.public }}</span>
                        </td>
                        <td class="text-center">
                            <span class="selectable">{{ configuration[currentSite].tftp.ip.internal }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_siteconfiguration_switchapi">SwitchApi</td>
                        <td class="text-center">
                            <span class="selectable">{{ configuration[currentSite].switchapi.ip.public }}</span>
                        </td>
                        <td class="text-center">
                            <span class="selectable">{{ configuration[currentSite].switchapi.ip.internal }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_icinga">Icinga</td>
                        <td colspan="2" class="text-center">{{ configuration[currentSite].icinga.baseurl }}</td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_siteconfiguration_managementsubnets">Management Subnets</td>
                        <td class="text-center">
                            <div *ngFor="let subnet of configuration[currentSite].management_subnets.public">
                                <small class="text-muted">{{ subnet.description }}</small>
                                <p>{{ subnet.value }}</p>
                            </div>
                        </td>
                        <td class="text-center">
                            <div *ngFor="let subnet of configuration[currentSite].management_subnets.internal">
                                <small class="text-muted">{{ subnet.description }}</small>
                                <p>{{ subnet.value }}</p>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_remotemanagement" rowspan="3">Remote Management</td>
                        <td class="text-center" colspan="2">
                            <span class="selectable">{{ configuration[currentSite].remote_management.openvpn.remote }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-center" colspan="2">
                            <p *ngFor="let subnet of configuration[currentSite].remote_management.subnets">
                                <span class="selectable">{{ subnet }}</span>
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-center" colspan="2">{{ configuration[currentSite].remote_management.dhcp }}</td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_common_privatenetwork">Private Network</td>
                        <td class="text-center" colspan="2">
                            <p *ngFor="let subnet of configuration[currentSite].private_networking.subnets">
                                <span class="selectable">{{ subnet }}</span>
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_siteconfiguration_nameserveripv4">Nameserver (IPv4 Address)</td>
                        <td class="text-center">
                            <p *ngFor="let ip of configuration[currentSite].nameserver.ipv4.public">
                                <span class="selectable">{{ ip }}</span>
                            </p>
                        </td>
                        <td class="text-center">
                            <p *ngFor="let ip of configuration[currentSite].nameserver.ipv4.internal">
                                <span class="selectable">{{ ip }}</span>
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <td i18n="@@bma_siteconfiguration_nameserveripv6">Nameserver (IPv6 Address)</td>
                        <td class="text-center">
                            <p *ngFor="let ip of configuration[currentSite].nameserver.ipv6.public">{{ ip }}</p>
                        </td>
                        <td class="text-center">
                            <p *ngFor="let ip of configuration[currentSite].nameserver.ipv6.internal">{{ ip }}</p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div *ngIf="!currentSite || !configuration">
            <p i18n="@@bma_siteconfiguration_description1">This is an overview of all NSE-API sites and their configuration.</p>
            <p i18n="@@bma_siteconfiguration_description2">Use the menu on the top to view configuration for a specific site.</p>
        </div>
    </div>
</div>

<ng-template #errorFetchingData>
    <span i18n="@@bma_common_errorfetchingdata" class="text-danger">Error fetching the data</span>
</ng-template>
