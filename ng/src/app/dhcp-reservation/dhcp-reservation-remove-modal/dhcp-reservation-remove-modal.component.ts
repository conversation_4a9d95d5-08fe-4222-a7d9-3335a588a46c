import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient, HttpParams } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert.service';

@Component({
  selector: 'app-dhcp-reservation-remove-modal',
  templateUrl: './dhcp-reservation-remove-modal.component.html',
  styleUrls: ['./dhcp-reservation-remove-modal.component.css'],
})
export class DhcpReservationRemoveModalComponent implements OnInit {
  dhcpReservation: any;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private alertService: AlertService,
    private httpClient: HttpClient,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.dhcpReservation = this.dynamicDialogConfig.data.dhcpReservation;
  }

  removeDhcpReservation(): void {
    this.alertService.clear();
    let params = new HttpParams();
    params = params.set('site', this.dhcpReservation.site);
    params = params.set('ipAddress', this.dhcpReservation.ip);
    params = params.set('macAddress', this.dhcpReservation.mac);
    this.isSubmitting = true;
    this.httpClient.delete('/_/internal/bmpapi/leases', { params }).subscribe(
      (data) => {
        this.isSubmitting = false;
        this.alertService.alert({
          type: 'success',
          description: `Removed DHCP reservation for ${this.dhcpReservation.ip} in site ${this.dhcpReservation.site}`,
        });
        this.closeModal({ dhcpReservationRemoved: true });
      },
      (error) => {
        this.isSubmitting = false;
        this.alertService.alertApiError(error.error, true);
        this.closeModal({ dhcpReservationRemoved: false });
      }
    );
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
