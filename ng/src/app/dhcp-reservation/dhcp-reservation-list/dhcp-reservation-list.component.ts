import { Component, OnInit } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { SiteService } from 'src/app/services/site.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { AlertService } from 'src/app/services/alert.service';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { CustomValidator } from 'src/app/validators/custom.validator';
import { ModalService } from 'src/app/services/modal.service';
import { DhcpReservationRemoveModalComponent } from '../dhcp-reservation-remove-modal/dhcp-reservation-remove-modal.component';

@Component({
  selector: 'app-dhcp-reservation-list',
  templateUrl: './dhcp-reservation-list.component.html',
  styleUrls: ['./dhcp-reservation-list.component.css'],
})
export class DhcpReservationListComponent implements OnInit {
  dhcpReservations: any;
  filters: UntypedFormGroup;
  sites: string[];
  isLoading = false;
  validFilter: '';

  constructor(
    private activatedRoute: ActivatedRoute,
    private alertService: AlertService,
    private modalService: ModalService,
    private customValidator: CustomValidator,
    private readonly formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private router: Router,
    private siteService: SiteService,
    private titleService: Title
  ) {}

  ngOnInit(): void {
    this.sites = this.siteService.sites();
    this.titleService.setTitle($localize`:@@bma_dhcpreservationlist_title:DHCP Reservations` + ' | Leaseweb');

    this.filters = this.formBuilder.group({
      filter: [null, Validators.required],
      site: ['AMS-01', Validators.required],
    });
    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    if (queryParams) {
      this.filters.reset(queryParams);
    } else {
      this.filters.reset();
    }
    this.getDhcpReservations();
  }

  getDhcpReservations(): void {
    this.alertService.clear();
    const filterValue = this.filters.get('filter').value;
    if (!filterValue) {
      return;
    }
    if (this.customValidator.isValidIp(filterValue) || this.customValidator.isValidMacAddress(filterValue)) {
      this.validFilter = filterValue;
      let params = new HttpParams();
      params = params.set('site', this.filters.get('site').value);
      if (this.customValidator.isValidIp(filterValue)) {
        params = params.set('ipAddress', filterValue);
      }
      if (this.customValidator.isValidMacAddress(filterValue)) {
        params = params.set('macAddress', filterValue);
      }

      this.isLoading = true;
      this.httpClient.get<any>('/_/internal/bmpapi/leases', { params }).subscribe(
        (data: any) => {
          this.isLoading = false;
          this.dhcpReservations = data;
        },
        (error: any) => {
          this.isLoading = false;
          this.dhcpReservations = [];
          this.alertService.alert(
            {
              type: 'error',
              description: 'Unable to get DHCP reservervations for ' + filterValue,
            },
            true
          );
        }
      );
    } else {
      this.alertService.alert(
        {
          type: 'error',
          description: 'Please enter a valid IP or MacAddress: ' + filterValue,
        },
        true
      );
    }
  }

  submitFilters(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  clearFilters(): void {
    this.validFilter = '';
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
    });
  }

  openDhcpReservationRemoveModal(dhcpReservation: any) {
    const dynamicDialogRef = this.modalService.show(DhcpReservationRemoveModalComponent, {
      dhcpReservation,
    });

    dynamicDialogRef.onClose.subscribe((event) => {
      if (event?.dhcpReservationRemoved === true) {
        this.dhcpReservations = [];
      }
    });
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
