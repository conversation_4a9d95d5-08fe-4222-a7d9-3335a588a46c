<div class="mb-4">
    <h1 i18n="@@bma_dhcpreservationlist_header">DHCP Reservations</h1>
</div>

<form [formGroup]="filters" (ngSubmit)="submitFilters()">
    <div class="row">
        <div class="col-8">
            <input i18n-placeholder="@@bma_dhcpreservationlist_placeholder" type="text" id="filter" formControlName="filter" class="form-control m-0" value="" placeholder="Filter by IP or MAC Address..." autofocus />
        </div>

        <div class="col-4">
            <select formControlName="site" label="Site" class="form-control">
                <option i18n="@@bma_common_site" value="null">Site</option>
                <option *ngFor="let site of sites" [value]="site">{{ site }}</option>
            </select>
        </div>
    </div>

    <div class="row mb-3 mt-3">
        <div class="col text-right">
            <button i18n-label="@@bma_common_reset" pButton type="button" icon="pi pi-refresh" label="Reset" class="p-button-secondary mr-2 text-white" (click)="clearFilters()"></button>
            <button i18n-label="@@bma_common_search" pButton [disabled]="isLoading" label="Search" icon="pi pi-search"></button>
        </div>
    </div>
</form>

<app-loader *ngIf="isLoading && validFilter"></app-loader>
<div class="row" *ngIf="!isLoading && dhcpReservations" class="table-responsive">
    <table class="table table-striped">
        <thead>
            <tr>
                <th i18n="@@bma_common_macadress" scope="col">Mac Address</th>
                <th i18n="@@bma_common_ipaddress" scope="col">IP Address</th>
                <th i18n="@@bma_common_bootfile" scope="col">Bootfile</th>
                <th i18n="@@bma_common_hostname" scope="col">Hostname</th>
                <th i18n="@@bma_common_lastclientrequest" scope="col">Last Client Request</th>
                <th i18n="@@bma_common_actions" scope="col" class="text-center">Actions</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngIf="!dhcpReservations.leases || dhcpReservations.leases.length === 0">
                <td i18n="@@bma_dhcpreservationlist_dhcpreservationnone" colspan="999" class="text-center p-4" *ngIf="validFilter">No DHCP Reservations found for {{ validFilter }} at {{ filters.get('site').value }}.</td>
            </tr>
            <tr *ngFor="let dhcpReservation of dhcpReservations.leases">
                <td class="text-center">
                    {{ dhcpReservation['mac'] | default: 'Not Available' }}
                </td>
                <td class="text-center">
                    {{ dhcpReservation['ip'] }}<span class="text-muted">/{{ dhcpReservation['netmask'] }}</span>
                </td>
                <td class="text-center">
                    {{ dhcpReservation['bootfile'] | default: 'Not Available' }}
                </td>
                <td class="text-center">
                    {{ dhcpReservation['hostname'] | default: 'Not Available' }}
                </td>
                <td class="text-center" *ngIf="dhcpReservation['lastClientRequest'] ?.length > 0; else nolastClientRequest">
                    <ul class="list-unstyled mb-0">
                        <li>
                            <b i18n="@@bma_dhcpreservationlist_type">Type:</b>
                            {{ dhcpReservation['lastClientRequest']['type'] | default: 'Not Available' }}
                        </li>
                        <li><b i18n="@@bma_common_at">At:</b> {{ dhcpReservation['updatedAt'] | default: 'Not Available' }}</li>
                        <li>
                            <b i18n="@@bma_common_via">Via:</b>
                            {{ dhcpReservation['lastClientRequest']['relayAgent'] | default: 'Not Available' }}
                        </li>
                        <li>
                            <b i18n="@@bma_common_from">From:</b>
                            {{ dhcpReservation['lastClientRequest']['userAgent'] | default: 'Not Available' }}
                        </li>
                    </ul>
                </td>
                <ng-template #nolastClientRequest>
                    <td i18n="@@bma_common_unused" class="text-center">Unused</td>
                </ng-template>
                <td class="text-center">
                    <a href="#" class="text-danger my-1" title="Remove DHCP Reservation" (click)="openDhcpReservationRemoveModal(dhcpReservation); false">
                        <span class="fa fa-delete"></span>
                        <span i18n="@@bma_dhcpreservationlist_remove">Remove</span>
                    </a>
                </td>
            </tr>
        </tbody>
    </table>
</div>
