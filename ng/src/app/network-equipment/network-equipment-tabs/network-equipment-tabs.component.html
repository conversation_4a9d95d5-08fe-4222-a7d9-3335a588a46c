<div class="dedicated-network-equipment-alerts">
    <div *ngIf="isEmployee && (missingInformation|default:'') && (missingInformation|default:'').length > 0" id="dedicated-network-equipment-missing-information-alert" class="alert alert-warning" role="alert">
        <p><span class="fa fa-alert"></span> <strong i18n="@@bma_networkequipmenttabs_missinginformation">Network Equipment Missing Information</strong></p>
        <ul>
            <li *ngFor="let message of missingInformation">{{ message }}</li>
        </ul>
    </div>
</div>

<app-customer-aware-header *ngIf="networkEquipment" [caption]="'Dedicated Network Equipment ' + networkEquipment.id" [reference]="networkEquipment.contract?.reference" [customerId]="networkEquipment.contract?.customerId" [salesOrgId]="networkEquipment.contract?.salesOrgId" [isEmployee]="isEmployee"></app-customer-aware-header>

<div *ngIf="!isEmployee && networkEquipment && networkEquipment.isRedundantPrivateNetworkCapable" class="float-right server-private-network-link">
    <a i18n="@@bma_common_order_redundantprivatenetwork" class="btn btn-success" (click)="openOrderRedundantPrivateNetworkModal(); false" href="#">Order Redundant Private Network</a>
</div>

<app-loader *ngIf="isLoading"></app-loader>

<ul class="nav nav-pills mb-4" *ngIf="networkEquipment">
    <li class="nav-item dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-details"></span>&nbsp;<span i18n="@@bma_common_details">Details</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a [routerLink]="['.']" class="dropdown-item">
                <span class="text-muted fa fa-details mr-2"></span>
                <span i18n="@@bma_common_equipmentdetails">Equipment Details</span>
            </a>
            <a [routerLink]="['network']" class="dropdown-item">
                <span class="text-muted fa fa-transfer mr-2"></span>
                <span i18n="@@bma_common_networkdetails">Network Details</span>
            </a>
            <a [routerLink]="['ips']" class="dropdown-item">
                <span class="text-muted fa fa-ip mr-2"></span>
                <span i18n="@@bma_common_ipaddresses">IP Addresses</span>
            </a>
            <a [routerLink]="['credentials']" class="dropdown-item">
                <span class="text-muted fa fa-lock mr-2"></span>
                <span i18n="@@bma_common_credentials">Credentials</span>
            </a>
        </div>
    </li>
    <li class="nav-item dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-usage"></span>&nbsp; <span i18n="@@bma_common_usage">Usage</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a [routerLink]="['graphs']" class="dropdown-item">
                <span class="text-muted fa fa-usage mr-2"></span>
                <span i18n="@@bma_common_graphs">Graphs</span>
            </a>
        </div>
    </li>
    <li class="nav-item navbar-right dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-tasks"></span>&nbsp;<span i18n="@@bma_common_activities">Activities</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a [routerLink]="['nullRouteHistory']" class="dropdown-item">
                <span class="text-muted fa fa-transfer mr-2"></span>
                <span i18n="@@bma_common_nullroutehistory">Null Route History</span>
            </a>
        </div>
    </li>
    <li class="nav-item">
        <a *ngIf="!isEmployee" [class.disabled]="!networkEquipment.contract|default:''" href="/bare-metals/privateNetwork/lookup" class="nav-link "> <span class="text-muted fa fa-privatenetwork"></span>&nbsp; <span i18n="@@bma_common_privatenetwork">Private Network</span> </a>
        <a *ngIf="isEmployee" [class.disabled]="!networkEquipment.contract|default:''" href="/emp/privateNetwork/lookup?customerId={{ networkEquipment.contract?.customerId }}&salesOrgId={{ networkEquipment.contract?.salesOrgId }}" class="nav-link "> <span class="text-muted fa fa-privatenetwork"></span>&nbsp; <span i18n="@@bma_common_privatenetwork">Private Network</span> </a>
    </li>
    <li class="nav-item navbar-right dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-administration"></span>&nbsp;<span i18n="@@bma_common_actions">Actions</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a href="javascript:void(0);" class="dropdown-item" (click)="openRemoteManagementModal(networkEquipmentId)">
                <span class="text-muted fa fa-remotemanagement mr-2"></span>
                <span i18n="@@bma_common_remotemanagement">Remote Management</span>
            </a>
            <a href="javascript:void(0);" [class.disabled]="(!networkEquipment.featureAvailability.powerCycle|default:'')" class="dropdown-item" (click)="openPowerOperationModal()">
                <span class="text-muted fa fa-powercycle mr-2"></span>
                <span i18n="@@bma_common_poweroperation">Power Operation</span>
            </a>
        </div>
    </li>
</ul>

<ng-container *ngIf="networkEquipment && !isLoading">
    <router-outlet (activate)="onNetworkEquipmentLoaded($event)"></router-outlet>
</ng-container>
