import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { HttpClient } from '@angular/common/http';
import { PowerOperationModalComponent } from 'src/app/power-operation-modal/power-operation-modal.component';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { SiteService } from 'src/app/services/site.service';
import { PrivateNetworkRedundancyOrderModalComponent } from 'src/app/private-network/private-network-redundancy-order-modal/private-network-redundancy-order-modal.component';
import { NetworkEquipmentRemoteManagementModalComponent } from '../network-equipment-remote-management-modal/network-equipment-remote-management-modal.component';

@Component({
  selector: 'app-network-equipment-tabs',
  templateUrl: './network-equipment-tabs.component.html',
  styleUrls: ['./network-equipment-tabs.component.css'],
})
export class NetworkEquipmentTabsComponent implements OnInit {
  isLoading = false;
  networkEquipment: any;
  networkEquipmentId: string;
  isEmployee = false;
  missingInformation: any;
  ips: any;
  dynamicDialogRef: DynamicDialogRef;

  constructor(
    private activatedRoute: ActivatedRoute,
    private titleService: Title,
    private currentUserService: CurrentUserService,
    private httpClient: HttpClient,
    private modalService: ModalService,
    private router: Router,
    private siteService: SiteService
  ) {}

  ngOnInit(): void {
    this.networkEquipmentId = this.activatedRoute.snapshot.url[1].path;
    this.isEmployee = this.currentUserService.isEmployee();

    this.isLoading = true;

    this.httpClient
      .get<any>(`/_/internal/dedicatedserverapi/v2/networkEquipments/${this.networkEquipmentId}`)
      .subscribe({
        next: (data: any) => {
          this.isLoading = false;
          this.networkEquipment = data;
          this.titleService.setTitle(
            `Dedicated Network Equipment ${this.networkEquipment.id} ${
              this.networkEquipment.contract?.reference ? this.networkEquipment.contract.reference : ''
            }| Leaseweb`
          );
          /*eslint-disable */
          const event = new Event('update_product_navbar');
          event['customerId'] = this.networkEquipment.contract?.customerId;
          event['salesOrgId'] = this.networkEquipment.contract?.salesOrgId;
          event['country'] = this.siteService.getCountry(this.networkEquipment.contract?.salesOrgId);
          window.dispatchEvent(event);
          /*eslint-enable */
        },
        error: (error: any) => {
          this.isLoading = false;
          this.router.navigate(['**']);
        },
      });
  }

  openPowerOperationModal() {
    this.modalService.show(PowerOperationModalComponent, {
      equipment: this.networkEquipment,
      equipmentType: 'networkEquipments',
    });
  }

  openRemoteManagementModal(networkEquipmentId: string) {
    this.dynamicDialogRef = this.modalService.show(NetworkEquipmentRemoteManagementModalComponent, {
      networkEquipmentId,
    });
  }

  onNetworkEquipmentLoaded(component) {
    if (this.networkEquipment) {
      component.equipment = this.networkEquipment;
      component.networkEquipment = this.networkEquipment;
      component.equipmentId = this.networkEquipment.id;
      component.equipmentType = 'networkEquipments';
    }
  }

  openOrderRedundantPrivateNetworkModal() {
    this.modalService.show(PrivateNetworkRedundancyOrderModalComponent, {
      equipment: this.networkEquipment,
      equipmentType: this.networkEquipment.type.toLowerCase(),
    });
  }
}
