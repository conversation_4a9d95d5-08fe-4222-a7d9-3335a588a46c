<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '800px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_common_orderprivatenetwork" class="modal-title">Order Private Network</h3>
    </ng-template>

    <form [formGroup]="form" (ngSubmit)="orderPrivateNetwork()">
        <div class="modal-body">
            <div class="alert alert-info" role="alert">
                <div class="alert-icon">
                    <i class="fa fa-information fa-lg" aria-hidden="true"></i>
                </div>
                <span i18n="@@bma_networkequipmentorderprivatenetworkmodal_description2" class="ml-4">Only one type of network equipment (Firewall or Load Balancer) is allowed to be associated with a Private network.</span>
            </div>
            <div class="form-group">
                <div class="ml-4">
                    <div i18n="@@bma_networkequipmentorderprivatenetworkmodal_description1" class="row">The Private Network connectivity service offers the possibility to connect your Network equipment to other Leaseweb products.</div>

                    <div class="row mt-3">
                        <h4 i18n="@@bma_networkequipmentorderprivatenetworkmodal_chooseequipmenttype">Choose the equipment type you want to add in Private Network</h4>
                    </div>

                    <div class="row">
                        <div class="form-check form-check-inline">
                            <input type="radio" class="form-check-input" formControlName="equipmentType" id="firewall" value="FIREWALL" autofocus />
                            <label class="form-check-label" for="firewall">
                                <span>Firewall</span>
                            </label>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-check form-check-inline">
                            <input type="radio" class="form-check-input" formControlName="equipmentType" id="loadbalancer" value="LOAD_BALANCER" autofocus />
                            <label class="form-check-label" for="loadbalancer">
                                <span>Load Balancer</span>
                            </label>
                        </div>
                    </div>

                    <div class="row mt-3 mb-3 d-block">
                        <p i18n="@@bma_common_followcapacity">We offer the following capacity:</p>
                    </div>
                    <div class="row">
                        <h4 i18n="@@bma_common_selectuplinkcapacity">Select an uplink capacity</h4>
                    </div>

                    <ng-container *ngIf="uplinkCapacities">
                        <div class="row">
                            <div class="form-check form-check-inline">
                                <input type="radio" class="form-check-input" formControlName="uplinkCapacity" id="uplink_capacity_100" value="100" autofocus />
                                <label class="form-check-label" for="uplink_capacity_100">
                                    {{ (uplinkCapacities['100']?.value|default) }}
                                    (
                                    <ng-container *ngIf="uplinkCapacities['100']?.price|default; else free">
                                        {{ uplinkCapacities['100']?.price|default|formatCurrency:salesOrgId }} /
                                        <span i18n="@@bma_common_month">month</span>
                                    </ng-container>
                                    <ng-template #free>
                                        <span i18n="@@bma_common_free">Free</span>
                                    </ng-template>
                                    )
                                </label>
                            </div>
                        </div>

                        <div class="row">
                            <div class="form-check form-check-inline">
                                <input type="radio" class="form-check-input" formControlName="uplinkCapacity" id="uplink_capacity_1000" value="1000" autofocus />
                                <label class="form-check-label" for="uplink_capacity_1000">
                                    {{ (uplinkCapacities['1000']?.value|default) }}
                                    (
                                    <ng-container *ngIf="uplinkCapacities['1000']?.price|default; else contactsales">
                                        {{ uplinkCapacities['1000']?.price|default|formatCurrency:salesOrgId }} /
                                        <span i18n="@@bma_common_month">month</span>
                                    </ng-container>
                                    <ng-template #contactsales>
                                        <span i18n="@@bma_common_contactsales">Contact our Sales Department</span>
                                    </ng-template>
                                    )
                                </label>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </div>
        </div>
    </form>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_cancel" pButton type="button" icon="pi pi-times" iconPos="left" label="Cancel" class="p-button-secondary" (click)="closeModal()"></button>
            <button i18n-label="@@bma_common_submit" pButton (click)="orderPrivateNetwork()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" [disabled]="!form.valid || error" label="Submit" class="p-button-success"></button>
        </div>
    </ng-template>
</p-dialog>
