import { Component, OnInit } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { PrivateNetworkService } from 'src/app/services/private-network.service';

@Component({
  selector: 'app-network-equipment-order-private-network-modal',
  templateUrl: './network-equipment-order-private-network-modal.component.html',
  styleUrls: ['./network-equipment-order-private-network-modal.component.css'],
})
export class NetworkEquipmentOrderPrivateNetworkModalComponent implements OnInit {
  networkEquipment: any;
  isSubmitting = false;
  isLoadingOptions = false;
  uplinkCapacities = null;
  showDialog = true;
  form: UntypedFormGroup;
  salesOrgId = '';

  constructor(
    private alertService: AlertService,
    private dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    private privateNetworkService: PrivateNetworkService
  ) {}

  ngOnInit(): void {
    this.networkEquipment = this.dynamicDialogConfig.data.networkEquipment;
    this.salesOrgId = this.currentUserService.getSalesOrgId();
    this.uplinkCapacities = this.privateNetworkService.getPrivateNetworkPricesForNetworkEquipment(this.salesOrgId);

    this.form = this.formBuilder.group({
      equipmentType: [null, Validators.required],
      uplinkCapacity: [null, Validators.required],
    });
  }

  orderPrivateNetwork(): void {
    this.isSubmitting = true;
    this.alertService.clear();
    this.hasNetworkEquipmentInPrivateNetwork().then((hasNetworkEquipmentInPrivateNetwork) => {
      if (hasNetworkEquipmentInPrivateNetwork) {
        this.alertService.alert(
          {
            type: 'error',
            description:
              'Only one type of network equipment (Firewall or Load Balancer) is allowed to be associated with a Private network.',
          },
          true
        );
        this.closeModal();
        return;
      }

      const body = {
        equipment: this.networkEquipment,
        uplinkCapacity: this.form.get('uplinkCapacity').value,
        equipmentType: this.form.get('equipmentType').value,
      };
      this.httpClient.post<any>('/_legacy/privateNetwork/order', body).subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.alertService.alert(
            {
              type: 'success',
              description: data.message,
            },
            true
          );
          this.closeModal();
        },
        error: (error: any) => {
          this.isSubmitting = false;
          this.alertService.alert(
            {
              type: 'error',
              description: error.message,
            },
            true
          );
          this.closeModal();
        },
      });
    });
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }

  async hasNetworkEquipmentInPrivateNetwork(): Promise<boolean> {
    let params = new HttpParams();
    params = params.set('privateNetworkEnabled', 'true');
    params = params.set('limit', 0);

    if (this.form.get('equipmentType').value === 'FIREWALL') {
      params = params.set('type', 'LOAD_BALANCER');
    }
    if (this.form.get('equipmentType').value === 'LOAD_BALANCER') {
      params = params.set('type', 'FIREWALL');
    }

    return await this.httpClient
      .get<any>('/_/internal/dedicatedserverapi/v2/networkEquipments', { params })
      .toPromise()
      .then(
        (data: any) => data._metadata.totalCount > 0,
        (error) => false
      );
  }
}
