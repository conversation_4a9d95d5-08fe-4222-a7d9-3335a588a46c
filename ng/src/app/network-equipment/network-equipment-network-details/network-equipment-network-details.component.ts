import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-network-equipment-network-details',
  templateUrl: './network-equipment-network-details.component.html',
  styleUrls: ['./network-equipment-network-details.component.css'],
})
export class NetworkEquipmentNetworkDetailsComponent {
  @Input() equipment: any;
  @Input() errorInformation: string[];
  equipmentType = 'networkEquipments';
  networkInterfaceOrder = ['public', 'internal', 'remoteManagement'];
}
