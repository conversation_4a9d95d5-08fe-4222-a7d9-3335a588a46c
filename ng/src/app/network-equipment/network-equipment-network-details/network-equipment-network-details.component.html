<div class="row">
    <div class="col-12 mb-3">
        <h3>
            <i class="fa fa-transfer" aria-hidden="true"></i>
            <span i18n="@@bma_common_networkdetails" class="mx-1">Network Details</span>
            <sup>
                <a href="https://kb.leaseweb.com/kb/dedicated-server/dedicated-server-managing-dedicated-server-network-settings/#ManagingDedicatedServernetworksettings-Network" target="_blank" rel="noopener"><i title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i><span class="sr-only">Knowledge Base</span></a>
            </sup>
        </h3>
    </div>
</div>

<ng-container *ngIf="equipment.networkInterfaces; else interfacesNotAvailable">
    <p-accordion [multiple]="true" *ngIf="!isLoading">
        <p-accordionTab *ngFor="let networkInterfaceKey of networkInterfaceOrder">
            <ng-template pTemplate="header">
                <div class="row col-sm-12 align-items-center">
                    <ng-container *ngIf="equipment.networkInterfaces[networkInterfaceKey]; else notAvailable">
                        <div class="col-sm-3">
                            <h5 i18n="@@bma_common_capremotemanagement" class="mt-2" *ngIf="networkInterfaceKey=='remoteManagement'">REMOTE MANAGEMENT</h5>
                            <h5 class="mt-2" *ngIf="networkInterfaceKey!='remoteManagement'">{{ networkInterfaceKey | uppercase}}</h5>
                        </div>
                        <div class="col-sm-3">
                            <h5 class="mt-2">{{ equipment.networkInterfaces[networkInterfaceKey].mac }}</h5>
                        </div>
                        <div class="col-sm-3">
                            <span class="h5" *ngIf="equipment.networkInterfaces[networkInterfaceKey]">
                                {{ equipment.networkInterfaces[networkInterfaceKey].ip }}
                            </span>
                        </div>
                        <div class="col-sm-3" *ngIf="equipment.networkInterfaces[networkInterfaceKey].ports">
                            <span class="h5 badge" [ngClass]="{'badge-success': equipment.networkInterfaces[networkInterfaceKey].ports.length >= 1,'badge-warning': equipment.networkInterfaces[networkInterfaceKey].ports.length == 0}">
                                {{ equipment.networkInterfaces[networkInterfaceKey].ports.length }} Port{{ equipment.networkInterfaces[networkInterfaceKey].ports.length > 1 ?
                                    's' : '' }}
                            </span>
                        </div>
                    </ng-container>
                    <ng-template #notAvailable>
                        <div class="col-sm-6">
                            <h5 i18n="@@bma_servernetworkdetails_notavailable" class="mt-2">{{ networkInterfaceKey | uppercase}} - NOT AVAILABLE</h5>
                        </div>
                    </ng-template>
                </div>
            </ng-template>
            <ng-template pTemplate="content" class="p-p-0">
                <div class="p-grid p-flex-column flex-nowrap">
                    <app-switch-port-status [equipment]="equipment" [equipmentType]="equipmentType" [networkInterfaceType]="networkInterfaceKey"></app-switch-port-status>
                </div>
            </ng-template>
        </p-accordionTab>
    </p-accordion>
</ng-container>
<ng-template #interfacesNotAvailable>
    <div class="p-col-12 p-py-0 p-pr-2">
        <div class="p-grid grid-row">
            <span i18n="@@bma_common_networkinterfacenotavailable" class="text-muted">Network Interface Not Available</span>
        </div>
    </div>
</ng-template>
