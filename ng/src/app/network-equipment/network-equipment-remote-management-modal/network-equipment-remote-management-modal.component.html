<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{width: '100%', 'max-height':'500px'}">
    <ng-template pTemplate="header">
        <h3 class="modal-title pull-left">
            <span class="mr-1" i18n="@@bma_networkequipmentremotemanagementmodal_remotemanagement">Remote Management</span>
            <sup>
                <a href="https://kb.leaseweb.com/kb/dedicated-network-equipment/remote-management-of-your-dedicated-network-equipment/">
                    <i i18n-title="@@bma_common_kb" title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i>
                </a>
            </sup>
        </h3>
    </ng-template>

    <div class="modal-body">
        <app-loader *ngIf="isLoadingEquipment || isLoadingCredentials"></app-loader>

        <div *ngIf="!isLoadingEquipment && !isLoadingCredentials && networkEquipment && credentials">
            <h5 *ngIf="networkEquipment.managed" i18n="@@bma_networkequipmentremotemanagementmodal_networkequipmentmanagedbytext" class="mt-4">This network equipment is currently managed by Leaseweb.</h5>
            <ng-conatiner *ngIf="!networkEquipment.managed">
                <div *ngIf="networkEquipment.featureAvailability.remoteManagement && credentials.credentials.length === 0" class="alert alert-warning alert-dismissible" role="alert">
                    <p i18n="@@bma_networkequipmentremotemanagementmodal_remotemanagementnosetupmessage">Your dedicated network equipment is capable of remote management, but it has either never been setup, or the credentials have been removed.</p>
                </div>
                <div *ngIf="!networkEquipment.featureAvailability.remoteManagement">
                    <h4 i18n="@@bma_networkequipmentremotemanagementmodal_remotemanagementnotavailable">Remote Management not available</h4>
                    <p i18n="@@bma_networkequipmentremotemanagementmodal_remotemanagementnotavailablededicated">Unfortunately remote management is not available for this dedicated network equipment.</p>
                    <p i18n="@@bma_networkequipmentremotemanagementmodal_remotemanagementnotavailableinrackmessage">Your dedicated network equipment, or the rack which it is located in is not capable of remote management.</p>
                    <p i18n="@@bma_networkequipmentremotemanagementmodal_remotemanagementnotavailablecontactsales">Contact a sales representative to discuss the available options.</p>
                </div>

                <div *ngIf="networkEquipment.featureAvailability.remoteManagement">
                    <div>
                        <h4 i18n="@@bma_networkequipmentremotemanagementmodal_remotemanagementipaddress">Remote management IP address</h4>
                        <p>
                            <span>{{ networkEquipment.networkInterfaces.remoteManagement.ip }}</span> -
                            <a target="_blank" href="http://{{ networkEquipment.networkInterfaces.remoteManagement.ip|cidrToIp }}"><span class="fa fa-login mr-1"></span><span i18n="@@bma_networkequipmentremotemanagementmodal_logintoipmiinterface">Login to IPMI Interface</span></a>
                        </p>
                        <div *ngIf="!isEmployee">
                            <p>
                                <a href="/bare-metals/remoteManagement"> <span class="fa fa-download mr-1"></span><span i18n="@@bma_networkequipmentremotemanagementmodal_downloadopenvpnprofile"> Download OpenVPN profile </span></a>
                            </p>
                            <p class="help-block">
                                <span i18n="@@bma_networkequipmentremotemanagementmodal_ipmiaccessvpnmessage">You are only able to access the IPMI interface of your dedicated network equipment if you are connected to the Leaseweb remote management network using OpenVPN. For more information on setting up an OpenVPN connection go to the</span>
                                <a i18n="@@bma_networkequipmentremotemanagementmodal_remotemanagementoverviewpage" href="/bare-metals/remoteManagement">Remote Management overview page</a>.
                            </p>
                        </div>
                    </div>

                    <div *ngIf="credentials.credentials.length > 0">
                        <h4 i18n="@@bma_networkequipmentremotemanagementmodal_remotemanagementcredentials">Remote management credentials</h4>
                        <div class="table-responsive">
                            <app-credential-list-table [equipmentId]="networkEquipmentId" equipmentType="networkEquipments" credentialsType="REMOTE_MANAGEMENT" [addCredentials]="false" [loadedCredentials]="credentials"></app-credential-list-table>
                        </div>
                    </div>
                    <p class="mt-5" i18n="@@bma_remotemanagement_remotemanagement_network_equipment_kb_info">
                        For details on how RM works, please visit our
                        <a href="https://kb.leaseweb.com/kb/dedicated-network-equipment/remote-management-of-your-dedicated-network-equipment/" target="_blank"> Knowledge Base </a>
                    </p>
                </div>
            </ng-conatiner>
        </div>
    </div>

    <ng-template pTemplate="footer">
        <div class="modal-footer">
            <button pButton type="button" icon="pi pi-times" iconPos="left" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
        </div>
    </ng-template>
</p-dialog>
