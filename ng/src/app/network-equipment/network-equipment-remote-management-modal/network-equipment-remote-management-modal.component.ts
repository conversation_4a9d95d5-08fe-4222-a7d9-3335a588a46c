import { Component, OnInit } from '@angular/core';
import { Clipboard } from '@angular/cdk/clipboard';
import { HttpClient } from '@angular/common/http';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-network-equipment-remote-management-modal',
  templateUrl: './network-equipment-remote-management-modal.component.html',
  styleUrls: ['./network-equipment-remote-management-modal.component.css'],
})
export class NetworkEquipmentRemoteManagementModalComponent implements OnInit {
  networkEquipmentId: string;
  networkEquipment: any;
  credentials: any;
  activeJob: any;
  isLoadingEquipment = false;
  isLoadingCredentials = false;
  isEmployee = false;
  powerCycle = true;
  showDialog = true;
  error: boolean;

  constructor(
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig,
    private clipboard: Clipboard
  ) {}

  ngOnInit(): void {
    this.networkEquipmentId = this.dynamicDialogConfig.data.networkEquipmentId;
    this.isEmployee = this.currentUserService.isEmployee();

    if (this.networkEquipment) {
      this.error = false;
      this.networkEquipmentId = this.networkEquipment.id;
    }

    if (!this.networkEquipmentId) {
      this.error = true;
      return;
    }

    if (!this.networkEquipment) {
      this.isLoadingEquipment = true;
      this.httpClient.get(`/_/internal/dedicatedserverapi/v2/networkEquipments/${this.networkEquipmentId}`).subscribe({
        next: (networkEquipment: any) => {
          this.isLoadingEquipment = false;
          this.networkEquipment = networkEquipment;
          this.error = false;
        },
        error: (error: any) => {
          this.isLoadingEquipment = false;
          this.error = true;
        },
      });
    }

    this.isLoadingCredentials = true;
    this.httpClient
      .get(`/_/internal/bmpapi/v2/networkEquipments/${this.networkEquipmentId}/credentials/REMOTE_MANAGEMENT`)
      .subscribe({
        next: (credentials: any) => {
          this.isLoadingCredentials = false;
          this.credentials = credentials;
          this.error = false;
        },
        error: (error: any) => {
          this.isLoadingCredentials = false;
          this.credentials = [];
        },
      });
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }
}
