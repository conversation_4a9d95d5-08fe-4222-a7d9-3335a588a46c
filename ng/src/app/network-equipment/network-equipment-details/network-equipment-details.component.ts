import { Component, OnInit, Input } from '@angular/core';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { ReferenceEditModalComponent } from 'src/app/reference-edit-modal/reference-edit-modal.component';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';

@Component({
  selector: 'app-network-equipment-details',
  templateUrl: './network-equipment-details.component.html',
  styleUrls: ['./network-equipment-details.component.css'],
})
export class NetworkEquipmentDetailsComponent implements OnInit {
  @Input() equipment: any;
  @Input() equipmentId: any;
  @Input() isEmployee: boolean;
  @Input() equipmentType: any;
  dynamicDialogRef: DynamicDialogRef;
  powerPortName: string;
  powerPort: string;
  displayAllSubnets = true;
  networkPorts = [];

  constructor(
    private modalService: ModalService,
    private currentUserService: CurrentUserService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();

    if (this.equipment.networkInterfaces) {
      const networkInterfaceOrder = ['public', 'internal', 'remoteManagement'];
      networkInterfaceOrder.forEach((networkInterfacerKey) => {
        if (
          this.equipment.networkInterfaces[networkInterfacerKey] &&
          this.equipment.networkInterfaces[networkInterfacerKey].ports.length > 0
        ) {
          const networkPorts = this.equipment.networkInterfaces[networkInterfacerKey].ports;
          networkPorts.forEach((networkPort) => {
            networkPort.networkType = networkInterfacerKey;
            this.networkPorts.push(networkPort);
          });
        }
      });
    }
  }

  openReferenceEditModal() {
    this.dynamicDialogRef = this.modalService.show(ReferenceEditModalComponent, {
      equipment: this.equipment,
      equipmentType: 'networkEquipments',
    });

    this.dynamicDialogRef.onClose.subscribe((reason: any) => {
      if (typeof reason === 'string') {
        this.equipment.contract.reference = reason;
      }
    });
  }

  selectedPowerOutlet(powerPortName: string, powerPort: string): void {
    this.powerPortName = powerPortName;
    this.powerPort = powerPort;
  }

  onDisplayAdditionalSubnets(): void {
    this.displayAllSubnets = !this.displayAllSubnets;
  }
}
