<div class="row">
    <div class="col-md-6">
        <h3>
            <span i18n="@@bma_common_technicaldetails" class="mr-1">Technical Details</span>
            <sup>
                <a target="_blank" href="https://kb.leaseweb.com/kb/dedicated-network-equipment/network-dedicated-network-equipment-switches/">
                    <i i18n-title="@@bma_common_kb" title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i>
                </a>
            </sup>
        </h3>
        <table class="table table-sm">
            <colgroup>
                <col class="table-col-25" />
                <col class="table-col-75" />
            </colgroup>

            <tbody>
                <tr>
                    <th i18n="@@bma_common_brand">Brand</th>
                    <td>{{ equipment.specs.brand }}</td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_model">Model</th>
                    <td>{{ equipment.specs.model|default:'-' }}</td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_name">Name</th>
                    <td *ngIf="equipment.name">
                        <a *ngIf="isEmployee" [routerLink]="['/networkDevices', networkEquipment.name]">{{ equipment.name }}</a>
                        <span *ngIf="!isEmployee">{{ equipment.name }}</span>
                    </td>
                    <td *ngIf="!equipment.name">
                        {{ equipment.name|default:'-' }}
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_serial">Serial</th>
                    <td>
                        <ng-container *ngIf="equipment.serialNumber|default; else notAvailable">
                            {{ equipment.serialNumber }}
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_publicip">Public IP</th>
                    <td>
                        <ng-container *ngIf="equipment.networkInterfaces.public?.ip|default; else notAvailable">
                            <span class="selectable">{{ equipment.networkInterfaces.public?.ip|default|cidrToIp }}</span>
                            <span *ngIf="equipment.networkInterfaces.public?.nullRouted|default" class="badge badge-danger">NULL ROUTED</span>
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_remotemanagement">Remote Management</th>
                    <td>
                        <ng-container *ngIf="equipment.featureAvailability.remoteManagement; else notAvailable">
                            <span class="selectable">{{ equipment.networkInterfaces.remoteManagement.ip|default:'No IP details'|cidrToIp }}</span>
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_managementinterface">Management Interface</th>
                    <td>
                        <ng-container *ngIf="equipment.featureAvailability.remoteManagement; else elseIfPublicIpBlock">
                            <a target="_blank" href="https://{{ equipment.networkInterfaces.remoteManagement.ip|cidrToIp }}">https://{{ equipment.networkInterfaces.remoteManagement.ip|cidrToIp }}</a>
                        </ng-container>
                        <ng-template #elseIfPublicIpBlock>
                            <ng-container *ngIf="equipment.networkInterfaces.public.ip; else notAvailable">
                                <a target="_blank" href="https://{{ equipment.networkInterfaces.public.ip|cidrToIp }}">https://{{ equipment.networkInterfaces.public.ip|cidrToIp }}</a>
                            </ng-container>
                        </ng-template>
                    </td>
                </tr>
            </tbody>
        </table>

        <h3 class="mb-2" i18n="@@bma_common_pduinformation">PDU Information</h3>

        <table class="table table-sm">
            <thead>
                <tr>
                    <th i18n="@@bma_common_pdu" scope="col">PDU</th>
                    <th i18n="@@bma_common_outlet" scope="col">Outlet</th>
                    <th scope="col"></th>
                </tr>
            </thead>

            <tbody>
                <tr *ngFor="let powerPort of equipment.powerPorts">
                    <td *ngIf="isEmployee" class="align-middle">
                        <a [routerLink]="['/powerbars', powerPort.name]">
                            {{ powerPort.name | uppercase }}
                        </a>
                    </td>
                    <td *ngIf="!isEmployee">{{ powerPort.name | uppercase }}</td>

                    <td class="align-middle">{{ powerPort.port | uppercase }}</td>

                    <td *ngIf="isEmployee" class="text-right">
                        <a href="javascript:void(0);" (click)="selectedPowerOutlet(powerPort.name, powerPort.port)">
                            <span class="fa fa-administration"></span>
                        </a>
                    </td>
                </tr>
                <tr *ngIf="equipment.powerPorts?.length == 0">
                    <td colspan="3" class="text-center p-4" i18n="@@bma_networkequipmentdetails_nopdumessage">There are no PDU outlets available for this dedicated network equipment.</td>
                </tr>
            </tbody>
        </table>

        <h3 class="mt-5 mb-2">
            <span i18n="@@bma_common_networkinformation">Network Information</span>
        </h3>

        <table class="table table-sm">
            <thead>
                <tr>
                    <th i18n="@@bma_common_switch" scope="col">Switch</th>
                    <th i18n="@@bma_common_network" scope="col">Network</th>
                    <th i18n="@@bma_common_port" scope="col" class="text-right">Port</th>
                </tr>
            </thead>
            <ng-container *ngIf="networkPorts">
                <tbody>
                    <tr *ngFor="let networkPort of networkPorts">
                        <td>
                            <a *ngIf="isEmployee" [routerLink]="['/networkDevices', networkPort.name]">
                                {{ networkPort.name }}
                            </a>
                            <span *ngIf="!isEmployee">{{ networkPort.name }}</span>
                        </td>
                        <td>{{ networkPort.networkType }}</td>
                        <td class="text-right">{{ networkPort.port }}</td>
                    </tr>
                </tbody>
            </ng-container>
            <tr *ngIf="networkPorts?.length == 0">
                <td i18n="@@bma_common_nonetworkports" colspan="3" class="text-center p-4">There are no network ports available for this equipment</td>
            </tr>
        </table>
    </div>

    <div class="col-md-6">
        <h3>
            <span i18n="@@bma_common_administrativedetails" class="mr-1">Administrative Details</span>
            <sup>
                <a target="_blank" href="https://kb.leaseweb.com/kb/dedicated-network-equipment/network-dedicated-network-equipment-switches/">
                    <i i18n-title="@@bma_common_kb" title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i>
                </a>
            </sup>
        </h3>
        <table class="table table-sm">
            <tbody>
                <tr *ngIf="'DEDICATED' == (equipment.rack.type|default|uppercase) && equipment.rack.id|default">
                    <th i18n="@@bma_common_dedicatedrack">Dedicated Rack</th>
                    <td>
                        <a [routerLink]="['/racks', equipment.rack.id]">
                            <span class="text-monospace">{{ equipment.rack.id }}</span>
                        </a>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_deliverystatus">Delivery Status</th>
                    <td>
                        <app-contract-delivery-status [deliveryStatus]="equipment.contract?.deliveryStatus|default:'UNASSIGNED'"> </app-contract-delivery-status>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_contractstatus">Contract Status</th>
                    <td>
                        <app-contract-status [status]="equipment.contract?.status|default:''"></app-contract-status>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_id">ID</th>
                    <td>
                        <span class="selectable">{{ equipment.id }}</span>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_product">Product</th>
                    <td>{{ equipment.type | titlecase }}</td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_location">Location</th>
                    <td>
                        <app-equipment-location [location]="equipment.location"></app-equipment-location>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_serviceid">Service ID</th>
                    <td>
                        <span *ngIf="equipment.contract; else notAvailable" class="selectable">{{ equipment.contract.id|default }}</span>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_reference">Reference</th>
                    <td>
                        <ng-container *ngIf="equipment.contract?.reference|default; else notAvailable">
                            {{ equipment.contract.reference|default }}
                        </ng-container>

                        <a href i18n-title="@@bma_common_editreference" (click)="openReferenceEditModal(); false" class="pull-right" title="Edit Reference">
                            <span class="fa fa-change" aria-hidden="true"></span>
                        </a>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_billingfrequency">Billing Frequency</th>
                    <td>
                        <ng-container *ngIf="equipment.contract; else notAvailable">
                            {{ equipment.contract.billingCycle | default }}
                            {{ equipment.contract.billingFrequency | default | lowercase }}(s)
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_contractterm">Contract Term</th>
                    <td>
                        <ng-container *ngIf="equipment.contract; else notAvailable">
                            {{ equipment.contract.contractTerm }} month(s)
                            <ng-container *ngIf="equipment.contract.contractType != 'NORMAL'">
                                &nbsp;
                                <span i18n="@@bma_common_contracttype" class="badge badge-info"> {{ equipment.contract.contractType|capitalize }} Contract </span>
                            </ng-container>
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_startdate">Start Date</th>
                    <td>
                        <ng-container *ngIf="equipment.contract?.startsAt|default; else notAvailable">
                            {{ equipment.contract.startsAt|lswDate }}
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_enddate">End Date</th>
                    <td>
                        <ng-container *ngIf="equipment.contract?.endsAt|default; else notAvailable">
                            {{ equipment.contract.endsAt|lswDate }}
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_connectivity">Connectivity</th>
                    <td>
                        <ng-container i18n="@@bma_common_connectivityseeotherpacks" *ngIf="(equipment.contract?.networkTraffic.type|default:'') == 'CONNECTIVITY'; else flatfee"> Connectivity - see other packs </ng-container>

                        <ng-template #flatfee>
                            <ng-container *ngIf="(equipment.contract?.networkTraffic.type|default:'') == 'FLATFEE'; else other">
                                Bandwidth (Flat) -
                                {{ equipment.contract.networkTraffic.datatrafficLimit }}
                                {{ equipment.contract.networkTraffic.datatrafficUnit }}
                                ({{ equipment.contract.networkTraffic.trafficType }})
                            </ng-container>
                        </ng-template>

                        <ng-template #other>
                            <ng-container *ngIf="equipment.contract?.networkTraffic.type|default; else notAvailable">
                                {{ equipment.contract.networkTraffic.type }}
                                {{ equipment.contract.networkTraffic.datatrafficLimit }}
                                {{ equipment.contract.networkTraffic.datatrafficUnit }}
                                ({{ equipment.contract.networkTraffic.trafficType }})
                            </ng-container>
                        </ng-template>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_subnets">Subnets</th>
                    <td>
                        <ng-container *ngIf="(equipment.contract.subnets|default:[]).length > 0; else notAvailable">
                            <ng-container *ngFor="let subnet of equipment.contract.subnets |keyvalue| slice:0:3">
                                {{ subnet.value.quantity }} x IPV{{ subnet.value.version }} :
                                {{ subnet.value.description }}
                                <br *ngIf="subnet.key < 2" />
                            </ng-container>
                            <div *ngIf="(equipment.contract.subnets|default:[]).length > 3" [ngClass]="{'d-none': displayAllSubnets}">
                                <ng-container *ngFor="let subnet of equipment.contract.subnets |keyvalue| slice:3">
                                    {{ subnet.value.quantity }} x IPV{{ subnet.value.version }} :
                                    {{ subnet.value.description }}
                                    <br />
                                </ng-container>
                            </div>
                            <ng-container *ngIf="(equipment.contract.subnets|default:[]).length > 3">
                                <a i18n-title="@@bma_common_additionalsubnets" (click)="onDisplayAdditionalSubnets()" class="pull-right" title="Additional subnets">
                                    <span i18n-title="@@bma_common_additionalsubnets" title="Additional subnets" class="fa fa-details" aria-hidden="true"></span>
                                </a>
                            </ng-container>
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_dataused">Data Used</th>
                    <td class="data_used">
                        <app-data-usage [equipment]="equipment" [equipmentType]="equipmentType"></app-data-usage>
                    </td>
                </tr>

                <tr>
                    <th>
                        <span i18n="@@bma_common_sla" class="mr-1">SLA</span>
                        <sup>
                            <a href="https://kb.leaseweb.com/support/service-level-agreement-sla" target="_blank">
                                <i i18n-title="@@bma_common_kb" title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i>
                            </a>
                        </sup>
                    </th>
                    <td>
                        <ng-container *ngIf="equipment.contract; else notAvailable">
                            {{ equipment.contract.sla }}
                        </ng-container>
                    </td>
                </tr>
            </tbody>
        </table>
        <app-private-network-information-section [equipment]="equipment" [equipmentType]="equipmentType"></app-private-network-information-section>
    </div>
</div>

<ng-template #notAvailable>
    <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
</ng-template>
