<div class="row">
    <div class="col-8">
        <app-customer-aware-header i18n-caption="@@bma_common_dedicatednetworkequipments" caption="Dedicated Network Equipment" [customerId]="customerId" [salesOrgId]="salesOrgId" [isEmployee]="isEmployee"></app-customer-aware-header>
    </div>
    <div class="col-4">
        <div class="float-right">
            <button *ngIf="isEmployee" i18n-label="@@bma_common_addtoprivatenetwork" pButton type="button" label="Add to Private Network" (click)="openAddToPrivateNetworkModal()" icon="pi pi-plus" class="p-button-success pull-right"></button>
            <button *ngIf="!isEmployee" i18n-label="@@bma_common_orderprivatenetwork" pButton type="button" label="Order Private Network" (click)="openOrderPrivateNetworkModal(); false" icon="pi pi-plus" class="p-button-success pull-right"></button>
        </div>
    </div>
</div>
<div>
    <form role="form" [formGroup]="filters" (ngSubmit)="submitFilters()">
        <div class="row mb-3">
            <div class="col">
                <input i18n-placeholder="@@bma_common_filter" type="text" id="filter" formControlName="filter" class="form-control m-0" value="" placeholder="Filter..." autofocus />
            </div>
        </div>
        <div class="row mb-3">
            <div class="col-3">
                <select class="form-control" formControlName="managed">
                    <option value="null" i18n="@@bma_common_managementpack">Management Pack</option>
                    <option value="true" i18n="@@bma_common_managed">Managed</option>
                    <option value="false" i18n="@@bma_common_unmanaged">Unmanaged</option>
                </select>
            </div>
            <div class="col-3">
                <select class="form-control" formControlName="type">
                    <option value="null" i18n="@@bma_common_type">Type</option>
                    <option value="FIREWALL" i18n="@@bma_common_firewall">Firewall</option>
                    <option value="LOAD_BALANCER" i18n="@@bma_common_loadbalancer">Load Balancer</option>
                    <option value="SWITCH" i18n="@@bma_common_switch">Switch</option>
                </select>
            </div>
            <div class="col-6">&nbsp;</div>
        </div>
        <div class="row mb-3">
            <div class="col-12 text-right">
                <button i18n-label="@@bma_common_reset" pButton (click)="clearFilters()" label="Reset" icon="pi pi-refresh" type="button" class="p-button-secondary mr-2"></button>
                <button i18n-label="@@bma_common_search" pButton [disabled]="isLoading" label="Search" icon="pi pi-search"></button>
            </div>
        </div>
    </form>
</div>

<app-loader *ngIf="isLoading"></app-loader>

<div *ngIf="!isLoading && networkEquipments">
    <div class="p-col-12 bg-white p-mb-2 p-py-4 text-center" *ngIf="networkEquipments.networkEquipments.length === 0">
        <span i18n="@@bma_networkequipmentlist_nonetworkequipments" *ngIf="isEmployee; else notAvailableForCustomer"> No Dedicated Network Equipment found. </span>
        <ng-template #notAvailableForCustomer>
            <span i18n="@@bma_networkequipmentlist_nonetworkequipmentscustomer">You don't have any Dedicated Network Equipment.</span>
        </ng-template>
    </div>
    <p-accordion [multiple]="true">
        <p-accordionTab *ngFor="let networkEquipment of networkEquipments.networkEquipments">
            <ng-template pTemplate="header">
                <div class="row col-sm-12 align-items-center">
                    <div class="col-sm-2">
                        <h3 class="mt-2">{{ networkEquipment.id }}</h3>
                    </div>
                    <div class="col-sm-3">
                        <span class="h5">
                            {{ networkEquipment.contract.reference ? networkEquipment.contract.reference : '-' }}
                        </span>
                    </div>
                    <div class="col-sm-1">
                        <span class="h5">
                            {{ networkEquipment.type|default:'' | replace : '_' : ' ' | titlecase }}
                        </span>
                    </div>
                    <div class="col-sm-3">
                        <app-equipment-location [location]="networkEquipment.location"></app-equipment-location>
                    </div>
                    <div class="col-sm-2">
                        <span *ngIf="(networkEquipment.networkInterfaces.public && networkEquipment.networkInterfaces.public.ip); else notAvailable" class="h5" [ngClass]="{'text-danger':(networkEquipment.networkInterfaces.public.nullRouted|default:'')}" [title]="{'Null Routed': networkEquipment.networkInterfaces.public.nullRouted|default:''}"> {{ networkEquipment.networkInterfaces.public.ip|default:'-'|cidrToIp }}</span>
                    </div>
                    <div class="col-sm-1">
                        <div class="managed">
                            <span i18n="@@bma_common_managed" *ngIf="networkEquipment.managed">Managed</span>
                            <span i18n="@@bma_common_unmanaged" *ngIf="!networkEquipment.managed">Unmanaged</span>
                        </div>
                    </div>
                </div>
            </ng-template>
            <ng-template pTemplate="content" class="p-p-0">
                <div class="p-grid border-bottom">
                    <div class="p-col-12 p-md-10 border-right">
                        <h5 class="p-ml-6 pt-2" i18n="@@bma_common_networkequipmentdetails">Network Equipment Details</h5>
                        <div class="p-col-12 p-d-flex p-flex-col p-pb-0">
                            <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-2">
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_name">Name</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <span class="selectable">{{ networkEquipment.name|default:'-' }}</span>
                                    </div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_equipmentid">Equipment ID</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <span class="selectable">{{ networkEquipment.id|default:'-' }}</span>
                                    </div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_devicetype">Device Type</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ networkEquipment.brand }} {{ networkEquipment.type }}</div>
                                </div>
                                <div class="p-grid grid-row network-equipment-managed">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_ipaddress">IP Address</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <span *ngIf="(networkEquipment.networkInterfaces.public && networkEquipment.networkInterfaces.public.ip); else notAvailable" [ngClass]="{'text-danger':(networkEquipment.networkInterfaces.public.nullRouted|default:'')}" [title]="{'Null Routed': networkEquipment.networkInterfaces.public.nullRouted|default:''}"> {{ networkEquipment.networkInterfaces.public.ip|default:'-'|cidrToIp }}</span>
                                    </div>
                                </div>
                                <div class="p-grid grid-row network-equipment-managed">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_managed">Managed</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <span *ngIf="networkEquipment.managed" class="badge badge-success-outline ng-star-inserted" i18n="@@bma_common_yes">Yes</span>
                                        <span *ngIf="!networkEquipment.managed" class="badge badge-danger-outline ng-star-inserted" i18n="@@bma_common_no">No</span>
                                    </div>
                                </div>
                            </div>
                            <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-5">
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_location">Location</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <app-equipment-location [location]="networkEquipment.location"></app-equipment-location>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <ng-template #notAvailable>
                            <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
                        </ng-template>
                    </div>
                    <div class="p-col-12 p-md-2 quick-actions">
                        <h5 class="p-2" i18n="@@bma_common_quickactions">Quick Actions</h5>
                        <div class="p-grid p-flex-column align-items-start">
                            <a i18n-label="@@bma_common_credentials" pButton [routerLink]="['/networkEquipments',networkEquipment.id,'credentials']" class="p-button-link" label="Credentials" title="Credentials"></a>
                            <a i18n-label="@@bma_common_ips" pButton [routerLink]="['/networkEquipments',networkEquipment.id,'ips']" class="p-button-link" label="IPs" title="IPs"></a>
                            <a i18n-label="@@bma_common_nullroutehistory" pButton [routerLink]="['/networkEquipments',networkEquipment.id,'nullRouteHistory']" class="p-button-link" label="Null Route History" title="Null Route History"></a>
                            <a i18n-label="@@bma_common_graphs" pButton [routerLink]="['/networkEquipments',networkEquipment.id,'graphs']" class="p-button-link" [ngClass]="{'disabled' : networkEquipment['type'] !== 'SWITCH' || networkEquipment['type'] === 'RM'}">Graphs</a>
                            <button pButton *ngIf="isEmployee && networkEquipment.privateNetworks.length > 0 && networkEquipment.privateNetworks[0].status === 'CONFIGURED'" (click)="openRemoveFromPrivateNetworkModal(networkEquipment); false" i18n-label="@@bma_common_remove_from_pn" label="Remove from Private Network" class="p-button-link"></button>
                        </div>
                    </div>
                </div>
                <div class="p-grid p-pr-5 p-pt-3">
                    <a i18n-label="@@bma_common_manage" pButton [routerLink]="['/networkEquipments', networkEquipment.id]" class="p-button-primary ml-auto" label="Manage" title="Manage"></a>
                </div>
            </ng-template>
        </p-accordionTab>
    </p-accordion>
</div>

<div *ngIf="!isLoading && networkEquipments">
    <app-pagination [totalCount]="networkEquipments._metadata.totalCount" [limit]="networkEquipments._metadata.limit" [offset]="networkEquipments._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
</div>
