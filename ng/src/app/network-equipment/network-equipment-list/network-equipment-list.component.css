.true-icon {
  color: #256029;
  margin-left: 50px;
}

.false-icon {
  color: #c63737;
  margin-left: 50px;
}

:host ::ng-deep .p-accordion .p-accordion-content {
  padding: 0.5rem;
}
.grid-content {
  margin-bottom: 5px;
  padding-bottom: 4px;
  padding-left: 2px;
  font-size: 0.8rem !important;
}
.grid-content-border {
  border-bottom: 1px solid #ddd;
}
.grid-row {
  padding-right: 10px;
}
.quick-actions a,
.quick-actions button {
  padding-top: 4px;
  padding-bottom: 4px;
  font-size: 0.9rem;
  text-align: left;
}
.p-grid.grid-row:last-of-type .grid-content-border {
  border-bottom: none;
}
.disabled {
  color: currentColor;
  cursor: not-allowed;
  opacity: 0.5;
  pointer-events: none;
  text-decoration: none;
}
.network-equipment-managed .false-icon {
  margin: 0;
}

.managed {
  text-align: center;
}
.managed span {
  font-size: 0.75rem;
  margin-bottom: 5px;
  display: inline-block;
}
.managed i.pi.false-icon {
  margin-left: 0;
  text-align: center;
}
