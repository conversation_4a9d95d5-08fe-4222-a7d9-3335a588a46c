import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { AlertService } from 'src/app/services/alert.service';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { CustomValidator } from 'src/app/validators/custom.validator';
import { SiteService } from 'src/app/services/site.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { PrivateNetworkAddDedicatedNetworkEquipmentModalComponent } from 'src/app/private-network/private-network-add-dedicated-network-equipment-modal/private-network-add-dedicated-network-equipment-modal.component';
import { PrivateNetworkRemoveModalComponent } from 'src/app/private-network/private-network-remove-modal/private-network-remove-modal.component';
import { NetworkEquipmentOrderPrivateNetworkModalComponent } from 'src/app/network-equipment/network-equipment-order-private-network-modal/network-equipment-order-private-network-modal.component';
import { PrivateNetworkService } from 'src/app/services/private-network.service';

@Component({
  selector: 'app-network-equipment-list',
  templateUrl: './network-equipment-list.component.html',
  styleUrls: ['./network-equipment-list.component.css'],
})
export class NetworkEquipmentListComponent implements OnInit {
  customerId = '';
  salesOrgId = '';
  headerTitle = '';
  value: any;
  equipmentType = 'networkEquipments';
  isLoading = false;
  isEmployee = false;
  filters: UntypedFormGroup;
  dynamicDialogRef: DynamicDialogRef;

  networkEquipments: any;
  privateNetwork: any;

  constructor(
    private activatedRoute: ActivatedRoute,
    private modalService: ModalService,
    private httpClient: HttpClient,
    private router: Router,
    private titleService: Title,
    private readonly formBuilder: UntypedFormBuilder,
    private alertService: AlertService,
    private currentUserService: CurrentUserService,
    private customValidator: CustomValidator,
    private siteService: SiteService,
    private privateNetworkService: PrivateNetworkService
  ) {}

  ngOnInit(): void {
    this.headerTitle = 'Dedicated Network Equipment';
    this.isEmployee = this.currentUserService.isEmployee();

    this.filters = this.formBuilder.group({
      offset: [0],
      limit: [20],
      filter: [null],
      customerId: [null],
      salesOrgId: [null],
      managed: [null],
      type: [null],
    });

    this.alertService.clear();
    if (!this.isEmployee) {
      this.customerId = this.currentUserService.getCustomerId();
      this.salesOrgId = this.currentUserService.getSalesOrgId();
    }

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
    this.privateNetworkService.getPrivateNetwork(this.customerId, this.salesOrgId).then((privateNetwork) => {
      this.privateNetwork = privateNetwork;
    });

    /*eslint-disable */
    const event = new Event('update_product_navbar');
    event['customerId'] = this.customerId;
    event['salesOrgId'] = this.salesOrgId;
    event['country'] = this.siteService.getCountry(this.salesOrgId);
    window.dispatchEvent(event);
    /*eslint-enable */

    if (this.customerId) {
      this.headerTitle = this.headerTitle + ' | Customer ' + this.customerId + ' | Leaseweb';
    }

    this.titleService.setTitle(this.headerTitle);
  }

  onQueryParamsChanged(queryParams): void {
    this.isLoading = true;
    this.filters.reset(queryParams);
    const params = this.sanitise(this.filters.getRawValue());

    if (queryParams.customerId) {
      this.customerId = queryParams.customerId;
    }

    if (queryParams.salesOrgId) {
      this.salesOrgId = queryParams.salesOrgId;
    }

    if (params.filter) {
      const filterType = this.getFilterType(params.filter);
      params[filterType] = params.filter;
    }

    this.httpClient.get<any>(`/_/internal/dedicatedserverapi/v2/networkEquipments`, { params }).subscribe(
      (data: any) => {
        this.isLoading = false;
        this.networkEquipments = data;
      },
      (error: any) => {
        this.isLoading = false;
        this.networkEquipments = [];
      }
    );
  }

  getFilterType(value: any): any {
    // TODO const contractItemId = /^[0-9]{14}$/ , will be developed in future;
    // TODO const networkEquipmentId = /^[0-9]{1,8}$/ ,will be developed in future;
    const site = /^[A-Z]{3}-[0-9]{2}$/;

    if (this.customValidator.isValidIp(value)) {
      return 'ip';
    }

    /* TODO filter by contractItemId,networkEquipmentId and macAddress will be developed in future;
    if (this.customValidator.isValidMacAddress(value)) {
      return 'macAddress';
    }
    if (contractItemId.test(value)) {
      return 'contractItemId';
    }
     if (networkEquipmentId.test(value)) {
      return 'networkEquipmentId';
    }
    */

    if (site.test(value)) {
      return 'site';
    }

    return 'reference';
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  onManagedChange(managedValue: any): void {
    let queryParams = {};
    queryParams = { managed: managedValue, offset: 0 };

    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams,
      queryParamsHandling: 'merge',
    });
  }

  clearFilters(): void {
    let queryParams = null;
    this.filters.reset();

    if (this.isEmployee) {
      queryParams = { salesOrgId: this.salesOrgId, customerId: this.customerId };
      this.filters.reset(queryParams);
    }

    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams,
    });
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }

  getMetadataByKey(networkDeviceAttributes: any[], key: string): string {
    if (!networkDeviceAttributes || networkDeviceAttributes.length === 0) {
      return '-';
    }

    const metadata = networkDeviceAttributes.find((value) => value.key === key);

    return metadata ? metadata.value : '-';
  }

  openAddToPrivateNetworkModal() {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkAddDedicatedNetworkEquipmentModalComponent, {
      privateNetwork: this.privateNetwork,
    });
  }

  openRemoveFromPrivateNetworkModal(equipment: any) {
    this.dynamicDialogRef = this.modalService.show(PrivateNetworkRemoveModalComponent, {
      privateNetwork: this.privateNetwork,
      equipment,
      equipmentType: this.equipmentType,
    });
  }
  openOrderPrivateNetworkModal() {
    this.modalService.show(NetworkEquipmentOrderPrivateNetworkModalComponent, {});
  }
}
