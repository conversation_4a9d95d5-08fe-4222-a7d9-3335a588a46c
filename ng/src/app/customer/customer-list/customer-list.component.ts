import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { SiteService } from 'src/app/services/site.service';
import { LocationService } from 'src/app/services/location.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';

@Component({
  selector: 'app-customer-list',
  templateUrl: './customer-list.component.html',
  styleUrls: ['./customer-list.component.css'],
})
export class CustomerListComponent implements OnInit {
  customer: any;
  isLoading = false;
  filters: UntypedFormGroup;
  isFiltered = false;

  constructor(
    private activatedRoute: ActivatedRoute,
    private httpClient: HttpClient,
    private router: Router,
    private titleService: Title,
    private siteService: SiteService,
    private locationService: LocationService,
    private readonly formBuilder: UntypedFormBuilder
  ) {}

  ngOnInit(): void {
    this.filters = this.formBuilder.group({
      filter: [null],
    });

    this.titleService.setTitle($localize`:@@bma_customerlist_title:Customer Lookup` + ' | Leaseweb');

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    this.filters.reset(queryParams);
    const params = this.sanitise(this.filters.getRawValue());

    this.isFiltered = Object.keys(params).length !== 0;

    if (!this.isFiltered) {
      return;
    }

    if (params.filter) {
      const filterType = this.getFilterType(params.filter);
      params[filterType] = params.filter;
    }

    this.isLoading = true;

    this.httpClient.get<any>('/_legacy/customers', { params }).subscribe(
      (data) => {
        this.isLoading = false;
        this.customer = data;
      },
      (error) => {
        this.isLoading = false;
        this.customer = null;
      }
    );
  }

  getCustomerSalesOrgIds() {
    if (!this.customer) {
      return [];
    }

    const validSalesOrgIds = this.siteService.salesOrgIds();
    validSalesOrgIds.push('2200'); // Add support for showing fiberring customers

    return Object.keys(this.customer.salesOrgs).filter((id) => validSalesOrgIds.includes(id));
  }

  getFilterType(value: any): any {
    return 'customerId';
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  submitFilters(): void {
    this.changeRouterParams();
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
