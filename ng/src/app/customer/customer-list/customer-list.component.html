<div>
    <h1 i18n="@@bma_customerlist_header">Customer</h1>
</div>

<form role="form" [formGroup]="filters" (ngSubmit)="submitFilters()">
    <fieldset>
        <div class="row mb-3">
            <div class="col">
                <input i18n-placeholder="@@bma_customerlist_placeholder" type="text" id="filter" formControlName="filter" class="form-control m-0" placeholder="Customer ID..." autofocus />
            </div>
        </div>
        <div class="row mb-3 mt-3">
            <div class="col text-right">
                <button i18n-label="@@bma_common_search" pButton [disabled]="isLoading" label="Search" icon="pi pi-search"></button>
            </div>
        </div>
    </fieldset>
</form>

<app-loader *ngIf="isLoading"></app-loader>

<div *ngIf="!isLoading && isFiltered">
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th i18n="@@bma_common_customer" class="text-left" scope="col">Customer</th>
                    <th i18n="@@bma_customerlist_salesorg" class="text-center" scope="col">Sales Org</th>
                    <th i18n="@@bma_common_products" class="text-right" scope="col">Products</th>
                </tr>
            </thead>

            <tbody>
                <tr *ngIf="!customer">
                    <td i18n="@@bma_customerlist_nocustomer" class="text-center p-4" colspan="8">No customer found</td>
                </tr>
                <tr *ngFor="let salesOrgId of getCustomerSalesOrgIds()" [ngClass]="{'table-danger' : customer?.salesOrgs[salesOrgId].isTerminated || customer?.salesOrgs[salesOrgId].isSuspended }">
                    <td>
                        <b>
                            {{ customer.name }}
                        </b>
                        <br />
                        {{ customer.id }}
                        <span *ngIf="customer?.salesOrgs[salesOrgId].isSuspended" class="text-center"> <strong>(SUSPENDED)</strong></span>
                        <span *ngIf="customer?.salesOrgs[salesOrgId].isTerminated" class="text-center"> <strong>(TERMINATED)</strong></span>
                    </td>

                    <td class="text-center">
                        <app-country-flag salesOrgId="{{ salesOrgId }}"></app-country-flag>
                        <br />
                        {{ salesOrgId }}
                    </td>

                    <td class="text-right">
                        <ul *ngIf="salesOrgId !== '2200'; else fiberringBlock" class="list-inline customer-action-list">
                            <li>
                                <a [href]="locationService.getAbsoluteUrlWithBase('/servers?customerId=' + customer.id + '&salesOrgId=' + salesOrgId)">
                                    <span class="customer-action fa fa-server"></span>
                                    <span i18n="@@bma_common_dedicatedservers">Dedicated Servers</span>
                                </a>
                            </li>

                            <li>
                                <a [href]="locationService.getAbsoluteUrlWithBase('/racks?customerId=' + customer.id + '&salesOrgId=' + salesOrgId)">
                                    <span class="customer-action fa fa-privaterack"></span>
                                    <span i18n="@@bma_common_dedicatedracks">Dedicated Racks</span>
                                </a>
                            </li>

                            <li>
                                <a [href]="locationService.getAbsoluteUrlWithBase('/colocations?customerId=' + customer.id + '&salesOrgId=' + salesOrgId)">
                                    <span class="customer-action fa fa-colocation"></span>
                                    <span i18n="@@bma_common_colocations">Colocations</span>
                                </a>
                            </li>

                            <li>
                                <!-- TODO change this to [routerLink] later -->
                                <a href="/emp/privateNetwork/lookup?customerId={{ customer.id }}&salesOrgId={{ salesOrgId }}">
                                    <span class="customer-action fa fa-privatenetwork"></span>&nbsp;
                                    <span i18n="@@bma_common_privatenetworks">Private Networks</span>
                                </a>
                            </li>

                            <li>
                                <a [routerLink]="['/floating-ips']" [queryParams]="{customerId: customer.id, salesOrgId: salesOrgId}">
                                    <span class="customer-action fa fa-floatingips"></span>
                                    <span i18n="@@bma_common_floatingips">Floating IPs</span>
                                </a>
                            </li>

                            <li>
                                <a [routerLink]="['/aggregationPacks']" [queryParams]="{customerId: customer.id, salesOrgId: salesOrgId}">
                                    <span class="customer-action fa fa-log"></span>
                                    <span i18n="@@bma_common_aggregationpacks">Aggregation Packs</span>
                                </a>
                            </li>

                            <li>
                                <a [routerLink]="['/networkEquipments']" [queryParams]="{customerId: customer.id, salesOrgId: salesOrgId}">
                                    <span class="customer-action fa fa-server"></span>&nbsp;
                                    <span i18n="@@bma_common_dedicatednetworkequipments">Dedicated Network Equipment</span>
                                </a>
                            </li>
                            <li>
                                <a [href]="locationService.getAbsoluteUrlWithBase('/dedicatedStorages?customerId=' + customer.id + '&salesOrgId=' + salesOrgId)">
                                    <span class="customer-action fa fa-server"></span>&nbsp;
                                    <span i18n="@@bma_common_dedicatedstorages">Dedicated Storages</span>
                                </a>
                            </li>
                        </ul>
                        <ng-template #fiberringBlock>
                            <ul class="list-inline customer-action-list">
                                <li>
                                    <a [routerLink]="['/directInternetAccess']" [queryParams]="{customerId: customer.id, salesOrgId: salesOrgId}">
                                        <span class="customer-action fa fa-network"></span>
                                        <span i18n="@@bma_directinternetaccesslist_title">Direct Internet Access</span>
                                    </a>
                                </li>
                                <li>
                                    <a [routerLink]="['/ipTransits']" [queryParams]="{customerId: customer.id, salesOrgId: salesOrgId}">
                                        <span class="customer-action fa fa-network"></span>
                                        <span i18n="@@bma_iptransitlist_title">IP Transits</span>
                                    </a>
                                </li>
                                <li>
                                    <a [routerLink]="['/aggregationPacks']" [queryParams]="{customerId: customer.id, salesOrgId: salesOrgId}">
                                        <span class="customer-action fa fa-log"></span>
                                        <span i18n="@@bma_common_aggregationpacks">Aggregation Packs</span>
                                    </a>
                                </li>
                            </ul>
                        </ng-template>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
