<div *ngIf="privateRack" class="row">
    <div class="col-md-6">
        <h3 class="mb-2" i18n="@@bma_common_technicaldetails">Technical Details</h3>

        <table class="table table-sm">
            <tbody>
                <tr>
                    <td i18n="@@bma_common_publicip">Public IP</td>
                    <td>
                        <ng-container *ngIf="privateRack.networkInterfaces.public && privateRack.networkInterfaces.public.ip|default:''; else notAvailable">
                            {{ privateRack.networkInterfaces.public.ip|default:'' }}
                        </ng-container>
                        <ng-container *ngIf="!isEmployee">
                            <a pButton i18n-title="@@bma_common_requestmoreips" *ngIf="privateRack.contract.status|default:'' == 'ACTIVE'; pendingContractModification" target="_blank" [href]="getCommerceRequestMoreIpsUrl()" class="float-right p-button-link p-button-sm p-py-0" title="Request more IPs" data-client-validation="true" data-force-another="2">
                                <i class="fa fa-ipmanagement"></i>
                            </a>
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <td i18n="@@bma_common_remotemanagement">Remote Management</td>
                    <td>
                        <ng-container *ngIf="privateRack.featureAvailability.remoteManagement;else notAvailable">
                            <span i18n="@@bma_common_active" class="badge badge-success">ACTIVE</span>
                        </ng-container>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="col-md-6">
        <h3 class="mb-2" i18n="@@bma_common_administrativedetails">Administrative Details</h3>
        <table class="table table-sm">
            <tbody>
                <tr>
                    <th i18n="@@bma_common_deliverystatus">Delivery Status</th>
                    <td>
                        <app-contract-delivery-status [deliveryStatus]="privateRack.contract.deliveryStatus|default:''"></app-contract-delivery-status>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_contractstatus">Contract Status</th>
                    <td>
                        <app-contract-status [status]="privateRack.contract.status|default:''"></app-contract-status>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_rackid">Rack ID</th>
                    <td>
                        {{ privateRack.id }}
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_serviceid">Service ID</th>
                    <td>
                        <ng-container *ngIf="privateRack.contract.id; else notAvailable">
                            {{ privateRack.contract.id|default }}
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_reference">Reference</th>
                    <td>
                        <ng-container *ngIf="privateRack.contract.reference|default:''; else notAvailable">
                            {{ privateRack.contract.reference }}
                        </ng-container>
                        <a i18n-title="@@bma_common_editreference" href (click)="openReferenceEditModal(); false" class="pull-right" title="Edit Reference">
                            <span class="fa fa-change" aria-hidden="true"></span>
                        </a>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_location">Location</th>
                    <td>
                        <app-equipment-location [location]="privateRack.location"></app-equipment-location>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_contractterm">Contract Term</th>
                    <td>
                        <ng-container *ngIf="privateRack.contract.contractTerm|default:''; else notAvailable">
                            {{ privateRack.contract.contractTerm }} month(s)
                            <ng-container *ngIf="privateRack.contract.contractType != 'NORMAL'">
                                &nbsp;
                                <span class="badge badge-info"> {{ privateRack.contract.contractType|uppercase }} Contract </span>
                            </ng-container>
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_startdate">Start Date</th>
                    <td>
                        <ng-container *ngIf="privateRack.contract.startsAt|default:''; else notAvailable">
                            {{ privateRack.contract.startsAt|lswDate }}
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_enddate">End Date</th>
                    <td>
                        <ng-container *ngIf="privateRack.contract.endsAt|default:''; else notAvailable">
                            {{ privateRack.contract.endsAt|lswDate }}
                        </ng-container>
                    </td>
                </tr>

                <tr>
                    <th i18n="@@bma_common_subnets">Subnets</th>
                    <td>
                        <ng-container *ngIf="(privateRack.contract.subnets|default:[]).length > 0; else hasNoSubnets">
                            <ng-container *ngFor="let subnet of privateRack.contract.subnets |keyvalue| slice:0:3">
                                {{ subnet.value.quantity }} x IPV{{ subnet.value.version }} :
                                {{ subnet.value.description }}
                                <br *ngIf="subnet.key < 2" />
                            </ng-container>
                            <div *ngIf="(privateRack.contract.subnets|default:[]).length > 3" [ngClass]="{'d-none': displayAllSubnets}">
                                <ng-container *ngFor="let subnet of privateRack.contract.subnets |keyvalue| slice:3">
                                    {{ subnet.value.quantity }} x IPV{{ subnet.value.version }} :
                                    {{ subnet.value.description }}
                                    <br />
                                </ng-container>
                            </div>
                            <ng-container *ngIf="(privateRack.contract.subnets|default:[]).length > 3">
                                <a i18n-title="@@bma_common_additionalsubnets" (click)="onDisplayAdditionalSubnets()" class="pull-right" title="Additional subnets">
                                    <span i18n-title="@@bma_common_additionalsubnets" title="Additional subnets" class="fa fa-details" aria-hidden="true"></span>
                                </a>
                            </ng-container>
                        </ng-container>
                    </td>
                </tr>
                <tr>
                    <th i18n="@@bma_common_connectivity">Connectivity</th>
                    <td>
                        <app-data-pack-display [equipment]="privateRack" [equipmentType]="equipmentType" [isEmployee]="isEmployee"></app-data-pack-display>
                    </td>
                </tr>

                <tr>
                    <th i18n-title="@@bma_common_dataused">Data used</th>
                    <td class="data_used">
                        <app-data-usage [equipment]="privateRack" [equipmentType]="equipmentType"></app-data-usage>
                    </td>
                </tr>

                <tr>
                    <th>
                        <span i18n="@@bma_common_sla">SLA</span>
                        <sup>
                            <a i18n-title="@@bma_common_kb" href="https://kb.leaseweb.com/support/service-level-agreement-sla" target="_blank">
                                <i title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i>
                            </a>
                        </sup>
                    </th>
                    <td>
                        <ng-container *ngIf="privateRack.contract.sla|default:''; else notAvailable">
                            {{ privateRack.contract.sla }}
                        </ng-container>
                    </td>
                </tr>
            </tbody>
        </table>
        <app-private-network-information-section [equipment]="privateRack" [equipmentType]="equipmentType"></app-private-network-information-section>
    </div>
</div>
<div id="private-rack-servers"></div>

<app-loader *ngIf="isRackEquipmentLoading"></app-loader>

<ng-container *ngIf="(privateRack.units|default:[]) && !isRackEquipmentLoading">
    <h3 *ngIf="caption|default:'Dedicated Rack Equipments'" class="mb-3 mt-3"><span class="fa fa-server mr-1"></span><span i18n="@@bma_privaterackdetails_dedicatedrackequipments">Dedicated Rack Equipments</span></h3>

    <table class="table table-rowspan-striped comfortable">
        <thead>
            <tr>
                <th i18n="@@bma_privaterackdetails_rackunit" class="text-center table-col-7">Rack Unit</th>
                <th i18n="@@bma_common_id" class="text-center table-col-7">ID</th>
                <th i18n="@@bma_common_reference">Reference</th>
                <th i18n="@@bma_common_equipmenttype" class="table-col-14">Equipment Type</th>
                <th i18n="@@bma_common_ip" class="text-center table-col-14">IP</th>
                <th i18n="@@bma_common_switch" class="text-center table-col-14">Switch</th>
                <th class="text-center">&nbsp;</th>
            </tr>
        </thead>

        <tbody>
            <ng-container *ngFor="let rackUnit of privateRack.units; let i = index">
                <tr *ngIf="rackUnit.connectedUnits.indexOf(previousUnit(i, privateRack.units)) > -1; else free" class="rack-unit">
                    <td class="border-right text-center">
                        <span class="text-monospace">{{ rackUnit.unit }}</span>
                    </td>
                </tr>

                <ng-template #free>
                    <tr *ngIf="'FREE' == (rackUnit.status|default:''); else equipments" class="rack-unit">
                        <td class="border-right text-center">
                            <span class="text-monospace">{{ rackUnit.unit }}</span>
                        </td>
                        <td>&nbsp;</td>
                        <td class="text-left" colspan="5">
                            <span class="text-muted" i18n="@@bma_common_free">Free</span>
                        </td>
                    </tr>
                </ng-template>

                <ng-template #equipments>
                    <ng-container *ngIf="rackUnit.equipment;else noEquipment">
                        <tr id="equipment_{{ rackUnit.equipment.id }}" class="{{ rackUnit.equipment && rackUnit.equipment.contract && rackUnit.equipment.contract.deliveryStatus|default:'Unknown'|lowercase }}-equipment rack-unit-status-{{ rackUnit.status|lowercase }}">
                            <td class="border-right text-center">
                                <span class="text-monospace">{{ rackUnit.unit }}</span>
                            </td>

                            <td class="text-center align-middle" [attr.rowspan]="rackUnit.connectedUnits?.length">
                                <ng-container *ngIf="'DEDICATED_SERVER' == (rackUnit.type|default:''); else networkEquipmentLink">
                                    <a *ngIf="isEmployee" href="/emp/servers/{{ rackUnit.equipment.id }}">
                                        <span class="text-monospace">{{ rackUnit.equipment.id }}</span>
                                    </a>
                                    <a *ngIf="!isEmployee" href="/bare-metals/servers/{{ rackUnit.equipment.id }}">
                                        <span class="text-monospace">{{ rackUnit.equipment.id }}</span>
                                    </a>
                                </ng-container>
                                <ng-template #networkEquipmentLink>
                                    <a *ngIf="isEmployee" href="/emp/networkEquipments/{{ rackUnit.equipment.id }}">
                                        <span class="text-monospace">{{ rackUnit.equipment.id }}</span>
                                    </a>
                                    <a *ngIf="!isEmployee" href="/bare-metals/networkEquipments/{{ rackUnit.equipment.id }}">
                                        <span class="text-monospace">{{ rackUnit.equipment.id }}</span>
                                    </a>
                                </ng-template>
                            </td>

                            <td class="align-middle" [attr.rowspan]="rackUnit.connectedUnits?.length">
                                <strong class="{{ rackUnit.equipment.contract && !(rackUnit.equipment.contract.reference|default:'') ? 'text-muted' : '' }}">
                                    {{ rackUnit.equipment.contract.reference|default:'-' }}
                                </strong>
                                <a *ngIf="!isEmployee" href (click)="openEditEquipmentReferenceModal(rackUnit.equipment.id,rackUnit.equipment.contract.reference); false" title="Edit Reference">
                                    <span class="fa fa-change" aria-hidden="true"></span>
                                </a>
                            </td>

                            <td class="align-middle" [attr.rowspan]="rackUnit.connectedUnits?.length">
                                {{ rackUnit.equipment.type|default:'' | replace : '_' : ' ' | titlecase }}
                                <ng-container *ngIf="'DEDICATED_SERVER' != (rackUnit.type|default:'')">
                                    <a *ngIf="isEmployee" href="/emp/networkDevices?filter={{ rackUnit.equipment.id }}">
                                        <span class="fa fa-search" aria-hidden="true"></span>
                                    </a>
                                </ng-container>
                            </td>
                            <td class="align-middle" [attr.rowspan]="rackUnit.connectedUnits?.length">
                                <span *ngIf="(rackUnit.equipment.networkInterfaces.public && rackUnit.equipment.networkInterfaces.public.ip); else notAvailable" class="text-monospace" [ngClass]="{'text-danger':(rackUnit.equipment.networkInterfaces.public.nullRouted|default:'')}" [title]="{'Null Routed': rackUnit.equipment.networkInterfaces.public.nullRouted|default:''}"> {{ rackUnit.equipment.networkInterfaces.public.ip|default:'-' }}</span>
                            </td>

                            <td class="switchport_status text-center align-middle" [attr.rowspan]="rackUnit.connectedUnits?.length">
                                <ng-container *ngIf="'DEDICATED_SERVER' == (rackUnit.type|default:''); else noSwitch">
                                    <a i18n="@@bma_common_viewstatus" *ngIf="(rackUnit.equipment.networkInterfaces.public && rackUnit.equipment.networkInterfaces.public.ports.length > 0); else notAvailable" [routerLink]="['/servers', rackUnit.equipment.id, 'network']">View Status</a>
                                </ng-container>
                                <ng-template #noSwitch> - </ng-template>
                            </td>

                            <td class="text-center align-middle" [attr.rowspan]="rackUnit.connectedUnits?.length">
                                <ng-container *ngIf="'DEDICATED_SERVER' == (rackUnit.type|default:''); else actionNotAvailable">
                                    <a *ngIf="isEmployee && (rackUnit.equipment.featureAvailability.powerCycle || rackUnit.equipment.featureAvailability.ipmiReboot); else notConnected" href="#" (click)="openPowerOperationModal(rackUnit.equipment); false" i18n-title="@@bma_common_poweroperation" title="Power Operation" class="fa fa-powercycle"></a>
                                    <span i18n="@@bma_common_poweroperation" class="sr-only">Power Operation</span>
                                    <ng-template #notConnected>
                                        <span class="text-muted">
                                            <span i18n-title="@@bma_common_poweroperation" title="Power Operation" class="fa fa-powercycle" aria-hidden="true"></span>
                                        </span>
                                    </ng-template>

                                    <a *ngIf="isEmployee && rackUnit.equipment.networkInterfaces.public && rackUnit.equipment.networkInterfaces.public.ports.length > 0; else customerDataGraph" href="/emp/servers/{{ rackUnit.equipment.id }}/graphs" i18n-title="@@bma_common_datagraph" title="Datagraph" class="fa fa-usage"></a>
                                    <ng-template #customerDataGraph>
                                        <a *ngIf="!isEmployee && rackUnit.equipment.networkInterfaces.public && rackUnit.equipment.networkInterfaces.public.ports.length > 0; else notDataGraph" href="/bare-metals/servers/{{ rackUnit.equipment.id }}/graphs" i18n-title="@@bma_common_datagraph" title="Datagraph" class="fa fa-usage"></a>
                                    </ng-template>
                                    <span i18n="@@bma_common_datagraph" class="sr-only">Datagraph</span>
                                    <ng-template #notDataGraph>
                                        <span class="text-muted">
                                            <span i18n-title="@@bma_common_datagraph" title="Datagraph" class="fa fa-usage" aria-hidden="true"></span>
                                        </span>
                                    </ng-template>

                                    <a *ngIf="isEmployee && rackUnit.equipment.featureAvailability.privateNetwork|default:'' && customerId|default:''; else customerPrivateNetwork" href="/emp/privateNetwork/lookup?customerId={{ privateRack.contract.customerId }}&salesOrgId={{ privateRack.contract.salesOrgId }}" class="fa fa-privatenetwork" i18n-title="@@bma_common_privatenetwork" title="Private Network"> </a>
                                    <ng-template #customerPrivateNetwork>
                                        <a *ngIf="!isEmployee && rackUnit.equipment.featureAvailability.privateNetwork|default:'' && customerId|default:''; else noPrivateNetwork" href="/bare-metals/privateNetwork/lookup?customerId={{ privateRack.contract.customerId }}&salesOrgId={{ privateRack.contract.salesOrgId }}" class="fa fa-privatenetwork" i18n-title="@@bma_common_privatenetwork" title="Private Network"> </a>
                                    </ng-template>
                                    <span i18n="@@bma_common_privatenetwork" class="sr-only">Private Network</span>
                                    <ng-template #noPrivateNetwork>
                                        <span class="text-muted">
                                            <span class="fa fa-privatenetwork" aria-hidden="true" i18n-title="@@bma_common_privatenetwork" title="Private Network"></span>
                                        </span>
                                    </ng-template>

                                    <a *ngIf="rackUnit.equipment.featureAvailability.remoteManagement; else noRemoteManagement" href="javascript:void(0);" (click)="openRemoteManagementModal(rackUnit.equipment.id)" i18n-title="@@bma_common_remotemanagement" title="Remote Management" class="fa fa-remotemanagement"></a>
                                    <span i18n="@@bma_common_remotemanagement" class="sr-only">Remote Management</span>
                                    <ng-template #noRemoteManagement>
                                        <span class="text-muted">
                                            <span class="fa fa-remotemanagement" aria-hidden="true"></span>
                                        </span>
                                    </ng-template>

                                    <a *ngIf="isEmployee" href="/emp/servers/{{ rackUnit.equipment.id }}" i18n-title="@@bma_common_manage" title="Manage" class="fa fa-administration"></a>
                                    <a *ngIf="!isEmployee" href="/bare-metals/servers/{{ rackUnit.equipment.id }}" i18n-title="@@bma_common_manage" title="Manage" class="fa fa-administration"></a>
                                    <span i18n="@@bma_common_manage" class="sr-only">Manage</span>
                                </ng-container>
                                <ng-template #actionNotAvailable> - </ng-template>
                            </td>
                        </tr>
                    </ng-container>
                    <ng-template #noEquipment>
                        <tr>
                            <td class="border-right text-center">
                                <span class="text-monospace">{{ rackUnit.unit }}</span>
                            </td>
                            <td [attr.rowspan]="rackUnit.connectedUnits?.length">&nbsp;</td>
                            <td colspan="5" class="text-left" [attr.rowspan]="rackUnit.connectedUnits?.length">
                                <span i18n="@@bma_privaterackdetails_occupied" class="text-muted">Occupied </span>
                            </td>
                        </tr>
                    </ng-template>
                </ng-template>
            </ng-container>
        </tbody>
    </table>
</ng-container>
<ng-template #notAvailable>
    <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
</ng-template>
<ng-template #pendingContractModification>
    <span title="Pending contract modification" class="float-right text-muted">
        <i class="fa fa-refresh"></i>
        <span i18n-title="@@bma_common_pendingcontractmodification" class="sr-only">Pending contract modification</span>
    </span>
</ng-template>
