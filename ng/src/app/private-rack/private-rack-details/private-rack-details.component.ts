import { Component, OnInit, Input, AfterViewChecked } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ReferenceEditModalComponent } from 'src/app/reference-edit-modal/reference-edit-modal.component';
import { ServerRemoteManagementModalComponent } from 'src/app/server/server-remote-management-modal/server-remote-management-modal.component';
import { PowerOperationModalComponent } from 'src/app/power-operation-modal/power-operation-modal.component';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { CommerceService } from 'src/app/services/commerce.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';

@Component({
  selector: 'app-private-rack-details',
  templateUrl: './private-rack-details.component.html',
  styleUrls: ['./private-rack-details.component.css'],
})
export class PrivateRackDetailsComponent implements OnInit, AfterViewChecked {
  @Input() privateRack: any;
  isEmployee = false;
  isRackEquipmentLoading = false;
  dynamicDialogRef: DynamicDialogRef;
  equipments: any;
  displayAllSubnets = true;

  constructor(
    private currentUserService: CurrentUserService,
    private httpClient: HttpClient,
    private modalService: ModalService,
    private commerceService: CommerceService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.getEquipments();
  }

  ngAfterViewChecked(): void {
    this.colorTables();
  }

  colorTables(): void {
    const tableRows = document.querySelectorAll('TABLE.table-rowspan-striped');
    let rowSpan = 1;
    let stripeClass = 'odd';
    tableRows.forEach((value) => {
      const rows = value.querySelectorAll('TBODY TR');
      rows.forEach((items) => {
        items.setAttribute('class', stripeClass);

        items.querySelectorAll('TD, TH').forEach((cells) => {
          if (+cells.getAttribute('rowSpan') > rowSpan) {
            rowSpan = +cells.getAttribute('rowSpan');
          }
        });

        if (0 === --rowSpan) {
          stripeClass = 'odd' === stripeClass ? 'even' : 'odd';
          rowSpan = 1;
        }
      });
    });
  }

  previousUnit(index: number, racks: any): void {
    if (index - 1 < 0) {
      return null;
    }

    return racks[Object.keys(racks)[index - 1]].unit;
  }

  openReferenceEditModal() {
    this.dynamicDialogRef = this.modalService.show(ReferenceEditModalComponent, {
      equipment: this.privateRack,
      equipmentType: 'privateRacks',
    });

    this.dynamicDialogRef.onClose.subscribe((reason: any) => {
      if (typeof reason === 'string') {
        this.privateRack.contract.reference = reason;
      }
    });
  }

  openEditEquipmentReferenceModal(equipmentId, equipmentReferenceValue) {
    this.dynamicDialogRef = this.modalService.show(ReferenceEditModalComponent, {
      equipment: this.privateRack,
      equipmentType: 'privateRacks',
      updateUrl: `/_/internal/dedicatedserverapi/v2/privateRacks/${this.privateRack.id}/equipments/${equipmentId}`,
      equipmentReference: equipmentReferenceValue,
    });

    this.dynamicDialogRef.onClose.subscribe((reason: any) => {
      if (typeof reason === 'string') {
        this.getEquipments();
      }
    });
  }

  getEquipments(): void {
    this.isRackEquipmentLoading = true;

    this.httpClient
      .get(`/_/internal/dedicatedserverapi/v2/privateRacks/${this.privateRack.id}/equipments`, {})
      .subscribe((data: any) => {
        if (data) {
          this.isRackEquipmentLoading = false;
          this.equipments = data.equipments;
          if (this.privateRack.units) {
            this.privateRack.units = this.assignEquipmentToRackUnits(this.privateRack.units, this.equipments);
          }
        }
      });
  }

  assignEquipmentToRackUnits(privateRackUnits, equipments): any {
    for (const equipment of equipments) {
      const unit = parseInt(equipment.location.unit, 10);
      if (unit !== 0) {
        for (const privateRackUnit of Object.keys(privateRackUnits)) {
          if (privateRackUnits[privateRackUnit].connectedUnits.includes(unit)) {
            privateRackUnits[privateRackUnit].equipment = equipment;
            privateRackUnits[privateRackUnit].type = equipment.type;
          }
        }
      }
    }
    privateRackUnits = Object.values(privateRackUnits);
    return privateRackUnits.sort((a, b) => (a.unit > b.unit ? -1 : a.unit < b.unit ? 1 : 0));
  }

  openRemoteManagementModal(serverId: string) {
    this.modalService.show(ServerRemoteManagementModalComponent, { serverId });
  }

  openPowerOperationModal(equipment: any) {
    this.modalService.show(PowerOperationModalComponent, {
      equipment,
      equipmentType: 'servers',
    });
  }

  onDisplayAdditionalSubnets(): void {
    this.displayAllSubnets = !this.displayAllSubnets;
  }

  getCommerceRequestMoreIpsUrl(): string {
    return this.commerceService.getCommerceConfigureProductUrl(this.privateRack.contract.id, 'DEDRAC02_MOD_IPV4');
  }
}
