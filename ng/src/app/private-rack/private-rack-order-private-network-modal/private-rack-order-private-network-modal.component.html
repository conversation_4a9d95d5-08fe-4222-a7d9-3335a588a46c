<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '800px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_common_orderprivatenetwork" class="modal-title">Order Private Network</h3>
    </ng-template>

    <form [formGroup]="form" (ngSubmit)="orderPrivateNetwork()">
        <div class="modal-body">
            <div class="form-group">
                <div class="ml-4">
                    <div i18n="@@bma_privaterackorderprivatenetworkmodal_description1" class="row">The Private Network connectivity service offers the possibility to connect your dedicated rack to other Leaseweb products.</div>
                    <div class="row mt-3 mb-3 d-block">
                        <p i18n="@@bma_privaterackorderprivatenetworkmodal_description2">The service consists of 2 redundant upstream uplinks and a switch. The rack is fully managed by Leaseweb.</p>
                        <p i18n="@@bma_common_followcapacity">We offer the following capacity:</p>
                    </div>
                    <div class="row">
                        <h4 i18n="@@bma_common_selectuplinkcapacity">Select an uplink capacity</h4>
                    </div>

                    <ng-container *ngIf="uplinkCapacities">
                        <div class="row">
                            <div class="form-check form-check-inline">
                                <input type="radio" class="form-check-input" formControlName="uplinkCapacity" id="uplink_capacity_10000" value="10000" autofocus />
                                <label class="form-check-label" for="uplink_capacity_10000">
                                    {{ (uplinkCapacities['10000']?.value|default) }}
                                    (
                                    <span i18n="@@bma_privaterackorderprivatenetworkmodal_capacity10000" *ngIf="uplinkCapacities['10000']?.price|default; else free">
                                        {{ (uplinkCapacities['10000']?.price|default|formatCurrency:privateRack.contract.salesOrgId) + ' / month' }}
                                    </span>
                                    <ng-template #free>
                                        <span i18n="@@bma_common_free">Free</span>
                                    </ng-template>
                                    )
                                </label>
                            </div>
                        </div>

                        <div class="row">
                            <div class="form-check form-check-inline">
                                <input type="radio" formControlName="uplinkCapacity" class="form-check-input" [attr.disabled]="blacklisted40GbUplinkCapacitySites.indexOf(privateRack.location.site) == -1 ? null : true" id="uplink_capacity_40000" value="40000" />
                                <label class="form-check-label" for="uplink_capacity_40000">
                                    {{ (uplinkCapacities['40000']?.value|default) }}
                                    (
                                    <span i18n="@@bma_privaterackorderprivatenetworkmodal_capacity40000" *ngIf="uplinkCapacities['40000']?.price|default; else contactsales">
                                        {{ (uplinkCapacities['40000']?.price|default|formatCurrency:privateRack.contract.salesOrgId) + ' / month' }}
                                    </span>
                                    <ng-template #contactsales>
                                        <span i18n="@@bma_common_contactsales">Contact our Sales Department</span>
                                    </ng-template>
                                    )
                                </label>
                            </div>
                        </div>

                        <div class="row">
                            <div class="form-check form-check-inline">
                                <input type="radio" formControlName="uplinkCapacity" class="form-check-input" [attr.disabled]="blacklisted100GbUplinkCapacitySites.indexOf(privateRack.location.site) == -1 ? null : true" id="uplink_capacity_100000" value="100000" />
                                <label class="form-check-label" for="uplink_capacity_100000">
                                    {{ uplinkCapacities['100000']?.value }}
                                    (
                                    <span i18n="@@bma_privaterackorderprivatenetworkmodal_capacity100000" *ngIf="uplinkCapacities['100000']?.price|default; else contactsales">
                                        {{ (uplinkCapacities['100000']?.price|default|formatCurrency:privateRack.contract.salesOrgId) + ' / month' }}
                                    </span>
                                    <ng-template #contactsales>
                                        <span i18n="@@bma_common_contactsales">Contact our Sales Department</span>
                                    </ng-template>
                                    )
                                </label>
                            </div>
                        </div>
                    </ng-container>
                </div>
                <div class="ml-4 mt-3">
                    <div i18n="@@bma_privaterackorderprivatenetworkmodal_servicedescription1" class="row">The service is registered on a rack level. You will have the flexibility without any additional fees, to modify servers port speed or decide on a server level which ones should be in a private network or not.</div>
                    <div i18n="@@bma_privaterackorderprivatenetworkmodal_servicedescription2" class="row mt-3 mb-3">Once the service is activated, you can manage it from the Private Network Overview page.</div>

                    <div i18n="@@bma_privaterackorderprivatenetworkmodal_aftersubmit" class="row">After submitting this form, you will be contacted by our Sales representatives.</div>
                    <div class="row mt-3">
                        <small>
                            <span i18n-title="@@bma_common_kb" title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></span>
                            <span i18n="@@bma_privaterackorderprivatenetworkmodal_kb">For more information about Private Network for Dedicated racks please visit our <a target="_blank" href="https://kb.leaseweb.com/network/private-network">KB page</a>.</span>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_cancel" pButton type="button" icon="pi pi-times" iconPos="left" label="Cancel" class="p-button-secondary" (click)="closeModal()"></button>
            <button i18n-label="@@bma_common_submit" pButton (click)="orderPrivateNetwork()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" [disabled]="!uplinkCapacities" label="Submit" class="p-button-success"></button>
        </div>
    </ng-template>
</p-dialog>
