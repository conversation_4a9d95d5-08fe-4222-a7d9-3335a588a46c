import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { AlertService } from 'src/app/services/alert.service';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { PrivateNetworkService } from 'src/app/services/private-network.service';

@Component({
  selector: 'app-private-rack-order-private-network-modal',
  templateUrl: './private-rack-order-private-network-modal.component.html',
  styleUrls: ['./private-rack-order-private-network-modal.component.css'],
})
export class PrivateRackOrderPrivateNetworkModalComponent implements OnInit {
  privateRack: any;
  isSubmitting = false;
  uplinkCapacities = null;
  showDialog = true;
  blacklisted40GbUplinkCapacitySites = [];
  blacklisted100GbUplinkCapacitySites = [];
  form: UntypedFormGroup;

  constructor(
    private alertService: AlertService,
    private dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private privateNetworkService: PrivateNetworkService
  ) {}

  ngOnInit(): void {
    this.privateRack = this.dynamicDialogConfig.data.privateRack;
    this.blacklisted40GbUplinkCapacitySites =
      this.privateNetworkService.getBlacklistedPrivateNetworkPrivateRack40GbSites();
    this.blacklisted100GbUplinkCapacitySites =
      this.privateNetworkService.getBlacklistedPrivateNetworkPrivateRack100GbSites();

    this.uplinkCapacities = this.privateNetworkService.getPrivateNetworkPricesForPrivateRack(
      this.privateRack.contract.salesOrgId
    );
    this.form = this.formBuilder.group({
      uplinkCapacity: ['10000'],
    });
  }

  orderPrivateNetwork(): void {
    this.isSubmitting = true;
    this.alertService.clear();
    const body = {
      equipment: this.privateRack,
      uplinkCapacity: this.form.get('uplinkCapacity').value,
      equipmentType: 'privateRack',
    };

    this.httpClient.post<any>(`/_legacy/privateNetwork/order`, body).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;
        this.alertService.alert(
          {
            type: 'success',
            description: data.message,
          },
          true
        );
        this.closeModal();
      },
      error: (error: any) => {
        this.isSubmitting = false;
        this.alertService.alert(
          {
            type: 'error',
            description: error.error.error,
          },
          true
        );
        this.closeModal();
      },
    });
  }

  closeModal() {
    this.dynamicDialogRef.close();
  }
}
