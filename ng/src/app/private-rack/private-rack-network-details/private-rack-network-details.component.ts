import { Component, Input, ViewContainerRef, ComponentFactoryResolver, Renderer2 } from '@angular/core';

@Component({
  selector: 'app-private-rack-network-details',
  templateUrl: './private-rack-network-details.component.html',
  styleUrls: ['./private-rack-network-details.component.css'],
})
export class PrivateRackNetworkDetailsComponent {
  @Input() privateRack: any;
  @Input() errorInformation: string[];

  equipmentType = 'privateRacks';
  networkInterfaceOrder = ['public'];

  constructor(
    private vcRef: ViewContainerRef,
    private cResolver: ComponentFactoryResolver,
    private renderer: Renderer2
  ) {}
}
