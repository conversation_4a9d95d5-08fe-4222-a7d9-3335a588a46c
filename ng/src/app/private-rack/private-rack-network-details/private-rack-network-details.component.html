<div class="row mb-2">
    <div class="col-12">
        <h3>
            <i class="fa fa-transfer mr-1" aria-hidden="true"></i>
            <span i18n="@@bma_common_networkdetails">Network Details</span>
        </h3>
    </div>
</div>
<ng-container *ngIf="privateRack.networkInterfaces else interfacesNotAvailable">
    <p-accordion [multiple]="true" *ngIf="!isLoading">
        <p-accordionTab *ngFor="let networkInterfaceKey of networkInterfaceOrder">
            <ng-template pTemplate="header">
                <div class="row col-sm-12 align-items-center">
                    <div class="col-sm-3">
                        <h5 class="mt-2">{{ networkInterfaceKey | uppercase}}</h5>
                        <span class="h5" i18n="@@bma_privateracknetworkdetails_notavailable" *ngIf="!privateRack.networkInterfaces[networkInterfaceKey]"> - Not Available </span>
                    </div>
                    <div class="col-sm-3" *ngIf="privateRack.networkInterfaces[networkInterfaceKey].ports">
                        <span class="h5 badge" [ngClass]="{'badge-success': privateRack.networkInterfaces[networkInterfaceKey].ports.length >= 1,'badge-warning': privateRack.networkInterfaces[networkInterfaceKey].ports.length == 0}">
                            {{ privateRack.networkInterfaces[networkInterfaceKey].ports.length }} Port{{ privateRack.networkInterfaces[networkInterfaceKey].ports.length > 1 ?
                                's' : '' }}
                        </span>
                    </div>
                </div>
            </ng-template>
            <ng-template pTemplate="content" class="p-p-0">
                <div class="p-grid p-flex-column flex-nowrap" [ngClass]="{'border-bottom': networkInterfaceKey=='internal'}">
                    <app-switch-port-status [equipment]="privateRack" [equipmentType]="equipmentType" [networkInterfaceType]="networkInterfaceKey"></app-switch-port-status>
                </div>
            </ng-template>
        </p-accordionTab>
    </p-accordion>
</ng-container>
<ng-template #interfacesNotAvailable>
    <div class="p-col-12 p-py-0  p-pr-2">
        <div class="p-grid grid-row">
            <span i18n="@@bma_common_networkinterfacenotavailable" class="text-muted">Network Interface Not Available</span>
        </div>
    </div>
</ng-template>
