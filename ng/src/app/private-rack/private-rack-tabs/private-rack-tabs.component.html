<app-customer-aware-header *ngIf="privateRack" caption="Dedicated Rack {{privateRack.id}}" [reference]="privateRack.contract.reference" [customerId]="privateRack.contract.customerId" [salesOrgId]="privateRack.contract.salesOrgId" [isEmployee]="isEmployee"></app-customer-aware-header>

<div *ngIf="!isEmployee && privateRack && !privateRack.contract.privateNetworkPortSpeed" class="float-right private-rack-private-network-link">
    <a class="btn btn-success" i18n="@@bma_common_orderprivatenetwork" (click)="openOrderPrivateNetworkModal(); false" href="#">Order Private Network</a>
</div>

<div *ngIf="!isEmployee && privateRack && privateRack.isRedundantPrivateNetworkCapable" class="float-right server-private-network-link">
    <a i18n="@@bma_common_order_redundantprivatenetwork" class="btn btn-success" (click)="openOrderRedundantPrivateNetworkModal(); false" href="#">Order Redundant Private Network</a>
</div>
<app-loader *ngIf="isLoading"></app-loader>

<ul class="nav nav-pills mb-4" *ngIf="privateRack">
    <li class="nav-item dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-details mr-2"></span>
            <span i18n="@@bma_common_details">Details</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a [routerLink]="['.']" class="dropdown-item">
                <span class="text-muted fa fa-details mr-2"></span>
                <span i18n="@@bma_common_rackdetails">Rack Details</span>
            </a>
            <a [routerLink]="['network']" class="dropdown-item">
                <span class="text-muted fa fa-transfer mr-2"></span>
                <span i18n="@@bma_common_networkdetails">Network Details</span>
            </a>
            <a [routerLink]="['ips']" class="dropdown-item">
                <span class="text-muted fa fa-ip mr-2"></span>
                <span i18n="@@bma_common_ipaddresses">IP Addresses</span>
            </a>
            <a [routerLink]="['credentials']" class="dropdown-item">
                <span class="text-muted fa fa-lock mr-2"></span>
                <span i18n="@@bma_common_credentials">Credentials</span>
            </a>
        </div>
    </li>
    <li class="nav-item dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-usage mr-2"></span>
            <span i18n="@@bma_common_usage">Usage</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a [routerLink]="['notifications']" class="dropdown-item">
                <span class="text-muted fa fa-notification mr-2"></span>
                <span i18n="@@bma_common_notifications">Notifications</span>
            </a>
            <a [routerLink]="['graphs']" class="dropdown-item">
                <span class="text-muted fa fa-usage mr-2"></span>
                <span i18n="@@bma_common_graphs">Graphs</span>
            </a>
        </div>
    </li>
    <li class="nav-item navbar-right dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-tasks mr-2"></span>
            <span i18n="@@bma_common_activities">Activities</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a [routerLink]="['nullRouteHistory']" class="dropdown-item">
                <span class="text-muted fa fa-transfer mr-2"></span>
                <span i18n="@@bma_common_nullroutehistory">Null Route History</span>
            </a>
        </div>
    </li>
    <li class="nav-item">
        <a *ngIf="!isEmployee" [class.disabled]="!privateRack.contract|default:''" href="/bare-metals/privateNetwork/lookup" class="nav-link">
            <span class="text-muted fa fa-privatenetwork mr-2"></span>
            <span i18n="@@bma_common_privatenetwork">Private Network</span>
        </a>
        <a *ngIf="isEmployee" [class.disabled]="!privateRack.contract|default:''" href="/emp/privateNetwork/lookup?customerId={{ privateRack.contract?.customerId }}&salesOrgId={{ privateRack.contract?.salesOrgId }}" class="nav-link">
            <span class="text-muted fa fa-privatenetwork mr-2"></span>
            <span i18n="@@bma_common_privatenetwork">Private Network</span>
        </a>
    </li>
    <li *ngIf="!isEmployee" class="nav-item navbar-right dropdown">
        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
            <span class="text-muted fa fa-administration mr-2"></span>
            <span i18n="@@bma_common_actions">Actions</span>
            <span class="caret"></span>
        </a>
        <div class="dropdown-menu" role="menu">
            <a href="/tickets/new?equipmentId={{ privateRack.id }}&category=technical-assistance" class="dropdown-item">
                <span class="text-muted fa fa-ticket mr-2"></span>
                <span i18n="@@bma_common_createticket">Create Ticket</span>
            </a>
            <a href="/backup/#/order/privateRacks" class="dropdown-item">
                <span class="text-muted fa fa-acronis mr-2"></span>
                <span i18n="@@bma_common_acronisbackup">Acronis Backup</span>
            </a>
            <a *ngIf="privateRack.contract && privateRack.contract.status == 'ACTIVE'" class="dropdown-item" target="_blank" [href]="getCommerceRequestMoreIpsUrl()">
                <span class="text-muted fa fa-ip mr-2"></span>
                <span i18n="@@bma_common_requestmoreipv4">Request More IPv4 Addresses</span>
            </a>
            <a *ngIf="!isEmployee && privateRack.contract && privateRack.contract.status == 'ACTIVE'" href="javascript:void(0);" class="dropdown-item" (click)="openIpV6RequestModal()">
                <span class="text-muted fa fa-ip mr-2"></span>
                <span i18n="@@bma_common_requestipv6">Request IPv6</span>
                <div i18n="@@bma_common_free" class="badge pull-right">Free</div>
            </a>
        </div>
    </li>
</ul>

<ng-container *ngIf="privateRack">
    <router-outlet (activate)="onPrivateRackLoaded($event)"></router-outlet>
</ng-container>
