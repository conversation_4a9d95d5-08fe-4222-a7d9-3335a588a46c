import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { ModalService } from 'src/app/services/modal.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { CommerceService } from 'src/app/services/commerce.service';
import { HttpClient } from '@angular/common/http';
import { IpV6RequestModalComponent } from 'src/app/ip/ip-v6-request-modal/ip-v6-request-modal.component';
import { SiteService } from 'src/app/services/site.service';
import { PrivateNetworkRedundancyOrderModalComponent } from 'src/app/private-network/private-network-redundancy-order-modal/private-network-redundancy-order-modal.component';
import { PrivateRackOrderPrivateNetworkModalComponent } from '../private-rack-order-private-network-modal/private-rack-order-private-network-modal.component';

@Component({
  selector: 'app-private-rack-tabs',
  templateUrl: './private-rack-tabs.component.html',
  styleUrls: ['./private-rack-tabs.component.css'],
})
export class PrivateRackTabsComponent implements OnInit {
  isLoading = false;
  privateRack: any;
  privateRackId: string;
  isEmployee = false;

  constructor(
    private activatedRoute: ActivatedRoute,
    private titleService: Title,
    private modalService: ModalService,
    private currentUserService: CurrentUserService,
    private httpClient: HttpClient,
    private router: Router,
    private siteService: SiteService,
    private commerceService: CommerceService
  ) {}

  ngOnInit(): void {
    this.privateRackId = this.activatedRoute.snapshot.url[1].path;
    this.isEmployee = this.currentUserService.isEmployee();

    this.isLoading = true;

    this.httpClient.get<any>(`/_/internal/dedicatedserverapi/v2/privateRacks/${this.privateRackId}`).subscribe({
      next: (data: any) => {
        this.isLoading = false;
        this.privateRack = data;
        this.titleService.setTitle(
          `${this.privateRack.id} - ` +
            $localize`:@@bma_common_dedicatedrack:Dedicated Rack` +
            `${this.privateRack.contract.reference ? this.privateRack.contract.reference : ''}| Leaseweb`
        );

        /*eslint-disable */
        const event = new Event('update_product_navbar');
        event['customerId'] = this.privateRack.contract.customerId;
        event['salesOrgId'] = this.privateRack.contract.salesOrgId;
        event['country'] = this.siteService.getCountry(this.privateRack.contract.salesOrgId);
        window.dispatchEvent(event);
        /*eslint-enable */
      },
      error: (error: any) => {
        this.isLoading = false;
        this.router.navigate(['**']);
      },
    });
  }

  openOrderPrivateNetworkModal(): void {
    this.modalService.show(PrivateRackOrderPrivateNetworkModalComponent, {
      privateRack: this.privateRack,
    });
  }

  openIpV6RequestModal() {
    this.modalService.show(IpV6RequestModalComponent, {
      equipment: this.privateRack,
      equipmentType: 'privateRacks',
    });
  }

  getCommerceRequestMoreIpsUrl(): string {
    return this.commerceService.getCommerceConfigureProductUrl(this.privateRack.contract.id, 'DEDRAC02_MOD_IPV4');
  }

  onPrivateRackLoaded(component) {
    if (this.privateRack) {
      component.equipment = this.privateRack;
      component.privateRack = this.privateRack;
      component.equipmentId = this.privateRack.id;
      component.equipmentType = 'privateRacks';
    }
  }

  openOrderRedundantPrivateNetworkModal() {
    this.modalService.show(PrivateNetworkRedundancyOrderModalComponent, {
      equipment: this.privateRack,
      equipmentType: 'dedicatedRack',
    });
  }
}
