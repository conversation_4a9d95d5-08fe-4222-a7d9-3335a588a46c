import { ActivatedRoute, Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-audit-logs',
  templateUrl: './audit-logs.component.html',
  styleUrls: ['./audit-logs.component.css'],
})
export class AuditLogsComponent implements OnInit {
  logs: Array<any> = [];
  isLoading = false;
  filter = '';

  constructor(
    private activatedRoute: ActivatedRoute,
    private httpClient: HttpClient,
    private router: Router,
    private titleService: Title
  ) {}

  ngOnInit(): void {
    this.titleService.setTitle('Audit Logs | Leaseweb');
    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    this.filter = queryParams.filter ?? '';
    this.logs = [];
    this.loadMore();
  }

  onSubmit(): void {
    this.router.navigate(['.'], { relativeTo: this.activatedRoute, queryParams: { filter: this.filter } });
  }

  loadMore(): void {
    this.isLoading = true;
    const params: any = {
      offset: this.logs.length.toString(),
    };

    if (this.filter.trim()) {
      params.filter = this.filter.trim();
    }

    this.httpClient.get<any>('/_legacy/audit-logs', { params }).subscribe({
      next: (data: any) => {
        this.logs.push(...(data as Array<any>));
        this.isLoading = false;
      },
      error: (error: any) => {
        this.isLoading = false;
      },
    });
  }

  getAuditLogValues(log: any): any {
    const values = { ...log.monolog.extra, ...log.monolog.context };
    for (const key in values) {
      if (-1 < ['api_user_id', 'client_ip', 'correlation_id', 'uri'].indexOf(key)) {
        delete values[key];
      } else if (!values[key]) {
        delete values[key];
      } else if (-1 < key.indexOf('route_param')) {
        delete values[key];
      }
    }
    return values;
  }
}
