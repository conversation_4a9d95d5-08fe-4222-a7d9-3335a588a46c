<div class="mb-4">
    <h1 i18n="@@bma_auditlogs_title">Audit Logs</h1>
</div>

<form (ngSubmit)="onSubmit()">
    <div class="row mb-3">
        <div class="col">
            <input i18n-placeholder="@@bma_common_filter" type="text" class="form-control m-0" name="filter" [(ngModel)]="filter" placeholder="Filter..." autofocus />
        </div>
    </div>

    <div class="row mb-3">
        <div class="col text-right">
            <button i18n-label="@@bma_common_reset" pButton type="button" label="Reset" icon="pi pi-refresh" class="p-button-secondary mr-3" [routerLink]="['.']"></button>
            <p-button i18n-label="@@bma_common_search" [disabled]="isLoading" label="Search" icon="pi pi-search" [routerLink]="['.']" [queryParams]="{filter: filter}"></p-button>
        </div>
    </div>
</form>

<div *ngIf="logs.length > 0" class="table-responsive">
    <table class="table table-striped">
        <thead>
            <tr>
                <th i18n="@@bma_auditlogs_date">Date</th>
                <th i18n="@@bma_auditlogs_requester">Requester</th>
                <th i18n="@@bma_auditlogs_log">Log</th>
                <th>&nbsp;</th>
            </tr>
        </thead>

        <tbody>
            <tr *ngFor="let log of logs">
                <td>
                    {{ log['@timestamp']|lswDateTime:"short" }}
                </td>
                <td>
                    <strong>
                        {{ log['monolog']['extra']['client_ip'] }}
                    </strong>
                    <br />
                    <span>
                        {{ log['monolog']['context']['customerId'] ? log['monolog']['context']['customerId'] : log['monolog']['extra']['api_user_id'] }}
                    </span>
                </td>
                <td class="text-break">
                    <strong>{{ log['message']|uppercase }}</strong>
                    <br />
                    <span *ngFor="let value of getAuditLogValues(log) | keyvalue" class="badge badge-light mr-1"> {{ value.key }}={{ value.value }} </span>
                </td>
                <td>
                    <a i18n-title="@@bma_common_viewalllog" [routerLink]="['.']" [queryParams]="{filter: log['monolog']['extra']['correlation_id']}" title="View all related log messages...">
                        <i class="fa fa-lg fa-tag"></i>
                    </a>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<div *ngIf="!isLoading && logs.length === 0" class="text-center">
    <i i18n="@@bma_auditlogs_noresults">Nothing matched your query {{ filter }}</i>
</div>

<nav class="text-center" *ngIf="!isLoading">
    <a i18n="@@bma_auditlogs_loadmore" (click)="loadMore()">Show Older Logs</a>
</nav>

<app-loader *ngIf="isLoading"></app-loader>
