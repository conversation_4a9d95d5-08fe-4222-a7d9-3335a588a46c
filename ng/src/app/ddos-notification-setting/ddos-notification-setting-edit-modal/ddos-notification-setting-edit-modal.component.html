<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '600px'}">
    <ng-template pTemplate="header">
        <h3 class="modal-title" i18n="bma_ddosnotificationsettingeditmodal_title">Change DDoS email notification settings</h3>
    </ng-template>
    <form [formGroup]="form" (ngSubmit)="onDdosNotificationSettingsChange()" class="form-horizontal">
        <div class="modal-body">
            <p class="my-3" i18n="bma_ddosnotificationsettingeditmodal_confirmationtext">Would you like to receive DDoS email notifications for this {{ equipmentType }} {{ equipmentId }}?</p>

            <table class="table table-striped">
                <tbody>
                    <tr>
                        <td class="font-weight-bold" i18n="bma_ddosnotificationsettingeditmodal_nullroute">Null Route IP Addresses Events:</td>
                        <td>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" formControlName="nulling" [checked]="ddosNotificationSetting.nulling == 'ENABLED'" id="notifications_setting_nulling_enable" value="ENABLED" autofocus />
                                <label i18n="bma_common_enable" class="form-check-label" for="notifications_setting_nulling_enable"> Enable </label>
                            </div>
                        </td>
                        <td>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" formControlName="nulling" [checked]="ddosNotificationSetting.nulling == 'DISABLED'" id="notifications_setting_nulling_disable" value="DISABLED" />
                                <label i18n="bma_common_disable" class="form-check-label" for="notifications_setting_nulling_disable"> Disable </label>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="font-weight-bold" i18n="bma_ddosnotificationsettingeditmodal_scrubbing">Scrubbing Events:</td>
                        <td>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" formControlName="scrubbing" [checked]="ddosNotificationSetting.scrubbing == 'ENABLED'" id="notifications_setting_scrubbing_enable" value="ENABLED" />
                                <label i18n="bma_common_enable" class="form-check-label" for="notifications_setting_scrubbing_enable"> Enable </label>
                            </div>
                        </td>
                        <td>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" formControlName="scrubbing" [checked]="ddosNotificationSetting.scrubbing == 'DISABLED'" id="notifications_setting_scrubbing_disable" value="DISABLED" />
                                <label i18n="bma_common_disable" class="form-check-label" for="notifications_setting_scrubbing_disable"> Disable </label>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </form>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_cancel" pButton (click)="closeModal()" icon="pi pi-times" iconPos="left" label="Cancel" class="p-button-secondary" type="button"></button>
            <button i18n-label="@@bma_common_save" pButton (click)="onDdosNotificationSettingsChange()" label="Save" icon="pi pi-check" [loading]="isSubmitting"></button>
        </div>
    </ng-template>
</p-dialog>
