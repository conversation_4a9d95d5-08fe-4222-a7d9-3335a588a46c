import { AlertService } from 'src/app/services/alert.service';
import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-ddos-notification-setting-edit-modal',
  templateUrl: './ddos-notification-setting-edit-modal.component.html',
  styleUrls: ['./ddos-notification-setting-edit-modal.component.css'],
})
export class DdosNotificationSettingEditModalComponent implements OnInit {
  ddosNotificationSetting: any;
  equipmentType: any;
  equipmentId: any;
  form: UntypedFormGroup;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private alertService: AlertService,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient
  ) {}

  ngOnInit(): void {
    this.equipmentId = this.dynamicDialogConfig.data.equipmentId;
    this.equipmentType = this.dynamicDialogConfig.data.equipmentType;
    this.ddosNotificationSetting = this.dynamicDialogConfig.data.ddosNotificationSetting;
    this.form = this.formBuilder.group({
      nulling: [this.ddosNotificationSetting.nulling, Validators.required],
      scrubbing: [this.ddosNotificationSetting.scrubbing, Validators.required],
    });
  }

  onDdosNotificationSettingsChange(): void {
    this.isSubmitting = true;
    this.alertService.clear();
    const params: any = {};
    params.nulling = this.form.get('nulling').value;
    params.scrubbing = this.form.get('scrubbing').value;
    this.httpClient
      .put(`/_/internal/nseapi/v2/${this.equipmentType}/${this.equipmentId}/notificationSettings/ddos`, params)
      .subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.alertService.alert(
            {
              type: 'success',
              description: $localize`:@@bma_ddosnotificationsettingeditmodal_successmessage:Successfully changed the DDoS notification settings.`,
            },
            true
          );

          this.closeModal({ ddosNotificationSettingUpdated: true });
        },
        error: (error: any) => {
          this.isSubmitting = false;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ ddosNotificationSettingUpdated: false });
        },
      });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
