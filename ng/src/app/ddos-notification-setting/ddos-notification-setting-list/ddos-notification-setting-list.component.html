<app-loader *ngIf="isLoading"></app-loader>
<div *ngIf="!isLoading" class="mb-3">
    <div>
        <h5>
            <span i18n="@@bma_ddosnotificationsettinglist_title">DDoS Protection Email Notification:</span>
            <sup>
                <a href="https://kb.leaseweb.com/kb/ddos-ip-protection/ddos-ip-protection-overview/#DDOSIPProtection-Enabling/disablingDDoSattacknotifications" target="_blank">
                    <i title="Knowledge Base" class="fa fa-knowledge mx-1" aria-hidden="true"></i>
                    <span i18n="@@bma_common_kb" class="sr-only">Knowledge Base</span>
                </a>
            </sup>
            <a href="javascript:void(0);" [class.disabled]="equipment.isSharedEol" (click)="openDdosNotificationSettingEditModal(ddosNotificationSettings)" title="Modify Email Notification Settings">
                <i class="fa fa-change"></i>
            </a>
            <span i18n-title="bma_ddosnotificationsettinglist_modifyemailnotificationlinktitle" class="sr-only">Modify Email Notification Settings</span>
        </h5>
    </div>
    <ng-container *ngIf="!ddosNotificationSettings">
        <div i18n="bma_common_notavailable" class="text-center col-12">Not Available</div>
    </ng-container>
    <ng-container *ngIf="ddosNotificationSettings">
        <div class="row">
            <div class="col text-nowrap">
                <strong i18n="bma_ddosnotificationsettinglist_nulling">Nulling</strong>
                <span class="badge ml-2" [ngClass]="{'badge-green-outline': ddosNotificationSettings['nulling'] == 'ENABLED', 'badge-red-outline': ddosNotificationSettings['nulling'] != 'ENABLED'}">{{ ddosNotificationSettings['nulling']|default: 'ENABLED' }}</span>
            </div>
            <div class="col text-nowrap">
                <strong i18n="bma_ddosnotificationsettinglist_scrubbing">Scrubbing</strong>
                <span class="badge ml-2" [ngClass]="{'badge-green-outline': ddosNotificationSettings['scrubbing'] == 'ENABLED', 'badge-red-outline': ddosNotificationSettings['scrubbing'] != 'ENABLED'}">{{ ddosNotificationSettings['scrubbing']|default: 'ENABLED' }}</span>
            </div>
        </div>
    </ng-container>
    <span i18n="bma_ddosnotificationsettinglist_modifyemailnotification" class="sr-only">Modify Email Notification Settings</span>
</div>
