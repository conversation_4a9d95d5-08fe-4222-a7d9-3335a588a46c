import { Component, OnInit, Input } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ModalService } from 'src/app/services/modal.service';
import { DdosNotificationSettingEditModalComponent } from '../ddos-notification-setting-edit-modal/ddos-notification-setting-edit-modal.component';

@Component({
  selector: 'app-ddos-notification-setting-list',
  templateUrl: './ddos-notification-setting-list.component.html',
  styleUrls: ['./ddos-notification-setting-list.component.css'],
})
export class DdosNotificationSettingListComponent implements OnInit {
  @Input() equipment: any;
  @Input() equipmentType: string;
  ddosNotificationSettings: any;
  isLoading = false;

  constructor(
    private httpClient: HttpClient,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.getDdosNotificationSettings();
  }

  getDdosNotificationSettings(): void {
    this.isLoading = true;
    this.httpClient
      .get(`/_/internal/nseapi/v2/${this.equipmentType}/${this.equipment.id}/notificationSettings/ddos`)
      .subscribe(
        (data: any) => {
          this.isLoading = false;
          if (data) {
            this.ddosNotificationSettings = data;
          } else {
            this.ddosNotificationSettings = {};
          }
        },
        (error: any) => {
          this.isLoading = false;
          this.ddosNotificationSettings = {};
        }
      );
  }

  openDdosNotificationSettingEditModal(ddosNotificationSetting: any) {
    const dynamicDialogRef = this.modalService.show(DdosNotificationSettingEditModalComponent, {
      equipmentType: this.equipmentType,
      equipmentId: this.equipment.id,
      ddosNotificationSetting,
    });

    dynamicDialogRef.onClose.subscribe((event) => {
      if (event?.ddosNotificationSettingUpdated === true) {
        this.getDdosNotificationSettings();
      }
    });
  }
}
