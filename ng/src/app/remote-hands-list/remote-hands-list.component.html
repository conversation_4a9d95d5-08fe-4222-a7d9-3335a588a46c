<app-customer-aware-header caption="Remote Hands Packages" [customerId]="customerId" [salesOrgId]="salesOrgId" [isEmployee]="isEmployee" reference="Manage your packages"></app-customer-aware-header>

<div class="row">
    <p-card class="col-12 mb-4" *ngFor="let package of remoteHands">
        <div class="d-flex flex-row">
            <div class="mr-2 mt-1rem">
                <span [class]="package.type.toLowerCase()">{{package.type}}</span>
            </div>
            <h2 i18n="@@bma_common_packagedetails">Package Details</h2>
        </div>
        <div class="row mt-3">
            <div class="col-4">
                <div class="d-flex flex-row">
                    <h4 class="mr-2 mt-025rem" i18n="@@bma_common_billingcycle">Billing Cycle</h4>
                    <p-tag [value]="package.billingFrequency" icon="pi pi-calendar" severity="success" *ngIf="package.billingFrequency; else none"></p-tag>
                </div>
            </div>
            <div class="col-4">
                <div class="d-flex flex-row">
                    <h4 class="mr-2 mt-025rem" i18n="@@bma_common_contractterm">Contract Term</h4>
                    <p-tag [value]="package.contractTerm" icon="pi pi-calendar" severity="success" *ngIf="package.contractTerm; else none"></p-tag>
                </div>
            </div>
            <div class="col-4">
                <div class="d-flex flex-row">
                    <h4 class="mr-2 mt-025rem" i18n="@@bma_common_minutespurchased">Minutes Purchased</h4>
                    <span class="mt-020rem">{{ package.minutes ?? '0 min' }}</span>
                </div>
            </div>
        </div>
    </p-card>
</div>
<ng-template #none>
    <span class="mt-020rem">--</span>
</ng-template>
<ng-template #notAvailable>
    <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
</ng-template>
