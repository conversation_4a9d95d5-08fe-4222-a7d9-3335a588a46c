import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HttpClient, HttpParams } from '@angular/common/http';
import { CurrentUserService } from 'src/app/services/current-user.service';

@Component({
  selector: 'app-remote-hands-list',
  templateUrl: './remote-hands-list.component.html',
  styleUrls: ['./remote-hands-list.component.css'],
})
export class RemoteHandsListComponent implements OnInit {
  remoteHands: any;
  isEmployee = false;
  customerId = '';
  salesOrgId = '';

  constructor(
    private activatedRoute: ActivatedRoute,
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();

    if (!this.isEmployee) {
      this.customerId = this.currentUserService.getCustomerId();
      this.salesOrgId = this.currentUserService.getSalesOrgId();
    }

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    if (queryParams.customerId) {
      this.customerId = queryParams.customerId;
    }

    if (queryParams.salesOrgId) {
      this.salesOrgId = queryParams.salesOrgId;
    }

    this.getRemoteHandsInformation();
  }

  getRemoteHandsInformation(): void {
    let params = new HttpParams();
    if (this.isEmployee) {
      params = params.set('customerId', this.customerId);
      params = params.set('salesOrgId', this.salesOrgId);
    }
    this.httpClient.get<any>('/_legacy/remotehands', { params }).subscribe((data: any) => {
      if (data && Object.keys(data.remoteHandsInformation).length > 0) {
        this.remoteHands = data.remoteHandsInformation;
      }
    });
  }
}
