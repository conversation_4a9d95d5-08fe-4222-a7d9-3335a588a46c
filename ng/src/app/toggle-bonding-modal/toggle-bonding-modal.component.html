<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '500px' }">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_bonding_changestatus" class="modal-title">Change bonding status</h3>
    </ng-template>

    <form (ngSubmit)="doAction()" class="form-horizontal">
        <div class="modal-body">
            <p i18n="@@bma_bonding_downtime">
                This action requires downtime. Are you sure you want to {{ action }} bonding for dedicated storage <span class="text-monospace">{{ equipmentId }}</span
                >?
            </p>
        </div>
    </form>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_close" pButton type="button" class="p-button-secondary" label="Close" (click)="closeModal()"></button>
            <button pButton (click)="doAction()" [loading]="isSubmitting" label="{{ buttonLabel|titlecase }}"></button>
        </div>
    </ng-template>
</p-dialog>
