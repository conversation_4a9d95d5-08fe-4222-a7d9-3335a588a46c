import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert.service';

@Component({
  selector: 'app-toggle-bonding-modal',
  templateUrl: './toggle-bonding-modal.component.html',
  styleUrls: ['./toggle-bonding-modal.component.css'],
})
export class ToggleBondingModalComponent implements OnInit {
  equipmentId: any;
  privateNetworkId: any;
  type: any;
  action: any;
  buttonLabel: any;

  isSubmitting = false;
  showDialog = true;

  constructor(
    private alertService: AlertService,
    private httpClient: HttpClient,
    private dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.equipmentId = this.dynamicDialogConfig.data.equipmentId;
    this.action = this.dynamicDialogConfig.data.action;
    this.privateNetworkId = this.dynamicDialogConfig.data.privateNetworkId;
    this.buttonLabel = this.action + ' ' + $localize`:@@bma_bonding_title:Bonding`;
  }

  doAction(): void {
    this.isSubmitting = true;
    this.alertService.clear();
    const params: any = {};
    params.bonding = this.action === 'enable' ? true : false;

    this.httpClient
      .put<any>(
        `/_/internal/dedicatedserverapi/v2/dedicatedStorages/${this.equipmentId}/privateNetworks/${this.privateNetworkId}`,
        params
      )
      .subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.alertService.alert({
            type: 'success',
            description: $localize`:@@bma_privatenetworkatogglebondingnmodal_requestinprogress:Request to change bonding status for Dedicated Storage ${this.equipmentId} is in progress. Please refresh the page for current status after few minutes.`,
          });
          this.closeModal({ bondingStatusUpdated: true });
        },
        error: (error: any) => {
          this.isSubmitting = false;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ bondingStatusUpdated: false });
        },
      });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
