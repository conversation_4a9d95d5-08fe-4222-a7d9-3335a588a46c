import { Component, OnInit } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { AlertService } from 'src/app/services/alert.service';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { CustomValidator } from 'src/app/validators/custom.validator';

@Component({
  selector: 'app-lookup',
  templateUrl: './lookup.component.html',
  styleUrls: ['./lookup.component.css'],
})
export class LookupComponent implements OnInit {
  form: UntypedFormGroup;
  lookupTypes: Array<any> = [];
  isLoading = false;
  search: string;
  type: string;

  constructor(
    private activatedRoute: ActivatedRoute,
    private httpClient: HttpClient,
    private router: Router,
    private titleService: Title,
    private readonly formBuilder: UntypedFormBuilder,
    private customValidator: CustomValidator,
    private alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.lookupTypes = [
      { value: 'all', display: 'I am feeling lucky' },
      { value: 'equipmentId', display: 'Equipment ID' },
      { value: 'customer_id', display: 'Customer ID' },
      { value: 'networkDevices', display: 'Network Device' },
      { value: 'powerbars', display: 'Powerbar' },
      { value: 'macAddress', display: 'Mac Addresss' },
      { value: 'ipAddress', display: 'IPv4 Address' },
      { value: 'bmpapi_job', display: 'BMP-API Job' },
      { value: 'diskSerial', display: 'Disk serial' },
    ];
    this.titleService.setTitle($localize`:@@bma_lookup_title:Lookup` + ' | Leaseweb');

    this.alertService.clear();

    this.activatedRoute.queryParams.subscribe((parameters) => {
      this.onQueryParamsChanged(parameters);

      if (parameters.type === 'diskSerial') {
        this.form.patchValue({
          type: 'diskSerial',
        });
      }
    });
  }

  onQueryParamsChanged(queryParams): void {
    if (queryParams.ipAddress) {
      this.search = queryParams.ipAddress.trim();
      this.type = 'ipAddress';
    } else if (queryParams.macAddress) {
      this.search = queryParams.macAddress.trim();
      this.type = 'macAddress';
    } else if (queryParams.equipmentId) {
      this.search = queryParams.equipmentId.trim();
      this.type = 'equipmentId';
    } else if (queryParams.customerId) {
      this.search = queryParams.customerId.trim();
      this.type = 'customer_id';
    } else if (queryParams.search) {
      this.search = queryParams.search.trim();
      this.type = 'all';
    } else if (queryParams.error) {
      this.alertService.alert(
        {
          type: 'error',
          description: $localize`:@@bma_common_notfoundsearch:We could not find what you are searching for.`,
        },
        true
      );
      this.search = queryParams.search.trim() ?? '';
      this.type = queryParams.type ?? 'all';
    }

    this.form = this.formBuilder.group({
      search: [this.search, []],
      type: [this.type ?? 'all'],
    });

    if (this.search) {
      this.fetchLookup();
    }
  }

  fetchLookup() {
    this.alertService.clear();

    if (!this.form.valid) {
      this.alertService.alert(
        {
          type: 'error',
          description: 'Please enter a valid lookup: ' + this.form.get('search').value,
        },
        true
      );
      return;
    }

    this.search = this.form.get('search').value.trim();
    this.type = this.form.get('type').value === 'all' ? this.guessSearchType(this.search) : this.form.get('type').value;

    if (!this.type) {
      this.alertService.alert(
        {
          type: 'error',
          description:
            'We could not guess what you are searching for. Try selecting the type of search from the dropdown.',
        },
        true
      );
      return;
    }

    if (this.type === 'customer_id') {
      this.router.navigate(['customers'], { queryParams: { filter: this.search } });
      return;
    }

    if (this.type === 'bmpapi_job') {
      window.location.href = '/emp/jobs?filter=' + this.search;
      return;
    }

    if (this.type === 'networkDevices' || this.type === 'powerbars') {
      this.router.navigate([this.type], { queryParams: { filter: this.search } });
      return;
    }

    if (this.type === 'diskSerial') {
      this.router.navigate(['disk', this.search]);
      return;
    }

    let params = new HttpParams();
    const equipmentTypes = ['networkdevice', 'powerbar', 'equipmentId'];

    if (equipmentTypes.includes(this.type)) {
      params = params.set('equipmentId', this.search);
    } else {
      params = params.set(this.type, this.search);
    }

    params = params.set('guess', this.form.get('type').value === 'all');

    this.isLoading = true;

    this.httpClient.get<any>('/_legacy/lookup', { params }).subscribe({
      next: (data: any) => {
        this.isLoading = false;
        if (data.customerType) {
          this.router.navigate(['customers'], { queryParams: { filter: this.search } });
          return;
        }

        if (!data.type) {
          this.alertService.alert(
            {
              type: 'error',
              description: 'Could not find an Equipment for ' + this.search,
            },
            true
          );
          return;
        }

        if (data.type === 'servers' || data.type === 'racks' || data.type === 'colocations') {
          this.router.navigate(['/', data.type, data.id]);
        } else if (data.type === 'networkDevices' && data.customerId) {
          this.router.navigate(['/networkEquipments', data.id]);
        } else if (data.type === 'powerbars' || data.type === 'networkDevices') {
          this.router.navigate(['/', data.type], { queryParams: { filter: data.id } });
        } else if (data.type === 'W-STORAPP') {
          this.router.navigate(['/dedicatedStorages', data.id]);
        } else if (data.type === 'K-DINTACC') {
          this.router.navigate(['/directInternetAccess', data.id]);
        } else if (data.type === 'K-IPTRANS') {
          this.router.navigate(['/ipTransit', data.id]);
        } else {
          this.alertService.alert(
            {
              type: 'error',
              description: `Equipment ${this.search} of ${data.type} cannot be viewed in EMP, please check it in SAP.`,
            },
            true
          );
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.alertService.alert(
          {
            type: 'error',
            description: `Could not find an Equipment for ${this.search}`,
          },
          true
        );
      },
    });
  }

  guessSearchType(search): string {
    const equipmentId = /^[0-9]{1,8}$/;
    const bmpapiJobUuid = /^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$/;
    const bmpapiJobNode =
      /^[a-fA-F0-9]{1,2}[:-][a-fA-F0-9]{1,2}[:-][a-fA-F0-9]{1,2}[:-][a-fA-F0-9]{1,2}[:-][a-fA-F0-9]{1,2}[:-][a-fA-F0-9]{1,2}!.*$/;
    const powerbar = /^.*(REBOOT|APC)[0-9]+$/;

    if (this.customValidator.isValidIp(search)) {
      return 'ipAddress';
    } else if (this.customValidator.isValidMacAddress(search)) {
      return 'macAddress';
    } else if (equipmentId.test(search)) {
      return 'equipmentId';
    } else if (bmpapiJobUuid.test(search) || bmpapiJobNode.test(search)) {
      return 'bmpapi_job';
    } else if (powerbar.test(search)) {
      return 'powerbars';
    } else if (this.customValidator.isValidNetworkDevice(search)) {
      return 'networkDevices';
    }

    return '';
  }
}
