<div>
    <h1 i18n="@@bma_lookup_header">
        Lookup
        <small>almost anything...</small>
    </h1>
</div>

<div class="card">
    <div class="card-body">
        <form class="row d-flex justify-content-center align-items-center" [formGroup]="form" (ngSubmit)="fetchLookup()">
            <div class="col-sm-7">
                <input i18n-placeholder="@@bma_lookup_search_placeholder" type="text" id="search" formControlName="search" class="form-control" value="" placeholder="Equipment ..." required autofocus />
            </div>

            <div class="col-sm-3">
                <select formControlName="type" class="form-control">
                    <option *ngFor="let lookupType of lookupTypes" [value]="lookupType.value">
                        {{lookupType.display}}
                    </option>
                </select>
            </div>

            <div class="col-sm-2">
                <button i18n-label="@@bma_common_search" pButton [disabled]="isLoading" label="Search" icon="pi pi-search"></button>
            </div>

            <div *ngIf="form.get('type').value === 'diskSerial'" class="col-sm-12 mt-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="partialMatch" name="partialMatch" [checked]="partialMatch" (change)="partialMatch = !partialMatch" />
                    <label i18n="@@bma_common_partialmatchsearch" class="form-check-label" for="partialMatch">Partial match search</label>
                </div>
            </div>
        </form>
    </div>
</div>

<p class="mt-2">
    <small i18n="@@bma_lookup_desc"> Using the above search bar you can search for a variety of things related to dedicated servers. After using the search once the most popular browsers such as Chrome, Safari and Firefox will install a quick search feature allowing you to search from your browsers address bar by prefixing your search query with "emp". </small>
</p>

<app-loader *ngIf="isLoading"></app-loader>
