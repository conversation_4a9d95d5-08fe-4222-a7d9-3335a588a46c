import { AlertService } from 'src/app/services/alert.service';
import { Component, OnInit } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { SiteService } from 'src/app/services/site.service';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-ip-performance-measurement-edit-modal',
  templateUrl: './ip-performance-measurement-edit-modal.component.html',
  styleUrls: ['./ip-performance-measurement-edit-modal.component.css'],
})
export class IpPerformanceMeasurementEditModalComponent implements OnInit {
  id = '';
  errors: any = {};
  isLoading = false;
  sites: any;
  form: UntypedFormGroup;
  isSubmitting = false;
  showDialog = true;
  measurement: any;

  constructor(
    private httpClient: HttpClient,
    private siteService: SiteService,
    private readonly formBuilder: UntypedFormBuilder,
    private router: Router,
    private alertService: AlertService,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.id = this.dynamicDialogConfig.data.id;
    this.sites = this.siteService.sites();
    this.form = this.formBuilder.group({});
    this.isLoading = true;

    this.httpClient.get<any>(`/_/internal/networkperformanceapi/measurements/${this.id}`).subscribe({
      next: (measurement: any) => {
        if (measurement) {
          this.measurement = measurement;

          this.form = this.formBuilder.group({
            name: [this.measurement.name, Validators.required],
            site: [this.measurement.site],
            public: [this.measurement.public],
          });
          this.isLoading = false;
        }
      },
      error: (error: any) => {
        this.isLoading = false;
        if (error.status === 404) {
          this.router.navigate(['**']);
        }
      },
    });
  }

  onMeasurementUpdate() {
    this.isSubmitting = true;
    this.alertService.clear();
    for (const formControl in this.form.controls) {
      if (this.form.controls.hasOwnProperty(formControl)) {
        this.form.controls[formControl].updateValueAndValidity();
        this.form.controls[formControl].markAsTouched();
        this.form.controls[formControl].markAsDirty();
      }
    }

    if (this.form.valid) {
      const body: any = {};
      body.name = this.form.get('name').value;
      body.site = this.form.get('site').value;
      body.public = this.form.get('public').value;

      this.httpClient.put(`/_/internal/networkperformanceapi/measurements/${this.measurement.id}`, body).subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.alertService.alert(
            {
              type: 'success',
              description: $localize`:@@bma_ipperformancemeasurementeditmodal_success:Successfully updated Measurement ${data.measurementId}`,
            },
            true
          );
          this.closeModal({ measurementUpdated: true });
        },
        error: (error: any) => {
          this.isSubmitting = false;
          if (error.error.errorDetails) {
            Object.entries(error.error.errorDetails).forEach(([key, value]) => {
              this.form.get(key).setErrors({ server: value[0] });
            });
          }
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ measurementUpdated: true });
        },
      });
    } else {
      this.alertService.alert(
        {
          type: 'error',
          description: $localize`:@@bma_common_invalidentry:Invalid entries entered. Please check it.`,
        },
        true
      );
    }
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
