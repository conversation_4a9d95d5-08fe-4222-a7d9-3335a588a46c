import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert.service';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-ip-performance-measurement-remove-modal',
  templateUrl: './ip-performance-measurement-remove-modal.component.html',
  styleUrls: ['./ip-performance-measurement-remove-modal.component.css'],
})
export class IpPerformanceMeasurementRemoveModalComponent implements OnInit {
  id: any;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private httpClient: HttpClient,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig,
    private alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.id = this.dynamicDialogConfig.data.id;
  }

  removeMeasurement(): void {
    this.isSubmitting = true;
    this.alertService.clear();
    this.httpClient.delete(`/_/internal/networkperformanceapi/measurements/${this.id}`, {}).subscribe({
      next: (data: any) => {
        this.isSubmitting = false;
        this.alertService.alert(
          {
            type: 'success',
            description: $localize`:@@bma_ipperformancemeasurementremovemodal_success:Successfully removed Measurement`,
          },
          true
        );
        this.closeModal({ measurementRemoved: true });
      },
      error: (error: any) => {
        this.isSubmitting = false;
        this.closeModal({ measurementRemoved: false });
      },
    });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
