import { Component, Input, OnInit } from '@angular/core';

declare let initTracemon: any;

@Component({
  selector: 'app-ip-performance-measurement-tracemon',
  templateUrl: './ip-performance-measurement-tracemon.component.html',
  styleUrls: ['./ip-performance-measurement-tracemon.component.css'],
})
export class IpPerformanceMeasurementTracemonComponent implements OnInit {
  @Input() measurement: any;

  ngOnInit(): void {
    initTracemon('#tracemon', {}, { measurements: [this.measurement.measurementId] });
  }
}
