import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { CurrentUserService } from 'src/app/services/current-user.service';

@Component({
  selector: 'app-ip-performance-measurement-tabs',
  templateUrl: './ip-performance-measurement-tabs.component.html',
  styleUrls: ['./ip-performance-measurement-tabs.component.css'],
})
export class IpPerformanceMeasurementTabComponent implements OnInit {
  id = '';
  isLoading = false;
  measurement: any;
  isEmployee = false;

  constructor(
    private activatedRoute: ActivatedRoute,
    private httpClient: HttpClient,
    private router: Router,
    private currentUserService: CurrentUserService
  ) {}

  ngOnInit(): void {
    this.id = this.activatedRoute.snapshot.url[2].path;
    this.isLoading = true;
    this.isEmployee = this.currentUserService.isEmployee();

    this.httpClient.get<any>(`/_/internal/networkperformanceapi/measurements/${this.id}`).subscribe({
      next: (measurement: any) => {
        if (measurement) {
          this.measurement = measurement;
          this.isLoading = false;
        }
      },
      error: (error: any) => {
        this.isLoading = false;
        this.router.navigate(['**']);
      },
    });
  }

  onMeasurementLoaded(component) {
    if (this.measurement) {
      component.measurement = this.measurement;
    }
  }
}
