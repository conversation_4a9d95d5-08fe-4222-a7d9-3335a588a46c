<app-customer-aware-header *ngIf="measurement" caption="{{ measurement.name }}" [isEmployee]="isEmployee"></app-customer-aware-header>

<app-loader *ngIf="isLoading"></app-loader>

<ul class="nav nav-pills mb-4" *ngIf="measurement">
    <li class="nav-item">
        <a [routerLink]="['graphs']" class="nav-link">
            <span class="text-muted fa fa-usage mr-2"></span>
            <span i18n="@@bma_common_reporting">Reporting</span>
        </a>
    </li>
    <li *ngIf="isEmployee" class="nav-item">
        <a [routerLink]="['tracemon']" class="nav-link">
            <span class="text-muted fa fa-usage mr-2"></span>
            <span i18n="@@bma_common_tracemon">Tracemon</span>
        </a>
    </li>
    <li class="nav-item">
        <a [routerLink]="['latencymon']" class="nav-link">
            <span class="text-muted fa fa-usage mr-2"></span>
            <span i18n="@@bma_common_latencymon">Latencymon</span>
        </a>
    </li>
</ul>

<ng-container *ngIf="measurement">
    <router-outlet (activate)="onMeasurementLoaded($event)"></router-outlet>
</ng-container>
