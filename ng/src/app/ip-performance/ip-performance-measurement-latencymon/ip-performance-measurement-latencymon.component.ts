import { Component, Input, OnInit } from '@angular/core';

declare let initLatencymon: any;

@Component({
  selector: 'app-ip-performance-measurement-latencymon',
  templateUrl: './ip-performance-measurement-latencymon.component.html',
  styleUrls: ['./ip-performance-measurement-latencymon.component.css'],
})
export class IpPerformanceMeasurementLatencymonComponent implements OnInit {
  @Input() measurement: any;

  ngOnInit(): void {
    initLatencymon(
      '#latencymon',
      {}, // Tool options, see table below for more info
      { measurements: [this.measurement.measurementId] } // Query options, see table below for more info
    );
  }
}
