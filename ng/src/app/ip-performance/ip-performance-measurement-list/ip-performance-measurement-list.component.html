<div *ngIf="isEmployee" class="row mb-4">
    <div class="col">
        <button i18n-label="@@bma_common_addmeasurement" pButton type="button" (click)="openAddMeasurementModal()" label="Add Measurement" icon="pi pi-plus" class="p-button-success float-right"></button>
        <h1 i18n="@@bma_common_measurements">Measurements</h1>
    </div>
</div>

<form role="form" [formGroup]="filters" (ngSubmit)="submitFilters()">
    <div class="row mb-3">
        <div class="col">
            <input i18n-placeholder="@@bma_common_name" formControlName="name" type="text" id="name" class="form-control m-0" placeholder="Name" autofocus />
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-2">
            <select i18n-label="@@bma_common_site" formControlName="site" label="Site" class="form-control">
                <option i18n="@@bma_common_site" value="null">Site</option>
                <option *ngFor="let site of sites" value="{{ site }}">{{ site }}</option>
            </select>
        </div>
        <div class="col-10 text-right">
            <button i18n-label="@@bma_common_reset" pButton type="button" (click)="clearFilters()" label="Reset" icon="pi pi-refresh" class="p-button-secondary mr-2"></button>
            <button i18n-label="@@bma_common_search" pButton [disabled]="isLoading" label="Search" icon="pi pi-search"></button>
        </div>
    </div>
</form>

<app-loader *ngIf="isLoading"></app-loader>

<div class="table-responsive" *ngIf="!isLoading && measurements">
    <table class="table table-striped mt-2">
        <thead>
            <tr>
                <th i18n="@@bma_common_measurementid" class="text-center">Measurement ID</th>
                <th i18n="@@bma_common_name" class="text-center">Name</th>
                <th i18n="@@bma_common_site" class="text-center">Site</th>
                <th i18n="@@bma_common_actions" *ngIf="isEmployee" class="text-center">Actions</th>
            </tr>
        </thead>

        <tbody>
            <tr *ngIf="measurements.measurements.length === 0">
                <td i18n="@@bma_common_nomeasurement" colspan="99" class="text-center p-4">No measurement found.</td>
            </tr>
            <tr *ngFor="let measurement of measurements.measurements">
                <td class="text-center">
                    <a [routerLink]="[measurement.id, 'graphs']">{{ measurement.measurementId}}</a>
                </td>
                <td class="text-center text-nowrap">
                    {{ measurement.name }}
                </td>
                <td class="text-center text-nowrap">
                    {{ measurement.site }}
                </td>
                <td *ngIf="isEmployee" class="text-center text-nowrap">
                    <ul class="list-inline mb-0">
                        <li class="list-inline-item">
                            <a (click)="openEditMeasurementModal(measurement.id)"><span class="fa fa-change" aria-hidden="true"></span></a>
                        </li>
                        <li class="list-inline-item">
                            <a (click)="openRemoveMeasurementModal(measurement.id)">
                                <span class="fa fa-delete" aria-hidden="true"></span>
                            </a>
                        </li>
                    </ul>
                </td>
            </tr>
        </tbody>
    </table>
</div>
<div *ngIf="!isLoading && measurements">
    <app-pagination [totalCount]="measurements._metadata.totalCount" [limit]="measurements._metadata.limit" [offset]="measurements._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
</div>
<ng-container>
    <router-outlet></router-outlet>
</ng-container>
