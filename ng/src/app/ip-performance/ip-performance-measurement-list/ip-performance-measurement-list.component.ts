import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { SiteService } from 'src/app/services/site.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { ModalService } from 'src/app/services/modal.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { IpPerformanceMeasurementAddModalComponent } from '../ip-performance-measurement-add-modal/ip-performance-measurement-add-modal.component';
import { IpPerformanceMeasurementEditModalComponent } from '../ip-performance-measurement-edit-modal/ip-performance-measurement-edit-modal.component';
import { IpPerformanceMeasurementRemoveModalComponent } from '../ip-performance-measurement-remove-modal/ip-performance-measurement-remove-modal.component';

@Component({
  selector: 'app-ip-performance-measurement-list',
  templateUrl: './ip-performance-measurement-list.component.html',
  styleUrls: ['./ip-performance-measurement-list.component.css'],
})
export class IpPerformanceMeasurementListComponent implements OnInit {
  measurements: any;
  sites: string[];
  isLoading = false;
  dynamicDialogRef: DynamicDialogRef;
  filters: UntypedFormGroup;
  isEmployee = false;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private siteService: SiteService,
    private titleService: Title,
    private modalService: ModalService,
    private currentUserService: CurrentUserService
  ) {}

  ngOnInit(): void {
    this.filters = this.formBuilder.group({
      offset: [0],
      limit: [20],
      name: [null],
      site: [null],
    });
    this.titleService.setTitle($localize`:@@bma_ipperformancemeasurementlist_title:Measurements` + ' | Leaseweb');
    this.sites = this.siteService.sites();
    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
    this.isEmployee = this.currentUserService.isEmployee();
  }

  onQueryParamsChanged(queryParams): void {
    this.isLoading = true;
    this.filters.reset(queryParams);
    const params = this.sanitise(this.filters.getRawValue());

    this.httpClient.get<any>('/_/internal/networkperformanceapi/measurements', { params }).subscribe({
      next: (data: any) => {
        this.isLoading = false;
        this.measurements = data;
      },
      error: (error: any) => {
        this.isLoading = false;
        this.measurements = null;
      },
    });
  }

  openAddMeasurementModal() {
    this.dynamicDialogRef = this.modalService.show(IpPerformanceMeasurementAddModalComponent, {});
    this.dynamicDialogRef.onClose.subscribe((event) => {
      if (event?.measurementAdded === true) {
        this.onQueryParamsChanged(this.filters.getRawValue());
      }
    });
  }

  openEditMeasurementModal(id) {
    this.dynamicDialogRef = this.modalService.show(IpPerformanceMeasurementEditModalComponent, { id });
    this.dynamicDialogRef.onClose.subscribe((event) => {
      if (event?.measurementUpdated === true) {
        this.onQueryParamsChanged(this.filters.getRawValue());
      }
    });
  }

  openRemoveMeasurementModal(id) {
    this.dynamicDialogRef = this.modalService.show(IpPerformanceMeasurementRemoveModalComponent, { id });
    this.dynamicDialogRef.onClose.subscribe((event) => {
      if (event?.measurementRemoved === true) {
        this.onQueryParamsChanged(this.filters.getRawValue());
      }
    });
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  clearFilters(): void {
    this.filters.reset();
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
    });
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
