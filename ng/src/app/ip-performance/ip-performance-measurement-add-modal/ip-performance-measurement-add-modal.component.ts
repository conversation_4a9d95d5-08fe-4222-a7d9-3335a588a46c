import { AlertService } from 'src/app/services/alert.service';
import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { SiteService } from 'src/app/services/site.service';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { DynamicDialogRef } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-ip-performance-measurement-add-modal',
  templateUrl: './ip-performance-measurement-add-modal.component.html',
  styleUrls: ['./ip-performance-measurement-add-modal.component.css'],
})
export class IpPerformanceMeasurementAddModalComponent implements OnInit {
  sites: any;
  form: UntypedFormGroup;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private siteService: SiteService,
    private httpClient: HttpClient,
    private readonly formBuilder: UntypedFormBuilder,
    private dynamicDialogRef: DynamicDialogRef,
    private alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.sites = this.siteService.sites();
    this.form = this.formBuilder.group({
      name: [null, Validators.required],
      measurementId: [],
      site: [''],
      public: [false],
    });
  }

  onMeasurementSubmit() {
    this.isSubmitting = true;
    this.alertService.clear();
    if (this.form.valid) {
      const body: any = {};
      body.name = this.form.get('name').value;
      body.measurementId = this.form.get('measurementId').value;
      body.site = this.form.get('site').value;
      body.public = this.form.get('public').value;

      this.httpClient.post('/_/internal/networkperformanceapi/measurements', body).subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.alertService.alert(
            {
              type: 'success',
              description: $localize`:@@bma_ipperformancemeasurementmodal_success:Successfully added Measurement ${data.measurementId}.`,
            },
            true
          );
          this.closeModal({ measurementAdded: true });
        },
        error: (error: any) => {
          this.isSubmitting = false;

          if (error.error.errorDetails) {
            Object.entries(error.error.errorDetails || {}).forEach(([key, value]) => {
              this.form.get(key).setErrors({ api: value[0] });
            });
          }
          this.alertService.alertApiError(error.error, true);
        },
      });
    } else {
      this.isSubmitting = false;
      this.alertService.alert(
        {
          type: 'error',
          description: $localize`:@@bma_common_invalidentry:Invalid entries entered. Please check it.`,
        },
        true
      );
    }
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
