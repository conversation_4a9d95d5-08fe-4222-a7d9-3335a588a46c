<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{width: '100%','max-height': '500px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_common_addmeasurement" class="modal-title">Add Measurement</h3>
    </ng-template>

    <form [formGroup]="form" (ngSubmit)="onMeasurementSubmit()">
        <div class="modal-body">
            <div class="row mt-1 mb-2">
                <div class="md-form input-group">
                    <div i18n="@@bma_common_name" class="input-group-prepend col-3">Name</div>
                    <div class="col-9">
                        <input type="text" id="name" formControlName="name" required="required" class="form-control" autofocus />
                        <span class="text-danger" *ngIf="form.get('name').errors && form.get('name').errors.server">{{ form.get('name').errors.server }}<br /></span>
                    </div>
                </div>
            </div>

            <div class="row mt-1 mb-2">
                <div class="md-form input-group">
                    <div i18n="@@bma_common_measurementid" class="input-group-prepend col-3">Measurement ID</div>
                    <div class="col-9">
                        <input type="text" id="measurementId" formControlName="measurementId" required="required" class="form-control" />
                        <span *ngIf="form.get('measurementId').errors && form.get('measurementId').errors.server">{{ form.get('measurementId').errors.server }}<br /></span>
                    </div>
                </div>
            </div>

            <div class="row mt-1 mb-2">
                <div class="md-form input-group">
                    <div i18n="@@bma_common_site" class="input-group-prepend col-3">Site</div>
                    <div class="col-9">
                        <select id="site" formControlName="site" label="Site" required="required" class="form-control">
                            <option i18n="@@bma_common_site" value="">Site</option>
                            <option *ngFor="let site of sites" value="{{ site }}">{{ site }}</option>
                        </select>
                        <span *ngIf="form.get('site').errors && form.get('site').errors.server">{{ form.get('site').errors.server }}<br /></span>
                    </div>
                </div>
            </div>
            <div class="row mt-1 mb-2">
                <div class="md-form input-group">
                    <div i18n="@@bma_common_publiccustomer" class="input-group-prepend col-3">Customer (Public)</div>
                    <div class="col-9">
                        <div class="checkbox">
                            <input type="checkbox" checked formControlName="public" class="form-check-input" id="public" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <div *ngIf="form.errors?.api?.errorMessage" class="alert alert-danger" role="alert">
        <span>{{ form.errors.api.errorMessage }}</span>
    </div>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_close" pButton type="button" icon="pi pi-times" iconPos="left" label="Close" class="p-button-secondary" (click)="closeModal()"></button>
            <button i18n-label="@@bma_common_add" pButton (click)="onMeasurementSubmit()" icon="pi pi-check" iconPos="left" [loading]="isSubmitting" label="Add"></button>
        </div>
    </ng-template>
</p-dialog>
