import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-ip-performance-measurement-traceroute-modal',
  templateUrl: './ip-performance-measurement-traceroute-modal.component.html',
  styleUrls: ['./ip-performance-measurement-traceroute-modal.component.css'],
})
export class IpPerformanceMeasurementTracerouteModalComponent implements OnInit {
  isLoading = false;
  showDialog = true;
  measurementId: any;
  probeId: any;
  at: any;
  traceroute: any;

  constructor(
    private httpClient: HttpClient,
    private router: Router,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.measurementId = this.dynamicDialogConfig.data.measurementId;
    this.probeId = this.dynamicDialogConfig.data.probeId;
    this.at = this.dynamicDialogConfig.data.at;

    this.isLoading = true;
    this.httpClient
      .get<any>(
        `/_/internal/networkperformanceapi/measurements/${this.measurementId}/traceroute/${this.probeId}/${this.at}`
      )
      .subscribe({
        next: (traceroute: any) => {
          this.traceroute = traceroute;
          this.isLoading = false;
        },
        error: (error: any) => {
          this.isLoading = false;
          this.traceroute = null;
        },
      });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
