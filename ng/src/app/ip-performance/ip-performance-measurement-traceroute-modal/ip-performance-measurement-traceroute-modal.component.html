<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{width: '100%', 'max-height': '500px'}">
    <ng-template pTemplate="header">
        <h3 class="modal-title">
            <span i18n="@@bma_ipperformancemeasurementtraceroutemodal_header_measurement_id">Measurement ID: {{ measurementId }}</span
            >, <span i18n="@@bma_ipperformancemeasurementtraceroutemodal_header_probe_id">Probe ID: {{ probeId }}</span>
            <span *ngIf="traceroute"> {{ measurementId }} </span>
        </h3>
    </ng-template>

    <app-loader *ngIf="isLoading"></app-loader>
    <div class="modal-body" *ngIf="!isLoading && traceroute && traceroute.traceroute">
        <h5 i18n="@@bma_ipperformancemeasurementtraceroutemodal_description1">
            <span>Traceroute from {{ traceroute.traceroute.src_addr }}</span> <span *ngIf="traceroute.traceroute.src_host_name && traceroute.traceroute.src_addr != traceroute.traceroute.src_host_name">({{ traceroute.traceroute.src_host_name }})</span> to {{ traceroute.traceroute.dst_addr }} <span *ngIf="traceroute.traceroute.dst_host_name && traceroute.traceroute.dst_addr != traceroute.traceroute.dst_host_name">({{ traceroute.traceroute.dst_host_name }})</span>, {{ traceroute.traceroute.size }} byte packets
        </h5>
        <h6 i18n="@@bma_ipperformancemeasurementtraceroutemodal_description2">Timestamp: {{at | date: 'yyyy-MM-dd h:mm a'}}</h6>

        <table class="table">
            <tr *ngFor="let result of traceroute.traceroute.result" class="d-flex">
                <th class="col-1">{{ result.hop }}</th>
                <td *ngIf="result.result && result.result.length != 0">
                    <span class="badge badge-success mr-1">{{ result.result[0].from }}</span>
                    <span *ngIf="result.result[0].from_host_name && result.result[0].from != result.result[0].from_host_name" class="badge badge-warning mr-1">{{ result.result[0].from_host_name }}</span>
                    <span *ngFor="let hopResult of result.result">
                        <span class="mr-1" *ngIf="hopResult.x">{{ hopResult.x }}</span>
                        <span class="badge badge-primary mr-1" *ngIf="!hopResult.x">{{ hopResult.rtt }}</span>
                    </span>
                </td>
            </tr>
        </table>
    </div>
    <div i18n="@@bma_ipperformancemeasurementtraceroutemodal_notraceroute" *ngIf="!isLoading && !traceroute">Traceroute details not found</div>
</p-dialog>
