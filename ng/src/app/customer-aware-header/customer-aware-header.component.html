<header class="mb-4">
    <div *ngIf="customer && customer.salesOrgs[salesOrgId].isSuspended" class="alert alert-danger" role="alert">
        <div class="alert-icon">
            <i class="fa fa-information fa-lg" aria-hidden="true"></i>
        </div>
        <h4 i18n="@@bma_customerawareheader_suspended" class="alert-heading">Suspended</h4>
        <p i18n="@@bma_customerawareheader_suspendedalert" class="alert-body">This customer is suspended!</p>
    </div>
    <div *ngIf="customer && customer.salesOrgs[salesOrgId].isTerminated" class="alert alert-danger" role="alert">
        <div class="alert-icon">
            <i class="fa fa-information fa-lg" aria-hidden="true"></i>
        </div>
        <h4 i18n="@@bma_customerawareheader_terminated" class="alert-heading">Terminated</h4>
        <p i18n="@@bma_customerawareheader_terminatedalert" class="alert-body">This customer is terminated!</p>
    </div>

    <h1>
        {{ caption }}
        <sup *ngIf="knowledgeBaseLink"
            ><a [href]="knowledgeBaseLink" target="_blank"><i i18n-title="@@bma_common_kb" title="Knowledge Base" class="fa fa-knowledge" aria-hidden="true"></i></a
        ></sup>
    </h1>

    <h3 *ngIf="reference">
        {{ reference }}
    </h3>

    <h2 *ngIf="isEmployee && customerId" class="overflow-ellipsis">
        <app-country-flag salesOrgId="{{ salesOrgId }}"></app-country-flag>
        <span>{{ customerId }}</span>
        <span *ngIf="customer"> {{ customer.name }}</span>
    </h2>
</header>
