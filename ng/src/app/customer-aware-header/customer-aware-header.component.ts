import { Component, OnInit, Input } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-customer-aware-header',
  templateUrl: './customer-aware-header.component.html',
  styleUrls: ['./customer-aware-header.component.css'],
})
export class CustomerAwareHeaderComponent implements OnInit {
  @Input() caption: string;
  @Input() reference: string;
  @Input() customerId: string;
  @Input() salesOrgId: string;
  @Input() knowledgeBaseLink: string;
  @Input() isEmployee = false;

  customer: any;

  constructor(private httpClient: HttpClient) {}

  ngOnInit(): void {
    if (this.isEmployee && this.customerId) {
      const params = { customerId: this.customerId };

      this.httpClient.get<any>('/_legacy/customers', { params }).subscribe(
        (data) => {
          this.customer = data;
        },
        (error) => {
          this.customer = null;
        }
      );
    }
  }
}
