<div class="row">
    <div class="col-12">
        <h3>
            <span class="fa fa-notification mr-1"></span>
            <span i18n="@@bma_notificationlist_header">Notifications</span>
        </h3>

        <p i18n="@@bma_notificationlist_description1">When a {{ equipmentType.slice(0, -1) | titlecase}} hits a bandwidth or data traffic threshold limit, you can get a notification, based on the frequency you set, on all technical contact email addresses.</p>

        <p i18n="@@bma_notificationlist_description2">The data usage is calculated on a 3-hour interval.</p>

        <p i18n="@@bma_notificationlist_description3" *ngIf="equipmentType == 'aggregationPacks' && !isEmployee">After an upgrade, the notification settings will be visible again within 3 hours. If you still don't see them, please open a <a href="/tickets/new?equipmentId={{ equipment.id }}&category=technical-assistance&subCategory=connectivity&subject=Bandwidth%20notification%20settings%20are%20not%20visible&message=Bandwidth%20notification%20settings%20are%20not%20visible">support ticket</a>.</p>

        <ng-container *ngIf="equipmentType != 'aggregationPacks' && equipment.contract && (equipment?.contract?.status == 'ACTIVE') && equipment.contract.networkTraffic && equipment.contract.networkTraffic.type && (equipment.contract.networkTraffic.type == '95TH' || equipment.contract.networkTraffic.type == 'DATATRAFFIC') && equipmentType == 'servers' && !isEmployee">
            <a i18n="@@bma_common_upgradeconnectivity" pButton type="button" label="Upgrade Connectivity" target="_blank" [href]="getCommerceUpgradeConnectivityUrl()" icon="pi pi-caret-up" class="p-button-success pull-right"></a>
        </ng-container>
    </div>
</div>

<app-loader *ngIf="isLoading"></app-loader>

<div class="row" *ngIf="!isLoading && notificationSettings">
    <div class="col-12">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th i18n="@@bma_common_type" scope="col">Type</th>
                        <th i18n="@@bma_common_frequency" scope="col">Frequency</th>
                        <th i18n="@@bma_common_threshold" scope="col">Threshold</th>
                        <th i18n="@@bma_notificationlist_thresholdexceed" scope="col">Threshold Exceeded At</th>
                        <th i18n="@@bma_notificationlist_lastemailtrigger" scope="col">Last Email Triggered At</th>
                        <th i18n="@@bma_common_actions" scope="col" class="text-center">Actions</th>
                    </tr>
                </thead>

                <tbody>
                    <tr *ngIf="notificationSettings.length === 0">
                        <td i18n="@@bma_notificationlist_none" class="text-center p-4" colspan="6">No Notification Settings found.</td>
                    </tr>
                    <tr *ngFor="let notificationSetting of notificationSettings; index as i">
                        <td>
                            {{ notificationSetting.type == 'datatraffic' ? 'DATA TRAFFIC' : 'BANDWIDTH' }}
                        </td>
                        <td>
                            {{ notificationSetting.frequency }}
                        </td>
                        <td>
                            {{ notificationSetting.threshold }}
                            {{ notificationSetting.unit }}
                        </td>
                        <td>
                            {{ notificationSetting.thresholdExceededAt ? (notificationSetting.thresholdExceededAt|lswDateTime:"short") : '-' }}
                        </td>
                        <td>
                            {{ notificationSetting.actions[0].lastTriggeredAt ? (notificationSetting.actions[0].lastTriggeredAt|lswDateTime:"short") : '-' }}
                        </td>

                        <td class="text-right">
                            <button i18n-label="@@bma_common_update" pButton label="Update" icon="fa fa-change" class="p-button-primary p-button-link p-button-sm" (click)="openNotificationEditModal(notificationSetting)" [class.disabled]="equipment.isSharedEol"></button>
                            <button i18n-label="@@bma_common_remove" pButton label="Remove" icon="fa fa-delete" class="p-button-link p-button-sm text-danger" (click)="openNotificationRemoveModal(notificationSetting)" [class.disabled]="equipment.isSharedEol"></button>
                        </td>
                    </tr>
                </tbody>
            </table>
            <button i18n-label="@@bma_notificationlist_add" pButton label="Add Notifications" class="p-button-sm pull-right mt-2 mb-2" (click)="openNotificationAddModal()" [class.disabled]="equipment.isSharedEol"></button>
        </div>
    </div>
</div>
