import { Component, OnInit, Input } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { CommerceService } from 'src/app/services/commerce.service';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { NotificationAddModalComponent } from 'src/app/notification/notification-add-modal/notification-add-modal.component';
import { NotificationEditModalComponent } from 'src/app/notification/notification-edit-modal/notification-edit-modal.component';
import { NotificationRemoveModalComponent } from 'src/app/notification/notification-remove-modal/notification-remove-modal.component';

@Component({
  selector: 'app-notification-list',
  templateUrl: './notification-list.component.html',
  styleUrls: ['./notification-list.component.css'],
})
export class NotificationListComponent implements OnInit {
  @Input() equipmentId: any;
  @Input() equipmentType: string;
  @Input() equipment: any;

  notificationSettings: any;
  isLoading = false;
  datatrafficNotificationSettings: any;
  bandwidthNotificationSettings: any;
  selectedNotification: any;
  selectedType: any;
  selectedThreshold: any;
  selectedUnit: any;
  selectedFrequency: any;
  notificationId: any;
  isEmployee = false;
  customerContacts: any;
  dynamicDialogRef: DynamicDialogRef;

  constructor(
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    private modalService: ModalService,
    private commerceService: CommerceService
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.getNotifications();
    this.getCustomerContacts();
  }

  async getNotifications() {
    this.notificationSettings = [];
    this.isLoading = true;
    const datatrafficUrl = `/_/internal/bmusageapi/v2/${this.equipmentType}/${this.equipmentId}/notificationSettings/datatraffic`;
    const bandwidthUrl = `/_/internal/bmusageapi/v2/${this.equipmentType}/${this.equipmentId}/notificationSettings/bandwidth`;

    await this.httpClient
      .get(datatrafficUrl)
      .toPromise()
      .then(
        (data: any) => {
          if (data.datatrafficNotificationSettings) {
            for (const datatrafficNotificationSetting of data.datatrafficNotificationSettings) {
              datatrafficNotificationSetting.type = 'datatraffic';
              this.notificationSettings.push(datatrafficNotificationSetting);
            }
          } else {
            this.notificationSettings = [];
            this.datatrafficNotificationSettings = [];
          }
        },
        (error) => {
          this.notificationSettings = [];
          this.datatrafficNotificationSettings = [];
          this.datatrafficNotificationSettings = [];
        }
      );

    await this.httpClient
      .get(bandwidthUrl)
      .toPromise()
      .then(
        (data: any) => {
          if (data.bandwidthNotificationSettings) {
            for (const bandwidthNotificationSetting of data.bandwidthNotificationSettings) {
              bandwidthNotificationSetting.type = 'bandwidth';
              this.notificationSettings.push(bandwidthNotificationSetting);
            }
          } else {
            this.notificationSettings = [];
            this.bandwidthNotificationSettings = [];
          }
        },
        (error) => {
          this.notificationSettings = [];
          this.bandwidthNotificationSettings = [];
        }
      );
    this.isLoading = false;
  }

  async getCustomerContacts() {
    if (this.equipment.contract) {
      let params = new HttpParams();
      params = params.set(
        'customerId',
        this.equipmentType === 'aggregationPacks' ? this.equipment.customerId : this.equipment.contract.customerId
      );
      params = params.set(
        'salesOrgId',
        this.equipmentType === 'aggregationPacks' ? this.equipment.salesOrgId : this.equipment.contract.salesOrgId
      );
      await this.httpClient
        .get<any>('/_/internal/dedicatedserverapi/v2/lookup/customerContacts', { params })
        .toPromise()
        .then(
          (data: any) => {
            if (data) {
              const contacts = [];
              for (const contact of data.contacts) {
                if (contact.emailAddress) {
                  contacts.push(contact.emailAddress);
                }
              }
              this.customerContacts = contacts.toString();
            }
          },
          (error) => {
            this.customerContacts = '';
          }
        );
    } else {
      this.customerContacts = '';
    }
  }

  refreshNotifications(): void {
    this.getNotifications();
  }

  openNotificationRemoveModal(selectedNotification: any) {
    this.dynamicDialogRef = this.modalService.show(NotificationRemoveModalComponent, {
      equipment: this.equipment,
      equipmentType: this.equipmentType,
      equipmentId: this.equipmentId,
      customerContacts: this.customerContacts,
      notification: selectedNotification,
    });

    this.dynamicDialogRef.onClose.subscribe((reason: any) => {
      if (reason?.notificationRemoved === true) {
        this.refreshNotifications();
      }
    });
  }

  openNotificationAddModal() {
    this.dynamicDialogRef = this.modalService.show(NotificationAddModalComponent, {
      equipment: this.equipment,
      equipmentType: this.equipmentType,
      equipmentId: this.equipmentId,
      customerContacts: this.customerContacts,
    });

    this.dynamicDialogRef.onClose.subscribe((reason: any) => {
      if (reason?.notificationCreated === true) {
        this.refreshNotifications();
      }
    });
  }

  openNotificationEditModal(selectedNotification: any) {
    this.dynamicDialogRef = this.modalService.show(NotificationEditModalComponent, {
      equipment: this.equipment,
      equipmentType: this.equipmentType,
      equipmentId: this.equipmentId,
      customerContacts: this.customerContacts,
      type: selectedNotification.type.toUpperCase(),
      threshold: selectedNotification.threshold,
      frequency: selectedNotification.frequency,
      unit: selectedNotification.unit,
      notificationId: selectedNotification.id,
    });

    this.dynamicDialogRef.onClose.subscribe((reason: any) => {
      if (reason?.notificationUpdated === true) {
        this.refreshNotifications();
      }
    });
  }

  getCommerceUpgradeConnectivityUrl(): string {
    return this.commerceService.getCommerceConfigureProductUrl(this.equipment.contract.id, 'DEDSER02_MOD_DATAPACK');
  }
}
