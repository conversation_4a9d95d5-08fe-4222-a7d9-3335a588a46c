import { Component, OnInit } from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert.service';

@Component({
  selector: 'app-notification-remove-modal',
  templateUrl: './notification-remove-modal.component.html',
  styleUrls: ['./notification-remove-modal.component.css'],
})
export class NotificationRemoveModalComponent implements OnInit {
  equipmentId: any;
  equipmentType: any;
  equipment: any;
  notification: any;
  isSubmitting = false;
  showDialog = true;

  constructor(
    private alertService: AlertService,
    private httpClient: HttpClient,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.equipmentId = this.dynamicDialogConfig.data.equipmentId;
    this.equipmentType = this.dynamicDialogConfig.data.equipmentType;
    this.equipment = this.dynamicDialogConfig.data.equipment;
    this.notification = this.dynamicDialogConfig.data.notification;
  }

  removeNotification(): void {
    this.isSubmitting = true;
    this.alertService.clear();
    this.httpClient
      .delete<any>(
        `/_/internal/bmusageapi/v2/${this.equipmentType}/${this.equipmentId}/notificationSettings/${this.notification.type}/${this.notification.id}`
      )
      .subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.alertService.alert({
            type: 'success',
            description: $localize`:@@bma_notificationremovemodal_removed:Notification successfully removed` + '.',
          });
          this.closeModal({ notificationRemoved: true });
        },
        error: (error: any) => {
          this.isSubmitting = false;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ notificationRemoved: true });
        },
      });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
