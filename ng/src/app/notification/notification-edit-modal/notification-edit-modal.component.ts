import { Component, OnInit } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-notification-edit-modal',
  templateUrl: './notification-edit-modal.component.html',
  styleUrls: ['./notification-edit-modal.component.css'],
})
export class NotificationEditModalComponent implements OnInit {
  equipmentId: any;
  equipmentType: any;
  equipment: any;
  type: any;
  threshold: any;
  frequency: any;
  unit: any;
  notificationId: any;
  customerContacts: any;

  form: UntypedFormGroup;
  isSubmitting = false;
  units: any;
  isEmployee = false;
  showDialog = true;

  constructor(
    private alertService: AlertService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.equipmentId = this.dynamicDialogConfig.data.equipmentId;
    this.equipmentType = this.dynamicDialogConfig.data.equipmentType;
    this.equipment = this.dynamicDialogConfig.data.equipment;
    this.customerContacts = this.dynamicDialogConfig.data.customerContacts;
    this.type = this.dynamicDialogConfig.data.type;
    this.threshold = this.dynamicDialogConfig.data.threshold;
    this.frequency = this.dynamicDialogConfig.data.frequency;
    this.unit = this.dynamicDialogConfig.data.unit;
    this.notificationId = this.dynamicDialogConfig.data.notificationId;
    this.form = this.formBuilder.group({
      type: [{ value: this.type.toUpperCase(), disabled: true }, Validators.required],
      email: [this.customerContacts],
      threshold: [this.threshold, Validators.required],
      unit: [this.unit, Validators.required],
      frequency: [this.frequency, Validators.required],
    });

    this.getUnits();
  }

  getUnits(): void {
    const bandwidth = [
      { display: 'Mbps', value: 'Mbps' },
      { display: 'Gbps', value: 'Gbps' },
    ];
    const datatraffic = [
      { display: 'MB', value: 'MB' },
      { display: 'GB', value: 'GB' },
      { display: 'TB', value: 'TB' },
    ];
    const type = this.form.get('type').value;
    if (type === 'DATATRAFFIC') {
      this.units = datatraffic;
    } else {
      this.units = bandwidth;
    }
  }

  updateNotificationSetting(): void {
    this.isSubmitting = true;
    this.alertService.clear();
    const params = {
      frequency: this.form.get('frequency').value,
      unit: this.form.get('unit').value,
      threshold: this.form.get('threshold').value,
    };
    const notificationType = this.form.get('type').value.toLowerCase();
    this.httpClient
      .put<any>(
        `/_/internal/bmusageapi/v2/${this.equipmentType}/${this.equipmentId}/notificationSettings/` +
          notificationType +
          '/' +
          this.notificationId,
        params
      )
      .subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.form.reset();
          this.alertService.alert({
            type: 'success',
            description:
              $localize`:@@bma_notificationeditmodal_updated:Notification setting updated successfully` + '.',
          });
          this.closeModal({ notificationUpdated: true });
        },
        error: (error: any) => {
          this.isSubmitting = false;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ notificationUpdated: false });
        },
      });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
