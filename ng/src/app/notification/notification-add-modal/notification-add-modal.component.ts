import { Component, OnInit } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { HttpClient } from '@angular/common/http';
import { AlertService } from 'src/app/services/alert.service';
import { CurrentUserService } from 'src/app/services/current-user.service';

@Component({
  selector: 'app-notification-add-modal',
  templateUrl: './notification-add-modal.component.html',
  styleUrls: ['./notification-add-modal.component.css'],
})
export class NotificationAddModalComponent implements OnInit {
  equipmentId: any;
  equipmentType: any;
  equipment: any;
  customerContacts: any;
  form: UntypedFormGroup;
  isSubmitting = false;
  units: any;
  isEmployee = false;
  showDialog = true;

  constructor(
    private alertService: AlertService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private currentUserService: CurrentUserService,
    public dynamicDialogRef: DynamicDialogRef,
    private dynamicDialogConfig: DynamicDialogConfig
  ) {}

  ngOnInit(): void {
    this.isEmployee = this.currentUserService.isEmployee();
    this.equipmentId = this.dynamicDialogConfig.data.equipmentId;
    this.equipmentType = this.dynamicDialogConfig.data.equipmentType;
    this.equipment = this.dynamicDialogConfig.data.equipment;
    this.customerContacts = this.dynamicDialogConfig.data.customerContacts;
    this.form = this.formBuilder.group({
      type: ['DATATRAFFIC', Validators.required],
      email: [this.customerContacts],
      threshold: [null, Validators.required],
      unit: [null, Validators.required],
      frequency: [null, Validators.required],
    });

    this.getUnits();
  }

  getUnits(): void {
    const bandwidth = [
      { display: 'Mbps', value: 'Mbps' },
      { display: 'Gbps', value: 'Gbps' },
    ];
    const datatraffic = [
      { display: 'MB', value: 'MB' },
      { display: 'GB', value: 'GB' },
      { display: 'TB', value: 'TB' },
    ];
    const type = this.form.get('type').value;
    if (type === 'DATATRAFFIC') {
      this.units = datatraffic;
    } else {
      this.units = bandwidth;
    }
    this.form.get('unit').setValue(this.units[0].value);
  }

  createNotificationSetting(): void {
    this.isSubmitting = true;
    this.alertService.clear();
    const params = {
      frequency: this.form.get('frequency').value,
      unit: this.form.get('unit').value,
      threshold: this.form.get('threshold').value,
    };
    const notificationType = this.form.get('type').value.toLowerCase();
    this.httpClient
      .post<any>(
        `/_/internal/bmusageapi/v2/${this.equipmentType}/${this.equipmentId}/notificationSettings/` + notificationType,
        params
      )
      .subscribe({
        next: (data: any) => {
          this.isSubmitting = false;
          this.form.reset();
          this.alertService.alert({
            type: 'success',
            description: $localize`:@@bma_notificationaddmodal_created:Notification setting created successfully` + '.',
          });
          this.closeModal({ notificationCreated: true });
        },
        error: (error: any) => {
          this.isSubmitting = false;
          this.alertService.alertApiError(error.error, true);
          this.closeModal({ notificationCreated: false });
        },
      });
  }

  closeModal(event: any = null) {
    this.dynamicDialogRef.close(event);
  }
}
