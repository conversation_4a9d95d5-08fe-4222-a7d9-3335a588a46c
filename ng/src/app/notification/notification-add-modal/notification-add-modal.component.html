<p-dialog [(visible)]="showDialog" [modal]="true" [dismissableMask]="true" [closeOnEscape]="true" [draggable]="false" [responsive]="true" [closable]="true" (onHide)="closeModal()" [style]="{ width: '100%', 'max-height': '600px'}">
    <ng-template pTemplate="header">
        <h3 i18n="@@bma_notificationaddmodal_header" class="modal-title">Create Notification Setting</h3>
    </ng-template>

    <form [formGroup]="form" (ngSubmit)="createNotificationSetting()" class="form-horizontal">
        <div class="modal-body">
            <p i18n="@@bma_common_notification1">The data usage is calculated on a 3-hour interval, and is reset on a monthly basis.</p>
            <p i18n="@@bma_common_notification2" *ngIf="!isEmployee">
                If you want to change the email where the notifications are sent to please see
                <a href="/customer/contacts">Manage Contacts</a> page
            </p>
            <div class="form-group">
                <label i18n="@@bma_common_type" for="type" class="col-form-label">Type</label>
                <select class="form-control" formControlName="type" id="type" (change)="getUnits()" autofocus>
                    <option i18n="@@bma_common_datatraffic" value="DATATRAFFIC" selected>Data Traffic</option>
                    <option i18n="@@bma_common_bandwidth" value="BANDWIDTH">Bandwidth</option>
                </select>
            </div>
            <div class="form-group">
                <label i18n="@@bma_common_email" class="col-form-label" for="email">Email</label>
                <input class="form-control" formControlName="email" type="text" id="email" value="customerContacts" readonly />
            </div>
            <div class="form-group">
                <label i18n="@@bma_common_threshold" class="col-form-label" for="unit">Threshold</label>
                <input class="form-control" formControlName="threshold" type="number" id="threshold" required />
                <select class="form-control" formControlName="unit" id="unit">
                    <option *ngFor="let unit of units" [ngValue]="unit.value">
                        {{ unit.display }}
                    </option>
                </select>
            </div>
            <div class="form-group">
                <label i18n="@@bma_common_frequency" class="col-form-label" for="frequency">Frequency</label>
                <select formControlName="frequency" class="form-control" id="frequency">
                    <option i18n="@@bma_common_daily" value="DAILY">Daily</option>
                    <option i18n="@@bma_common_weekly" value="WEEKLY">Weekly</option>
                    <option i18n="@@bma_common_monthly" value="MONTHLY">Monthly</option>
                </select>
            </div>
        </div>
    </form>

    <ng-template pTemplate="footer">
        <div class="mt-2">
            <button i18n-label="@@bma_common_cancel" pButton (click)="closeModal()" type="button" icon="pi pi-times" iconPos="left" label="Cancel" class="p-button-secondary"></button>
            <button i18n-label="@@bma_common_save" pButton (click)="createNotificationSetting()" label="Save" icon="pi pi-check" iconPos="left" [loading]="isSubmitting"></button>
        </div>
    </ng-template>
</p-dialog>
