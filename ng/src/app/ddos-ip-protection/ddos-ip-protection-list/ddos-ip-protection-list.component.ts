import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Title } from '@angular/platform-browser';
import { SiteService } from 'src/app/services/site.service';
import { ActivatedRoute, Router } from '@angular/router';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';

@Component({
  selector: 'app-ddos-ip-protection-list',
  templateUrl: './ddos-ip-protection-list.component.html',
  styleUrls: ['./ddos-ip-protection-list.component.css'],
})
export class DdosIpProtectionListComponent implements OnInit {
  ddosIpProtections: any;
  ddosProfiles: any;
  sources: any;
  isLoading = false;
  error = false;
  sites: any;
  filters: UntypedFormGroup;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private formbuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private siteService: SiteService,
    private titleService: Title
  ) {}

  ngOnInit(): void {
    this.filters = this.formbuilder.group({
      offset: [0],
      limit: [10],
      ip: [null],
      site: [null],
      profile: [null],
      source: [null],
    });

    this.sites = this.siteService.sites();
    this.router.routeReuseStrategy.shouldReuseRoute = () => false;

    this.titleService.setTitle($localize`:@@bma_ddosipprotectionlist_title:DDoS IP Protection` + '| Leaseweb');

    this.ddosProfiles = [
      '10::Standard::Default::5G',
      '100::Standard::Default::5G',
      '1000::Standard::Default::5G',
      '2000::Standard::Default::5G',
      '5000::Standard::Default::5G',
      '10000::Standard::Default::5G',
      '20000::Standard::Default::5G',
      '25000::Standard::Default::5G',
      '40000::Standard::Default::5G',
      '100::Advanced::Default::10G',
      '1000::Advanced::Default::10G',
      '10000::Advanced::Default::10G',
      '100::Advanced::LowUDP::10G',
      '1000::Advanced::LowUDP::10G',
      '10000::Advanced::LowUDP::10G',
      '100::Advanced::MedUDP::10G',
      '1000::Advanced::MedUDP::10G',
      '10000::Advanced::MedUDP::10G',
    ];

    this.sources = ['IPAM_OVERRIDE', 'DOWN_LINK_SPEED', 'TOR_SWITCH', 'ADVANCED_DETECTION', 'IPAM_IP'];

    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    this.isLoading = true;
    this.filters.reset(queryParams);
    const params = this.sanitise(this.filters.getRawValue());
    this.httpClient.get<any>('/_/internal/nseapi/v1/ddos/wanguardProfileCaches', { params }).subscribe(
      (data: any) => {
        this.isLoading = false;
        this.error = false;
        this.ddosIpProtections = data;
      },
      (error: any) => {
        this.isLoading = false;
        this.error = true;
        this.ddosIpProtections = { _metadata: { totalCount: 0 }, wanguardProfileCaches: [] };
      }
    );
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  clearFilters(): void {
    this.filters.reset();
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
    });
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }
}
