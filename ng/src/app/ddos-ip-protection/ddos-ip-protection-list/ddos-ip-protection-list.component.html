<div class="mb-4">
    <h1>DDoS IP Protection</h1>
</div>

<form role="form" [formGroup]="filters" (ngSubmit)="submitFilters()">
    <div class="row mb-3">
        <div class="col">
            <input i18n-placeholder="@@bma_ddosipprotectionlist_ipplaceholder" type="text" class="form-control m-0" formControlName="ip" placeholder="IP Address..." autofocus />
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-2">
            <select i18n-label="@@bma_ddosipprotectionlist_site" formControlName="site" label="Site" class="form-control">
                <option i18n="@@bma_ddosipprotectionlist_site" value="null">Site</option>
                <option *ngFor="let site of sites" value="{{ site }}">{{ site }}</option>
            </select>
        </div>

        <div class="col-3">
            <select i18n-label="@@bma_ddosipprotectionlist_profile" formControlName="profile" label="Profile" class="form-control">
                <option i18n="@@bma_ddosipprotectionlist_profile" value="null">Profile</option>
                <option *ngFor="let ddosProfile of ddosProfiles" value="{{ ddosProfile }}">
                    {{ ddosProfile }}
                </option>
            </select>
        </div>

        <div class="col-3">
            <select i18n-label="@@bma_ddosipprotectionlist_source" formControlName="source" label="Source" class="form-control">
                <option i18n="@@bma_ddosipprotectionlist_source" value="null">Source</option>
                <option *ngFor="let source of sources" value="{{ source }}">{{ source }}</option>
            </select>
        </div>

        <div class="col-4 text-right">
            <button i18n-label="@@bma_ddosipprotectionlist_reset" pButton type="button" (click)="clearFilters()" label="Reset" icon="pi pi-refresh" class="p-button-secondary mr-2"></button>
            <button i18n-label="@@bma_ddosipprotectionlist_search" pButton [disabled]="isLoading" label="Search" icon="pi pi-search"></button>
        </div>
    </div>
</form>

<app-loader *ngIf="isLoading"></app-loader>

<div class="table-responsive" *ngIf="!isLoading && ddosIpProtections">
    <table class="table table-striped">
        <thead>
            <tr>
                <th i18n="@@bma_ddosipprotectionlist_site" class="text-center table-col-size-1">Site</th>
                <th i18n="@@bma_ddosipprotectionlist_subnet" class="table-col-size-2 text-center">Subnet</th>
                <th i18n="@@bma_ddosipprotectionlist_profile" class="table-col-size-2 text-center">Profile</th>
                <th i18n="@@bma_ddosipprotectionlist_sorce" class="table-col-size-2 text-center">Source</th>
                <th i18n="@@bma_ddosipprotectionlist_router">Router</th>
                <th i18n="@@bma_ddosipprotectionlist_switch">Switch</th>
            </tr>
        </thead>

        <tbody>
            <tr *ngIf="ddosIpProtections.wanguardProfileCaches.length === 0 && !error">
                <td i18n="@@bma_ddosipprotectionlist_noresult" colspan="99" class="text-center p-4">No DDoS IP protection found.</td>
            </tr>
            <tr *ngIf="error">
                <td i18n="@@bma_common_errorfetchingdata" colspan="99" class="text-center p-4 text-danger">Error fetching the data</td>
            </tr>
            <tr *ngFor="let ddosIpProtection of ddosIpProtections.wanguardProfileCaches">
                <td class="text-center text-nowrap">
                    <small class="text-monospace">{{ ddosIpProtection.site }}</small>
                </td>
                <td class="text-nowrap">
                    <small class="text-monospace selectable">{{ ddosIpProtection.subnet }}</small
                    ><br />
                    <small class="text-muted" *ngIf="ddosIpProtection.nextHop">
                        <span i18n="@@bma_ddosipprotectionlist_via">Via</span> <span class="text-monospace">{{ ddosIpProtection.nextHop }}</span>
                    </small>
                </td>
                <td class="text-center">
                    <small class="text-monospace">
                        {{ ddosIpProtection.profile }}
                    </small>
                </td>
                <td class="text-center text-nowrap">
                    <small class="text-monospace">{{ ddosIpProtection.source }}</small>
                </td>
                <td>
                    <a [routerLink]="['/networkDevices', ddosIpProtection.router]"> {{ ddosIpProtection.router }} </a><br />

                    <small *ngIf="ddosIpProtection.routerPort">
                        <span i18n="@@bma_ddosipprotectionlist_port" class="text-muted">port:</span>
                        {{ ddosIpProtection.routerPort }}
                    </small>
                </td>
                <td>
                    <a *ngIf="ddosIpProtection.switch" [routerLink]="['/networkDevices', ddosIpProtection.switch]"> {{ ddosIpProtection.switch }} </a><br />

                    <small *ngIf="ddosIpProtection.switchPort; else notAvailable">
                        <span i18n="@@bma_ddosprotectionlist_port" class="text-muted">port:</span>
                        {{ ddosIpProtection.switchPort }}
                    </small>
                </td>
            </tr>
        </tbody>
    </table>
    <ng-template #notAvailable>
        <span i18n="@@bma_common_notavailable" class="text-muted">Not Available</span>
    </ng-template>
</div>
<div *ngIf="!isLoading && ddosIpProtections">
    <app-pagination [totalCount]="ddosIpProtections._metadata.totalCount" [limit]="ddosIpProtections._metadata.limit" [offset]="ddosIpProtections._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
</div>
