import { ActivatedRoute, Router } from '@angular/router';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ModalService } from 'src/app/services/modal.service';
import { CommerceService } from 'src/app/services/commerce.service';
import { Component, OnInit } from '@angular/core';
import { CurrentUserService } from 'src/app/services/current-user.service';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { Title } from '@angular/platform-browser';
import { SiteService } from 'src/app/services/site.service';
import { DdosProfileChangeModalComponent } from 'src/app/ddos-profile/ddos-profile-change-modal/ddos-profile-change-modal.component';

@Component({
  selector: 'app-ddos-ip-protection-advanced-list',
  templateUrl: './ddos-ip-protection-advanced-list.component.html',
  styleUrls: ['./ddos-ip-protection-advanced-list.component.css'],
})
export class DdosIpProtectionAdvancedListComponent implements OnInit {
  ddosProfiles: any;
  filters: UntypedFormGroup;
  isEmployee: boolean;
  isLoading: boolean;
  dynamicDialogRef: DynamicDialogRef;
  sites: any;
  isChangeProfileInProgress: boolean;

  constructor(
    private activatedRoute: ActivatedRoute,
    private commerceService: CommerceService,
    private modalService: ModalService,
    private currentUserService: CurrentUserService,
    private formBuilder: UntypedFormBuilder,
    private httpClient: HttpClient,
    private router: Router,
    private siteService: SiteService,
    private titleService: Title
  ) {}

  ngOnInit(): void {
    this.titleService.setTitle($localize`:@@bma_ddosprofileslist_title:DDoS IP Protection` + '| Leaseweb');
    this.isEmployee = this.currentUserService.isEmployee();
    this.filters = this.formBuilder.group({
      limit: [null],
      offset: [null],
      site: [null],
      equipmentType: [null],
      detectionProfile: [null],
      customerId: [null],
      salesOrgId: [null],
    });

    this.sites = this.siteService.sites();
    this.activatedRoute.queryParams.subscribe((parameters) => this.onQueryParamsChanged(parameters));
  }

  onQueryParamsChanged(queryParams): void {
    this.filters.reset(queryParams);

    const params = this.sanitise(this.filters.getRawValue());

    if (this.isEmployee) {
      /*eslint-disable */
      const event = new Event('update_product_navbar');
      event['customerId'] = this.filters.get('customerId').value;
      event['salesOrgId'] = this.filters.get('salesOrgId').value;
      event['country'] = this.siteService.getCountry(this.filters.get('salesOrgId').value);
      window.dispatchEvent(event);
      /*eslint-enable */
    }

    this.isLoading = true;

    this.httpClient.get<any>('/_/internal/nseapi/v2/ddosProfiles', { params }).subscribe(
      (data: any) => {
        this.ddosProfiles = data;
        this.isLoading = false;
      },
      (error: any) => {
        this.isLoading = false;
      }
    );
  }

  changeRouterParams(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: this.sanitise(this.filters.getRawValue()),
    });
  }

  submitFilters(): void {
    this.filters.patchValue({ offset: 0 });
    this.changeRouterParams();
  }

  onPageChange(event): void {
    this.filters.patchValue({ offset: event.offset, limit: event.limit });
    this.changeRouterParams();
  }

  clearFilters(): void {
    if (this.isEmployee) {
      this.filters.get('site').reset();
      this.filters.get('equipmentType').reset();
      this.filters.get('detectionProfile').reset();

      this.changeRouterParams();
    } else {
      this.filters.reset();
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
      });
    }
  }

  sanitise(obj: any): any {
    return Object.entries(obj).reduce((acc, [key, val]) => {
      if (val && val !== 'null') {
        acc[key] = val;
      }
      return acc;
    }, {});
  }

  openDdosProfileChangeModal(equipmentId, equipmentType, ipAddress) {
    this.isChangeProfileInProgress = true;
    this.httpClient.get<any>(`/_/internal/dedicatedserverapi/v2/${equipmentType}/${equipmentId}`).subscribe({
      next: (equipment: any) => {
        this.httpClient
          .get<any>(`/_/internal/dedicatedserverapi/v2/${equipmentType}/${equipmentId}/ips/${ipAddress}`)
          .subscribe({
            next: (ipDetails: any) => {
              this.isChangeProfileInProgress = false;
              this.dynamicDialogRef = this.modalService.show(DdosProfileChangeModalComponent, {
                equipment,
                equipmentType,
                ipDetails,
              });

              this.dynamicDialogRef.onClose.subscribe((data) => {
                if (data?.ddosProfileChanged === true) {
                  location.reload();
                }
              });
            },
          });
      },
    });
  }
}
