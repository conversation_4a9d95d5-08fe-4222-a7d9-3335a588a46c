<div class="row">
    <div class="col">
        <app-customer-aware-header caption="DDoS IP Protection" [customerId]="filters.get('customerId').value" [salesOrgId]="filters.get('salesOrgId').value" [isEmployee]="isEmployee"></app-customer-aware-header>
    </div>
</div>
<div>
    <form [formGroup]="filters" (ngSubmit)="submitFilters()" role="form">
        <div class="row mb-5">
            <div class="col-4">
                <select i18n-label="@@bma_common_site" formControlName="site" label="Site" class="form-control">
                    <option i18n="@@bma_common_site" value="null">Site</option>
                    <option *ngFor="let site of sites" value="{{ site }}">{{ site }}</option>
                </select>
            </div>

            <div class="col-4">
                <select i18n-label="@@bma_common_profile" formControlName="detectionProfile" label="Profile" class="form-control">
                    <option i18n="@@bma_common_profile" value="null">Profile</option>
                    <option value="ADVANCED_DEFAULT" i18n="@@bma_common_advanced_default">Advanced Default</option>
                    <option value="ADVANCED_LOW_UDP" i18n="@@bma_common_advanced_lowudp">Advanced Low UDP</option>
                    <option value="ADVANCED_MED_UDP" i18n="@@bma_common_advanced_mediumudp">Advanced Medium UDP</option>
                </select>
            </div>

            <div class="col-4">
                <select i18n-label="@@bma_common_equipmentType" formControlName="equipmentType" label="Equipment Type" class="form-control">
                    <option i18n="@@bma_common_equipmentType" value="null">Equipment Type</option>
                    <option value="DEDICATED_SERVER" i18n="@@bma_common_dedicatedserver">Dedicated Server</option>
                    <option value="COLOCATION" i18n="@@bma_common_colocation">Colocation</option>
                    <option value="PRIVATE_RACK" i18n="@@bma_common_privaterack">Private Rack</option>
                    <option value="CLOUD" i18n="@@bma_common_cloud">Cloud</option>
                </select>
            </div>
            <input type="hidden" *ngIf="isEmployee" formControlName="customerId" value="filters.get('customerId').value" />
            <input type="hidden" *ngIf="isEmployee" formControlName="salesOrgId" value="filters.get('salesOrgId').value" />
        </div>

        <div class="row mb-3">
            <div class="col text-right">
                <button i18n-label="@@bma_common_reset" pButton (click)="clearFilters()" label="Reset" icon="pi pi-refresh" type="button" class="p-button-secondary mr-2"></button>
                <button i18n-label="@@bma_common_search" pButton [disabled]="isLoading" label="Search" icon="pi pi-search"></button>
            </div>
        </div>
    </form>
</div>

<div class="row mt-4 mb-4">
    <div class="col">
        <ul class="list-unstyled">
            <li class="font-weight-bold" i18n="@@bma_ddosipprotectionadvancedlist_tabletext">Standard DDoS protection is active for all your IPs. IPs featured on this page have been upgraded with Advanced DDoS protection as part of your plan.</li>
        </ul>
    </div>
</div>

<app-loader *ngIf="isLoading"></app-loader>

<div *ngIf="!isLoading && ddosProfiles">
    <div class="p-grid p-col-12 bg-white p-mb-2 p-py-4 justify-content-center" *ngIf="ddosProfiles.ddosProfiles.length === 0">
        <span *ngIf="isEmployee;else noDdosProfiles" i18n="@@bma_ddosipprotectionadvancedlist_noresultemployee">No DDoS Profiles found.</span>
        <ng-template #noDdosProfiles>
            <span i18n="@@bma_ddosipprotectionadvancedlist_noresultcustomer">You don't have DDoS Profiles</span>
        </ng-template>
    </div>
    <p-accordion [multiple]="true">
        <p-accordionTab *ngFor="let ddosProfile of ddosProfiles.ddosProfiles">
            <ng-template pTemplate="header">
                <div class="row col-sm-12 align-items-center">
                    <div class="col-sm-4">
                        <span class="h5">{{ ddosProfile.ipAddress }}</span>
                    </div>
                    <div class="col-sm-4">
                        <ng-container [ngSwitch]="ddosProfile.equipmentType">
                            <span class="h5" *ngSwitchCase="'DEDICATED_SERVER'" i18n="@@bma_common_dedicatedserver">Dedicated Server</span>
                            <span class="h5" *ngSwitchCase="'COLOCATION'" i18n="@@bma_common_colocation">Colocation</span>
                            <span class="h5" *ngSwitchCase="'PRIVATE_RACK'" i18n="@@bma_common_privaterack">Private Rack</span>
                            <span class="h5" *ngSwitchCase="'CLOUD'" i18n="@@bma_common_cloud">Cloud</span>
                            <span class="h5" *ngSwitchDefault i18n="@@bma_common_notavailable">Not Available</span>
                        </ng-container>
                    </div>
                    <div class="col-sm-4">
                        <span class="h5">
                            <ng-container [ngSwitch]="ddosProfile.detectionProfile">
                                <span *ngSwitchCase="'ADVANCED_DEFAULT'" i18n="@@bma_common_advanced_default">Advanced Default</span>
                                <span *ngSwitchCase="'ADVANCED_LOW_UDP'" i18n="@@bma_common_advanced_lowudp">Advanced Low UDP</span>
                                <span *ngSwitchCase="'ADVANCED_MED_UDP'" i18n="@@bma_common_advanced_mediumudp">Advanced Medium UDP</span>
                                <span *ngSwitchCase="'STANDARD_DEFAULT'" i18n="@@bma_common_standarddefault">Standard Default</span>
                                <span *ngSwitchDefault i18n="@@bma_common_notavailable">Not Available</span>
                            </ng-container>
                        </span>
                    </div>
                </div>
            </ng-template>
            <ng-template pTemplate="content" class="p-p-0">
                <div class="p-grid border-bottom">
                    <div class="p-col-12 p-md-10 border-right">
                        <div class="p-col-12 p-d-flex p-flex-col p-pb-0">
                            <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-2">
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_ipaddress">IP Address</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">{{ ddosProfile.ipAddress }}</div>
                                </div>
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_equipmenttype">Equipment Type</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <ng-container [ngSwitch]="ddosProfile.equipmentType">
                                            <span *ngSwitchCase="DEDICATED_SERVER" i18n="@@bma_common_dedicatedserver">Dedicated Server</span>
                                            <span *ngSwitchCase="COLOCATION" i18n="@@bma_common_colocation">Colocation</span>
                                            <span *ngSwitchCase="PRIVATE_RACK" i18n="@@bma_common_privaterack">Private Rack</span>
                                            <span *ngSwitchCase="CLOUD" i18n="@@bma_common_cloud">Cloud</span>
                                            <span *ngSwitchDefault i18n="@@bma_common_notavailable">Not Available</span>
                                        </ng-container>
                                    </div>
                                </div>
                            </div>
                            <div class="p-col-12 p-md-6 p-py-0 p-pl-6 p-pr-5">
                                <div class="p-grid grid-row">
                                    <div class="p-col-4 grid-content grid-content-border"><strong i18n="@@bma_common_profile">Profile</strong></div>
                                    <div class="p-col-8 grid-content grid-content-border">
                                        <ng-container [ngSwitch]="ddosProfile.detectionProfile">
                                            <span *ngSwitchCase="'ADVANCED_DEFAULT'" i18n="@@bma_common_default">Default</span>
                                            <span *ngSwitchCase="'ADVANCED_LOW_UDP'" i18n="@@bma_common_lowudp">Low UDP</span>
                                            <span *ngSwitchCase="'ADVANCED_MED_UDP'" i18n="@@bma_common_mediumudp">Medium UDP</span>
                                            <span *ngSwitchDefault i18n="@@bma_common_notavailable">Not Available</span>
                                        </ng-container>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-col-12 p-md-2 quick-actions">
                        <h5 i18n="@@bma_common_quickactions" class="p-2">Quick Actions</h5>
                        <div class="p-grid p-flex-column align-items-start">
                            <a *ngIf="ddosProfile.equipmentType == 'DEDICATED_SERVER'" pButton i18n-label="@@bma_ddosipprotectionadvancedlist_changeprofile" (click)="openDdosProfileChangeModal(ddosProfile.equipmentId,'servers',ddosProfile.ipAddress)" label="Change DDoS profile" class="p-button-link" [loading]="isChangeProfileInProgress"></a>
                            <a *ngIf="ddosProfile.equipmentType == 'COLOCATION'" pButton i18n-label="@@bma_ddosipprotectionadvancedlist_changeprofile" (click)="openDdosProfileChangeModal(ddosProfile.equipmentId,'colocations',ddosProfile.ipAddress)" label="Change DDoS profile" class="p-button-link" [loading]="isChangeProfileInProgress"></a>
                            <a *ngIf="ddosProfile.equipmentType == 'PRIVATE_RACK'" pButton i18n-label="@@bma_ddosipprotectionadvancedlist_changeprofile" (click)="openDdosProfileChangeModal(ddosProfile.equipmentId,'privateRacks',ddosProfile.ipAddress)" label="Change DDoS profile" class="p-button-link" [loading]="isChangeProfileInProgress"></a>
                            <app-ddos-mitigation-report [ipAddress]="ddosProfile.ipAddress" [isDdosIpProtectionAdvancedListPage]="true"></app-ddos-mitigation-report>
                        </div>
                    </div>
                </div>
                <div class="p-grid p-pr-5 p-pt-3">
                    <a *ngIf="ddosProfile.equipmentType == 'DEDICATED_SERVER'" i18n-label="@@bma_common_manage" pButton [routerLink]="['/servers', ddosProfile.equipmentId]" class="p-button-primary ml-auto" label="Manage"></a>
                    <a *ngIf="ddosProfile.equipmentType == 'COLOCATION'" i18n-label="@@bma_common_manage" pButton [routerLink]="['/colocations', ddosProfile.equipmentId]" class="p-button-primary ml-auto" label="Manage"></a>
                    <a *ngIf="ddosProfile.equipmentType == 'PRIVATE_RACK'" i18n-label="@@bma_common_manage" pButton [routerLink]="['/privateRacks', ddosProfile.equipmentId]" class="p-button-primary ml-auto" label="Manage"></a>
                    <a *ngIf="ddosProfile.equipmentType == 'CLOUD' && !isEmployee" i18n-label="@@bma_common_manage" pButton href="clouddash/customer/resource_pool/view/{{ddosProfile.equipmentId}}" class="p-button-primary ml-auto" label="Manage"></a>
                    <button [disabled]="true" *ngIf="ddosProfile.equipmentType == 'CLOUD' && isEmployee" i18n-label="@@bma_common_manage" pButton class="p-button-primary ml-auto" label="Manage"></button>
                </div>
            </ng-template>
        </p-accordionTab>
    </p-accordion>
</div>

<div class="row mb-3" *ngIf="!isLoading && ddosProfiles">
    <div class="col-12">
        <app-pagination [totalCount]="ddosProfiles._metadata.totalCount" [limit]="ddosProfiles._metadata.limit" [offset]="ddosProfiles._metadata.offset" (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>
