import { Injectable } from '@angular/core';
import { HttpEvent, HttpInterceptor, HttpHandler, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';
import { CurrentUserService } from 'src/app/services/current-user.service';

@Injectable()
export class PrefixInterceptor implements HttpInterceptor {
  constructor(private currentUserService: CurrentUserService) {}

  intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
    let prefix = '';

    if (this.currentUserService.isEmployee()) {
      prefix = '/emp';
    } else {
      prefix = this.currentUserService.isFiberring() ? '/connectivity' : '/bare-metals';
    }
    let prefixedRequest;
    if (request.url.includes('mercure')) {
      prefixedRequest = request.clone({
        url: request.url,
      });
    } else {
      prefixedRequest = request.clone({
        url: prefix + request.url,
      });
    }
    return next.handle(prefixedRequest);
  }
}
