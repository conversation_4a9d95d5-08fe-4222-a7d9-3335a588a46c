{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ng": {"i18n": {"sourceLocale": "en", "locales": {"fr": "src/locale/messages_fr.xlf"}}, "projectType": "application", "schematics": {"@schematics/angular:component": {"skipTests": true}, "@schematics/angular:class": {"skipTests": true}, "@schematics/angular:directive": {"skipTests": true}, "@schematics/angular:guard": {"skipTests": true}, "@schematics/angular:interceptor": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}, "@schematics/angular:resolver": {"skipTests": true}, "@schematics/angular:service": {"skipTests": true}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"localize": true, "outputPath": "../web/ng", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["src/polyfills.ts"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.css"], "scripts": ["./node_modules/chart.js/dist/chart.js"]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "none", "namedChunks": true, "sourceMap": false, "extractLicenses": false, "budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "4mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "20kb"}]}, "development": {"localize": false, "outputPath": {"base": "../web/ng/browser/en", "browser": ""}, "optimization": false, "outputHashing": "none", "namedChunks": true, "sourceMap": false, "extractLicenses": false}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "ng:build:production"}, "development": {"browserTarget": "ng:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "ng:build"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"analytics": false, "warnings": {"versionMismatch": false}}, "schematics": {"@angular-eslint/schematics:application": {"setParserOptionsProject": true}, "@angular-eslint/schematics:library": {"setParserOptionsProject": true}}}