{"name": "leaseweb/emp-dashboard", "description": "emp-dashboard", "license": "proprietary", "type": "project", "autoload": {"psr-4": {"App\\": "src/"}, "classmap": ["app/AppKernel.php", "app/AppCache.php"]}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}, "files": ["vendor/symfony/symfony/src/Symfony/Component/VarDumper/Resources/functions/dump.php"]}, "repositories": [{"type": "composer", "url": "https://artifactory.devleaseweb.com/artifactory/api/composer/composer"}], "require": {"php": ">=8.1.2", "guzzlehttp/guzzle": "^6.5.8", "leaseweb/authenticationbundle": "^2.2.0", "sensio/framework-extra-bundle": "^3.0.29", "symfony/assetic-bundle": "~2.8.2", "symfony/monolog-bundle": "~2.12.1", "symfony/swiftmailer-bundle": "~2.6.7", "symfony/symfony": "3.4.49", "twig/extensions": "~1.5.4"}, "scripts": {"post-install-cmd": ["mkdir -p var/logs var/cache web", "test -f web/app.php || cp app/app.php.dist web/app.php", "bin/console assets:install web --symlink --relative"], "post-update-cmd": ["mkdir -p var/logs var/cache web", "test -f web/app.php || cp app/app.php.dist web/app.php", "bin/console assets:install web --symlink --relative"]}, "config": {"bin-dir": "bin", "platform": {"php": "8.1.2"}, "sort-packages": true}, "archive": {"exclude": ["*", ".*", "!/app", "!/bin", "!/data", "!/ng/dist", "!/src", "!/templates", "!/var/cache", "!/var/logs", "!/vendor", "!/web", "/app/config/parameters.yml", "/var/cache/*", "/var/logs/*", "Test", "test", "Tests", "tests"]}, "minimum-stability": "stable", "extra": {"symfony-app-dir": "app", "symfony-bin-dir": "bin", "symfony-var-dir": "var", "symfony-web-dir": "web", "symfony-tests-dir": "tests", "symfony-assets-install": "relative"}}