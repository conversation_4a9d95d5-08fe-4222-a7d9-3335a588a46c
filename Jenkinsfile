#!groovy

lswci([node: "docker", mattermost: "bare-metal-cicd", stagingBranch: "master"]) {
    name = env.JOB_NAME.toLowerCase().split("/")[0..1].join("-docker/")
    branch = env.CHANGE_BRANCH ? env.CHANGE_BRANCH.replace("/", "-") : env.BRANCH_NAME.replace("/", "-")

    docker.withRegistry("https://artifactory.devleaseweb.com", "svc_jenkins") {
        stage("Build Container") {
            image = docker.build("${name}:${branch}-dev", "--target phpdev .")
        }

        image.inside {
            stage("Composer") {
                sh "make composer";
            }

            stage("Lint") {
                sh "cd ng && HOME=. npm install";
                sh "make lint";
            }

        }

        stage("Push Container") {
            docker.build("${name}:${branch}").push()
        }
    }

    stage ("Sync translations") {
        if (env.BR<PERSON>CH_NAME == "master") {
            translationSync([sourceFolder: "ng/src/locale"])
        }
    }
}
