---
imports:
  - { resource: config.yml }

framework:
  router:
    strict_requirements: true
  profiler:
    only_exceptions: false

web_profiler:
  toolbar: true
  intercept_redirects: false

assetic:
  use_controller: true

services:
  twig.extension.debug:
    class: Twig\Extension\DebugExtension
    tags: [{ name: 'twig.extension' }]

lease_web_auth:
  client_id: "%leaseweb_auth_client_id_emp%"
  secret: "%leaseweb_auth_secret_emp%"
  provider_url: "%leaseweb_auth_provider_url_emp%"
  exception_listener_enabled: true
