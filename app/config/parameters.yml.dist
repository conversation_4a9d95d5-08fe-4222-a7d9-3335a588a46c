---
parameters:
  mailer_transport: smtp
  mailer_host: 127.0.0.1
  mailer_user: ~
  mailer_password: ~

  locale: en
  secret: ThisTokenIsNotSoSecretChangeIt

  # rest api base urls
  lc2_endpoint: ~
  es_base_url: ~
  es_username: ~
  es_password: ~

  # For rendering external links
  profile_baseurl: https://profile.leaseweb.com

  leaseweb_auth_client_id_emp: ThisClientIDIsNotSoSecretChangeIt
  leaseweb_auth_secret_emp: ThisSecretIsNotSoSecretChangeIt
  leaseweb_auth_provider_url_emp: https://auth.example.com/

  leaseweb_auth_client_id_cp: ThisClientIDIsNotSoSecretChangeIt
  leaseweb_auth_secret_cp: ThisSecretIsNotSoSecretChangeIt
  leaseweb_auth_provider_url_cp: https://auth.example.com/

  leaseweb_auth_client_id_fbs: ThisClientIDIsNotSoSecretChangeIt
  leaseweb_auth_secret_fbs: ThisSecretIsNotSoSecretChangeIt
  leaseweb_auth_provider_url_fbs: https://auth.example.com/

  commerce_base_url: https://commerce-storefront-ws-dev.devleaseweb.com/secure

  cp_notification_hub_url: "https://secure.staging.devleaseweb.com/.well-known/mercure"
  cp_notification_hub_topic_url: "https://secure.staging.devleaseweb.com/floating-ip"
  cp_notification_token_url: "https://secure.staging.devleaseweb.com/bare-metals"

  emp_notification_hub_url: "https://emp.staging.devleaseweb.com/.well-known/mercure"
  emp_notification_hub_topic_url: "https://emp.staging.devleaseweb.com/bare-metals"
  emp_notification_token_url: "https://emp.staging.devleaseweb.com/emp"