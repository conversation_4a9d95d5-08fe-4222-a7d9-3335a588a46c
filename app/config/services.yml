parameters:
  assets_version: "latest"
  customerportal_assets_url: "https://assets.leaseweb.com"
  cp_css_url_v3: "https://assets.leaseweb.com/3.0.0/styles/css/lsw-light-styles.css"
  cp_vendor_js_url_v3: "https://assets.leaseweb.com/3.0.0/styles/js/cp-vendor.js"
  cp_vendor_head_js_url_v3: "https://assets.leaseweb.com/3.0.0/styles/js/cp-vendor-head.js"
  cp_elements_base_url_v3: "https://assets.leaseweb.com/3.0.0/web-components"
  cp_elements_file_name_v3: "customer-portal-elements.js"

services:
  leaseweb_auth.util.json_mapper:
    class: apimatic\jsonmapper\JsonMapper

  leaseweb.ajax_authentication_listener:
      class: App\Listener\AjaxAuthenticationListener
      arguments:
        - "@security.token_storage"
        - "%kernel.debug%"
      tags:
          - { name: kernel.event_listener, event: kernel.response, priority: 0}

  leaseweb_auth.security.user_role_filter:
    class: App\Services\EMPUserRoleFilter
    arguments: ["@session"]

  leaseweb.listener.customer:
      class: App\Listener\CustomerListener
      arguments:
          - "@security.token_storage"
          - "@twig"
      tags:
          - { name: kernel.event_listener, event: kernel.request }

  leaseweb.listener.profiler:
      class: App\Listener\ProfilerListener
      arguments:
        - "@logger"
      tags:
        - { name: kernel.event_listener, event: kernel.response }

  leaseweb.service.customer:
      class: App\Services\CustomerService
      arguments:
        - "@leaseweb.api"
        - "%kernel.cache_dir%/customers"

  leaseweb.service.user_roles:
      class: App\Services\UserRolesService
      arguments:
        - "@security.authorization_checker"
        - "@security.token_storage"
        - "@session"

  leaseweb.monolog.processor.request:
      class: App\Monolog\RequestProcessor
      arguments:
        - "@request_stack"
        - "@security.token_storage"
      tags:
        - { name: monolog.processor, method: processRecord }
