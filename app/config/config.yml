---
imports:
  - { resource: security.yml }
  - { resource: services.yml }
  - { resource: twig_extensions.yml }
  - { resource: api_clients.yml }
  - { resource: parameters.yml }
  - { resource: blacklists.yml }

framework:
  secret: "%secret%"
  router:
    resource: "%kernel.root_dir%/config/routing.yml"
    strict_requirements: ~
  form: ~
  csrf_protection: ~
  validation:
    enable_annotations: true
  templating:
    engines: ['twig']
  default_locale: "%locale%"
  trusted_proxies: ~
  session:
    handler_id: ~
    gc_probability: 0
  fragments: ~
  http_method_override: true
  translator: { fallbacks: ["%locale%"] }

# Twig Configuration
twig:
  debug: "%kernel.debug%"
  strict_variables: "%kernel.debug%"
  globals:
    assets_version: "%assets_version%"
    customerportal_assets_url: "%customerportal_assets_url%"
    cp_css_url_v3: "%cp_css_url_v3%"
    cp_vendor_js_url_v3: "%cp_vendor_js_url_v3%"
    cp_vendor_head_js_url_v3: "%cp_vendor_head_js_url_v3%"
    cp_elements_base_url_v3: "%cp_elements_base_url_v3%"
    cp_elements_file_name_v3: "%cp_elements_file_name_v3%"
    profile_baseurl: "%profile_baseurl%"
    commerce_base_url: "%commerce_base_url%"
    cp_notification_hub_url: "%cp_notification_hub_url%"
    cp_notification_hub_topic_url: "%cp_notification_hub_topic_url%"
    cp_notification_token_url: "%cp_notification_token_url%"
    emp_notification_hub_url: "%emp_notification_hub_url%"
    emp_notification_hub_topic_url: "%emp_notification_hub_topic_url%"
    emp_notification_token_url: "%emp_notification_token_url%"
  form_themes:
    - 'bootstrap_3_layout.html.twig'
  exception_controller:  App:Exception:showException
  paths:
    '%kernel.root_dir%/../templates': ~

# Assetic Configuration
assetic:
  debug: "%kernel.debug%"
  use_controller: false
  bundles: []
  filters:
    cssrewrite: ~

# Swiftmailer Configuration
swiftmailer:
  transport: "%mailer_transport%"
  host: "%mailer_host%"
  username: "%mailer_user%"
  password: "%mailer_password%"
  spool: { type: memory }

# Monolog configuration
monolog:
  handlers:
    main:
      type: stream
      level: info
      path: "%kernel.logs_dir%/%kernel.environment%.log"
      formatter: monolog.formatter.json
