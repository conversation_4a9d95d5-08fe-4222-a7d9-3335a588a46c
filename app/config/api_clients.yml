services:
    leaseweb.api.plugin.profiler:
        class: App\Guzzle\ProfilerPlugin
        arguments:
          - "@logger"

    leaseweb.api.plugin.bearer_token:
        class: App\Guzzle\BearerTokenPlugin
        arguments:
          - "@security.token_storage"

    leaseweb.api.handler_stack:
        class: GuzzleHttp\HandlerStack
        factory: ['GuzzleHttp\HandlerStack', 'create']
        calls:
            - [push, ["@leaseweb.api.plugin.profiler", "profiler"]]
            - [push, ["@leaseweb.api.plugin.bearer_token", "bearer_token"]]

    leaseweb.api:
        class: GuzzleHttp\Client
        arguments:
            - base_uri: "%lc2_endpoint%"
              headers:
                Content-Type: "application/json"
              handler: "@leaseweb.api.handler_stack"

    # We do not have valid hostname for elastic search in production
    # Hence the verify: false
    emp.service.es_api:
        class: GuzzleHttp\Client
        arguments:
            - base_uri: "%es_base_url%"
              auth: ["%es_username%", "%es_password%", "Basic"]
              verify: false
              headers:
                Content-Type: "application/json"
