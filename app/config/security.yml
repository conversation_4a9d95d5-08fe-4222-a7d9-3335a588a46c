security:
    providers:
        leaseweb_oauth:
            id: leaseweb_auth.security.user_provider

    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false

        # All leaseweb customer portal pages are protected by this firewall
        leaseweb_portal:
            pattern: ^/bare-metals
            context: leaseweb_auth_context
            provider: leaseweb_oauth
            leaseweb_auth: ~
            logout:
                success_handler: leaseweb_auth.logout.success_handler
                path: "/bare-metals/logout"

        # All fiberring customer portal pages are protected by this firewall
        fiberring_portal:
            pattern: ^/connectivity
            context: leaseweb_auth_context
            provider: leaseweb_oauth
            leaseweb_auth: ~
            logout:
                success_handler: leaseweb_auth.logout.success_handler
                path: "/connectivity/logout"

        # All employee portal pages are protected by this firewall
        employee_dashboard:
            pattern: ^/emp
            context: leaseweb_auth_context
            provider: leaseweb_oauth
            leaseweb_auth: ~
            logout:
                success_handler: leaseweb_auth.logout.success_handler
                path: "/emp/logout"

    access_control:
        - path: ^/connectivity/lswAuth/me
          allow_if: true
        - path: ^/connectivity
          roles:
            - ROLE_CUSTOMER_TECH
        - path: ^/bare-metals/lswAuth/me
          allow_if: true
        - path: ^/bare-metals
          roles:
            - ROLE_CUSTOMER_TECH
        # Keep only "ROLE_EMPLOYEE" once devp deploys the change to prod
        - path: ^/emp
          roles:
            - ROLE_FUNC_EMPLOYEE
            - ROLE_FUNC_SHIFT_LEADER
            - ROLE_FUNC_TEAM_MANAGER
            - ROLE_FUNC_MANAGER
            - ROLE_EMPLOYEE
