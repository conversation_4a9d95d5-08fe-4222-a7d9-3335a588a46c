---
imports:
  - { resource: config_dev.yml }

parameters:
  lc2_endpoint: 'http://localhost/'

framework:
  test: ~
  session:
    storage_id: session.storage.mock_file
  profiler:
    collect: false

web_profiler:
  toolbar: false
  intercept_redirects: false

swiftmailer:
  disable_delivery: true

lease_web_auth:
  client_id: "%leaseweb_auth_client_id_emp%"
  secret: "%leaseweb_auth_secret_emp%"
  provider_url: "%leaseweb_auth_provider_url_emp%"
  exception_listener_enabled: true
