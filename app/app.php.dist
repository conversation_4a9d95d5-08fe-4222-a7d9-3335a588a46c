<?php

// Make sure url's ending with a trailing slash match the @Route without a trailing slash
$_SERVER['REQUEST_URI'] = preg_replace('#/$|/(\?)#', '\1', $_SERVER['REQUEST_URI'], 1);

if (0 === strpos($_SERVER['REQUEST_URI'], '/bare-metals')) {
    $env = 'prod_cp';
} elseif (0 === strpos($_SERVER['REQUEST_URI'], '/emp')) {
    $env = 'prod_emp';
} elseif (0 === strpos($_SERVER['REQUEST_URI'], '/connectivity')) {
    $env = 'prod_fbs';
} else {
    die('Unknown environment');
}

use Symfony\Component\HttpFoundation\Request;

require __DIR__.'/../vendor/autoload.php';

$kernel = new AppKernel($env, false);
$request = Request::createFromGlobals();
$response = $kernel->handle($request);
$response->send();
$kernel->terminate($request, $response);
