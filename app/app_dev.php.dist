<?php

// This if condition allow the build in php webserver to serve static files
if (is_file(__DIR__."/".$_SERVER["PHP_SELF"])) {
    return false;
}

// Make sure url's ending with a trailing slash match the @Route without a trailing slash
$_SERVER['REQUEST_URI'] = preg_replace('#/$|/(\?)#', '\1', $_SERVER['REQUEST_URI'], 1);

if (0 === strpos($_SERVER['REQUEST_URI'], '/bare-metals')) {
    $env = 'dev_cp';
} elseif (0 === strpos($_SERVER['REQUEST_URI'], '/emp')) {
    $env = 'dev_emp';
} elseif (0 === strpos($_SERVER['REQUEST_URI'], '/connectivity')) {
    $env = 'dev_fbs';
} else {
    die('Unknown environment');
}

use Symfony\Component\Debug\Debug;
use Symfony\Component\HttpFoundation\Request;

require __DIR__.'/../vendor/autoload.php';
Debug::enable();

$kernel = new AppKernel($env, true);
$request = Request::createFromGlobals();
$response = $kernel->handle($request);
$response->send();
$kernel->terminate($request, $response);
