{% extends 'base.html.twig' %}

{% block title %} An Error Occurred {% endblock title %}

{% block body %}
<div class="alert alert-danger alert-dismissible" role="alert">
    <button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
    {{ errorMessage }}
</div>

<div class="row">
    <div class="col-md-12 col-xs-12">
        <h3>An Error Occurred</h3>
        <p>It seems something is not working as expected</p>
        <p>If you require further assistance, please:</p>
        <ul>
            <li>Check the <a href="https://wiki.ocom.com/display/KC/BMA+-+How+to+troubleshoot+common+Automation+errors">How to troubleshoot common Automation errors</a> Wiki first</li>
            <li>Reach out to the Bare Metal team on the <a href="https://mattermost.leaseweb.com/leaseweb/channels/bma-nwa-support">BMA / NWA Support channel on Mattermost</a></li>
            <li>Follow the <a href="https://wiki.ocom.com/display/KC/'How+to+file+an+incident+for+Bare+Metal+Product+Engineering+Team">How to file an incident for Bare Metal Product Engineering Team</a> guide on the Wiki if you need to report an incident</li>
        </ul>
    </div>
</div>

{% endblock %}
