FROM ubuntu:22.04 AS phpbase
ENV DEBIAN_FRONTEND=noninteractive
RUN set -ex && \
    apt-get update && \
    apt-get install --no-install-recommends --yes --quiet \
        ca-certificates \
        php8.1 \
        php8.1-bcmath \
        php8.1-cli \
        php8.1-common \
        php8.1-curl \
        php8.1-fpm \
        php8.1-intl \
        php8.1-mbstring \
        php8.1-opcache \
        php8.1-readline \
        php8.1-xml \
        php8.1-xmlrpc \
        php8.1-zip \
        rsync \
        && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*
WORKDIR /app

FROM phpbase AS phpdev
COPY --from=composer:2 /usr/bin/composer /usr/bin/composer
RUN set -ex && \
    apt-get update && \
    apt-get install --no-install-recommends --yes --quiet \
        curl \
        gnupg \
        && \
        curl -sL https://deb.nodesource.com/setup_24.x | bash - \
        && \
    apt-get install --no-install-recommends --yes --quiet \
        git \
        make \
        nodejs \
        php8.1-pcov \
        && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*
RUN npm install -g prettier
VOLUME /.composer

FROM phpdev AS phpbuilder
COPY --from=composer:2 /usr/bin/composer /usr/bin/composer
COPY composer.json composer.json
COPY composer.lock composer.lock
COPY app ./app
COPY bin ./bin
COPY ng ./ng
COPY src ./src
COPY templates ./templates
COPY web ./web
RUN cp /app/app/config/parameters.yml.dist /app/app/config/parameters.yml
RUN composer --no-cache install --optimize-autoloader --no-interaction --no-ansi --no-dev --no-progress --prefer-dist
RUN cd /app/ng && npm install
RUN cd /app/ng && node_modules/.bin/ng build
RUN rm -rf /app/ng

FROM phpbase
COPY fpm.conf /etc/php/8.1/fpm/pool.d/www.conf
COPY --from=phpbuilder /app/ /app/
RUN rm -rf /app/var/cache && mkdir -p /app/var/cache && \
    rm -rf /app/var/logs && mkdir -p /app/var/logs && \
    chown www-data:www-data /app/var/cache /app/var/logs
ENV SYMFONY_ENV=prod
EXPOSE 8000
CMD ["php-fpm8.1"]
