app/config/parameters.yml:
	cp app/config/parameters.yml.dist app/config/parameters.yml

.PHONY: composer
composer: app/config/parameters.yml
	composer install --no-progress --no-ansi --no-interaction --prefer-dist

.PHONY: cc
cc:
	rm -rf var/cache/*

.PHONY: clear
clear: cc
	rm -rf var/build/*
	rm -rf var/logs/*

.PHONY: lint
lint: cc lint-json lint-yaml lint-twig lint-php lint-composer lint-eol lint-spaces lint-tabs lint-assets lint-ng
	@echo All good.

.PHONY: lint-tabs
lint-tabs:
	@echo "==> Validating literal tab characters in files:"
	@! git ls-files app bin src templates | xargs grep --files-with-matches --recursive --exclude Makefile '	' || ( echo '[ERROR] Above files have literal tab characters' && exit 1 )
	@echo All files use spaces

.PHONY: lint-eol
lint-eol:
	@echo "==> Validating unix style line endings of files:"
	@! git ls-files app bin src templates | xargs grep --files-with-matches --recursive --perl-regexp "\r" || ( echo '[ERROR] Above files have CRLF line endings' && exit 1 )
	@echo All files have valid line endings

.PHONY: lint-spaces
lint-spaces:
	@echo "==> Validating trailing whitespaces in files:"
	@! git ls-files app bin src templates | xargs grep --files-with-matches --recursive --extended-regexp ' +$$' || ( echo '[ERROR] Above files have trailing whitespace' && exit 1 )
	@echo No files have trailing whitespaces

.PHONY: lint-composer
lint-composer:
	@echo "==> Validating composer.json and composer.lock:"
	composer validate --strict --no-check-publish --no-check-all --no-interaction --no-ansi

.PHONY: lint-json
lint-json:
	@echo "==> Validating all json files:"
	@git ls-files '*.json' | php -R 'echo "$$argn\t\t";json_decode(file_get_contents($$argn));if(json_last_error()!==0){echo "<-- invalid\n";exit(1);}else{echo "\n";}'

.PHONY: lint-yaml
lint-yaml:
	@echo "==> Validating all yaml files:"
	@git ls-files '*.yml' | sed -r 's|/[^/]+$$||' | sort | uniq | while read folder; do echo -n "$$folder"; bin/console --no-debug --no-ansi --no-interaction --env=test lint:yaml "$$folder" || exit 1; done

.PHONY: lint-twig
lint-twig:
	@echo "==> Validating all twig files:"
	@git ls-files '*.twig' | sed -r 's|/[^/]+$$||' | sort | uniq | while read folder; do echo -n "$$folder"; bin/console --no-debug --no-ansi --no-interaction --env=test lint:twig "$$folder" || exit 1; done

.PHONY: lint-php
lint-php:
	@echo "==> Validating all php files:"
	for file in $(git ls-files '*.php'); do php -l $file; done

.PHONY: lint-ng
lint-ng: outdated-prettier
	$(MAKE) --directory=ng lint

.PHONY: lint-assets
lint-assets:
	if grep -R -E "asset\(.[^/]" templates; then echo "You must prefix assets with a '/'" && exit 1; fi

.PHONY: ci
ci: clear composer lint
	@echo "All quality checks passed"

.PHONY: watch
watch:
	$(MAKE) --directory=ng watch

.PHONY: locale
locale:
	$(MAKE) --directory=ng locale

.PHONY: outdated-prettier
outdated-prettier:
	@if npm outdated -g | grep 'prettier'; then echo "Prettier is outdated and must be updated to the newest version" && exit 1; fi

# The default make target is ci
.DEFAULT_GOAL := ci
