emp-dashboard
=============

cloning
-------

Download composer
For more information on how to install composer see: https://getcomposer.org/

Clone the repository:

    git clone ssh://****************/infra/emp-dashboard.git
    cd emp-dashboard

installation
------------

Create `parameters.yml` and add your database configuration:

    $ cp app/config/parameters.yml.dist app/config/parameters.yml

Download 3rd party dependencies:

    composer install

live replica credentials
------------------------

Include the live replica url's to your `/etc/hosts` file

    # /etc/hosts

    # live replica
    *********** api.leaseweb.com
    *********** auth.leaseweb.com

Ask DevP for live replica credentials for your application. It should redirect to no specific endpoint.
You will need access to the live replica mail box, and have FoxyProxy (or similar) enabled.

You will receive your credentials as below:

    +--------------+------------------------------------------------------------------+
    | Field        | Value                                                            |
    +--------------+------------------------------------------------------------------+
    | Id           | emp-dashboard.{YOUR_NAMESPACE}.devleaseweb.com                   |
    | Name         | emp-dashboard.{YOUR_NAMESPACE}.devleaseweb.com                   |
    | Secret       | {YOUR_AUTHENTICATION_HASH}                                       |
    | Grant types  | authorization_code,refresh_token,client_credentials              |
    | Roles        | ROLE_INTERNAL_CLIENT                                             |
    | Redirect URI | http://emp-dashboard.{YOUR_NAMESPACE}.devleaseweb.com/           |
    | Owner        | <NONE>                                                           |
    | Customer     | <NONE>                                                           |
    | Legal entity | <NONE>                                                           |
    | Enabled      | <TRUE>                                                           |
    +--------------+------------------------------------------------------------------+

Configure your credentials.

    # app/config/parameters.yml

    leaseweb.api.url:               http://api.leaseweb.com
    leaseweb.api.auth.client_id:    emp-dashboard.{YOUR_NAMESPACE}.devleaseweb.com
    leaseweb.api.auth.secret:       {YOUR_AUTHENTICATION_HASH}
    leaseweb.api.auth.provider_url: https://auth.leaseweb.com

    leaseweb_auth_client_id:        emp-dashboard.{YOUR_NAMESPACE}.devleaseweb.com
    leaseweb_auth_secret:           {YOUR_AUTHENTICATION_HASH}
    leaseweb_auth_provider_url:     https://auth.leaseweb.com/

    lc2_endpoint: https://api.leaseweb.com

FoxyProxy configuration
------------------------

If you are using Google Chrome, enable the FoxyProxy standard plugin

    https://chrome.google.com/webstore/detail/foxyproxy-standard/gcknhkkoolaabfmlnjonogaaifnjlfnp?hl=en-US

Add a new proxy, name it "live replica" (or similar) in the `General` tab, and configure the `Proxy Detail`
parameters as follow:

    [ ] Direct Internet connection (no proxy)
    [x] Manual Proxy Configuration
        [x] Socks Proxy ?
        [ ] Socks v4/4a
        [x] Socks v5
        [x] Save Login Credentials
            Username: <BLANK>
            Password: <BLANK>
            Password Again: <BLANK>
    [ ] Automatic proxy configuration url

Now, whenever you are working with Emp, remember to enable FoxyProxy, by clicking on its icon and selecting
`User proxy live replica for all urls`. If you defined a color for this proxy on the `Generals` tab while
configuring the proxy, you will notice that the extension's icon will switch to that color.
