.sidebarNavigation[bma-custom-navbar] {
	min-height:calc(100vh - 1.625rem);
	height:100%;
	padding:2.5rem 0rem 1.25rem;
	z-index:1020;
	max-width:16.6rem;
	width:16.6rem
}
.sidebarNavigation[bma-custom-navbar]   .nav-link {
	padding:.35rem .25rem
}
.sidebarNavigation[bma-custom-navbar]   .dropdown-item {
	width:100%;
	padding:.25rem 1.5rem;
	clear:both;
	font-weight:400;
	text-align:inherit;
	white-space:nowrap;
	border:0;
	line-height:1.5
}
.sidebarNavigation[bma-custom-navbar]   .navbar-toggler {
	padding:10px;
	font-size:.984375rem;
	line-height:1;
	background-color:transparent;
	border:1px solid transparent;
	border-radius:0
}
.sidebarNavigation[bma-custom-navbar]   .navbar-toggler {
	display:none
}
.sidebarNavigation[bma-custom-navbar]   a {
	text-decoration:none
}
.sidebarNavigation[bma-custom-navbar]   .dropdown-toggle:after {
	display:inline-block;
	margin-left:.255em;
	vertical-align:.255em;
	content:"";
	border-top:.3em solid;
	border-right:.3em solid transparent;
	border-bottom:0;
	border-left:.3em solid transparent
}
.sidebarNavigation[bma-custom-navbar]   .p-button.p-button-secondary {
	font-family:DIN Next LT Pro,Barlow,sans-serif;
	font-weight:500;
	color:#fff;
	background:#6c757d;
	border:1px solid #6c757d;
	line-height:1.5rem;
	padding:.5rem 1rem
}
.sidebarNavigation[bma-custom-navbar]   .p-button.p-button-secondary   .fa {
	margin-right:.5rem
}
.sidebarNavigation[bma-custom-navbar]   .p-button.p-button-secondary:focus {
	box-shadow:0 0
}
.sidebarNavigation[bma-custom-navbar] > .nav {
	padding:0;
	display:flex
}
.sidebarNavigation[bma-custom-navbar] > .nav > .nav-item {
	width:16.6rem;
	list-style:none
}
.sidebarNavigation[bma-custom-navbar] > .nav > .nav-item > a {
	font-size:1.09375rem
}
.sidebarNavigation[bma-custom-navbar] > .nav > .nav-item > a   .fa,
.sidebarNavigation[bma-custom-navbar] > .nav > .nav-item > a   .pi {
	font-size:1.5rem;
	line-height:2rem;
	color:#ddd;
	margin-right:.5rem
}
.sidebarNavigation[bma-custom-navbar] > .nav > .nav-item > a   .label {
	line-height:2;
	height:2rem;
	font-weight:500
}
.sidebarNavigation[bma-custom-navbar] > .nav > .nav-item   :after {
	position:absolute;
	right:0;
	top:1.25rem;
	transition:transform .3s
}
.sidebarNavigation[bma-custom-navbar] > .nav > .nav-item   .nav-link {
	margin-left:1.25rem;
	margin-right:1.25rem;
	border-bottom:1px solid #DDDDDD;
	padding-left:0;
	display:flex
}
.sidebarNavigation[bma-custom-navbar] > .nav > .nav-item   .nav-link.hide-border-bottom {
	border-bottom:1px solid #5685C4
}
.sidebarNavigation[bma-custom-navbar] > .nav > .nav-item   a {
	font-family:DIN Next LT Pro,Barlow,sans-serif;
	font-weight:500;
	color:#fff;
	position:relative;
	display:flex;
	text-decoration:none
}
.sidebarNavigation[bma-custom-navbar] > .nav > .nav-item   .dropdown-menu {
	position:relative;
	background:none;
	border:none;
	overflow:hidden;
	padding:0;
	top:auto;
	width:100%;
	font-size:.875rem;
	display:none;
	margin:.125rem 0 0;
	border-radius:unset
}
.sidebarNavigation[bma-custom-navbar] > .nav > .nav-item   .dropdown-menu   .dropdown-marker {
	margin-left:1.25rem;
	margin-right:1.25rem;
	border-bottom:1px solid #DDDDDD;
	margin-top:.35rem
}
.sidebarNavigation[bma-custom-navbar] > .nav > .nav-item   .dropdown-menu.show {
	display:block
}
.sidebarNavigation[bma-custom-navbar] > .nav > .nav-item.show   a:after {
	transform:rotate(180deg)
}
.sidebarNavigation[bma-custom-navbar] > .nav > .nav-item   .dropdown-item {
	padding-top:.35rem;
	white-space:nowrap
}
.sidebarNavigation[bma-custom-navbar] > .nav > .nav-item   .dropdown-item   div {
	padding-left:2rem
}
.sidebarNavigation[bma-custom-navbar] > .nav > .nav-item   .active-link {
	background-color:#f2f2f2;
	color:#5e85c7
}
.sidebarNavigation[bma-custom-navbar] > .nav > .nav-item   .active-link   i {
	color:#5e85c7
}
@media screen and (max-width: 1099px) {
	.navbar {
		transform:translate(-100%);
		height:auto;
		transition:transform .8s;
		width:16.6rem;
		max-width:calc(100% - 46px);
		min-width:0
	}
	.navbar   .navbar-toggler {
		position:absolute;
		left:100%;
		top:.25rem;
		cursor:pointer;
		background:#fff;
		display:block
	}
	.navbar   .navbar-toggler   .bar {
		cursor:pointer;
		width:1.75rem;
		height:.25rem;
		background:#aaa;
		display:flex;
		border-radius:.125rem;
		margin-bottom:.3rem
	}
	.navbar   .navbar-toggler:nth-of-type(3) {
		margin-bottom:0
	}
	.navbar   :focus {
		outline:none
	}
	.navbar.show {
		transform:translate(0)
	}
	.navbar.show   .navbar-toggler   .bar {
		background-color:#83b91e
	}
	.navbar.show   .navbar-toggler   :nth-of-type(1) {
		transform:translateY(225%) rotate(45deg)
	}
	.navbar.show   .navbar-toggler   :nth-of-type(2) {
		opacity:0
	}
	.navbar.show   .navbar-toggler   :nth-of-type(3) {
		transform:translateY(-225%) rotate(-45deg)
	}
}

.lsw-flags {
	object-fit: none;
	height: 15px;
	width: 20px;
	float: left;
	box-shadow: 0px 0px 1px 0px #888;
	background-repeat: no-repeat;
	background-color: #DBDBDB;
	object-position: 20px 0;
	margin-top: 5px;
	transform: scale(1.3);
}

.lsw-flags.flag-nl {
	height: 14px;
	object-position: -3752px 0px;
}

.lsw-flags.flag-de {
	height: 12px;
	object-position: -1269px 0px;
}

.lsw-flags.flag-ca {
	height: 10px;
	object-position: -834px 0px;
}

.lsw-flags.flag-us {
	height: 11px;
	object-position: -5263px 0px;
}

.lsw-flags.flag-sg {
	height: 14px;
	object-position: -4449px 0px;
}

.lsw-flags.flag-uk {
	height: 10px;
	object-position: -1775px 0px;
}

.lsw-flags.flag-cdn {
	font-size: x-small;
	width: 22px;
}

.lsw-flags.flag-jp {
	height: 14px;
	object-position: -2611px 0px;
}

.lsw-flags.flag-au {
	height: 10px;
	object-position: -286px 0px;
}

.lsw-flags.flag-hk {
	height: 14px;
	object-position: -2171px 0px;
}

.flag-network {
	height: 22px;
}

.d-none {
	display: none;
}