caption {
    caption-side: top !important;
}

.overflow-ellipsis {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

.payload-wrapper {
    position: relative;
}

a.btn-clipboard {
    background-color: darkgray;
    border: 1px solid #e1e1e8;
    border-radius: 0 0 0 4px;
    cursor: pointer;
    display: block;
    font-size: 12px;
    padding: 5px 8px;
    position: absolute;
    right: 1px;
    top: 1px;
    z-index: 10;
}

a.btn-clipboard .payload-save {
    color: #fff;
}

a.btn-clipboard:hover {
    background-color: #ececec;
    border-color: #ececec;
    color: #737373;
}

.job-list {
    border-bottom: none;
    border-collapse: separate;
}
.job-list .no-jobs {
    padding: 4em;
    border: none;
    text-align: center;
    background-color: #f2f2f2;
}
.job-list TR:not(:first-child) H3 {
    margin-top: 3em;
}
.job-list .job-status  {
    width: 7em;
    line-height: 2.1em;
    height: 2em;
    display: inline-block;
}
.job-list TR {
}
.job-list TR TD,
.job-list TR TH {
    line-height: 1.5em!important;
    vertical-align: middle!important;
}
#job-filter {}
#job-filter FIELDSET {}
#job-filter FIELDSET LEGEND {
    display: none;
}
#job-filter UL LI {
    margin-bottom: 0.5em;
}

/**
 * tasks table
 */

 .bmp-task-list {
}
.bmp-task-list THEAD,
.bmp-task-list TFOOT {
    background-color: #efefef;
}
.bmp-task-list TBODY TD,
.bmp-task-list TBODY TH {
    line-height: 2em !important;
    min-height: 2em !important;
}
.bmp-task-list TBODY TH {
    color: black;
    background-color: #eeeeee80;
    border-bottom: solid 1px black !important;
}
/* inprogress & pending tasks */
.bmp-task-list TR.bmp-task-inprogress {
    background-color: #eee;
}
.bmp-task-list TR.bmp-task-failed {
    background-color: #D9534F10;
}
.bmp-task-list TR.bmp-task-warning {
    background-color: #F0AD4E10;
}
.bmp-task-list TR.bmp-task-disabled,
.bmp-task-list TR.bmp-task-disabled TD,
.bmp-task-list TR.bmp-task-disabled TH {
    color: #BBB !important;
}
.bmp-task-list TR.bmp-task-disabled A {
    color: #00336650 !important;
}

.bmp-task-list TBODY .bmp-task-message CODE {
    display: block;
    padding: 0 !important;
    line-height: 1.5em;
    margin: 0.75em 0 !important;
}

.bmp-task-actor SPAN {
    font-size: 1.2em;
}
.bmp-task-status {
    font-size: 0.9em;
}
/* canceled, expired and warning tasks */
.bmp-task-status.bmp-task-status-canceled,
.bmp-task-status.bmp-task-status-expired,
.bmp-task-status.bmp-task-status-warning {
    color: #ffc107;
    font-weight: bold;
}
/* failed tasks */
.bmp-task-status.bmp-task-status-failed {
    color: #E03F79;
    font-weight: bold;
}

.bmp-task-list TFOOT UL.bmp-task-progress LI {
    margin-left: 1em !important;
    font-size: 16px;
}

/**
 * widgets
 */

.widget .widget-icon {
    font-size: 24px;
}

.widget .widget-contents {
    display: none;
    position: absolute;
    right: 0;
    background-color: white;
    z-index: 20;
    min-width: 16em;
    text-align: center;
    padding: 0.5em 1em;
    border: solid 1px #E81;
    border-right-width: 30px;
}

.redacted:before {
    content: '[ redacted ]';
}

/**
 * install-information
 */
.install-information {
    color: #0D3DCF !important;
}

.ipperformance-table{
    border: 1px solid #ddd;
}
