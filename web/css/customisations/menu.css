.bd-navbar {
	box-shadow: 0 0.188rem 0.313rem #CCCCCC;
	height: 3.3rem;
}

.bd-navbar .navbar-nav .nav-item {
	position: relative;
}

.bd-navbar .navbar-nav .nav-item > .fa {
	font-size: 2rem;
}

.bd-navbar .navbar-nav .nav-item .fa-account {
	color: #FFFFFF;
	background-color: #AAAAAA;
	border-radius: 9999rem;
	line-height: 1.25;
	min-width: 2.5rem;
}

.bd-navbar .navbar-nav .nav-item:after {
	content: none;
}

.bd-navbar .navbar-nav .nav-item .fa-notification {
	color: #AAAAAA;
}

.bd-navbar .navbar-nav .nav-item .account-menu {
	width: 19rem;
	box-shadow: 0 0.188rem 0.313rem #CCCCCC;
	border-radius: 0 0 0.25rem 0.25rem;
	margin-top: 0.5rem;
}

@media screen and (max-width: 1099px) {
  /* Navbar */
	.bd-navbar {
		padding-top: 0;
		padding-bottom: 0;
	}
}

#sideNavigation {
	min-height: calc(100vh - 1.625rem);
	height: 100%;
	padding: 2.5rem 0rem 1.25rem 0rem;
	max-width: 16.6rem;
	width: 16.6rem;
	display: block;
}

#sideNavigation > .nav > .nav-item {
	margin-top: -1px;
}

#sideNavigation > .nav > .nav-item > a {
	font-size: 1.09375rem;
}

#sideNavigation > .nav > .nav-item > a .fa {
	font-size: 1.75rem;
	color: #DDDDDD;
	line-height: 1.1;
}

#sideNavigation > .nav > .nav-item > a .label {
	display: inline-block;
	line-height: 2;
	padding-left: 0.35rem;
	height: 2rem;
}

#sideNavigation > .nav > .nav-item :after {
	position: absolute;
	right: 0;
	top: 1.25rem;
	transition: transform 0.3s;
}

#sideNavigation > .nav > .nav-item .nav-link {
	margin-left: 1.25rem;
	margin-right: 1.25rem;
	border-bottom: 1px solid #DDDDDD;
	padding-left: 0;
	display: flex;
}

#sideNavigation > .nav > .nav-item .nav-link.hide-border-bottom {
	border-bottom: 1px solid #663398;
}

#sideNavigation > .nav > .nav-item a {
	font-family: "Montserrat", Open Sans, sans-serif;
	color: #FFFFFF;
	position: relative;
	display: flex;
}

#sideNavigation > .nav > .nav-item .dropdown-menu {
	position: relative;
	background: none;
	border: none;
	overflow: hidden;
	padding: 0;
	top: auto;
	width: 100%;
	border-radius: unset;
}

#sideNavigation > .nav > .nav-item .dropdown-menu .dropdown-marker {
	margin-left: 1.25rem;
	margin-right: 1.25rem;
	border-bottom: 1px solid #DDDDDD;
	margin-top: 0.35rem;
}

#sideNavigation > .nav > .nav-item.show a:after {
	transform: rotate(180deg);
}

#sideNavigation > .nav > .nav-item .dropdown-item {
	padding-top: 0.35rem;
}

#sideNavigation > .nav > .nav-item .dropdown-item div {
	padding-left: 2rem;
}

#sideNavigation > .nav > .nav-item .active-link {
	background-color: #F2F2F2;
	color: #663398;
}

#sideNavigation > .nav > .nav-item .active-link i {
	color: #663398;
}

@media screen and (max-width: 1099px) {
	#sideNavigation {
		transform: translateX(-100%);
		position: absolute;
		height: auto;
		transition: transform 0.8s;
		width: 16.6rem;
		max-width: calc(100% - 46px);
		min-width: 0;
	}

	#sideNavigation .navbar-toggler {
		position: absolute;
		left: 100%;
		top: 0.25rem;
		cursor: pointer;
		background: #FFFFFF;
	}

	#sideNavigation .navbar-toggler .bar {
		cursor: pointer;
		width: 1.75rem;
		height: 0.25rem;
		background: #AAAAAA;
		display: block;
		border-radius: 0.125rem;
		margin-bottom: 0.3rem;
	}

	#sideNavigation .navbar-toggler:nth-of-type(3) {
		margin-bottom: 0;
	}

	#sideNavigation :focus {
		outline: none;
	}

	#sideNavigation.show {
		transform: translateX(0%);
	}

	#sideNavigation.show .navbar-toggler .bar {
		background-color: #83B91E;
	}

	#sideNavigation.show .navbar-toggler :nth-of-type(1) {
		transform: translateY(225%) rotate(45deg);
	}

	#sideNavigation.show .navbar-toggler :nth-of-type(2) {
		opacity: 0;
	}

	#sideNavigation.show .navbar-toggler :nth-of-type(3) {
		transform: translateY(-225%) rotate(-45deg);
	}
}

#top-navbar-global-navigation {
	padding-bottom: 0;
	background-color: #222222;
}

#top-navbar-global-navigation .navbar-nav .nav-item {
	font-size: 0.875rem;
	margin: 0 0 0 1rem;
	font-weight: 300;
	line-height: 1.72;
}

#top-navbar-global-navigation .navbar-nav .nav-item.active {
	font-weight: 700;
}

#top-navbar-global-navigation .navbar-nav .nav-item.active .nav-link:hover {
	color: #DDDDDD;
}

#top-navbar-global-navigation .navbar-nav .nav-link {
	padding-top: 0;
	padding-bottom: 0;
}

#top-navbar-global-navigation .navbar-brand {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1030;
}

@media screen and (max-width: 1099px) {
	#top-navbar-global-navigation {
		padding: 0;
		overflow: hidden;
	}

	#top-navbar-global-navigation .navbar-nav {
		flex-direction: row;
		direction: rtl;
		display: flex;
	}

	#top-navbar-global-navigation .navbar-nav .nav-item {
		padding: 0 0.5em;
		white-space: nowrap;
	}

	#top-navbar-global-navigation .top-navbar-global-container {
		overflow-x: scroll;
	}
}

.lsw-flags {
	object-fit: none;
	height: 15px;
	width: 20px;
	float: left;
	box-shadow: 0px 0px 1px 0px #888;
	background-repeat: no-repeat;
	background-color: #DBDBDB;
	object-position: 20px 0;
	margin-top: 5px;
	transform: scale(1.3);
}

.lsw-flags.flag-nl {
	height: 14px;
	object-position: -3752px 0px;
}

.lsw-flags.flag-de {
	height: 12px;
	object-position: -1269px 0px;
}

.lsw-flags.flag-ca {
	height: 10px;
    object-position: -834px 0px;
}

.lsw-flags.flag-us {
	height: 11px;
	object-position: -5263px 0px;
}

.lsw-flags.flag-sg {
	height: 14px;
	object-position: -4449px 0px;
}

.lsw-flags.flag-uk {
	height: 10px;
	object-position: -1775px 0px;
}

.lsw-flags.flag-cdn {
	font-size: x-small;
	width: 22px;
}

.lsw-flags.flag-jp {
	height: 14px;
	object-position: -2611px 0px;
}

.lsw-flags.flag-au {
	height: 10px;
	object-position: -286px 0px;
}

.lsw-flags.flag-hk {
	height: 14px;
	object-position: -2171px 0px;
}

.flag-network {
	height: 22px;
}

.btn-group {
	padding-left: 1.5rem;
	padding-top: 0.25rem;
}

.btn-group button {
	background-color: #5685c4;
	border: 1px solid black;
	color: white;
}

.btn-group button:not(:last-child) {
	border-right: none;
}

.btn-group button:not(:disabled):hover {
	background-color: #0D3DCF;
}

.btn-group button:disabled {
	background-color: #F18800;
}
