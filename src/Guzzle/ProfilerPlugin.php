<?php

namespace App\Guzzle;

use Monolog\Logger;
use Psr\Http\Message\RequestInterface;
use GuzzleHttp\TransferStats;

class ProfilerPlugin
{
    protected $logger;

    public function __construct(Logger $logger)
    {
        $this->logger = $logger;
    }

    public function __invoke(callable $handler)
    {
        $logger = $this->logger;

        return function (RequestInterface $request, array $options = []) use ($handler, $logger) {
            $options['on_stats'] = function (TransferStats $stats) use ($logger) {
                $request = $stats->getRequest();
                $response = $stats->getResponse();

                $correlationId = $response->getHeader('apigw-correlation-id');
                $correlationId = reset($correlationId);

                $logger->info('leaseweb.api.request', [
                    'request_uri' => $request->getRequestTarget(),
                    'request_method' => $request->getMethod(),
                    'response_status_code' => $stats->getHandlerStat('http_code'),
                    'response_total_time' => round($stats->getHandlerStat('total_time'), 3),
                    'response_size' => $stats->getHandlerStat('size_download'),
                    'correlation_id' => $correlationId,
                ]);
            };

            return $handler($request, $options);
        };
    }
}
