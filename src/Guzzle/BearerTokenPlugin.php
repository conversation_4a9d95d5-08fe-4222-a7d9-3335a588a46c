<?php

namespace App\Guzzle;

use Psr\Http\Message\RequestInterface;

class BearerTokenPlugin
{
    protected $tokenStorage;

    public function __construct($tokenStorage)
    {
        $this->tokenStorage = $tokenStorage;
    }

    public function __invoke(callable $handler)
    {
        $token = $this->tokenStorage->getToken();

        return function (RequestInterface $request, array $options = []) use ($handler, $token) {
            if ($token) {
                $request = $request->withHeader('AUTHORIZATION', "BEARER {$token->getAccessToken()}");
            }

            return $handler($request, $options);
        };
    }
}
