<?php

namespace App\Services;

use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\HttpFoundation\Session\SessionInterface;

class UserRolesService
{
    private $authChecker;
    private $tokenStorage;

    public function __construct(AuthorizationCheckerInterface $authChecker, TokenStorageInterface $tokenStorage, SessionInterface $session)
    {
        $this->authChecker = $authChecker;
        $this->tokenStorage = $tokenStorage;
        $this->session = $session;
    }

    public function hasPermission($permission)
    {
        return in_array($permission, $this->session->get('permissions'));
    }

    public function hasPermissionToViewSalesOrg($salesOrgId)
    {
        return $this->hasPermission("view_customer_{$salesOrgId}");
    }

    public function isEmployee()
    {
        // If there is no token or firewall configured, we don't even check
        // (Meaning, if user does not have authentication in this route, authorization is pointless)
        if (null === $this->tokenStorage->getToken()) {
            return false;
        }

        // Keep only "ROLE_EMPLOYEE" once devp deploys the change to prod
        return $this->authChecker->isGranted(array(
            'ROLE_FUNC_EMPLOYEE',
            'ROLE_FUNC_TEAM_MANAGER',
            'ROLE_FUNC_SHIFT_LEADER',
            'ROLE_FUNC_MANAGER',
            'ROLE_EMPLOYEE',
        ));
    }

    /**
     * @return ?string
     */
    public function getRequiredPermissionForJobType(string $jobType)
    {
        switch (strtolower($jobType)) {
            case 'configureHardwareRaid':
                return 'hardware_raid_schedule';

            case 'firmwareUpdate':
                return 'firmware_update_schedule';

            case 'hardwareScan':
                return 'hardwarescan_schedule';

            case 'clear_partitions':
            case 'install':
                return 'install_schedule';

            case 'ipmiReset':
                return 'ipmi_reset';

            case 'powercycle':
            case 'poweron':
            case 'poweroff':
                return 'server_powercycle';

            case 'prepareForSale':
            case 'wipe':
                return 'prepare_for_sale_schedule';

            case 'rescueMode':
                return 'rescue_mode_schedule';
        }

        return null;
    }
}
