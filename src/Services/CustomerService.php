<?php

namespace App\Services;

use GuzzleHttp\Client;

class CustomerService
{
    private $leasewebApi;
    private $cachePath;

    public function __construct(Client $leasewebApi, string $cachePath)
    {
        $this->leasewebApi = $leasewebApi;
        $this->cachePath = $cachePath;
    }

    public function getCustomer(string $customerId)
    {
        $cacheFile = "{$this->cachePath}/{$customerId}.json";

        if (is_file($cacheFile) && time() - filemtime($cacheFile) < 2 * 3600) {
            return json_decode(file_get_contents($cacheFile), true);
        }

        try {
            $customers = json_decode($this->leasewebApi->get("/internal/dedicatedserverapi/v2/customers?customerId=${customerId}")->getBody(), true);
            $customer = !empty($customers) ? $customers['customers'][0] : null;
        } catch (\Exception $e) {
            $customer = null;
        }

        if (!is_dir($this->cachePath)) {
            mkdir($this->cachePath);
        }
        file_put_contents($cacheFile, json_encode($customer));

        return $customer;
    }
}
