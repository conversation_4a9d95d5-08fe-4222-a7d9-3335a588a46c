<?php

namespace App\Services;

class EntityMapping
{
    public static function salesOrgIdToCurrencySymbol(string $salesOrgId)
    {
        switch ($salesOrgId) {
            case '1300':
                return '£';
            case '1400':
                return 'AU$';
            case '1500':
                return 'HK$';
            case '1600':
                return '¥';
            case '1700':
                return 'CA$';
            case '2700':
                return '$';
            case '2800':
                return 'S$';
            case '2000':
            case '2600':
            default:
                return '€';
        }
    }

    public static function salesOrgIdToCurrencyCode(string $salesOrgId)
    {
        switch ($salesOrgId) {
            case '1300':
                return 'GBP';
            case '1400':
                return 'AUD';
            case '1500':
                return 'HKD';
            case '1600':
                return 'JPY';
            case '1700':
                return 'CAD';
            case '2700':
                return 'USD';
            case '2800':
                return 'SGD';
            case '2000':
            case '2600':
            default:
                return 'EUR';
        }
    }
}
