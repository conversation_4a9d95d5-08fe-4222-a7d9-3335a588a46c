<?php

namespace App\Services;

use LeaseWeb\AuthBundle\Security\User\OAuthUserProvider\RoleFilter;
use Symfony\Component\HttpFoundation\Session\SessionInterface;

class EMPUserRoleFilter implements RoleFilter
{
    protected $session;

    public function __construct(SessionInterface $session)
    {
        $this->session = $session;
    }

    public function filter(array $roles)
    {
        $permissions = [];

        foreach ($roles as $role) {
            $permissions = array_merge($permissions, $this->getPermissionsForRole($role));
        }

        $permissions = array_unique($permissions);

        $this->session->set('permissions', $permissions);
        $this->session->set('oauth_roles', $roles);
        $this->session->save();

        return $roles;
    }

    protected function getPermissionsForRole($role)
    {
        switch ($role) {
            case 'ROLE_ENT_GLOBAL':
            case 'ROLE_ENT_NETWORK':
                return [
                    'view_customer_1300',
                    'view_customer_1400',
                    'view_customer_1500',
                    'view_customer_1600',
                    'view_customer_2000',
                    'view_customer_2600',
                    'view_customer_2700',
                    'view_customer_2800',
                ];

            case 'ROLE_ENT_UK':
                return [
                    'view_customer_1300',
                    'view_customer_2000',
                ];

            case 'ROLE_ENT_DE':
                return [
                    'view_customer_2600',
                ];

            case 'ROLE_ENT_NL':
                return [
                    'view_customer_1300',
                    'view_customer_2000',
                ];

            case 'ROLE_ENT_ASIA':
                return [
                    'view_customer_1400',
                    'view_customer_1500',
                    'view_customer_1600',
                    'view_customer_2800',
                ];

            case 'ROLE_ENT_USA':
                return [
                    'view_customer_2700',
                ];

            case 'ROLE_DPMT_ABUSE':
                return [
                    'powerbar_outlet_operations',
                    'server_powercycle',
                    'networkdevice_ports',
                ];

            case 'ROLE_DPMT_SALES_SUPPORT':
                return [
                    'credentials_view',
                    'firmware_update_schedule',
                    'hardware_raid_schedule',
                    'hardwarescan_schedule',
                    'install_schedule',
                    'ipmi_credentials_view',
                    'ipmi_reset',
                    'job_expire_and_cancel',
                    'job_wizard',
                    'networkdevice_ports',
                    'networkdevice_ports_speed',
                    'prepare_for_sale_schedule',
                    'privatenetwork_manage',
                    'rescue_mode_schedule',
                    'server_powercycle',
                    'floating_ip_crud',
                ];

            case 'ROLE_DPMT_DEVELOPMENT':
            case 'ROLE_DPMT_NETWORK':
                return [
                    'change_ddos_notification_setting',
                    'credentials_view',
                    'dhcplease_remove',
                    'firmware_update_schedule',
                    'hardware_raid_schedule',
                    'hardwarescan_schedule',
                    'install_schedule',
                    'ipmi_credentials_view',
                    'ipmi_reset',
                    'job_expire_and_cancel',
                    'job_wizard',
                    'networkdevice_add',
                    'networkdevice_remove',
                    'networkdevice_edit',
                    'networkdevice_modify_router', // <--- unique only to network department
                    'networkdevice_ports',
                    'networkdevice_ports_speed',
                    'powerbar_add',
                    'powerbar_remove',
                    'powerbar_edit',
                    'powerbar_outlet_operations',
                    'prepare_for_sale_schedule',
                    'privatenetwork_manage',
                    'rescue_mode_schedule',
                    'server_powercycle',
                    'floating_ip_crud',
                ];

            case 'ROLE_DPMT_OPERATIONS':
            case 'ROLE_DPMT_PROCESS_MGMT':
            case 'ROLE_DPMT_PROVISIONING':
            case 'ROLE_DPMT_SECURITY':
            case 'ROLE_DPMT_SUPPORT':
                return [
                    'change_ddos_notification_setting',
                    'credentials_view',
                    'dhcplease_remove',
                    'firmware_update_schedule',
                    'hardware_raid_schedule',
                    'hardwarescan_schedule',
                    'install_schedule',
                    'ipmi_credentials_view',
                    'ipmi_reset',
                    'job_expire_and_cancel',
                    'job_wizard',
                    'networkdevice_add',
                    'networkdevice_remove',
                    'networkdevice_edit',
                    'networkdevice_ports',
                    'networkdevice_ports_speed',
                    'powerbar_add',
                    'powerbar_remove',
                    'powerbar_edit',
                    'powerbar_outlet_operations',
                    'prepare_for_sale_schedule',
                    'privatenetwork_manage',
                    'rescue_mode_schedule',
                    'server_powercycle',
                    'floating_ip_crud',
                ];
        }

        return [];
    }
}
