<?php

namespace App\Listener;

use Monolog\Logger;
use Symfony\Component\EventDispatcher\Event;

class ProfilerListener
{
    protected $logger;

    public function __construct(Logger $logger)
    {
        $this->logger = $logger;
    }

    public function onKernelResponse(Event $event)
    {
        // check if we are in the context of a request
        $request = $event->getRequest();

        if (!$request) {
            return;
        }

        $start = $request->server->get('REQUEST_TIME_FLOAT');
        $end = microtime(true);

        $this->logger->info('kernel.response', [
            'request_method' => $request->getMethod(),
            'request_uri' => $request->getRequestUri(),
            'request_at' => date('c', $start),
            'response_at' => date('c', $end),
            'duration' => round($end - $start, 3),
            'status_code' => $event->getResponse() ? $event->getResponse()->getStatusCode() : null,
        ]);
    }
}
