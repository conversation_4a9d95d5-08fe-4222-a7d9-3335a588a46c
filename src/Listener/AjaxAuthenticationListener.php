<?php

namespace App\Listener;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpKernel\Event\FilterResponseEvent;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class AjaxAuthenticationListener
{
    private $tokenStorage;

    public function __construct(TokenStorageInterface $tokenStorage, $debug = false)
    {
        $this->tokenStorage = $tokenStorage;
        $this->debug = $debug;
    }

    /**
     * Modifies the response on AJAX call when session is expired.
     */
    public function onKernelResponse(FilterResponseEvent $event)
    {
        $request = $event->getRequest();

        // If the kernel is in debug mode then make sure that the web debug
        // toolbar ajax requests to /_wdt are not affected by this listener.
        if (true === $this->debug && '_wdt' === $request->attributes->get('_route')) {
            return;
        }

        // If we are attempting to access a resource through AJAX without authentication,
        // We should return a 401 Unauthorized response instead of let the normal 302 redirection flow happen.
        // Any 401 Unauthorized response, by its nature, should encourage the user to login again
        if ($request->isXmlHttpRequest() && null === $this->tokenStorage->getToken()) {
            $response = new JsonResponse(null, 401);
            $event->setResponse($response);
        }
    }
}
