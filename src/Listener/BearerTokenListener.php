<?php

namespace App\Listener;

use Symfony\Component\EventDispatcher\Event;
use LeaseWeb\AuthBundle\Security\Authentication\Token\OAuthToken;

class BearerTokenListener
{
    protected $leasewebApi;
    protected $tokenStorage;

    public function __construct($leasewebApi, $tokenStorage)
    {
        $this->leasewebApi = $leasewebApi;
        $this->tokenStorage = $tokenStorage;
    }

    public function onKernelRequest(Event $event)
    {
        $token = $this->tokenStorage->getToken();

        if ($token instanceof OAuthToken) {
            $this->leasewebApi->setDefaultOption('headers/Authorization', "Bearer {$token->getAccessToken()}");
        }
    }
}
