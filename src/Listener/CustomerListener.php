<?php

namespace App\Listener;

use LeaseWeb\AuthLib\Security\User\OAuthUser;
use Symfony\Component\HttpKernel\Event\GetResponseEvent;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Twig\Environment;

/**
 * If a customer is authenticated check if he/she is suspended.
 *
 * This Listener only applies to logged in customer users and checks if they
 * are suspended or not.
 */
class CustomerListener
{
    private $tokenStorage;
    private $twig;

    public function __construct(TokenStorageInterface $tokenStorage, Environment $twig)
    {
        $this->tokenStorage = $tokenStorage;
        $this->twig = $twig;
    }

    public function onKernelRequest(GetResponseEvent $event)
    {
        // This is an anonymous route so no need to do this check
        if (!$token = $this->tokenStorage->getToken()) {
            return;
        }

        // This is an anonymous route so no need to do this check
        if (!$user = $token->getUser()) {
            return;
        }

        // This Listener only deals with OAuthUser
        if (!$user instanceof OAuthUser) {
            return;
        }

        // This listener only deals with customers not employees
        if (empty($user->getCustomerId())) {
            return;
        }

        // The auth requests need to be allowed to continue towards the proxy controller
        if (!in_array($event->getRequest()->getPathInfo(), ['/bare-metals/lswAuth/me', '/connectivity/lswAuth/me'])) {
            // Customer does not have the TECH role, so he is forbidden
            if (!in_array('ROLE_CUSTOMER_TECH', $user->getRolesValues())) {
                $event->setResponse($this->generateResponseForTemplate($user, $template = 'Customer/forbidden.html.twig'));
            }

            // Customer is suspended, so he is also forbidden
            if ($user->isSuspended()) {
                $event->setResponse($this->generateResponseForTemplate($user, $template = 'Customer/suspended.html.twig'));
            }
        }

        // The customer is allowed to see the page
    }

    private function generateResponseForTemplate(OAuthUser $user, $template)
    {
        $body = $this->twig->render("{$template}", [
            'customer' => $user,
        ]);

        $response = new Response();
        $response->setContent($body);
        $response->setStatusCode(Response::HTTP_FORBIDDEN);

        return $response;
    }
}
