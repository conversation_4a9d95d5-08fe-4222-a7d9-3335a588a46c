<?php

namespace App\Twig;

use App\Services\EntityMapping;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class LegacyExtension extends AbstractExtension
{
    public function getFilters()
    {
        return array(
            new TwigFilter('formatCurrency', [$this, 'formatCurrency']),
        );
    }

    public function formatCurrency(string $value, string $salesOrgId, string $type = null): string
    {
        $currency = EntityMapping::salesOrgIdToCurrencySymbol($salesOrgId);

        if ($type == 'code') {
            $currency = EntityMapping::salesOrgIdToCurrencyCode($salesOrgId);
        }

        return $currency.trim($value);
    }
}
