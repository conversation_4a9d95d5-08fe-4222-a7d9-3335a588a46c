<?php

namespace App\Twig;

use Symfony\Bridge\Twig\Extension\AssetExtension;
use Symfony\Component\HttpFoundation\RequestStack;

class AssetDecoratorExtension extends AssetExtension
{
    protected $requestStack;
    protected $version;

    public function setRequestStack(RequestStack $requestStack)
    {
        $this->requestStack = $requestStack;
    }

    public function setAssetsVersion(string $version)
    {
        $this->version = $version;
    }

    public function getAssetUrl($path, $packageName = null, $absolute = false, $version = null)
    {
        if (null === $request = $this->requestStack->getCurrentRequest()) {
            return parent::getAssetUrl($path, $packageName, $absolute, $version);
        }

        if (0 === strpos($request->getRequestUri(), '/bare-metals')) {
            $prefix = '/bare-metals';
        } elseif (0 === strpos($request->getRequestUri(), '/connectivity')) {
            $prefix = '/connectivity';
        } else {
            $prefix = '/emp';
        }

        $url = $prefix.$path;

        if (is_null($version) && !is_null($this->version)) {
            $version = $this->version;
            $url .= "?version={$this->version}";
        }

        return parent::getAssetUrl($url, $packageName, $absolute, $version);
    }
}
