<?php

namespace App\Monolog;

use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorage;

class RequestProcessor
{
    protected $requestId;
    protected $requestStack;
    protected $tokenStorage;

    public function __construct(RequestStack $requestStack, TokenStorage $tokenStorage)
    {
        $this->requestStack = $requestStack;
        $this->tokenStorage = $tokenStorage;
    }

    public function processRecord(array $record)
    {
        // check if we are in the context of a request
        $request = $this->requestStack->getCurrentRequest();

        if (!$request) {
            return $record;
        }

        if (!$this->requestId) {
            $this->requestId = $request->server->get('REQUEST_ID', bin2hex(random_bytes(12)));
        }

        $record['extra']['request_id'] = $this->requestId;
        $record['extra']['client_ip'] = $request->getClientIp();

        if ($request->hasSession()) {
            $record['extra']['session_id'] = $request->getSession()->getId();
        }

        // check if we have a current logged in user available
        $token = $this->tokenStorage->getToken();

        if ($token) {
            $record['extra']['username'] = $token->getUser()->getUserName();

            if ($token->getUser()->getCustomerId()) {
                $record['extra']['customer_id'] = $token->getUser()->getCustomerId();
                $record['extra']['sales_org_id'] = $token->getUser()->getSalesOrgId();
            }
        }

        return $record;
    }
}
