<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Request;

class ExceptionController extends Controller
{
    public function showExceptionAction(Request $request, $exception)
    {
        $menu = $this->get('session')->get('template', '');

        $uri = $request->getUri();
        $errorMessage = date('[Y-m-d H:i:s]');
        $errorMessage .= isset($exception) && method_exists($exception, 'getMessage') && $exception->getMessage()
            ? " {$exception->getMessage()}"
            : ' Undefined error';

        if ($menu == '') {
            if (strpos($uri, 'bare-metals') == false) {
                return $this->render(
                    'TwigBundle:Exception:error-emp.html.twig',
                    array('errorMessage' => $errorMessage)
                );
            } else {
                // is a bare-metal page so render SSC (user is not logged in)
                return $this->render(
                    'TwigBundle:Exception:error-ssc.html.twig',
                    array('errorMessage' => $errorMessage)
                );
            }
        } else {
            return $this->render(
                'TwigBundle:Exception:error-ssc.html.twig',
                array('errorMessage' => $errorMessage)
            );
        }
    }
}
