<?php

namespace App\Controller;

use GuzzleHttp\Exception\BadResponseException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class AuditLogController extends Controller
{
    /**
     * @Route("/emp/_legacy/audit-logs")
     * @Method("GET")
     */
    public function getAuditLogAction(Request $request)
    {
        $payload = [
            'sort' => [
                [
                    '@timestamp' => [
                        'order' => 'desc',
                    ],
                ],
            ],
            'size' => min(max((int) $request->query->get('limit', 50), 0), 100),
            'from' => max((int) $request->query->get('offset', 0), 0),
            '_source' => [
                'includes' => [
                    '@timestamp',
                    'message',
                    'monolog.*',
                ],
            ],
            'query' => [
                'bool' => [
                    'must' => [
                        [
                            'term' => [
                                'labels.log_type' => 'monolog',
                            ],
                        ],
                        [
                            'terms' => [
                                'labels.team' => ['BMA', 'NWA'],
                            ],
                        ],
                        [
                            'term' => [
                                'log.logger' => 'audit',
                            ],
                        ],
                    ],
                ],
            ],
        ];

        if ($request->query->has('filter')) {
            $payload['query']['bool']['must'][] = [
                'query_string' => [
                    'query' => $request->query->get('filter'),
                    'analyze_wildcard' => true,
                    'default_operator' => 'and',
                    'analyzer' => 'keyword',
                    'lenient' => true,
                    'fields' => ['message', 'monolog.*'],
                ],
            ];
        }

        try {
            $logs = json_decode($this->get('emp.service.es_api')->post('/logstash-*/_search', ['json' => $payload])->getBody(), true);
        } catch (BadResponseException $e) {
            return new Response($e->getResponse()->getBody(), $e->getResponse()->getStatusCode());
        }

        return new JsonResponse(array_map(function ($log) {
            return $log['_source'];
        }, (array) ($logs['hits']['hits'] ?? [])));
    }
}
