<?php

namespace App\Controller;

use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

class NetworkDeviceController extends Controller
{
    /**
     * @Route("/emp/_legacy/networkDevices/{equipmentId}", requirements={"equipmentId": "\d+"}, methods={"GET"})
     * @Method("GET")
     */
    public function getNetworkDeviceDetailAction(Request $request, string $equipmentId)
    {
        try {
            $equipmentData = json_decode($this->get('leaseweb.api')->get("/internal/sap/equipments/{$equipmentId}")->getBody(), true);
        } catch (\Exception $e) {
            return new JsonResponse([
                'errorMessage' => "Equipment {$equipmentId} is not in SAP",
            ], 404);
        }

        $equipmentDescription = $equipmentData['equipments'][0]['description'] ?? null;
        if (false !== strpos($equipmentDescription, ' ')) {
            $equipmentDescription = null; // SAP names with spaces should not be suggested for auto-fill
        }

        $equipmentType = $equipmentData['equipments'][0]['type'] ?? null;

        switch ($equipmentType) {
            case 'V-LOADBAL':
                $equipmentType = 'LOADBALANCER';
                break;
            case 'V-FIREWALL':
                $equipmentType = 'FIREWALL';
                break;
            case 'V-SWITCH':
                $equipmentType = 'SWITCH';
                break;
            default:
                $equipmentType = strtoupper($equipmentType);
        }

        $equipmentBrand = $equipmentData['equipments'][0]['attributes']['brand'] ?? null;

        switch ($equipmentBrand) {
            case 'ARIS':
                $equipmentBrand = 'ARISTA';
                break;
            case 'CISC':
                $equipmentBrand = 'CISCO';
                break;
            case 'JUNI':
                $equipmentBrand = 'JUNIPER';
                break;
            default:
                $equipmentBrand = strtoupper($equipmentBrand);
        }

        $equipmentSite = $equipmentData['equipments'][0]['attributes']['site']['name'] ?? null;

        $filters = [
            'primary' => 'true',
            'limit' => 1,
            'equipmentIds' => $equipmentId,
            'type' => 'NORMAL_IP',
        ];
        $queryString = http_build_query($filters);

        try {
            $ips = json_decode($this->get('leaseweb.api')->get("/internal/ipam/ips?{$queryString}")->getBody(), true);
        } catch (\Exception $e) {
            $ips = null;
        }

        $equipmentIp = $ips['ips'][0]['ip'] ?? null;

        return new JsonResponse([
            'id' => $equipmentId,
            'type' => $equipmentType,
            'brand' => $equipmentBrand,
            'site' => $equipmentSite,
            'ip' => $equipmentIp,
            'description' => $equipmentDescription,
        ]);
    }
}
