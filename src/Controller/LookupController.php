<?php

namespace App\Controller;

use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class LookupController extends Controller
{
    /**
     * @Route("/emp/_legacy/lookup")
     * @Method("GET")
     */
    public function lookupEquipmentAction(Request $request)
    {
        $equipment = [];

        if ($request->query->has('macAddress')) {
            $equipment = $this->lookupEquipmentByMac($request->query->get('macAddress'));
        } elseif ($request->query->has('ipAddress')) {
            $equipment = $this->lookupEquipmentByIp($request->query->get('ipAddress'));
        } elseif ($request->query->has('equipmentId')) {
            $equipment = $this->lookupEquipmentById($request->query->get('equipmentId'));

            if (empty($equipment) && $request->query->get('guess', false)) {
                $equipment = $this->get('leaseweb.service.customer')->getCustomer($request->query->get('equipmentId'));
            }
        }

        if (empty($equipment)) {
            return new Response(null, 404);
        }

        return new JsonResponse($equipment);
    }

    /**
     * @Route("/emp/_legacy/customers")
     * @Method("GET")
     */
    public function getCustomerAction(Request $request)
    {
        $customerId = $request->query->get('customerId', '');

        if (empty($customerId)) {
            return new JsonResponse([
                'errorMessage' => 'Missing customerId parameter',
            ], 400);
        }

        $customer = $this->get('leaseweb.service.customer')->getCustomer($customerId);

        if (empty($customer)) {
            return new JsonResponse([
                'errorMessage' => 'Customer ' . $customerId . ' not found',
            ], 404);
        }

        return new JsonResponse($customer);
    }

    protected function lookupEquipmentByIp($ipAddress)
    {
        if (!filter_var($ipAddress, FILTER_VALIDATE_IP)) {
            return null;
        }

        try {
            $ip = json_decode($this->get('leaseweb.api')->get("/internal/ipam/ips/{$ipAddress}")->getBody(), true);
        } catch (\Exception $e) {
            return null;
        }

        if (empty($ip['assignedNode'])) {
            return null;
        }

        return $this->lookupEquipmentById($ip['assignedNode']['id']);
    }

    protected function lookupEquipmentById($equipmentId)
    {
        try {
            $equipmentData = json_decode($this->get('leaseweb.api')->get("/internal/sap/equipments/{$equipmentId}")->getBody(), true);
        } catch (\Exception $e) {
            return null;
        }

        if (empty($equipmentData)) {
            return null;
        }

        return [
            'id' => $equipmentData['equipments'][0]['id'],
            'type' => $this->getEquipmentType($equipmentData['equipments'][0]),
            'customerId' => !empty($equipmentData['equipments'][0]['customerId']) ? $equipmentData['equipments'][0]['customerId'] : null,
            'salesOrgId' => !empty($equipmentData['equipments'][0]['salesOrgId']) ? $equipmentData['equipments'][0]['salesOrgId'] : null,
        ];
    }

    protected function lookupEquipmentByMac($macAddress)
    {
        if (!filter_var($macAddress, FILTER_VALIDATE_MAC)) {
            return null;
        }

        try {
            $equipmentData = json_decode($this->get('leaseweb.api')->get("/internal/sap/equipments?macAddress={$macAddress}")->getBody(), true);
        } catch (\Exception $e) {
            return null;
        }

        if (empty($equipmentData)) {
            return null;
        }

        if (!array_key_exists('equipments', $equipmentData)) {
            return null;
        }

        return [
            'id' => $equipmentData['equipments'][0]['id'],
            'type' => $this->getEquipmentType($equipmentData['equipments'][0]),
        ];
    }

    private function getEquipmentType($equipment)
    {
        if (in_array($equipment['type'], ['F-SERVER', 'F-BLADESVR'])) {
            return 'servers';
        }
        if ('B-RACK' === $equipment['type'] && 'Dedicated' === $equipment['attributes']['type']) {
            return 'racks';
        }
        if ('B-RACK' === $equipment['type'] && 'Private Colocation' === $equipment['attributes']['type']) {
            return 'colocations';
        }
        if ('U-UPOS' === $equipment['type'] && 'Shared Colocation' === $equipment['attributes']['type']) {
            return 'colocations';
        }
        if (in_array($equipment['type'], ['V-CHASSIS', 'V-FIREWALL', 'V-LINECARD', 'V-LOADBAL', 'V-SWITCH'])) {
            return 'networkDevices';
        }
        if (preg_match('/PDU|ATS/i', $equipment['type'])) {
            return 'powerbars';
        }

        return $equipment['type'];
    }
}
