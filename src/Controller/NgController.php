<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;

class NgController extends Controller
{
    public function showAction($path = null)
    {
        $angularV3Paths = [
            'bare-metals/servers',
            'emp/servers',
            'bare-metals/racks',
            'emp/racks',
            'bare-metals/dedicatedStorages',
            'emp/dedicatedStorages',
            'bare-metals/colocations',
            'emp/colocations',
            'emp/disks'
        ];

        if (in_array($path, $angularV3Paths)) {
            return $this->render('ng-v3.html.twig');
        }

        return $this->render('ng.html.twig');
    }
}