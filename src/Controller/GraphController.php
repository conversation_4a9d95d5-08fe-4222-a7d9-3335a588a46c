<?php

namespace App\Controller;

use App\Controller\Traits\AppBaseControllerTrait;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GraphController extends Controller
{
    use AppBaseControllerTrait;

    /**
     * @Route("/bare-metals/metrics/reports/graphs/datatraffic", name="bare_metals_metrics_download_datatraffic")
     * @Route("/emp/metrics/reports/graphs/datatraffic", name="emp_bare_metals_metrics_download_datatraffic")
     * @Method("GET")
     */
    public function metricsReportDatatrafficAction(Request $request)
    {
        list($customerId, $salesOrgId) = $this->safelyGetCustomerIdAndSalesOrgId();

        if ($this->get('leaseweb.service.user_roles')->isEmployee() && (empty($customerId) || empty($salesOrgId))) {
            throw new \RuntimeException('You need to specify a customerId and salesOrgId');
        }

        $requestData = $this->buildDatatrafficRequest($request);
        $requestData['aggregation'] = 'SUM';
        $requestData['customerId'] = $customerId;
        $requestData['salesOrgId'] = $salesOrgId;

        if ($request->query->get('format') === 'CSV') {
            $data = $this->get('leaseweb.api')->get('/internal/bmusageapi/csvMetricsPerEquipment/datatraffic?' . http_build_query($requestData), ['Accept' => 'text/csv'])->getBody(true);

            $response = new Response();
            $response->headers->set('Content-Disposition', sprintf('attachment; filename="%s"', 'export.csv'));
            if (!$response->headers->has('Content-Type')) {
                $response->headers->set('Content-Type', 'text/csv');
            }
            $response->setContent($data);
        } else {
            $response = new JsonResponse(json_decode($this->get('leaseweb.api')->get('/internal/bmusageapi/metrics/datatraffic?' . http_build_query($requestData))->getBody(), true), 200, ['Content-Disposition' => sprintf('attachment; filename="%s"', 'export.json')]);
        }

        return $response;
    }

    /**
     * @Route("/bare-metals/metrics/reports/graphs/bandwidth", name="bare_metals_metrics_download_bandwidth")
     * @Route("/emp/metrics/reports/graphs/bandwidth", name="emp_bare_metals_metrics_download_bandwidth")
     * @Method("GET")
     */
    public function metricsReportBandwidthAction(Request $request)
    {
        list($customerId, $salesOrgId) = $this->safelyGetCustomerIdAndSalesOrgId();

        if ($this->get('leaseweb.service.user_roles')->isEmployee() && (empty($customerId) || empty($salesOrgId))) {
            throw new \RuntimeException('You need to specify a customerId and salesOrgId');
        }

        $requestData = $this->buildBandwidthRequest($request);
        $requestData['customerId'] = $customerId;
        $requestData['salesOrgId'] = $salesOrgId;

        $response = new JsonResponse();
        $response->setData(json_decode($this->get('leaseweb.api')->get('/internal/bmusageapi/metrics/bandwidth?' . http_build_query($requestData))->getBody(), true));

        return $response;
    }

    public function buildDatatrafficRequest(Request $request)
    {
        $payload = [];

        list($payload['from'], $payload['to']) = $this->extractDatesFromRequest($request);

        if ($request->query->has('granularity')) {
            $payload['granularity'] = $request->query->get('granularity');
        }

        if ($request->query->has('aggregation')) {
            $payload['aggregation'] = $request->query->get('aggregation');
        }

        if ($request->query->has('networkType')) {
            $payload['networkType'] = $request->query->get('networkType');
        }

        return $payload;
    }

    public function buildBandwidthRequest(Request $request)
    {
        $payload = [];

        list($payload['from'], $payload['to'], $specialToDate) = $this->extractDatesFromRequest($request);

        if ($request->query->has('granularity')) {
            $payload['granularity'] = $request->query->get('granularity');
        }

        if ($request->query->has('aggregation')) {
            $payload['aggregation'] = $request->query->get('aggregation');
        }

        if ('AVG' === ($payload['aggregation'] ?? null) && !isset($payload['granularity'])) {
            $payload['to'] = $specialToDate;
        }

        if ($request->query->has('networkType')) {
            $payload['networkType'] = $request->query->get('networkType');
        }

        return $payload;
    }

    public function extractDatesFromRequest(Request $request)
    {
        $fromDate = new \DateTime($request->query->get('from') ?: date('Y-m-01'));
        $toDate = new \DateTime($request->query->get('to') ?: date('Y-m-d', strtotime('first day of next month')));
        $lastDate = new \DateTime($toDate->format('Y-m-d'));
        $currentDate = new \DateTime(date('Y-m-d'));
        $specialToDate = $toDate;

        if ($lastDate > $currentDate && $fromDate < $toDate) {
            $specialToDate = $currentDate;
        }

        if ($fromDate == $specialToDate) {
            $specialToDate = $specialToDate->modify('+1 day');
        }

        return array($fromDate->format('Y-m-d'), $toDate->format('Y-m-d'), $specialToDate->format('Y-m-d'));
    }
}
