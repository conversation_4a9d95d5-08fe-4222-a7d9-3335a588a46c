<?php

namespace App\Controller\Traits;

trait AppBaseControllerTrait
{
    protected function safelyGetCustomerIdAndSalesOrgId()
    {
        if ($this->get('leaseweb.service.user_roles')->isEmployee()) {
            $request = $this->container->get('request_stack')->getCurrentRequest();

            return array($request->query->get('customerId'), $request->query->get('salesOrgId'));
        }

        return array($this->getUser()->getCustomerId(), $this->getUser()->getSalesOrgId());
    }
}
