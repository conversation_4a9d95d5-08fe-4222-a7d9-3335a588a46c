<?php

namespace App\Controller;

use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Request;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;

class RedirectTicketController extends Controller
{
    /**
     * @Route("/emp/_legacy/redirect-ticket/{ticketId}", requirements={"ticketId": "\d+"})
     * @Security("has_role('ROLE_EMPLOYEE')")
     */
    public function redirectTicketAction(Request $request, string $ticketId)
    {
        return $this->redirect("https://my327235-sso.crm.ondemand.com/sap/public/byd/runtime?bo_ns=http://sap.com/thingTypes&bo=COD_GENERIC&node=Root&operation=OnExtInspect&param.Type=COD_SRQ_AGENT_TT&sapbyd-agent=TAB&param.InternalID={$ticketId}");
    }
}