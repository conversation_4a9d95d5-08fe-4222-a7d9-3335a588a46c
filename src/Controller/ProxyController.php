<?php

namespace App\Controller;

use GuzzleHttp\Exception\RequestException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class ProxyController extends Controller
{
    /**
     * @Route("/emp/_/{path}", requirements={"path"=".+"}, name="emp_proxy")
     * @Route("/bare-metals/_/{path}", requirements={"path"=".+"}, name="proxy")
     * @Route("/connectivity/_/{path}", requirements={"path"=".+"}, name="fbs_proxy")
     */
    public function proxyPassAction(Request $request, string $path)
    {
        $uri = '/' . trim($path, '/');

        $allowedEndpoints = implode('|', [
            '/bareMetals/v2/',
            '/internal/bmpapi/',
            '/internal/bmsdb/',
            '/internal/bmusageapi/',
            '/internal/dedicatedserverapi/',
            '/internal/ipam/ips',
            '/internal/monitoring/',
            '/internal/networkperformanceapi/',
            '/internal/nseapi/',
            '/secrets/',
            '/internal/vlanapi/',
            '/internal/cloud/v2/privateClouds',
            '/tickets/v2/tickets',
        ]);

        if (1 !== preg_match("#^($allowedEndpoints).*$#", $uri)) {
            return new Response(null, 403);
        }

        try {
            $upstreamResponse = $this->get('leaseweb.api')->request($request->getMethod(), $uri, [
                'body' => $request->getContent(),
                'query' => $request->query->all(),
                'headers' => array_filter($request->headers->all(), function ($h) {
                    return strtolower(substr($h, 0, 28)) === 'apigw-impersonated-customer-';
                }, ARRAY_FILTER_USE_KEY),
            ]);
        } catch (RequestException $e) {
            $upstreamResponse = $e->getResponse();
        }

        // Transfer-Encoding is a hop-by-hop header and thus should not be proxied
        // It breaks the frontend if the chunked directive is used
        $headers = $upstreamResponse->getHeaders();
        unset($headers['Transfer-Encoding']);
        unset($headers['transfer-encoding']);

        return new Response($upstreamResponse->getBody(), $upstreamResponse->getStatusCode(), $headers);
    }

    /**
     * @Route("/emp/lswAuth/me", name="emp_auth_me")
     * @Route("/bare-metals/lswAuth/me", name="auth_me")
     * @Route("/connectivity/lswAuth/me", name="fbs_auth_me")
     */
    public function proxyPassAuthMeAction(Request $request)
    {
        $upstreamResponse = json_decode($this->get('leaseweb.api')->request('GET', "{$this->container->getParameter('leaseweb_auth_provider_url')}/me")->getBody(), true);

        return new JsonResponse([
            'token' => [
                'roles' => $upstreamResponse['token']['roles'],
                'user' => $upstreamResponse['token']['user'],
            ],
        ]);
    }

    /**
     * @Route("/emp/lswAuth/api/profile/customerAccounts", name="emp_auth_customer_accounts")
     * @Route("/bare-metals/lswAuth/api/profile/customerAccounts", name="auth_customer_accounts")
     * @Route("/connectivity/lswAuth/api/profile/customerAccounts", name="fbs_auth_customer_accounts")
     */
    public function proxyPassAuthSwitchAccountsAction(Request $request)
    {
        $upstreamResponse = $this->get('leaseweb.api')->request('GET', "{$this->container->getParameter('leaseweb_auth_provider_url')}/api/profile/customerAccounts?status=ENABLED");

        $headers = $upstreamResponse->getHeaders();
        unset($headers['Transfer-Encoding']);
        unset($headers['transfer-encoding']);

        return new Response($upstreamResponse->getBody(), $upstreamResponse->getStatusCode(), $headers);
    }

    /**
     * @Route("/bare-metals/lswApi/internal/notifications", name="notifications_api")
     * @Route("/connectivity/lswApi/internal/notifications", name="fbs_notifications_api")
     * @Method("GET", "POST")
     */
    public function proxyPassNotificationsApiAction(Request $request)
    {
        $upstreamResponse = $this->get('leaseweb.api')->request($request->getMethod(), '/internal/notifications', [
            'body' => $request->getContent(),
            'query' => $request->query->all(),
        ]);

        $headers = $upstreamResponse->getHeaders();
        unset($headers['Transfer-Encoding']);
        unset($headers['transfer-encoding']);

        return new Response($upstreamResponse->getBody(), $upstreamResponse->getStatusCode(), $headers);
    }
}
