<?php

namespace App\Controller\Contracts;

use App\Controller\Traits\AppBaseControllerTrait;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

class RemoteHandsContractController extends Controller
{
    use AppBaseControllerTrait;

    # TODO: get this from Colocations class in bundle when moving this route to a proper API
    private const PACKAGES = [
        'Basic',
        'Bronze',
        'Silver',
        'Gold',
        'Platinum',
    ];

    /**
     * @Route("/emp/_legacy/remotehands/options")
     * @Route("/bare-metals/_legacy/remotehands/options")
     * @Method("GET")
     */
    public function RemoteHandsOptionsAction(Request $request)
    {
        list($customerId, $salesOrgId) = $this->safelyGetCustomerIdAndSalesOrgId();
        $contractRequest = $this->get('leaseweb.api')->get('internal/sap/contractItems', [
            'query' => [
                'customerId' => $customerId,
                'salesOrgId' => $salesOrgId,
                'productType' => 'REMHNDS,REMHNDS02',
                'itemStatus' => 'ACT',
            ]
        ]);
        $contractResponse = json_decode($contractRequest->getBody(), true);

        $remoteHandsInformation = !empty($contractResponse['contractItems']) ? reset($contractResponse['contractItems']) : [];

        return new JsonResponse([
            'remoteHandsInformation' => $remoteHandsInformation,
        ]);
    }

    /**
     * @Route("/emp/_legacy/remotehands")
     * @Route("/bare-metals/_legacy/remotehands")
     * @Method("GET")
     */
    public function getRemoteHandsAction(Request $request)
    {
        list($customerId, $salesOrgId) = $this->safelyGetCustomerIdAndSalesOrgId();
        $contractRequest = $this->get('leaseweb.api')->get('internal/sap/contractItems', [
            'query' => [
                'customerId' => $customerId,
                'salesOrgId' => $salesOrgId,
                'productType' => 'REMHNDS,REMHNDS02',
                'itemStatus' => 'ACT',
            ]
        ]);
        $contractResponse = json_decode($contractRequest->getBody(), true);

        $remoteHandsInformation = !empty($contractResponse['contractItems']) ? $contractResponse['contractItems'] : [];

        return new JsonResponse([
            'remoteHandsInformation' => $this->formatRemoteHandsInfo($remoteHandsInformation),
        ]);
    }

    # TODO: move to a proper API project and make this better
    private function formatRemoteHandsInfo(array $remoteHandsPackages): array
    {
        $packages = $this->fillRemoteHandsPackages();
        foreach ($remoteHandsPackages as $value) {
            $packages[$value['configuration']['remoteHands']['type']] = [
                'type' => $value['configuration']['remoteHands']['type'],
                'billingFrequency' => $value['billingFrequency'] ?? null,
                'contractTerm' => $value['contractTerm'] ?? null,
                'minutes' => $value['configuration']['remoteHands']['minutes'] ?? null,
            ];
        }
        return array_values($packages);
    }

    private function fillRemoteHandsPackages(): array
    {
        $packages = [];
        foreach (self::PACKAGES as $value) {
            $packages[$value] = [
                'type' => $value,
                'billingFrequency' => null,
                'contractTerm' => null,
                'minutes' => null,
            ];
        }
        return $packages;
    }
}
