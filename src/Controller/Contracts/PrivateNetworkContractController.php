<?php

namespace App\Controller\Contracts;

use App\Controller\Traits\AppBaseControllerTrait;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

class PrivateNetworkContractController extends Controller
{
    use AppBaseControllerTrait;

    /**
     * @Route("/bare-metals/privateNetwork")
     * @Route("/bare-metals/privateNetwork/lookup")
     * @Route("/emp/privateNetwork")
     * @Route("/emp/privateNetwork/lookup")
     * @Method("GET")
     */
    public function lookupPrivateNetworkAction(Request $request)
    {
        list($customerId, $salesOrgId) = $this->safelyGetCustomerIdAndSalesOrgId();

        $privateNetwork = $this->getOrCreatePrivateNetwork($customerId, $salesOrgId);

        if ($this->get('leaseweb.service.user_roles')->isEmployee()) {
            $url = "/emp/privateNetworks/{$privateNetwork['id']}";
        } else {
            $url = "/bare-metals/privateNetworks/{$privateNetwork['id']}";
        }

        return $this->redirect($url);
    }

    /**
     * @Route("/bare-metals/_legacy/privateNetwork/order")
     * @Method("POST")
     */
    public function orderPrivateNetworkAction(Request $request)
    {
        $data = json_decode($request->getContent(), true);
        if (array_key_exists('equipment', $data)){
            $equipment = $data['equipment'];
        }
        $uplinkCapacity = $data['uplinkCapacity'];
        $equipmentType = $data['equipmentType'];
        $isPrivateNetworkRedundancy = $data['isPrivateNetworkRedundancy'] ?? false;

        list($customerId, $salesOrgId) = $this->safelyGetCustomerIdAndSalesOrgId();

        $uplinkCapacities = [
            '100' => ['value' => '100Mbps'],
            '1000' => ['value' => '1Gbps'],
            '10000' => ['value' => '10Gbps'],
            '25000' => ['value' => '25Gbps'],
            '40000' => ['value' => '40Gbps'],
            '100000' => ['value' => '100Gbps'],
        ];

        if ($this->get('leaseweb.service.user_roles')->isEmployee()) {
            return new JsonResponse(['error' => "Only customers can request for private network for {$equipmentType} via the customer portal"], 400);
        }

        try {
            $privateNetwork = $this->getOrCreatePrivateNetwork($customerId, $salesOrgId);

            switch ($equipmentType) {
                case 'colocation':
                    $switch = $data['switch'];
                    $auditLogEquipment = 'colocation';
                    $privateNetworkResponse = $this->orderPrivateNetworkForColocation($equipment, $privateNetwork['id'], $uplinkCapacities[$uplinkCapacity]['value'], $switch);
                    break;
                case 'privateRack':
                    $auditLogEquipment = 'private_rack';
                    $privateNetworkResponse = $this->orderPrivateNetworkForPrivateRack($equipment, $privateNetwork['id'], $uplinkCapacities[$uplinkCapacity]['value']);
                    break;
                case 'privateCloud':
                    $auditLogEquipment = 'private_cloud';
                    $privateNetworkResponse = $this->orderPrivateNetworkForPrivateCloud($equipment, $privateNetwork['id'], $uplinkCapacities[$uplinkCapacity]['value']);
                    break;
                case 'server':
                    $auditLogEquipment = 'server';
                    if($isPrivateNetworkRedundancy){
                        $privateNetworkResponse = $this->orderPrivateNetworkRedundancyForServer($equipment,$privateNetwork['id'],$uplinkCapacities[$uplinkCapacity]['value']);
                    }
                    else{
                        return new JsonResponse(['error' => "This action is not allowed for dedicated server."], 400);
                    }
                    break;
                case 'FIREWALL' || 'LOAD_BALANCER':
                    $auditLogEquipment = 'network_equipment';
                    $privateNetworkResponse = $this->orderPrivateNetworkForNetworkEquipment($uplinkCapacities[$uplinkCapacity]['value'], $equipmentType);
                    break;
                default:
                    return new JsonResponse(['error' => "{$equipmentType} is not allowed to order"], 400);
            }

            $logData = [
                'salesOrdId' => $salesOrgId,
                'privateNetworkId' => $privateNetwork['id'],
                'equipmentId' => $equipment['id'] ?? null,
                'uplinkCapacity' => $uplinkCapacities[$uplinkCapacity]['value'],
            ];

            if ('colocation' === $equipmentType) {
                $logData = array_merge($logData, [
                    'switch' => $switch,
                ]);
            }

            $this->get('logger')->info("order_private_network_{$auditLogEquipment}", $logData);

            return $privateNetworkResponse;
        } catch (\Exception $exception) {
            return new JsonResponse(['error' => "There was an error ordering the private network for {$equipmentType}."], 400);
        }
    }

    /**
     * @Route("/bare-metals/_legacy/privateNetwork/remove")
     * @Method("POST")
     */
    public function removePrivateCloudFromPrivateNetworkAction(Request $request)
    {
        $data = json_decode($request->getContent(), true);
        $privateCloud = json_decode($this->get('leaseweb.api')->get("/internal/cloud/v2/privateClouds/{$data['privateCloudId']}")->getBody(), true);
        $customer = $this->get('security.token_storage')->getToken()->getUser();

        try {
            $this->get('leaseweb.api')->post('/tickets/v2/tickets', [
                'body' => json_encode([
                    'type' => 'SERVICE',
                    'created_by' => $customer->getEmail(),
                    'body' => $this->renderView('removePrivateCloud.html.twig', [
                        'privateCloudId' => $privateCloud['id'],
                        'privateNetworkId' => $data['privateNetworkId'],
                        'customerId' => $privateCloud['customerId'],
                    ]),
                    'subject' => "Request for removing private cloud from private network {$privateCloud['id']} ({$data['privateNetworkId']}) on behalf of customer {$privateCloud['customerId']}",
                    'equipments' => [[
                        'equipmentId' => $privateCloud['id']
                    ]],
                    'attributes' => [[
                        'name' => 'privateNetworkId',
                        'value' => $data['privateNetworkId']
                    ],
                    [
                        'name' => 'privateCloudId',
                        'value' => $privateCloud['id']
                    ]],
                ]),
            ])->getBody();
        } catch (\Exception $exception) {
            $this->get('logger')->warning($exception->getMessage());

            return new JsonResponse(['errorMessage' => 'We were unable to process your order request. Please contact Customer Care in order to solve it.'], 400);
        }

        $this->get('logger')->info('cancel_private_network_cloud', [
            'salesOrgId' => $customer->getSalesOrgId(),
            'customerId' => $customer->getCustomerId(),
            'privateCloudId' => $privateCloud['id'],
            'privateNetworkId' => $data['privateNetworkId'],
        ]);

        return new JsonResponse(null, 204);
    }

    private function getOrCreatePrivateNetwork($customerId, $salesOrgId)
    {
        // First determine if the customer has a private network
        $privateNetwork = $this->getPrivateNetwork($customerId, $salesOrgId);

        // If the customer does not have a private network yet, create one
        if (!$privateNetwork) {
            $privateNetwork = $this->createPrivateNetwork($customerId, $salesOrgId);
        }

        return $privateNetwork;
    }

    private function getPrivateNetwork($customerId, $salesOrgId)
    {
        $queryParameters = [];
        if ($this->get('leaseweb.service.user_roles')->isEmployee()) {
            $queryParameters['filter'] = $customerId;
        }

        $response = $this->get('leaseweb.api')->get('/internal/nseapi/v2/privateNetworks', ['query' => $queryParameters]);

        $privateNetworks = json_decode($response->getBody(), true);
        $resultPrivateNetwork = null;

        foreach ($privateNetworks['privateNetworks'] as $privateNetwork) {
            if ($privateNetwork['salesOrgId'] == $salesOrgId) {
                $resultPrivateNetwork = $privateNetwork;
                break;
            }
        }

        return $resultPrivateNetwork;
    }

    private function createPrivateNetwork($customerId, $salesOrgId)
    {
        $data = null;

        if ($this->get('leaseweb.service.user_roles')->isEmployee()) {
            $data = json_encode([
                'customerId' => $customerId,
                'salesOrgId' => $salesOrgId,
            ]);
        }

        $response = $this->get('leaseweb.api')->post('/internal/nseapi/v2/privateNetworks', ['body' => $data]);

        $privateNetwork = json_decode($response->getBody(), true);

        return $privateNetwork;
    }

    private function orderPrivateNetworkRedundancyForServer(array $equipment,$privateNetworkId,$uplinkCapacity){
        $customer = $this->get('security.token_storage')->getToken()->getUser();

        try {
            $ticket = json_decode($this->get('leaseweb.api')->post('/tickets/v2/tickets', [
                'body' => json_encode([
                    'type' => 'SERVICE',
                    'created_by' => $customer->getEmail(),
                    'body' => $this->renderView('orderPrivateNetworkRedundancy.html.twig', [
                        'equipmentId' => $equipment['id'],
                        'privateNetworkId' => $privateNetworkId,
                        'customerId' => $equipment['contract']['customerId'],
                        'uplinkCapacity' => $uplinkCapacity,
                    ]),
                    'subject' => "Request for adding equipment {$equipment['id']} to private network redundancy{$privateNetworkId} on behalf of customer {$equipment['contract']['customerId']}",
                    'equipments' => [[
                        'equipmentId' =>  $equipment['id']
                    ]],
                    'attributes' => [[
                        'name' => 'privateNetworkId',
                        'value' => $privateNetworkId
                    ],
                    [
                        'name' => 'serverId',
                        'value' => $equipment['id']
                    ],
                    [
                        'name' => 'uplinkCapacity',
                        'value' => $uplinkCapacity
                    ]],
                ]),
            ])->getBody(), true);
            $this->get('logger')->info("Dedicated Server to private network redundancy order request successful for private network", [
                'privateNetworkId' => $privateNetworkId,
                'serverId' => $equipment['id'],
                'uplinkCapacity' => $uplinkCapacity,
            ]);

            return new JsonResponse(['message' => "Dedicated Server to private network redundancy order request successful. Track it via ticket {$ticket['id']}. Changes are not reflected in the Customer Portal immediately."], 200);
        } catch (\Exception $exception) {
            $this->get('logger')->warning($exception->getMessage());

            return new JsonResponse(['error' => 'We were unable to process your order request. Please contact Customer Care in order to solve it.'], 400);
        }
    }

    private function orderPrivateNetworkForColocation(array $colocation, $privateNetworkId, $uplinkCapacity, $switch)
    {
        $customer = $this->get('security.token_storage')->getToken()->getUser();

        try {
            $ticket = json_decode($this->get('leaseweb.api')->post('/tickets/v2/tickets', [
                'body' => json_encode([
                    'type' => 'SERVICE',
                    'created_by' => $customer->getEmail(),
                    'body' => $this->renderView('orderPrivateNetworkForColocation.html.twig', [
                        'colocationId' => $colocation['id'],
                        'privateNetworkId' => $privateNetworkId,
                        'customerId' => $colocation['contract']['customerId'],
                        'uplinkCapacity' => $uplinkCapacity,
                        'switch' => $switch,
                    ]),
                    'subject' => "Request for adding colocation {$colocation['id']} to private network {$privateNetworkId} on behalf of customer {$colocation['contract']['customerId']}",
                    'equipments' => [[
                        'equipmentId' =>  $colocation['id']
                    ]],
                    'attributes' => [[
                        'name' => 'privateNetworkId',
                        'value' => $privateNetworkId
                    ],
                    [
                        'name' => 'colocationId',
                        'value' => $colocation['id']
                    ],
                    [
                        'name' => 'uplinkCapacity',
                        'value' => $uplinkCapacity
                    ],
                    [
                        'name' => 'switch',
                        'value' => $switch
                    ]],
                ]),
            ])->getBody(), true);
            $this->get('logger')->info('Colocation to private network order request successful for private network', [
                'privateNetworkId' => $privateNetworkId,
                'colocationId' => $colocation['id'],
                'uplinkCapacity' => $uplinkCapacity,
                'switch' => $switch,
            ]);

            return new JsonResponse(['message' => "Colocation to private network order request successful. Track it via ticket {$ticket['id']}. Changes are not reflected in the Customer Portal immediately."], 200);
        } catch (\Exception $exception) {
            $this->get('logger')->warning($exception->getMessage());

            return new JsonResponse(['error' => 'We were unable to process your order request. Please contact Customer Care in order to solve it.'], 400);
        }
    }

    private function orderPrivateNetworkForPrivateRack(array $privateRack, $privateNetworkId, $uplinkCapacity)
    {
        $customer = $this->get('security.token_storage')->getToken()->getUser();

        try {
            $ticket = json_decode($this->get('leaseweb.api')->post('/tickets/v2/tickets', [
                'body' => json_encode([
                    'type' => 'SERVICE',
                    'created_by' => $customer->getEmail(),
                    'body' => $this->renderView('orderPrivateNetworkForPrivateRack.html.twig', [
                        'privateRackId' => $privateRack['id'],
                        'privateNetworkId' => $privateNetworkId,
                        'customerId' => $privateRack['contract']['customerId'],
                        'uplinkCapacity' => $uplinkCapacity,
                    ]),
                    'subject' => "Request for adding dedicated rack {$privateRack['id']} to private network {$privateNetworkId} on behalf of customer {$privateRack['contract']['customerId']}",
                    'equipments' => [[
                        'equipmentId' =>  $privateRack['id']
                    ]],
                    'attributes' => [[
                        'name' => 'privateNetworkId',
                        'value' => $privateNetworkId
                    ],
                    [
                        'name' => 'privateRackId',
                        'value' => $privateRack['id']
                    ],
                    [
                        'name' => 'uplinkCapacity',
                        'value' => $uplinkCapacity
                    ]],
                ]),
            ])->getBody(), true);
            $this->get('logger')->info('Dedicated rack to private network order request successful for private network', [
                'privateNetworkId' => $privateNetworkId,
                'privateRackId' => $privateRack['id'],
                'uplinkCapacity' => $uplinkCapacity,
            ]);

            return new JsonResponse(['message' => "Dedicated rack to private network order request successful. Track it via ticket {$ticket['id']}. Changes are not reflected in the Customer Portal immediately."], 200);
        } catch (\Exception $exception) {
            $this->get('logger')->warning($exception->getMessage());

            return new JsonResponse(['error' => 'We were unable to process your order request. Please contact Customer Care in order to solve it.'], 400);
        }
    }

    private function orderPrivateNetworkForPrivateCloud(array $privateCloud, $privateNetworkId, $uplinkCapacity)
    {
        $customer = $this->get('security.token_storage')->getToken()->getUser();

        try {
            $ticket = json_decode($this->get('leaseweb.api')->post('/tickets/v2/tickets', [
                'body' => json_encode([
                    'type' => 'SERVICE',
                    'created_by' => $customer->getEmail(),
                    'body' => $this->renderView('order1GPrivateNetworkForPrivateCloud.html.twig', [
                        'privateCloudId' => $privateCloud['id'],
                        'privateNetworkId' => $privateNetworkId,
                        'customerId' => $privateCloud['contract']['customerId'],
                        'uplinkCapacity' => $uplinkCapacity,
                    ]),
                    'subject' => "Request for adding private cloud {$privateCloud['id']} to private network {$privateNetworkId} on behalf of customer {$privateCloud['contract']['customerId']}",
                    'equipments' => [[
                        'equipmentId' =>  $privateCloud['id']
                    ]],
                    'attributes' => [[
                        'name' => 'privateNetworkId',
                        'value' => $privateNetworkId
                    ],
                    [
                        'name' => 'privateCloudId',
                        'value' => $privateCloud['id']
                    ],
                    [
                        'name' => 'uplinkCapacity',
                        'value' => $uplinkCapacity
                    ]]
                ]),
            ])->getBody(), true);
            $this->get('logger')->info('Private cloud to private network order request successful for private network', [
                'privateNetworkId' => $privateNetworkId,
                'privateCloudId' => $privateCloud['id'],
                'uplinkCapacity' => $uplinkCapacity,
            ]);

            return new JsonResponse(['message' => "Private cloud to private network order request successful. Track it via ticket {$ticket['id']}. Changes are not reflected in the Customer Portal immediately."], 200);
        } catch (\Exception $exception) {
            $this->get('logger')->warning($exception->getMessage());

            return new JsonResponse(['error' => 'We were unable to process your order request. Please contact Customer Care in order to solve it.'], 400);
        }
    }

    private function orderPrivateNetworkForNetworkEquipment($uplinkCapacity, $equipmentType)
    {
        $customer = $this->get('security.token_storage')->getToken()->getUser();
        try {
            $ticket = json_decode($this->get('leaseweb.api')->post('/tickets/v2/tickets', [
                'body' => json_encode([
                    'type' => 'SALES',
                    'created_by' => $customer->getEmail(),
                    'body' => $this->renderView('orderPrivateNetworkForNetworkEquipment.html.twig', [
                        'equipmentType' => $equipmentType,
                        'customerId' => $customer->getCustomerId(),
                        'uplinkCapacity' => $uplinkCapacity,
                        'salesOrgId' => $customer->getSalesOrgId(),
                    ]),
                    'subject' => "Request for adding network equipment {$equipmentType} to private network on behalf of customer {$customer->getCustomerId()}}",
                    'attributes' => [[
                        'name' => 'equipmentType',
                        'value' => $equipmentType
                    ],
                    [
                        'name' => 'customerId',
                        'value' => $customer->getCustomerId()
                    ],
                    [
                        'name' => 'uplinkCapacity',
                        'value' => $uplinkCapacity
                    ],
                    [
                        'name' => 'salesOrgId',
                        'value' => $customer->getSalesOrgId()
                    ]
                    ]
                ]),
            ])->getBody(), true);
            $this->get('logger')->info('Network equipment to private network order request successful for private network', [
                'equipmentType' => $equipmentType,
                'uplinkCapacity' => $uplinkCapacity,
                'customerId' => $customer->getCustomerId(),
                'salesOrgId' => $customer->getSalesOrgId(),
            ]);

            return new JsonResponse(['message' => "Network equipment to private network order request successful. Track it via ticket {$ticket['id']}. Changes are not reflected in the Customer Portal immediately."], 200);
        } catch (\Exception $exception) {
            $this->get('logger')->warning($exception->getMessage());

            return new JsonResponse(['error' => 'We were unable to process your order request. Please contact Customer Care in order to solve it.'], 400);
        }
    }
}