<?php

namespace App\Controller\Contracts;


use App\Controller\Traits\AppBaseControllerTrait;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

class CloudConnectContractController extends Controller
{
    use AppBaseControllerTrait;

    /**
     * @Route("/bare-metals/_legacy/cloudConnect/order")
     * @Method("POST")
     */

     public function requestCloudConnectOrderAction(Request $request)
    {
        $data = json_decode($request->getContent(), true);

        $uplinkCapacity = $data['uplinkCapacity'];
        $publicCloudProvider = $data['publicCloudProvider'];
        $awsHostedType = $data['awsHostedType'];
        $sidea = $data['sidea'];
        $sideb = $data['sideb'];

        list($customerId, $salesOrgId) = $this->safelyGetCustomerIdAndSalesOrgId();


        $uplinkCapacities = [
            '100' => ['value' => '100Mbps'],
            '500' => ['value' => '500Mbps'],
            '1000' => ['value' => '1Gbps'],
            '2000' => ['value' => '2Gbps'],
            '5000' => ['value' => '5Gbps'],
            '10000' => ['value' => '10Gbps'],
        ];

        $customer = $this->get('security.token_storage')->getToken()->getUser();

        $attributes =  [[
            'name' => 'customerId',
            'value' => $customerId
        ],
        [
            'name' => 'salesOrgId',
            'value' => $salesOrgId
        ],
        [
            'name' => 'uplinkCapacity',
            'value' => $uplinkCapacities[$uplinkCapacity]['value'],
        ],
        [
            'name' => 'publicCloudProvider',
            'value' => $publicCloudProvider
        ],
        [
            'name' => 'sidea',
            'value' => $sidea
        ],
        [
            'name' => 'sideb',
            'value' => $sideb
        ]];

        if($publicCloudProvider == 'AWS'){
            $attributes[] = [
                'name' => 'awsHostedType',
                'value' => $awsHostedType
            ];
        }

        try {
            $ticket = json_decode($this->get('leaseweb.api')->post('/tickets/v2/tickets', [
                'body' => json_encode([
                    'type' => 'SALES',
                    'created_by' => $customer->getEmail(),
                    'body' => $this->renderView('orderCloudConnect.html.twig', [
                        'customerId' => $customerId,
                        'salesOrgId' => $salesOrgId,
                        'publicCloudProvider' => $publicCloudProvider,
                        'awsHostedType' => $awsHostedType,
                        'sidea' => $sidea,
                        'sideb' => $sideb,
                        'uplinkCapacity' => $uplinkCapacities[$uplinkCapacity]['value'],
                    ]),
                    'subject' => "Request for cloud connect order on behalf of customer {$customerId}",
                    'attributes' => $attributes,
                ]),
            ])->getBody(), true);
            $this->get('logger')->info('Cloud Connect order request successful', [
                'customerId' => $customerId,
                'salesOrgId' => $salesOrgId,
                'uplinkCapacity' => $uplinkCapacity,
                'publicCloudProvider' => $publicCloudProvider,
                'awsHostedType' => $awsHostedType,
                'sidea' => $sidea,
                'sideb' => $sideb,
            ]);

            return new JsonResponse(['message' => "Cloud Connect order request successful. Track it via ticket {$ticket['id']}. Changes are not reflected in the Customer Portal immediately."], 200);
        } catch (\Exception $exception) {
            $this->get('logger')->warning($exception->getMessage());

            return new JsonResponse(['error' => 'We were unable to process your order request. Please contact Customer Care in order to solve it.'], 400);
        }
    }
}
