<?php

namespace App\Controller\Contracts;

use App\Services\EntityMapping;
use Exception;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

class IpContractController extends Controller
{
    const COMMON_IPV6_CIDR_SUBNET = "112";
    const PRIVATE_COLOCATION_IPV6_CIDR_SUBNET = "64";

    /**
     * @Route("/bare-metals/_legacy/requestIpv6/options")
     * @Method("POST")
     */
    public function requestIpv6OptionsAction(Request $request)
    {
        $data = json_decode($request->getContent(), true);
        $type = trim($data['type']);

        // Disable IPv6 only for colocation equipment type
        if (in_array($type, ['Private Colocation', 'Shared Colocation']) && in_array($data['site'], $this->container->getParameter('blacklisted_ipv6_sites'))) {
            return new JsonResponse(['error' => 'Please contact Sales/Support to request IPv6.'], 400);
        }

        $ipv6Value = self::COMMON_IPV6_CIDR_SUBNET;
        if ($type == 'Private Colocation') {
            $ipv6Value = self::PRIVATE_COLOCATION_IPV6_CIDR_SUBNET;
        }

        return new JsonResponse([
            'ipv6' => $ipv6Value,
        ]);
    }

     /**
     * @Route("/bare-metals/_legacy/requestIpv6/request")
     * @Method("POST")
     */
    public function requestIpv6Action(Request $request)
    {
        $data = json_decode($request->getContent(), true);
        $equipment = $data['equipment'];
        $contractItemId = $data['contractItemId'];
        $equipmentId = $data['equipmentId'];
        $ipv6Value = $data['ipv6'];

        $customer = $this->get('security.token_storage')->getToken()->getUser();

        try {
            $ticket = json_decode($this->get('leaseweb.api')->post('/tickets/v2/tickets', [
                'body' => json_encode([
                    'type' => 'SERVICE',
                    'created_by' => $customer->getEmail(),
                    'body' => $this->renderView('requestIPv6.html.twig', [
                        'equipmentId' =>  $equipmentId,
                        'customerId' => $equipment['contract']['customerId'],
                        'ipv6Value' => $ipv6Value,
                    ]),
                    'equipments' => [[
                        'equipmentId' => $equipmentId
                    ]],
                    'subject' => "Request for IPv6 on behalf of customer {$equipment['contract']['customerId']}",
                    'attributes' =>[[
                        'name' => 'equipmentId',
                        'value' => $equipmentId
                     ],
                     [
                        'name' => 'customerId',
                        'value' => $equipment['contract']['customerId']
                     ],
                     [
                        'name' => 'ipv6Value',
                        'value' => $ipv6Value

                     ]],
                ]),
            ])->getBody(), true);
            $this->get('logger')->info('IPv6 address upgrade request successful for equipment', [
                'equipmentId' => $equipmentId,
                'ipv6Value' => $ipv6Value,
                'equipmentType' => $equipment['type'],
            ]);

            return new JsonResponse(['message' => "IPv6 address upgrade request successful for equipment {$contract['equipmentId']}."], 201);
        } catch (\Exception $exception) {
            $this->get('logger')->warning($exception->getMessage());

            return new JsonResponse(['error' => 'We were unable to process your order request. Please contact Customer Care in order to solve it.'], 400);
        }
    }
}
