{% extends 'ssc-base.html.twig' %}

{% block title %}
    Suspended Account
{% endblock title %}

{% block body %}
    {% if errorMessage %}
        <div class="alert alert-danger alert-dismissible mt-3" role="alert">
            <div class="alert-icon"><i class="fa fa-information fa-lg" aria-hidden="true"></i></div>
            <button type="button" class="close" data-dismiss="alert">
                <span aria-hidden="true">&times;</span>
                <span class="sr-only">Close</span>
            </button>
            <p class="alert-body">
            {{ errorMessage }}
            </p>
        </div>
    {% endif %}

    <div class="row">
        <div class="col-12 mt-3">
            <h2 class='text-danger mb-3'>
                Your account is suspended
            </h2>
            <p>
                All Customer Portal functions are currently disabled except
                Finance, and Tickets.
            </p>
            <p>
                To un-suspend your account, please try one of the below.
            </p>
            <ul class="mt-4 ml-3">
                <li class="mb-3">
                    <h4 class="text-info">Check your email.</h4>
                    Instructions how to un-suspend your account
                    should have been sent to you.
                </li>
                <li class="mb-3">
                    <h4 class="text-info">Check your invoices</h4>
                    Go to <a class='btn-link' href='https://secure.leaseweb.com/customer/finance'>Finance</a>
                    and check if you have pending invoices.
                </li>
                <li class="mb-3">
                    <h4 class="text-info">Contact Leaseweb</h4>
                    Contact <a href="https://secure.leaseweb.com/tickets/">Leaseweb</a>,
                    or send an email to <a class="btn-link" href="mailto:<EMAIL>"><EMAIL></a>.
                </li>
            </ul>
        </div>
    </div>
{% endblock %}
