{% set language = app.request.cookies.get('language')|default('en')|lower %}
<!doctype html>
<html lang="{{ language }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        {% if 'emp' in app.environment %}
        <title>EMP Dashboard</title>
        <base href="/emp">
        {% elseif 'cp' in app.environment %}
        <title>Customer Portal</title>
        <base href="/bare-metals">
        {% elseif 'fbs' in app.environment %}
        <title>Fiberring Portal</title>
        <base href="/connectivity">
        {% endif %}
        <link rel="icon" type="image/x-icon" href="{{ asset('/favicon.ico') }}">
        <link rel="stylesheet" href="{{ asset('/ng/browser/' ~ language ~ '/styles.css') }}" />
        {% if 'emp' in app.environment %}
        <link rel="stylesheet" type="text/css" href="{{ customerportal_assets_url }}/primeng/13.x/css/styles.css?version={{ assets_version }}"/>
        <link rel="stylesheet" type="text/css" href="{{ asset('/css/customisations/menu.css') }}"/>
        {% elseif 'cp' in app.environment %}
        <link rel="stylesheet" type="text/css" href="{{ customerportal_assets_url }}/primeng/13.x/css/styles.css?version={{ assets_version }}"/>
        {% elseif 'fbs' in app.environment %}
        <link rel="stylesheet" type="text/css" href="{{ customerportal_assets_url }}/primeng/13.x/css/fiberring-styles.css?version={{ assets_version }}"/>
        {% endif %}
        <script type="text/javascript">
            var ROLES = "{{ app.user.rolesValues|join(',') }}";
            var PERMISSIONS = "{{ app.session.get('permissions') |join(',') }}";
            var CUSTOMERID = "{{ app.user.customerId }}";
            var SALESORGID = "{{ app.user.salesOrgId }}";
            var COMMERCE_BASE_URL = "{{ commerce_base_url }}";
            var NOTIFICATION_HUB_URL = "{{ 'emp' in app.environment ? emp_notification_hub_url : cp_notification_hub_url}}";
            var NOTIFICATION_HUB_TOPIC_URL = "{{ 'emp' in app.environment ? emp_notification_hub_topic_url :  cp_notification_hub_topic_url}}";
            var NOTIFICATION_TOKEN_URL = "{{ 'emp' in app.environment ? emp_notification_token_url : cp_notification_token_url}}";
            {% if 'prod' in app.environment %}
            var matomoSiteId = "{{ 'emp' in app.environment ? '2' : '1' }}";
            var MONITORING_URL = "{{ 'emp' in app.environment ? 'https://one.leaseweb.net/monitoring' : 'https://secure.leaseweb.com/monitoring/overview' }}";
            {% else %}
            var MONITORING_URL = "https://one.staging.devleaseweb.com/monitoring/overview";
            {% endif %}
        </script>
        {% if 'cp' in app.environment %}
        <script src="{{ customerportal_assets_url }}/0.2.0/styles/js/cp-vendor-head.js?version={{ assets_version }}"></script>
        {% endif %}
    </head>
    <body>
        <cp-topbar></cp-topbar>
        <div class="container-fluid">
            <div class="row">
                {% if 'emp' in app.environment %}
                {% include "menu.html.twig" %}
                {% elseif 'cp' in app.environment %}
                <cp-sidebar baseUrl="/bare-metals"></cp-sidebar>
                {% elseif 'fbs' in app.environment %}
                <cp-sidebar baseUrl="/connectivity"></cp-sidebar>
                {% endif %}
                <div class="col align-self-start">
                    {% if 'emp' in app.environment %}
                    {% include 'header.html.twig' %}
                    {% elseif 'cp' in app.environment %}
                    <cp-header baseUrl="/bare-metals"></cp-header>
                    {% elseif 'fbs' in app.environment %}
                    <cp-header baseUrl="/connectivity"></cp-header>
                    {% endif %}
                    <div id="inner_content">
                        <div id="pagewrapper" class="container pt-5 pb-5">
                            <app-root></app-root>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script src="https://use.fortawesome.com/d78a4b7c.js"></script>
        {% if 'emp' in app.environment %}
        <script src="{{ customerportal_assets_url }}/0.2.0/styles/js/cp-internal.js?version={{ assets_version }}"></script>
        <script src="{{ customerportal_assets_url }}/0.2.0/web-components/{{ language }}/customer-portal-elements.js?version={{ assets_version }}"></script>
        {% elseif 'cp' in app.environment %}
        <script src="{{ customerportal_assets_url }}/0.2.0/styles/js/cp-vendor.js?version={{ assets_version }}"></script>
        <script src="{{ customerportal_assets_url }}/0.2.0/web-components/{{ language }}/customer-portal-elements.js?version={{ assets_version }}"></script>
        {% elseif 'fbs' in app.environment %}
        <script src="{{ customerportal_assets_url }}/0.2.0/fiberring/styles/js/cp-vendor.js?version={{ assets_version }}"></script>
        <script src="{{ customerportal_assets_url }}/0.2.0/fiberring/web-components/customer-portal-elements.js?version={{ assets_version }}"></script>
        {% endif %}
        <script src="{{ asset('/ng/browser/' ~ language ~ '/polyfills.js') }}" type="module"></script>
        <script src="{{ asset('/ng/browser/' ~ language ~ '/scripts.js') }}" type="module"></script>
        <script src="{{ asset('/ng/browser/' ~ language ~ '/main.js') }}" type="module"></script>
        <script src="https://www-static.ripe.net/static/rnd-ui/atlas/static/measurements/widgets/latencymon/latencymon-widget-main.js"></script>
        {% if 'cp' not in app.environment %}
        <script src="https://www-static.ripe.net/static/rnd-ui/atlas/static/measurements/widgets/tracemon/tracemon-widget-main.js"></script>
        {% endif %}
    </body>
</html>
