{% set language = app.request.cookies.get('language')|default('en')|lower %}
<!-- @format -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    {% if 'emp' in app.environment %}
    <title>EMP Dashboard</title>
    <base href="/emp">
    <link rel="stylesheet" type="text/css" href="{{ asset('/css/customisations/menu-v3.css') }}"/>
    {% elseif 'cp' in app.environment %}
    <title>Customer Portal</title>
    <base href="/bare-metals">
    {% elseif 'fbs' in app.environment %}
    <title>Fiberring Portal</title>
    <base href="/connectivity">
    {% endif %}
    <link rel="stylesheet" type="text/css" href="{{ cp_css_url_v3 }}">
    <link rel="stylesheet" href="{{ asset('/ng/browser/' ~ language ~ '/styles.css') }}" />
    {% if 'cp' in app.environment %}
    <script type="text/javascript" src="{{ cp_vendor_head_js_url_v3 }}"></script>
    <script type="text/javascript" src="{{ cp_elements_base_url_v3 }}/{{ language }}/{{ cp_elements_file_name_v3 }}?version={{ assets_version }}"></script>
    {% elseif 'emp' in app.environment %}
    <script type="text/javascript" src="{{ cp_elements_base_url_v3 }}/{{ language }}/{{ cp_elements_file_name_v3 }}?version={{ assets_version }}"></script>
    {% endif %}
    <link rel="icon" type="image/x-icon" href="{{ asset('/favicon.ico') }}">
    <script type="text/javascript">
        var ROLES = "{{ app.user.rolesValues|join(',') }}";
        var PERMISSIONS = "{{ app.session.get('permissions') |join(',') }}";
        var CUSTOMERID = "{{ app.user.customerId }}";
        var SALESORGID = "{{ app.user.salesOrgId }}";
        var COMMERCE_BASE_URL = "{{ commerce_base_url }}";
        var NOTIFICATION_HUB_URL = "{{ 'emp' in app.environment ? emp_notification_hub_url : cp_notification_hub_url}}";
        var NOTIFICATION_HUB_TOPIC_URL = "{{ 'emp' in app.environment ? emp_notification_hub_topic_url :  cp_notification_hub_topic_url}}";
        var NOTIFICATION_TOKEN_URL = "{{ 'emp' in app.environment ? emp_notification_token_url : cp_notification_token_url}}";
        {% if 'prod' in app.environment %}
        var matomoSiteId = "{{ 'emp' in app.environment ? '2' : '1' }}";
        var MONITORING_URL = "{{ 'emp' in app.environment ? 'https://one.leaseweb.net/monitoring' : 'https://secure.leaseweb.com/monitoring/overview' }}";
        {% else %}
        var MONITORING_URL = "https://one.staging.devleaseweb.com/monitoring/overview";
        {% endif %}
    </script>
  </head>
  <body>
    <cp-topbar></cp-topbar>
    <div class="container-grid">
      <div id="sidebar">
        {% if 'emp' in app.environment %}
        {% include "menu-v3.html.twig" %}
        {% elseif 'cp' in app.environment %}
        <cp-sidebar baseUrl="/bare-metals"></cp-sidebar>
        {% elseif 'fbs' in app.environment %}
        <cp-sidebar baseUrl="/connectivity"></cp-sidebar>
        {% endif %}
      </div>
      <div id="content">
        {% if 'emp' in app.environment %}
        <cp-header baseUrl="/bare-metals"></cp-header>
        {% elseif 'cp' in app.environment %}
        <cp-header baseUrl="/bare-metals"></cp-header>
        {% elseif 'fbs' in app.environment %}
        <cp-header baseUrl="/connectivity"></cp-header>
        {% endif %}
        <div class="m-3 my-5 lg:m-8">
          <app-root></app-root>
        </div>
      </div>
    </div>
    {% if 'emp' in app.environment %}
    <script>
      document.addEventListener('DOMContentLoaded', function(event) {
        const dropdownToggles = document.querySelectorAll('#sideNavigationBar a.nav-link.dropdown-toggle');
        dropdownToggles.forEach(function(toggle) {
            toggle.addEventListener('click', function(event) {
                // Prevent the default link behavior
                event.preventDefault();

                this.parentElement.classList.toggle('show');

                // Toggle the 'show' class on the clicked element
                this.classList.toggle('hide-border-bottom');

                const dropdownMenu = this.parentElement.querySelector('.dropdown-menu');

                if (dropdownMenu) {
                    dropdownMenu.classList.toggle('show');
                }
            });
        });
      });
    </script>
    {% elseif 'cp' in app.environment %}
    <script type="text/javascript" src="{{ cp_vendor_js_url_v3 }}"></script>
    {% endif %}
    <script src="{{ asset('/ng/browser/' ~ language ~ '/polyfills.js') }}" type="module"></script>
    <script src="{{ asset('/ng/browser/' ~ language ~ '/scripts.js') }}" type="module"></script>
    <script src="{{ asset('/ng/browser/' ~ language ~ '/main.js') }}" type="module"></script>
    <script src="https://www-static.ripe.net/static/rnd-ui/atlas/static/measurements/widgets/latencymon/latencymon-widget-main.js"></script>
    <script src="https://use.fortawesome.com/d78a4b7c.js"></script>
  </body>
</html>