<nav bma-custom-navbar id="sideNavigationBar" class="sidebarNavigation flex flex-column navbar absolute lg:static navbar-expand-lg col-11 md:col-4 lg:col-3 xl:col-2 bg-primary relative">
  <button pstyleclass="@parent" toggleclass="show" type="button" data-toggle="collapse" data-target="#sideNavigationBar" aria-controls="sideNavigationBar" aria-expanded="false" aria-label="Toggle navigation" class="p-element navbar-toggler">
    <span class="bar"></span>
    <span class="bar"></span>
    <span class="bar"></span>
  </button>

  <ul class="nav flex-column mt-4">
    <li class="nav-item">
      <a href="/emp" target="">
        <div class="nav-link w-full">
          <i aria-hidden="true" class="fa fa-search"></i>
          <span class="label">Lookup</span>
        </div>
      </a>
    </li>
    <li class="nav-item">
      <a data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false" class="nav-link dropdown-toggle">
        <i class="fa fa-server" aria-hidden="true"></i>
        <span class="label">Bare Metal Automation</span>
      </a>
      <div class="dropdown-menu" id="Bare Metal Automation">
        <a class="dropdown-item"  href="/emp/jobs"><div>BMP-API Jobs</div></a>
        <a class="dropdown-item" href="/emp/job-wizard"><div>Job Wizard</div></a>
        <a class="dropdown-item" href="/emp/dhcp-reservations"><div>DHCP Reservations</div></a>
        <a class="dropdown-item" href="/emp/firmware-versions"><div>Firmware versions overview</div></a>
        <a class="dropdown-marker"></a>
      </div>
    </li>

    <li class="nav-item site-navigation">
        <a data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false" class="nav-link dropdown-toggle">
            <i class="fa fa-network" aria-hidden="true"></i>
            <span class="label">Network Automation</span>
        </a>
        <div class="dropdown-menu {% if app.request.pathinfo in ['/emp/nse/sites-configuration', '/emp/networkDevices', '/emp/privateNetworks', '/emp/floating-ips', '/emp/ddos/profiles', '/emp/ddos/anomalies', '/emp/vlans', '/emp/ip-performance/measurements','/emp/arp-checker/entries'] %}show{% endif %}">
            <a class="dropdown-item" href="/emp/nse/sites-configuration"><div>Site Configuration</div></a>
            <a class="dropdown-item" href="/emp/networkDevices"><div>Network Devices</div></a>
            <a class="dropdown-item" href="/emp/privateNetworks"><div>Private Networks</div></a>
            <a class="dropdown-item" href="/emp/floating-ips"><div>Floating IP Ranges</div></a>
            <a class="dropdown-item" href="/emp/ddos/profiles"><div>DDoS IP Protection</div></a>
            <a class="dropdown-item" href="/emp/ddos/anomalies"><div>Active Anomalies</div></a>
            <a class="dropdown-item" href="/emp/vlans"><div>Vlans</div></a>
            <a class="dropdown-item" href="/emp/ip-performance/measurements"><div>IP Performance Measurements</div></a>
            <a class="dropdown-item" href="/emp/arp-checker/entries"><div>ARP Checker</div></a>
        </div>
    </li>

    <li class="nav-item site-navigation">
        <a class="nav-link active dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true">
            <i class="fa fa-powercycle" aria-hidden="true"></i>
            <span class="label">Power Automation</span>
        </a>
        <div class="dropdown-menu {% if app.request.pathinfo starts with '/emp/powerbars' %}show{% endif %}">
            <a class="dropdown-item" href="/emp/powerbars"><div>PDU & ATS</div></a>
        </div>
    </li>

    <li class="nav-item site-navigation">
        <a class="nav-link active dropdown-toggle" href data-toggle="dropdown" role="button" aria-haspopup="true">
            <i class="fa fa-log" aria-hidden="true"></i>
            <span class="label">Audit Logs</span>
        </a>
        <div class="dropdown-menu {% if app.request.pathinfo starts with '/emp/audit-logs' %}show{% endif %}">
            <a class="dropdown-item" href="/emp/audit-logs"><div>Audit Logs</div></a>
        </div>
    </li>

    <li class="nav-item site-navigation">
        <a class="nav-link active dropdown-toggle" href data-toggle="dropdown" role="button" aria-haspopup="true">
            <i class="fa fa-change" aria-hidden="true"></i>
            <span class="label">Feedback</span>
        </a>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="https://ideas.leaseweb.dev/"><div>Features & Ideas</div></a>
            <a class="dropdown-item" href="https://one.leaseweb.net/serviceportal#reportIncident"><div>Report an Incident</div></a>
            <a class="dropdown-item" href="https://mattermost.leaseweb.com/leaseweb/channels/bma-nwa-support"><div>BareMetal Support Channel</div></a>
        </div>
    </li>

    <li id="product_navbar" class="nav-item customer-product-menu show d-none">
        <a class="nav-link active dropdown-toggle" href="#" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="true">
            <img id="flag" src="{{ customerportal_assets_url }}/0.2.0/styles/images/flags.png?version={{ assets_version }}"/>
            <span id="customer-id-label" class="label"></span>
        </a>
        <div class="dropdown-menu show">
            <a target="_self" id="dedicated_servers" class="dropdown-item " href="#" id="">
                <div>Dedicated Servers</div>
            </a>
            <a target="_self" id="dedicated_network_equipments" class="dropdown-item " href="#" id="">
                <div>Dedicated Network Equipment</div>
            </a>
            <a target="_self" id="private_racks" class="dropdown-item " href="#" id="">
                <div>Dedicated Racks</div>
            </a>
            <a target="_self" id="colocation" class="dropdown-item " href="#" id="">
                <div>Colocations</div>
            </a>
            <a target="_self" id="dedicated_storages" class="dropdown-item " href="#" id="">
                <div>Dedicated Storages</div>
            </a>
            <a target="_self" id="private_networks" class="dropdown-item " href="#" id="">
                <div>Private Networks</div>
            </a>
            <a target="_self" id="floating_ips" class="dropdown-item " href="#" id="">
                <div>Floating IPs</div>
            </a>
            <a target="_self" id="ddos_ip_protection_advanced" class="dropdown-item " href="#" id="">
                <div>DDoS IP Protection Advanced</div>
            </a>
            <a target="_self" id="aggregation_packs" class="dropdown-item " href="#" id="">
                <div>Aggregation Packs</div>
            </a>
            <a target="_self" id="metrics_report" class="dropdown-item " href="#" id="">
                <div>Metrics Report</div>
            </a>

            <a target="_self" id="ip_transits" class="dropdown-item " href="#" id="">
                <div>IP Transits</div>
            </a>

            <a target="_self" id="direct_internet_access" class="dropdown-item " href="#" id="">
                <div>Direct Internet Access</div>
            </a>

            <a target="_self" id="cloud_connects" class="dropdown-item " href="#" id="">
                <div>Cloud Connects</div>
            </a>
        </div>
    </li>
  </ul>
</nav>

<script>
    window.addEventListener('update_product_navbar', function(event) {
        var productNavbar = document.getElementById('product_navbar');
        productNavbar.classList.remove('d-none');
        document.getElementById('customer-id-label').innerHTML = event.customerId;
        document.getElementById('flag').setAttribute('class', 'lsw-flags flag-'+ event.country +' mt-2 mr-1');
        document.getElementById('dedicated_servers').setAttribute('href', '/emp/servers?customerId='+ event.customerId +'&salesOrgId='+ event.salesOrgId);
        document.getElementById('dedicated_network_equipments').setAttribute('href', '/emp/networkEquipments?customerId='+ event.customerId +'&salesOrgId='+ event.salesOrgId);
        document.getElementById('dedicated_storages').setAttribute('href', '/emp/dedicatedStorages?customerId='+ event.customerId +'&salesOrgId='+ event.salesOrgId);
        document.getElementById('private_racks').setAttribute('href', '/emp/racks?customerId='+ event.customerId +'&salesOrgId='+ event.salesOrgId);
        document.getElementById('colocation').setAttribute('href', '/emp/colocations?customerId='+ event.customerId +'&salesOrgId='+ event.salesOrgId);
        document.getElementById('private_networks').setAttribute('href', '/emp/privateNetwork/lookup?customerId='+ event.customerId +'&salesOrgId='+ event.salesOrgId);
        document.getElementById('floating_ips').setAttribute('href', '/emp/floating-ips?customerId='+ event.customerId +'&salesOrgId='+ event.salesOrgId);
        document.getElementById('ddos_ip_protection_advanced').setAttribute('href', '/emp/ddos-ip-protection?customerId='+ event.customerId +'&salesOrgId='+ event.salesOrgId);
        document.getElementById('aggregation_packs').setAttribute('href', '/emp/aggregationPacks?customerId='+ event.customerId +'&salesOrgId='+ event.salesOrgId);
        document.getElementById('metrics_report').setAttribute('href', '/emp/metrics/overview?customerId='+ event.customerId +'&salesOrgId='+ event.salesOrgId);
        document.getElementById('cloud_connects').setAttribute('href', '/emp/cloud-connects?customerId='+ event.customerId +'&salesOrgId='+ event.salesOrgId);

        if(event.salesOrgId === '2200') {
            document.getElementById('direct_internet_access').setAttribute('href', '/emp/directInternetAccess?customerId='+ event.customerId +'&salesOrgId='+ event.salesOrgId);
            document.getElementById('ip_transits').setAttribute('href', '/emp/ipTransits?customerId='+ event.customerId +'&salesOrgId='+ event.salesOrgId);
            document.getElementById('direct_internet_access').style.display = 'block';
            document.getElementById('ip_transits').style.display = 'block';
        } else {
            document.getElementById('direct_internet_access').style.display = 'none';
            document.getElementById('ip_transits').style.display = 'none';
        }
    })
</script>