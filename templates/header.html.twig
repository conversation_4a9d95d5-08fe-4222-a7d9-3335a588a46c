<header class="row flex-wrap justify-content-between navbar navbar-expand bd-navbar bg-white mb-3">
    <a class="navbar-brand d-lg-none mr-n5 order-md-1" href="/">
        <img alt="Logo" class="mx-auto d-block" width="265" src="{{ customerportal_assets_url }}/0.2.0/styles/images/logo.svg?version={{ assets_version }}">
    </a>
    <ul class="navbar-nav flex-row ml-md-auto order-md-3">
        <li class="nav-item dropdown ml-1 show">
            <a aria-expanded="false" aria-haspopup="true" aria-hidden="true" class="nav-item dropdown-toggle mr-md-2 p-0 text-center" data-toggle="dropdown" href>
                <i class="fa fa-account" aria-hidden="true"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right pb-3">
                <h3 class="mt-2 ml-4 mr-4 mb-1">{{ app.user.username }}</h3>
                <p class="mt-1 ml-4 mr-4 mb-1">
                    <ul>
                        {% for role in app.user.roles %}
                        <li>{{ role.role }}</li>
                        {% endfor %}
                    </ul>
                </p>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="{{ profile_baseurl|default }}">My Profile</a>
                <a class="dropdown-item" href="/emp/logout">Logout</a>
                {% if 'emp' in app.environment or 'dev' in app.environment %}
                <div class="btn-group">
                    <button type="button" {{ app.request.cookies.get('language')|lower != 'en' ?: "disabled" }} onclick="document.cookie='language=en';window.location.reload()">EN</button>
                    <button type="button" {{ app.request.cookies.get('language')|lower != 'fr' ?: "disabled" }} onclick="document.cookie='language=fr';window.location.reload()">FR</button>
                </div>
                {% endif %}
            </div>
        </li>
    </ul>
</header>
