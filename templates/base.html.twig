{% set language = app.request.cookies.get('language')|default('en')|lower %}
<!DOCTYPE html>
<html lang="en">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta charset="utf-8" name="viewport" content="width=device-width, initial-scale=1">

        <link rel="shortcut icon" href="/favicon.ico"/>
        <link rel="icon" type="image/x-icon" href="/favicon.ico">
        <link rel="search" type="application/opensearchdescription+xml" href="{{ app.request.getSchemeAndHttpHost() }}/osd.xml" title="Leaseweb EMP"/>

        <link rel="stylesheet" type="text/css" href="{{ asset('/css/customisations/common.css') }}"/>
        <link rel="stylesheet" type="text/css" href="{{ customerportal_assets_url }}/primeng/13.x/css/styles.css?version={{ assets_version }}"/>

        <script type="text/javascript">
            window.isEmployee = true;
            var matomoSiteId = '2';
        </script>

        <script src="https://use.fortawesome.com/d78a4b7c.js"></script>
        <script src="{{ customerportal_assets_url }}/0.2.0/styles/js/cp-internal.js?version={{ assets_version }}"></script>
        <script src="{{ customerportal_assets_url }}/0.2.0/web-components/{{ language }}/customer-portal-elements.js?version={{ assets_version }}"></script>

        <title>
            {% block title %}{% endblock %} | Leaseweb
        </title>

        {% block stylesheets %}{% endblock %}
    </head>
    <body>
        <cp-topbar></cp-topbar>
        <div class="container-fluid">
            <div class="row">
                {% include "menu.html.twig" %}
                <div class="col align-self-start">
                    {% include 'header.html.twig' %}
                    <div id="inner_content">
                        <div id="pagewrapper" class="container pt-5 pb-5">
                            {% block body %}{% endblock %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% block javascript %}{% endblock %}
    </body>
</html>
